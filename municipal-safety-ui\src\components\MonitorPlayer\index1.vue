<script lang="ts" setup>
import EZUIKit from "ezuikit-js";

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const props = defineProps({
    url: {
        type: Array,
        default: [],
    },
    accessToken: {
        type: String,
        default: "",
    },
    width: {
        type: Number,
        default: 600,
    },
    height: {
        type: Number,
        default: 400,
    },
    monitorVal: {
        type: Number,
        default: 1,
    },
    monitorIndex: {
        type: Number,
        default: 0,
    },
    index0: {
        type: Number,
        default: 0,
    }
});

const emit = defineEmits(["capturePicture"]);
const player = ref([]);

const destroy = () => {
    const destroyPromise = player.value[props.monitorIndex].destroy();
    destroyPromise.then((data: any) => {
        console.log("promise 获取 数据", data);
    });
    player.value[props.monitorIndex] = null!;
};

const init = (url: string[]) => {
    if (player.value[props.monitorIndex]) {
        destroy();
    }
    // fetch("https://open.ys7.com/jssdk/ezopen/demo/token")
    //   .then((response) => response.json())
    //   .then((res) => {
    //     var accessToken = res.data.accessToken;
    player.value[props.monitorIndex] = new EZUIKit.EZUIKitPlayer({
        id: `video-container${props.monitorIndex}`, // 视频容器ID
        accessToken: props.accessToken,
        url: url[props.monitorIndex],
        // simple: 极简版; pcLive: pc直播; pcRec: pc回放; mobileLive: 移动端直播; mobileRec: 移动端回放;security: 安防版; voice: 语音版;
        template: "pcLive",
        plugin: ["talk"], // 加载插件，talk-对讲
        width: props.width,
        height: props.height,
        // language: "en", // zh | en
        handleError: (err: any) => {
            console.error("handleError", err);
        },
        // staticPath: "/ezuikit_static", // 如果想使用本地静态资源，请复制根目录下ezuikit_static 到当前目录下， 然后设置该值
        env: {
            // https://open.ys7.com/help/1772?h=domain
            // domain默认是 https://open.ys7.com, 如果是私有化部署或海外的环境，请配置对应的domain
            // The default domain is https://open.ys7.com If it is a private deployment or overseas (outside of China) environment, please configure the corresponding domain
            domain: "https://open.ys7.com",
        },
        // staticPath: "https://openstatic.ys7.com/ezuikit_js/v8.1.9/ezuikit_static",
        // 日志打印设置
        loggerOptions: {
            // player.setLoggerOptions(options)
            level: "INFO", // INFO LOG  WARN  ERROR
            name: "ezuikit",
            showTime: true,
        },
        // 视频流的信息回调类型
        /**
         * 打开流信息回调，监听 streamInfoCB 事件
         * 0 : 每次都回调
         * 1 : 只回调一次
         * 注意：会影响性能
         * 默认值 1
         */
        streamInfoCBType: 1,
        download: false, // 是否支持下载
    });
    player.value[props.monitorIndex].eventEmitter.on(
        EZUIKit.EZUIKitPlayer.EVENTS.videoInfo,
        (info: any) => {
            console.log("videoinfo", info);
        },
    );

    player.value[props.monitorIndex].eventEmitter.on(
        EZUIKit.EZUIKitPlayer.EVENTS.audioInfo,
        (info: any) => {
            console.log("audioInfo", info);
        },
    );

    // 首帧渲染成功
    // first frame display
    player.value[props.monitorIndex].eventEmitter.on(
        EZUIKit.EZUIKitPlayer.EVENTS.firstFrameDisplay,
        () => {
            console.log("firstFrameDisplay ");
        },
    );
    player.value[props.monitorIndex].eventEmitter.on(
        EZUIKit.EZUIKitPlayer.EVENTS.streamInfoCB,
        (info: any) => {
            console.log("streamInfoCB ", info);
        },
    );
    // 监听截图事件
    player.value[props.monitorIndex].eventEmitter.on(EZUIKit.EZUIKitPlayer.EVENTS.capturePicture, (info: any) => {
        emit("capturePicture", info.data.base64);
    });
    window.player = player[props.monitorIndex];
    // });
};
watch(() => props.url, (val: string[]) => {
    if (val) {
        nextTick(() => {
            if (props.monitorIndex == props.index0) {
                init(val);
            }
        })
    }
},
    {
        immediate: true,
        deep: true
    }
)
onBeforeUnmount(() => {
    player.value.forEach((item: any) => {
        item.destroy();
        item = null!;
    })
})
</script>

<template>
    <div :class="`hello-ezuikit-js${monitorVal}`">
        <div>
            <div :id="`video-container${index0}`"></div>
        </div>
    </div>
</template>
<style>
.hello-ezuikit-js1 {
    width: 100% !important;

    .header-controls {
        opacity: 0 !important;
    }

    #video-container0-wrap {
        width: 100% !important;
        height: 76vh !important;

        #video-container0 {
            width: 100% !important;
            height: 100% !important;
        }
    }
}

.hello-ezuikit-js4 {
    width: 100% !important;

    .header-controls {
        opacity: 0 !important;
    }

    #video-container0-wrap {
        width: 100% !important;
        height: 36.7vh !important;

        #video-container0 {
            width: 100% !important;
            height: 100% !important;
        }
    }

    #video-container1-wrap {
        width: 100% !important;
        height: 36.7vh !important;

        #video-container1 {
            width: 100% !important;
            height: 100% !important;
        }
    }

    #video-container2-wrap {
        width: 100% !important;
        height: 36.7vh !important;

        #video-container2 {
            width: 100% !important;
            height: 100% !important;
        }
    }

    #video-container3-wrap {
        width: 100% !important;
        height: 36.7vh !important;

        #video-container3 {
            width: 100% !important;
            height: 100% !important;
        }
    }
}

.hello-ezuikit-js9 {
    width: 100% !important;

    .header-controls {
        opacity: 0 !important;
    }

    /* .footer-controls {
        width: auto !important;
    } */

    #video-container0-wrap {
        width: 100% !important;
        height: 24vh !important;

        #video-container0 {
            width: 100% !important;
            height: 100% !important;
        }
    }

    #video-container1-wrap {
        width: 100% !important;
        height: 24vh !important;

        #video-container1 {
            width: 100% !important;
            height: 100% !important;
        }
    }

    #video-container2-wrap {
        width: 100% !important;
        height: 24vh !important;

        #video-container2 {
            width: 100% !important;
            height: 100% !important;
        }
    }

    #video-container3-wrap {
        width: 100% !important;
        height: 24vh !important;

        #video-container3 {
            width: 100% !important;
            height: 100% !important;
        }
    }

    #video-container4-wrap {
        width: 100% !important;
        height: 24vh !important;

        #video-container4 {
            width: 100% !important;
            height: 100% !important;
        }
    }

    #video-container5-wrap {
        width: 100% !important;
        height: 24vh !important;

        #video-container5 {
            width: 100% !important;
            height: 100% !important;
        }
    }

    #video-container6-wrap {
        width: 100% !important;
        height: 24vh !important;

        #video-container6 {
            width: 100% !important;
            height: 100% !important;
        }
    }

    #video-container7-wrap {
        width: 100% !important;
        height: 24vh !important;

        #video-container7 {
            width: 100% !important;
            height: 100% !important;
        }
    }

    #video-container8-wrap {
        width: 100% !important;
        height: 24vh !important;

        #video-container8 {
            width: 100% !important;
            height: 100% !important;
        }
    }
}

/* .hello-ezuikit-js12 {
    width: 100% !important;

    .header-controls {
        opacity: 0 !important;
    }

    #video-container-wrap {
        width: 100% !important;
        height: 24vh !important;

        #video-container {
            width: 100% !important;
            height: 100% !important;
        }
    }
} */
</style>

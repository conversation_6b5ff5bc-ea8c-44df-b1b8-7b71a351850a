2025-05-01 08:28:18 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-01 08:28:18 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 9580 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-01 08:28:18 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-01 08:28:23 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-01 08:28:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-01 08:28:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-01 08:28:24 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@524418a2
2025-05-01 08:28:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-01 08:28:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-01 08:28:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-01 08:28:25 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-01 08:28:26 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-01 08:28:26 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-01 08:28:26 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-01 08:28:27 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-01 08:49:31 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-01 08:49:51 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-01 08:49:51 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 7932 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-01 08:49:51 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-01 08:49:56 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-01 08:49:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-01 08:49:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-01 08:49:57 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@588614f9
2025-05-01 08:49:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-01 08:49:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-01 08:49:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-01 08:49:58 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-01 08:49:58 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-01 08:49:59 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-01 08:49:59 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-01 08:50:00 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>
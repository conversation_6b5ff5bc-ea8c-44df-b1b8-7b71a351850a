{"doc": " 数据权限处理\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "doBefore", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.mybatis.annotation.DataPermission"], "doc": " 处理请求前执行\n"}, {"name": "doAfterReturning", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.mybatis.annotation.DataPermission"], "doc": " 处理完请求后执行\n\n @param joinPoint 切点\n"}, {"name": "doAfterThrowing", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.mybatis.annotation.DataPermission", "java.lang.Exception"], "doc": " 拦截异常操作\n\n @param joinPoint 切点\n @param e         异常\n"}], "constructors": []}
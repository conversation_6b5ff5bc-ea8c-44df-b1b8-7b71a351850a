package org.dromara.quality.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 设备管理对象 quality_device
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("quality_device")
public class QualityDevice extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    @TableId(value = "device_id")
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 规格型号
     */
    private String specification;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 设备图片OSS ID
     */
    private Long deviceImageOssId;

    /**
     * 设备简介
     */
    private String deviceDescription;

    /**
     * 设备用途
     */
    private String devicePurpose;

    /**
     * 使用说明
     */
    private String usageInstructions;

    /**
     * 使用说明书OSS ID
     */
    private Long manualFileOssId;

    /**
     * 设备状态（0正常 1停用）
     */
    private String status;

    /**
     * 版本
     */
    @Version
    private Long version;

    /**
     * 删除标志
     */
    @TableLogic
    private Long delFlag;

}

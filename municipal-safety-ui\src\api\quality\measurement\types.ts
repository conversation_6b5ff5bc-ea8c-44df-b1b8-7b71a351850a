/**
 * 实测实量查询对象类型
 */
export interface MeasurementQuery extends PageQuery {
  deviceId?: number;
  measurementItem?: string;
  status?: string;
  createTime?: string[];
}

/**
 * 实测实量返回对象
 */
export interface MeasurementVO extends BaseEntity {
  measurementId: number;
  deviceId: number;
  deviceName?: string;
  measurementItem: string;
  measurementResult: number;
  standardValue: number;
  tolerance: number;
  unit: string;
  measurementTime: string;
  measurementLocation: string;
  measurementPerson: string;
  status: string;
  isHazardMarked: boolean;
  hazardDescription?: string;
  remark?: string;
  createByName?: string;
  updateByName?: string;
}

/**
 * 实测实量表单类型
 */
export interface MeasurementForm {
  measurementId?: number;
  deviceId: number;
  measurementItem: string;
  measurementResult: number;
  standardValue: number;
  tolerance: number;
  unit: string;
  measurementTime: string;
  measurementLocation: string;
  measurementPerson: string;
  status: string;
  isHazardMarked?: boolean;
  hazardDescription?: string;
  remark?: string;
}

/**
 * 设备信息响应
 */
export interface DeviceInfoResponse {
  deviceId: number;
  deviceName: string;
  specification: string;
  deviceCode: string;
}

/**
 * 推送信息请求
 */
export interface PushInfoRequest {
  measurementId: number;
  pushType: string;
  pushContent: string;
  recipients: string[];
}

/**
 * 标记隐患请求
 */
export interface MarkHazardRequest {
  measurementId: number;
  hazardDescription: string;
}

/**
 * 测量统计数据
 */
export interface MeasurementStatistics {
  total: number;
  normalCount: number;
  abnormalCount: number;
  hazardCount: number;
  retestCount: number;
  increase: number;
  increaseRate: number;
  normalRate: number;
  hazardRate: number;
}

/**
 * 图表数据查询参数
 */
export interface ChartDataQuery {
  period: 'week' | 'month' | 'quarter';
  startDate?: string;
  endDate?: string;
}

/**
 * 图表数据响应
 */
export interface ChartDataResponse {
  dates: string[];
  normalData: number[];
  abnormalData: number[];
  hazardData: number[];
}

/**
 * 质量管理统计概览
 */
export interface QualityOverview {
  deviceStats: {
    total: number;
    increase: number;
  };
  measurementStats: {
    total: number;
    normal: number;
    hazard: number;
    increase: number;
    normalRate: number;
    hazardRate: number;
  };
  pendingTasks: {
    hazardCount: number;
    retestCount: number;
    reportCount: number;
  };
}

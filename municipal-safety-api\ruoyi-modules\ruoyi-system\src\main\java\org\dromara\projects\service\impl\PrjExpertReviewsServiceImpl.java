package org.dromara.projects.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.expert.domain.Expert;
import org.dromara.expert.mapper.ExpertMapper;
import org.dromara.projects.domain.PrjExpertReviewParticipants;
import org.dromara.projects.domain.PrjExpertReviews;
import org.dromara.projects.domain.bo.ExpertFullBo;
import org.dromara.projects.domain.bo.ExpertUserBo;
import org.dromara.projects.domain.vo.PrjExpertDetailVo;
import org.dromara.projects.mapper.PrjExpertReviewParticipantsMapper;
import org.dromara.projects.mapper.PrjExpertReviewsMapper;
import org.dromara.projects.service.IPrjExpertReviewsService;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.mapper.SysUserMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * [项目管理] 记录专项施工方案专家论证会议，包含利害关系预警Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-06-05
 */
@RequiredArgsConstructor
@Service
public class PrjExpertReviewsServiceImpl implements IPrjExpertReviewsService {

    private final PrjExpertReviewsMapper baseMapper;
    private final PrjExpertReviewParticipantsMapper prjExpertReviewParticipantsMapper;
    private final ExpertMapper expertMapper;
    private final SysUserMapper sysUserMapper;

    /**
     * 保存专家信息
     *
     * @param expertBo 专家信息对象
     * @return 保存结果，true表示保存成功，false表示保存失败
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean save(ExpertFullBo expertBo) {
        //存储会议
        PrjExpertReviews reviews = expertBo.getPrjExpertReviews();

        //发起者为当前用户
        reviews.setConvenorUserId(LoginHelper.getUserId());

        baseMapper.insertOrUpdate(reviews);

        //清理之前的专家
        LambdaQueryWrapper<PrjExpertReviewParticipants> deleteWrapper = Wrappers.lambdaQuery();
        deleteWrapper.eq(PrjExpertReviewParticipants::getReviewId, reviews.getReviewId());

        List<PrjExpertReviewParticipants> oldPrjExpert = prjExpertReviewParticipantsMapper.selectList(deleteWrapper);
        Map<Long, PrjExpertReviewParticipants> oldExpertMap = oldPrjExpert.stream().collect(Collectors.toMap(PrjExpertReviewParticipants::getUserId, t -> t));

        //现在的专家解析
        List<ExpertUserBo> expertUser = expertBo.getExpertUser();

        //身份证提取
        List<Long> idCardList = expertUser.stream().map(ExpertUserBo::getUserId).toList();

        LambdaQueryWrapper<SysUser> wrapper = Wrappers.lambdaQuery();
        wrapper.in(SysUser::getUserName, idCardList);

        List<SysUserVo> sysUserVos = sysUserMapper.selectVoList(wrapper);
        Map<String, SysUserVo> sysUserVoMap = sysUserVos.stream().collect(Collectors.toMap(SysUserVo::getUserName, t -> t));


        List<PrjExpertReviewParticipants> expertList = new ArrayList<>();

        for (ExpertUserBo userBo : expertUser) {
            SysUserVo sysUserVo = sysUserVoMap.get(userBo.getUserId().toString());

            PrjExpertReviewParticipants reviewParticipants = oldExpertMap.remove(sysUserVo.getUserId());

            PrjExpertReviewParticipants participants = new PrjExpertReviewParticipants();
            participants.setReviewId(reviews.getReviewId());
            participants.setUserId(sysUserVo.getUserId());
            participants.setRoleInMeeting(userBo.getRoleInMeeting());
            participants.setIsAttendingExpert(userBo.getIsAttendingExpert());
            participants.setParticipantId(reviewParticipants == null ? null : reviewParticipants.getParticipantId());

            expertList.add(participants);
        }

        if (CollectionUtil.isNotEmpty(expertList)) {

            List<PrjExpertReviewParticipants> updateList = expertList.stream().filter(t -> t.getParticipantId() != null).toList();
            List<PrjExpertReviewParticipants> insertList = expertList.stream().filter(t -> t.getParticipantId() == null).toList();
            //批量存储新的
            prjExpertReviewParticipantsMapper.insertBatch(insertList);
            prjExpertReviewParticipantsMapper.updateBatchById(updateList);
            //删除旧的
            if (CollectionUtil.isNotEmpty(oldExpertMap)) {
                List<Long> ids = oldExpertMap.values().stream().map(PrjExpertReviewParticipants::getParticipantId).toList();
                prjExpertReviewParticipantsMapper.deleteBatchIds(ids);
            }
        }

        return true;
    }

    /**
     * 获取项目专家详情
     *
     * @param planId 项目ID
     * @return 项目专家详情对象
     */
    @Override
    public PrjExpertDetailVo getDetail(Long planId) {

        LambdaQueryWrapper<PrjExpertReviews> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PrjExpertReviews::getPlanId, planId);

        //查询会议
        PrjExpertReviews reviews = baseMapper.selectOne(wrapper);

        if (reviews == null) {
            return null;
        }

        //查询专家
        LambdaQueryWrapper<PrjExpertReviewParticipants> expertWrapper = Wrappers.lambdaQuery();
        expertWrapper.eq(PrjExpertReviewParticipants::getReviewId, reviews.getReviewId());

        List<PrjExpertReviewParticipants> participants = prjExpertReviewParticipantsMapper.selectList(expertWrapper);

        if (participants.isEmpty()) {
            return null;
        }

        List<Long> userIds = participants.stream().map(PrjExpertReviewParticipants::getUserId).toList();

        //查询用户信息
        List<SysUser> sysUsers = sysUserMapper.selectByIds(userIds);

        //查询专家信息
        List<String> idCardList = sysUsers.stream().map(SysUser::getUserName).toList();
        LambdaQueryWrapper<Expert> expertLambdaQueryWrapper = Wrappers.lambdaQuery();
        expertLambdaQueryWrapper.in(Expert::getIdCard, idCardList);

        List<Expert> experts = expertMapper.selectList(expertLambdaQueryWrapper);

        return new PrjExpertDetailVo(reviews, participants, experts, sysUsers);
    }
}

{"doc": " 绿能烟感对象 ln_smoke\n\n <AUTHOR>\n @date 2025-07-25\n", "fields": [{"name": "id", "doc": " 主键\n"}, {"name": "deviceNo", "doc": " 编号\n"}, {"name": "deviceName", "doc": " 设备名称\n"}, {"name": "eventCode", "doc": " 事件类型\n"}, {"name": "eventContent", "doc": " 上报时间\n"}, {"name": "eventTime", "doc": " 上报时间\n"}, {"name": "devNo", "doc": " 设备编号\n"}, {"name": "delFlag", "doc": " 删除标志 (0代表存在 1代表删除)\n"}, {"name": "createTime", "doc": " 创建时间\n"}], "enumConstants": [], "methods": [], "constructors": []}
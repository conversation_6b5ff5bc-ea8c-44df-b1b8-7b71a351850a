package org.dromara.flow.service;

import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistQuestionBo;
import org.dromara.flow.domain.bo.PrjSpecialistQuestionDTO;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistQuestionVo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 省厅自动工单专家建议Service接口
 *
 * <AUTHOR> Li
 * @date 2025-06-21
 */
public interface IPrjHazardousItemsSpecialistQuestionService {

    /**
     * 查询省厅自动工单专家建议
     *
     * @param id 主键
     * @return 省厅自动工单专家建议
     */
    PrjHazardousItemsSpecialistQuestionVo queryById(Long id);

    /**
     * 分页查询省厅自动工单专家建议列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 省厅自动工单专家建议分页列表
     */
    TableDataInfo<PrjHazardousItemsSpecialistQuestionVo> queryPageList(PrjHazardousItemsSpecialistQuestionBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的省厅自动工单专家建议列表
     *
     * @param bo 查询条件
     * @return 省厅自动工单专家建议列表
     */
    List<PrjHazardousItemsSpecialistQuestionVo> queryList(PrjHazardousItemsSpecialistQuestionBo bo);

    /**
     * 新增省厅自动工单专家建议
     *
     * @param bo 省厅自动工单专家建议
     * @return 是否新增成功
     */
    Boolean insertByBo(PrjHazardousItemsSpecialistQuestionBo bo);

    /**
     * 修改省厅自动工单专家建议
     *
     * @param bo 省厅自动工单专家建议
     * @return 是否修改成功
     */
    Boolean updateByBo(PrjHazardousItemsSpecialistQuestionBo bo);

    /**
     * 校验并批量删除省厅自动工单专家建议信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean addQuestion(List<PrjSpecialistQuestionDTO> questionDTOS);

    /**
     * 获取单个专家的详情
     *
     * @param taskId
     * @return
     */
    Map<String, Object> getDetail(String taskId);

    /**
     * 获取所有专家意见
     *
     * @param taskId
     * @return
     */
    Map<Long, List<PrjHazardousItemsSpecialistQuestionVo>> getDetailAll(String taskId);
}

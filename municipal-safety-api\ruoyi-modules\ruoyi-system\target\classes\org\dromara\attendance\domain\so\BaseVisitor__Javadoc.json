{"doc": " 访客系统人员名单对象 base_visitor\n\n <AUTHOR>\n @date 2025-04-15\n", "fields": [{"name": "id", "doc": " id\n"}, {"name": "userId", "doc": " 用户id\n"}, {"name": "code", "doc": " 人员ID\n"}, {"name": "name", "doc": " 人员姓名\n"}, {"name": "mobile", "doc": " 手机号\n"}, {"name": "registerFace", "doc": " 注册人脸\n"}, {"name": "role", "doc": " 人员角色固定传1。 0->普通人员。1->白名单人员。2->黑名单人员。-1->所有人员。\n"}, {"name": "accessCard", "doc": " 门禁卡号生成方式。0-共有卡号，1-自动生成，2-手动输入\n"}, {"name": "accessCardNumber", "doc": " 门禁卡号\n"}, {"name": "rollType", "doc": " 名单类型。0-永久名单，1-临时名单\n"}, {"name": "isUpload", "doc": " 是否下发。0-是，1-否\n"}, {"name": "scheduling", "doc": " 调度级别\n"}, {"name": "startTime", "doc": " 起始时间\n"}, {"name": "endTime", "doc": " 到期时间\n"}, {"name": "isExpired", "doc": " 是否过期\n"}, {"name": "content", "doc": " 自定义内容\n"}, {"name": "deleted", "doc": " 是否删除。0-否，1-是\n"}, {"name": "deptId", "doc": " 部门id\n"}], "enumConstants": [], "methods": [], "constructors": []}
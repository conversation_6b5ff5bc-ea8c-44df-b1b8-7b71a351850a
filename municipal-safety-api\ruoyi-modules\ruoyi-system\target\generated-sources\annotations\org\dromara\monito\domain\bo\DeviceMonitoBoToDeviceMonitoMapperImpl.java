package org.dromara.monito.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.monito.domain.DeviceMonito;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class DeviceMonitoBoToDeviceMonitoMapperImpl implements DeviceMonitoBoToDeviceMonitoMapper {

    @Override
    public DeviceMonito convert(DeviceMonitoBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        DeviceMonito deviceMonito = new DeviceMonito();

        deviceMonito.setSearchValue( arg0.getSearchValue() );
        deviceMonito.setCreateDept( arg0.getCreateDept() );
        deviceMonito.setCreateBy( arg0.getCreateBy() );
        deviceMonito.setCreateTime( arg0.getCreateTime() );
        deviceMonito.setUpdateBy( arg0.getUpdateBy() );
        deviceMonito.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            deviceMonito.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        deviceMonito.setMonitoId( arg0.getMonitoId() );
        deviceMonito.setProjectId( arg0.getProjectId() );
        deviceMonito.setItemId( arg0.getItemId() );
        deviceMonito.setDeviceName( arg0.getDeviceName() );
        deviceMonito.setDeviceType( arg0.getDeviceType() );
        deviceMonito.setDeviceCode( arg0.getDeviceCode() );
        deviceMonito.setDeviceStatus( arg0.getDeviceStatus() );
        deviceMonito.setEnableSnapshot( arg0.getEnableSnapshot() );
        deviceMonito.setSnapshotTime( arg0.getSnapshotTime() );
        deviceMonito.setRemarks( arg0.getRemarks() );
        deviceMonito.setChannelNo( arg0.getChannelNo() );

        return deviceMonito;
    }

    @Override
    public DeviceMonito convert(DeviceMonitoBo arg0, DeviceMonito arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setMonitoId( arg0.getMonitoId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setDeviceName( arg0.getDeviceName() );
        arg1.setDeviceType( arg0.getDeviceType() );
        arg1.setDeviceCode( arg0.getDeviceCode() );
        arg1.setDeviceStatus( arg0.getDeviceStatus() );
        arg1.setEnableSnapshot( arg0.getEnableSnapshot() );
        arg1.setSnapshotTime( arg0.getSnapshotTime() );
        arg1.setRemarks( arg0.getRemarks() );
        arg1.setChannelNo( arg0.getChannelNo() );

        return arg1;
    }
}

package org.dromara.facility.service;

import org.dromara.facility.domain.vo.JlLifterRealVo;
import org.dromara.facility.domain.bo.JlLifterRealBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 升降机实时数据Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
public interface IJlLifterRealService extends BaseFacilityHandle {

    /**
     * 查询升降机实时数据
     *
     * @param id 主键
     * @return 升降机实时数据
     */
    JlLifterRealVo queryById(Long id);

    /**
     * 分页查询升降机实时数据列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 升降机实时数据分页列表
     */
    TableDataInfo<JlLifterRealVo> queryPageList(JlLifterRealBo bo, PageQuery pageQuery);

    /**
     * 新增升降机实时数据
     *
     * @param bo 升降机实时数据
     * @return 是否新增成功
     */
    Boolean insertByBo(JlLifterRealBo bo);
}

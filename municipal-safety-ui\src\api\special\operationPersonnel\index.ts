import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { OperationPersonnelVO, OperationPersonnelForm, OperationPersonnelQuery } from '@/api/special/operationPersonnel/types';

/**
 * 查询特种作业人员信息列表
 * @param query
 * @returns {*}
 */

export const listOperationPersonnel = (query?: OperationPersonnelQuery): AxiosPromise<OperationPersonnelVO[]> => {
  return request({
    url: '/special/operationPersonnel/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询特种作业人员信息详细
 * @param sopId
 */
export const getOperationPersonnel = (sopId: string | number): AxiosPromise<OperationPersonnelVO> => {
  return request({
    url: '/special/operationPersonnel/' + sopId,
    method: 'get'
  });
};

/**
 * 新增特种作业人员信息
 * @param data
 */
export const addOperationPersonnel = (data: OperationPersonnelForm) => {
  return request({
    url: '/special/operationPersonnel',
    method: 'post',
    data: data
  });
};

/**
 * 修改特种作业人员信息
 * @param data
 */
export const updateOperationPersonnel = (data: OperationPersonnelForm) => {
  return request({
    url: '/special/operationPersonnel',
    method: 'put',
    data: data
  });
};

/**
 * 删除特种作业人员信息
 * @param sopId
 */
export const delOperationPersonnel = (sopId: string | number | Array<string | number>) => {
  return request({
    url: '/special/operationPersonnel/' + sopId,
    method: 'delete'
  });
};
/**
 * 同步特种作业人员信息
 * @param sopId
 */
export const equipmentsync = (data) => {
  return request({
    url: '/special/equipment/sync',
    method: 'post',
    data
  });
};

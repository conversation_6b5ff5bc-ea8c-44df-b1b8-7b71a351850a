{"doc": " <AUTHOR>\n @date 2025/6/12 16:18\n @Description TODO\n @Version 1.0\n", "fields": [], "enumConstants": [], "methods": [{"name": "xqzg", "paramTypes": ["org.dromara.flow.domain.dto.DownTemplateDTO", "jakarta.servlet.http.HttpServletResponse"], "doc": " 限期整改\n\n @param dto\n @param response\n @throws IOException\n"}, {"name": "tgzg", "paramTypes": ["org.dromara.flow.domain.dto.DownTemplateDTO", "jakarta.servlet.http.HttpServletResponse"], "doc": " 停工通知\n\n @param dto\n @param response\n @throws IOException\n"}, {"name": "xzcfjds", "paramTypes": ["org.dromara.flow.domain.dto.DownTemplateDTO", "jakarta.servlet.http.HttpServletResponse"], "doc": " 行政处罚决定书\n\n @param dto\n @param response\n @throws IOException\n"}], "constructors": []}
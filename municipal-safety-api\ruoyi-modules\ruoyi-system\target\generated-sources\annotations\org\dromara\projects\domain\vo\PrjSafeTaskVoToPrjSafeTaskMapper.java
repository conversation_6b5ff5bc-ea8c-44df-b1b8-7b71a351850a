package org.dromara.projects.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.PrjSafeTask;
import org.dromara.projects.domain.PrjSafeTaskToPrjSafeTaskVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjSafeTaskToPrjSafeTaskVoMapper.class},
    imports = {}
)
public interface PrjSafeTaskVoToPrjSafeTaskMapper extends BaseMapper<PrjSafeTaskVo, PrjSafeTask> {
}

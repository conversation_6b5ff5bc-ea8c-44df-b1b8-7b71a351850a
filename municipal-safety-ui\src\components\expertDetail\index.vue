<template>
  <div class="expertDetail" v-if="expertDetailData?.expertId">
    <!-- 专家信息详情弹框 -->
    <el-dialog :title="dialogDetail.title" v-model="dialogDetail.visible" append-to-body @close="handleClose"
      width="60%">
      <el-descriptions border title="专家基本信息" :column="4">
        <el-descriptions-item label="头像" :rowspan="2" align="center" :min-width="100">
          <HeaderPrewiew :src="expertDetailData.avatar" :width="50" :height="50"
            :preview-src-list="[expertDetailData.avatar]"></HeaderPrewiew>
        </el-descriptions-item>
        <el-descriptions-item label="名称" align="center" :min-width="100">{{ expertDetailData.name
        }}</el-descriptions-item>
        <el-descriptions-item label="性别" align="center" :min-width="100">
          <dict-tag :options="sys_user_sex" :value="expertDetailData.sex" />
        </el-descriptions-item>
        <el-descriptions-item label="身份证号" align="center">{{ expertDetailData.idCard
        }}</el-descriptions-item>
        <el-descriptions-item label="职称" align="center">{{ expertDetailData.title }}</el-descriptions-item>
        <el-descriptions-item label="电话" align="center">{{ expertDetailData.phone }}</el-descriptions-item>
        <el-descriptions-item label="工作单位" align="center">{{ expertDetailData.workUnit }}</el-descriptions-item>
        <el-descriptions-item label="专业" align="center">
          <dict-tag :options="expert_major" :value="expertDetailData.major" />
        </el-descriptions-item>
        <el-descriptions-item label="行业" align="center">
          <dict-tag :options="expert_industry" :value="expertDetailData.industry" />
        </el-descriptions-item>
        <el-descriptions-item label="专家类型" align="center">
          <dict-tag :options="expert_type" :value="expertDetailData.type" />
        </el-descriptions-item>
        <el-descriptions-item label="专家所在地" align="center">{{ expertDetailData.expertArea }}</el-descriptions-item>
        <el-descriptions-item label="简介" label-align="center">{{ expertDetailData.introduce }}</el-descriptions-item>
      </el-descriptions>
      <p style="color: #303133;font-size: 16px;font-weight: bold;margin: 20px 0;">参与项目</p>
      <el-table class="expertTable2" v-loading="loading" :data="expertDetailData.expertProjectList"
        show-overflow-tooltip>
        <el-table-column label="项目名称" align="center" prop="name" min-width="120px" />
        <el-table-column label="项目状态" align="center" prop="certificateType" min-width="120px">
          <template #default="scope">
            <dict-tag :options="expert_project_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="开始时间" align="center" prop="startDate" min-width="120px"></el-table-column>
        <el-table-column label="结束时间" align="center" prop="endDate" min-width="120px" />
        <el-table-column label="项目地点" align="center" prop="location" min-width="120px" />
        <el-table-column label="项目描述" align="center" prop="description" min-width="150px"></el-table-column>
      </el-table>
      <p style="color: #303133;font-size: 16px;font-weight: bold;margin: 20px 0;">专家领域</p>
      <el-table class="expertTable2" v-loading="loading" :data="expertDetailData.expertFieldList" show-overflow-tooltip>
        <el-table-column label="领域名称" align="center" prop="name" />
        <el-table-column label="领域简介" align="center" prop="description" />
        <el-table-column label="创建时间" align="center" prop="createTime">
          <template #default="scope">
            {{ dayjs(scope.row.createTime).format('YYYY-MM-DD') }}
          </template>
        </el-table-column>
        <el-table-column label="更新时间" align="center" prop="updateTime">
          <template #default="scope">
            {{ dayjs(scope.row.updateTime).format('YYYY-MM-DD') }}
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogDetail.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getExpert } from '@/api/expert/expert';
import { listByIds } from '@/api/system/oss/index'
import HeaderPrewiew from '@/components/ImagePreview/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { expert_industry, sys_user_sex, expert_major, expert_type, expert_project_status } = toRefs<any>(proxy?.useDict('expert_industry', 'sys_user_sex', 'expert_major', 'expert_type', 'expert_project_status'));
import { ExpertVO, ExpertQuery, ExpertForm } from '@/api/expert/expert/types'
import dayjs from 'dayjs';

const emit = defineEmits(['update:isShowModel']);
const props = defineProps({
  expertId: {
    type: String,
    default: ''
  },
  isShowModel: {
    type: Boolean,
    default: false
  }
});
// 专家详情信息的列表数据
const expertDetailData = ref();
// 专家信息详情弹框
const dialogDetail = reactive<DialogOption>({
  visible: false,
  title: ''
});
const loading = ref(false)
watch(() => props.isShowModel, (val) => {
  dialogDetail.visible = val;
  getExpertDetail(props.expertId);
})
// 使用ossId查询图片的url地址
const getHeaderUrl = async (ossId: string | number) => {
  const { data } = await listByIds(ossId);
  return data[0].url;
}
// 详情点击事件方法
const getExpertDetail = async (expertId: string) => {
  loading.value = true;
  const { data } = await getExpert(expertId);
  expertDetailData.value = data;
  expertDetailData.value.avatar = await getHeaderUrl(expertDetailData.value.avatar);
  expertDetailData.value.expertArea = expertDetailData.value.provinceName + ' ' + expertDetailData.value.cityName + ' ' + expertDetailData.value.areaName;
  dialogDetail.title = "专家信息详情";
  loading.value = false;
}
// 关闭详情弹框
const handleClose = () => {
  emit('update:isShowModel', false);
}
</script>

<style lang="scss">
.expertTable2 {

  .el-popper,
  .is-dark {
    max-width: 220px;
  }
}
</style>
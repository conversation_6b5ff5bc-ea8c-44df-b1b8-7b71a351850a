package org.dromara.flow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.flow.domain.PrjHazardousItemsSpecialist;
import org.dromara.flow.domain.PrjHazardousItemsSpecialistQuestion;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistQuestionBo;
import org.dromara.flow.domain.bo.PrjSpecialistQuestionDTO;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistQuestionVo;
import org.dromara.flow.mapper.PrjHazardousItemsSpecialistMapper;
import org.dromara.flow.mapper.PrjHazardousItemsSpecialistQuestionMapper;
import org.dromara.flow.service.IPrjHazardousItemsSpecialistQuestionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 省厅自动工单专家建议Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-06-21
 */
@RequiredArgsConstructor
@Service
public class PrjHazardousItemsSpecialistQuestionServiceImpl implements IPrjHazardousItemsSpecialistQuestionService {

    private final PrjHazardousItemsSpecialistQuestionMapper baseMapper;
    private final PrjHazardousItemsSpecialistMapper prjHazardousItemsSpecialistMapper;

    /**
     * 查询省厅自动工单专家建议
     *
     * @param id 主键
     * @return 省厅自动工单专家建议
     */
    @Override
    public PrjHazardousItemsSpecialistQuestionVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询省厅自动工单专家建议列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 省厅自动工单专家建议分页列表
     */
    @Override
    public TableDataInfo<PrjHazardousItemsSpecialistQuestionVo> queryPageList(PrjHazardousItemsSpecialistQuestionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PrjHazardousItemsSpecialistQuestion> lqw = buildQueryWrapper(bo);
        Page<PrjHazardousItemsSpecialistQuestionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的省厅自动工单专家建议列表
     *
     * @param bo 查询条件
     * @return 省厅自动工单专家建议列表
     */
    @Override
    public List<PrjHazardousItemsSpecialistQuestionVo> queryList(PrjHazardousItemsSpecialistQuestionBo bo) {
        LambdaQueryWrapper<PrjHazardousItemsSpecialistQuestion> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PrjHazardousItemsSpecialistQuestion> buildQueryWrapper(PrjHazardousItemsSpecialistQuestionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PrjHazardousItemsSpecialistQuestion> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PrjHazardousItemsSpecialistQuestion::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), PrjHazardousItemsSpecialistQuestion::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getDetail()), PrjHazardousItemsSpecialistQuestion::getDetail, bo.getDetail());
        lqw.eq(bo.getResultId() != null, PrjHazardousItemsSpecialistQuestion::getResultId, bo.getResultId());
        lqw.eq(bo.getSpecialist() != null, PrjHazardousItemsSpecialistQuestion::getSpecialist, bo.getSpecialist());
        return lqw;
    }

    /**
     * 新增省厅自动工单专家建议
     *
     * @param bo 省厅自动工单专家建议
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PrjHazardousItemsSpecialistQuestionBo bo) {
        PrjHazardousItemsSpecialistQuestion add = MapstructUtils.convert(bo, PrjHazardousItemsSpecialistQuestion.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改省厅自动工单专家建议
     *
     * @param bo 省厅自动工单专家建议
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PrjHazardousItemsSpecialistQuestionBo bo) {
        PrjHazardousItemsSpecialistQuestion update = MapstructUtils.convert(bo, PrjHazardousItemsSpecialistQuestion.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PrjHazardousItemsSpecialistQuestion entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除省厅自动工单专家建议信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Transactional
    @Override
    public Boolean addQuestion(List<PrjSpecialistQuestionDTO> questionDTOS) {

        List<PrjHazardousItemsSpecialistQuestion> questions = new ArrayList<>();

        PrjSpecialistQuestionDTO dto = questionDTOS.get(0);
        String taskId = dto.getTaskId();

        LambdaQueryWrapper<PrjHazardousItemsSpecialist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PrjHazardousItemsSpecialist::getTaskId, taskId)
            .select(PrjHazardousItemsSpecialist::getId, PrjHazardousItemsSpecialist::getTaskId);

        PrjHazardousItemsSpecialist specialist = prjHazardousItemsSpecialistMapper.selectOne(wrapper);

        Long id = specialist.getId();

        String nickname = LoginHelper.getLoginUser().getNickname();

        for (PrjSpecialistQuestionDTO questionDTO : questionDTOS) {
            PrjHazardousItemsSpecialistQuestion question = new PrjHazardousItemsSpecialistQuestion();
            question.setName(nickname);
            question.setDetail(questionDTO.getDetail());
            question.setResultId(questionDTO.getResultId());
            question.setSpecialist(id);
            questions.add(question);
        }

        return baseMapper.insertBatch(questions);
    }

    @Override
    public Map<String, Object> getDetail(String taskId) {

        Map<String, Object> map = new HashMap<>();

        LambdaQueryWrapper<PrjHazardousItemsSpecialist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PrjHazardousItemsSpecialist::getTaskId, taskId)
            .select(PrjHazardousItemsSpecialist::getId, PrjHazardousItemsSpecialist::getTaskId, PrjHazardousItemsSpecialist::getInstruction);

        PrjHazardousItemsSpecialist specialist = prjHazardousItemsSpecialistMapper.selectOne(wrapper);

        LambdaQueryWrapper<PrjHazardousItemsSpecialistQuestion> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PrjHazardousItemsSpecialistQuestion::getSpecialist, specialist.getId())
            .eq(BaseEntity::getCreateBy, LoginHelper.getUserId());

        List<PrjHazardousItemsSpecialistQuestionVo> prjHazardousItemsSpecialistQuestionVos = baseMapper.selectVoList(queryWrapper);

        map.put("question", prjHazardousItemsSpecialistQuestionVos);
        map.put("instruction", specialist.getInstruction());

        return map;
    }

    @Override
    public Map<Long, List<PrjHazardousItemsSpecialistQuestionVo>> getDetailAll(String taskId) {

        LambdaQueryWrapper<PrjHazardousItemsSpecialist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PrjHazardousItemsSpecialist::getTaskId, taskId)
            .select(PrjHazardousItemsSpecialist::getId, PrjHazardousItemsSpecialist::getTaskId, PrjHazardousItemsSpecialist::getInstruction);

        PrjHazardousItemsSpecialist specialist = prjHazardousItemsSpecialistMapper.selectOne(wrapper);

        LambdaQueryWrapper<PrjHazardousItemsSpecialistQuestion> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PrjHazardousItemsSpecialistQuestion::getSpecialist, specialist.getId());

        List<PrjHazardousItemsSpecialistQuestionVo> prjHazardousItemsSpecialistQuestionVos = baseMapper.selectVoList(queryWrapper);

        return prjHazardousItemsSpecialistQuestionVos.stream().collect(Collectors.groupingBy(PrjHazardousItemsSpecialistQuestionVo::getResultId));
    }
}

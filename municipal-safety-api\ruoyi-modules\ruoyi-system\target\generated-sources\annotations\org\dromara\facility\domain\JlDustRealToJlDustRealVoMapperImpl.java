package org.dromara.facility.domain;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.vo.JlDustRealVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class JlDustRealToJlDustRealVoMapperImpl implements JlDustRealToJlDustRealVoMapper {

    @Override
    public JlDustRealVo convert(JlDustReal arg0) {
        if ( arg0 == null ) {
            return null;
        }

        JlDustRealVo jlDustRealVo = new JlDustRealVo();

        jlDustRealVo.setId( arg0.getId() );
        jlDustRealVo.setMn( arg0.getMn() );
        jlDustRealVo.setDatatime( arg0.getDatatime() );
        jlDustRealVo.setB03Avg( arg0.getB03Avg() );
        jlDustRealVo.setB03Flag( arg0.getB03Flag() );
        jlDustRealVo.setPm25Avg( arg0.getPm25Avg() );
        jlDustRealVo.setPm25Flag( arg0.getPm25Flag() );
        jlDustRealVo.setPm10Avg( arg0.getPm10Avg() );
        jlDustRealVo.setPm10Flag( arg0.getPm10Flag() );
        jlDustRealVo.setW02Avg( arg0.getW02Avg() );
        jlDustRealVo.setW02Flag( arg0.getW02Flag() );
        jlDustRealVo.setW01Avg( arg0.getW01Avg() );
        jlDustRealVo.setW01Flag( arg0.getW01Flag() );
        jlDustRealVo.setT01Avg( arg0.getT01Avg() );
        jlDustRealVo.setT01Flag( arg0.getT01Flag() );
        jlDustRealVo.setH01Avg( arg0.getH01Avg() );
        jlDustRealVo.setH01Flag( arg0.getH01Flag() );
        jlDustRealVo.setTspAvg( arg0.getTspAvg() );
        jlDustRealVo.setTspFlag( arg0.getTspFlag() );
        jlDustRealVo.setO3Avg( arg0.getO3Avg() );
        jlDustRealVo.setO3Flag( arg0.getO3Flag() );
        jlDustRealVo.setCoAvg( arg0.getCoAvg() );
        jlDustRealVo.setCoFlag( arg0.getCoFlag() );
        jlDustRealVo.setSo2Avg( arg0.getSo2Avg() );
        jlDustRealVo.setSo2Flag( arg0.getSo2Flag() );
        jlDustRealVo.setNo2Avg( arg0.getNo2Avg() );
        jlDustRealVo.setNo2Flag( arg0.getNo2Flag() );
        jlDustRealVo.setA01006Rtd( arg0.getA01006Rtd() );
        jlDustRealVo.setA01006Flag( arg0.getA01006Flag() );

        return jlDustRealVo;
    }

    @Override
    public JlDustRealVo convert(JlDustReal arg0, JlDustRealVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setMn( arg0.getMn() );
        arg1.setDatatime( arg0.getDatatime() );
        arg1.setB03Avg( arg0.getB03Avg() );
        arg1.setB03Flag( arg0.getB03Flag() );
        arg1.setPm25Avg( arg0.getPm25Avg() );
        arg1.setPm25Flag( arg0.getPm25Flag() );
        arg1.setPm10Avg( arg0.getPm10Avg() );
        arg1.setPm10Flag( arg0.getPm10Flag() );
        arg1.setW02Avg( arg0.getW02Avg() );
        arg1.setW02Flag( arg0.getW02Flag() );
        arg1.setW01Avg( arg0.getW01Avg() );
        arg1.setW01Flag( arg0.getW01Flag() );
        arg1.setT01Avg( arg0.getT01Avg() );
        arg1.setT01Flag( arg0.getT01Flag() );
        arg1.setH01Avg( arg0.getH01Avg() );
        arg1.setH01Flag( arg0.getH01Flag() );
        arg1.setTspAvg( arg0.getTspAvg() );
        arg1.setTspFlag( arg0.getTspFlag() );
        arg1.setO3Avg( arg0.getO3Avg() );
        arg1.setO3Flag( arg0.getO3Flag() );
        arg1.setCoAvg( arg0.getCoAvg() );
        arg1.setCoFlag( arg0.getCoFlag() );
        arg1.setSo2Avg( arg0.getSo2Avg() );
        arg1.setSo2Flag( arg0.getSo2Flag() );
        arg1.setNo2Avg( arg0.getNo2Avg() );
        arg1.setNo2Flag( arg0.getNo2Flag() );
        arg1.setA01006Rtd( arg0.getA01006Rtd() );
        arg1.setA01006Flag( arg0.getA01006Flag() );

        return arg1;
    }
}

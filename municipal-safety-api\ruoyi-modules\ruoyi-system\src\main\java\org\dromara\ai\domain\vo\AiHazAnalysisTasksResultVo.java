package org.dromara.ai.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.ai.domain.AiHazAnalysisTasksResult;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;


/**
 * 隐患AI分析结果视图对象 ai_haz_analysis_tasks_result
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiHazAnalysisTasksResult.class)
public class AiHazAnalysisTasksResultVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 隐患结果主键
     */
    @ExcelProperty(value = "隐患结果主键")
    private Long resultId;

    /**
     * 隐患描述
     */
    @ExcelProperty(value = "隐患描述")
    private Long taskId;

    /**
     * 隐患描述
     */
    @ExcelProperty(value = "隐患描述")
    private String violation;

    /**
     * 涉及条款
     */
    @ExcelProperty(value = "涉及条款")
    private String regulation;

    /**
     * 位置坐标
     */
    @ExcelProperty(value = "位置坐标")
    private String coordinate;

    /**
     * 危险评估等级（2：重大隐患，1：一般隐患）
     */
    @ExcelProperty(value = "危险评估等级", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "hidden_danger_type")
    private String level;

    /**
     * 整改措施建议
     */
    @ExcelProperty(value = "整改措施建议")
    private String measure;


}

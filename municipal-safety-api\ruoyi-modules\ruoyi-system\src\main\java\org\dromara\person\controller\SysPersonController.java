package org.dromara.person.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.ContentType;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.ObjectUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.person.domain.bo.SysPersonBo;
import org.dromara.person.domain.vo.SysPersonVo;
import org.dromara.person.service.ISysPersonService;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.ISysOssService;
import org.dromara.util.ImageResizeUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * 人员基本信息
 *
 * <AUTHOR> zu da
 * @date 2025-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/person")
public class SysPersonController extends BaseController {

    private final ISysPersonService sysPersonService;
    private final ISysOssService sysOssService;

    /**
     * 查询人员基本信息列表
     */
    @SaCheckPermission("system:person:list")
    @GetMapping("/list")
    public TableDataInfo<SysPersonVo> list(SysPersonBo bo, PageQuery pageQuery) {
        return sysPersonService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出人员基本信息列表
     */
    @SaCheckPermission("system:person:export")
    @Log(title = "人员基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysPersonBo bo, HttpServletResponse response) {
        List<SysPersonVo> list = sysPersonService.queryList(bo);
        ExcelUtil.exportExcel(list, "人员基本信息", SysPersonVo.class, response);
    }

    /**
     * 获取人员基本信息详细信息
     *
     * @param personId 主键
     */
    @SaCheckPermission("system:person:query")
    @GetMapping("/{personId}")
    public R<SysPersonVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long personId) {
        return R.ok(sysPersonService.queryById(personId));
    }

    /**
     * 新增人员基本信息
     */
    @SaCheckPermission("system:person:add")
    @Log(title = "人员基本信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysPersonBo bo) throws IOException {
        SysPersonVo sysPersonVo = sysPersonService.queryByIdCard(bo.getIdCard());

        if (ObjectUtils.isNotEmpty(bo.getHeadImgId())) {
            SysOssVo ossVo = sysOssService.getById(bo.getHeadImgId());

            String url = ossVo.getUrl();

            InputStream inputStream = ImageResizeUtils.downloadImage(url);

            byte[] resizeImageJpg = ImageResizeUtils.resizeImageJpg(inputStream, 30);


            SysOssVo sysOssVo = sysOssService.upload(new ByteArrayInputStream(resizeImageJpg), IdUtil.fastSimpleUUID()
                , ".jpg", ContentType.MULTIPART.getValue());

            bo.setHeadImgMini(sysOssVo.getOssId());
        }

        if (sysPersonVo != null) {
            if (sysPersonVo.getEnterpriseId() == -1) {
                bo.setPersonId(sysPersonVo.getPersonId());
                return toAjax(sysPersonService.updateByBo(bo));
            }
        }
        return toAjax(sysPersonService.insertByBo(bo));
    }

    /**
     * 修改人员基本信息
     */
    @SaCheckPermission("system:person:edit")
    @Log(title = "人员基本信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysPersonBo bo) throws IOException {

        SysPersonVo sysPersonVo = sysPersonService.queryById(bo.getPersonId());
        if (!bo.getHeadImgId().equals(sysPersonVo.getHeadImgId())) {

            SysOssVo ossVo = sysOssService.getById(bo.getHeadImgId());

            String url = ossVo.getUrl();

            InputStream inputStream = ImageResizeUtils.downloadImage(url);

            byte[] resizeImageJpg = ImageResizeUtils.resizeImageJpg(inputStream, 30);


            SysOssVo sysOssVo = sysOssService.upload(new ByteArrayInputStream(resizeImageJpg), IdUtil.fastSimpleUUID()
                , ".jpg", ContentType.MULTIPART.getValue());

            bo.setHeadImgMini(sysOssVo.getOssId());
        }

        return toAjax(sysPersonService.updateByBo(bo));
    }

    /**
     * 人员迁出
     *
     * @param sysPersonBo
     * @return
     */
    @SaCheckPermission("system:person:edit")
    @PostMapping("/moveOut")
    public R<Void> moveOut(@RequestBody SysPersonBo sysPersonBo) {
        sysPersonBo.setEnterpriseId(-1L);
        return toAjax(sysPersonService.moveOutByBo(sysPersonBo));
    }

    /**
     * 删除人员基本信息
     *
     * @param personIds 主键串
     */
    @SaCheckPermission("system:person:remove")
    @Log(title = "人员基本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{personIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] personIds) {
        return toAjax(sysPersonService.deleteWithValidByIds(List.of(personIds), true));
    }

}

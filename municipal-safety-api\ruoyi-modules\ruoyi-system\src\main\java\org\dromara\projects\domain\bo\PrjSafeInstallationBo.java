package org.dromara.projects.domain.bo;

import org.dromara.projects.domain.PrjSafeInstallation;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 【安拆任务】安拆人员业务对象 prj_safe_installation
 *
 * <AUTHOR> Li
 * @date 2025-08-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjSafeInstallation.class, reverseConvertGenerate = false)
public class PrjSafeInstallationBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long installationId;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 自定义岗位名称
     */
    private String userPositionName;

    /**
     * 人脸照片地址
     */
    private String face;

    /**
     * 资质证书地址
     */
    private String certificate;

    /**
     * 关联安拆prj_safe_task.open_task_id
     */
    @NotNull(message = "关联安拆prj_safe_task.open_task_id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long openTaskId;
}

package org.dromara.ai.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.ai.domain.AiHazAnalysisTasks;
import org.dromara.ai.domain.AiHazAnalysisTasksResult;
import org.dromara.ai.domain.bo.AiHazAnalysisTasksBo;
import org.dromara.ai.domain.dto.AiAnalysisResultDto;
import org.dromara.ai.domain.dto.AiHazAnalysisTasksDto;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksVo;
import org.dromara.ai.enums.AiHazAnalysisTasksReCheckStatus;
import org.dromara.ai.enums.AiHazAnalysisTasksStatus;
import org.dromara.ai.mapper.AiHazAnalysisTasksMapper;
import org.dromara.ai.mapper.AiHazAnalysisTasksResultMapper;
import org.dromara.ai.service.IAiHazAnalysisTasksService;
import org.dromara.common.core.domain.dto.StartProcessReturnDTO;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.flow.domain.PrjHazardousItemsSpecialist;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistBo;
import org.dromara.flow.mapper.PrjHazardousItemsSpecialistMapper;
import org.dromara.projects.domain.vo.PrjHazardousItemsVo;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.mapper.PrjHazardousItemsMapper;
import org.dromara.projects.mapper.PrjProjectsMapper;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.vo.DangerListVo;
import org.dromara.system.mapper.SysConfigMapper;
import org.dromara.system.mapper.SysUserMapper;
import org.dromara.system.service.IDangerListService;
import org.dromara.system.service.ISysConfigService;
import org.dromara.system.service.ISysOssService;
import org.dromara.warm.flow.orm.entity.FlowTask;
import org.dromara.warm.flow.orm.mapper.FlowTaskMapper;
import org.dromara.workflow.domain.bo.CompleteTaskBo;
import org.dromara.workflow.domain.bo.StartProcessBo;
import org.dromara.workflow.service.IFlwTaskService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能隐患分析任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class AiHazAnalysisTasksServiceImpl implements IAiHazAnalysisTasksService {

    private final AiHazAnalysisTasksMapper baseMapper;
    private final ISysOssService sysOssService;
    private final IDangerListService dangerListService;
    private final PrjProjectsMapper prjProjectsMapper;
    private final PrjHazardousItemsMapper prjHazardousItemsMapper;
    private final AiHazAnalysisTasksResultMapper aiHazAnalysisTasksResultMapper;
    private final IFlwTaskService flwTaskService;
    private final FlowTaskMapper flowTaskMapper;
    private final SysUserMapper sysUserMapper;
    private final AiHazAnalysisTasksMapper aiHazAnalysisTasksMapper;
    private final PrjHazardousItemsSpecialistMapper prjHazardousItemsSpecialistMapper;
    private final ISysConfigService sysConfigService;

    @Value("${ai.analysis.url}")
    private String aiAnalysisUrl;

    /**
     * 查询智能隐患分析任务
     *
     * @param taskId 主键
     * @return 智能隐患分析任务
     */
    @Override
    public AiHazAnalysisTasksVo queryById(Long taskId) {
        return baseMapper.selectVoById(taskId);
    }

    /**
     * 查询智能隐患分析任务
     *
     * @param taskId 主键
     * @return 智能隐患分析任务
     */
    @Override
    public AiHazAnalysisTasksVo queryByIdNew(Long taskId) {
        // 查询基本任务信息
        AiHazAnalysisTasksVo aiHazAnalysisTasksVo = baseMapper.selectVoById(taskId);

        if (aiHazAnalysisTasksVo != null) {
            // 关联的危大项目信息
            if (aiHazAnalysisTasksVo.getProjectId() != null) {
                PrjProjectsVo prjProjectsVo = prjProjectsMapper.selectVoById(aiHazAnalysisTasksVo.getProjectId());
                aiHazAnalysisTasksVo.setPrjProjectsVo(prjProjectsVo);
            }

            // 关联的危大工程信息
            if (aiHazAnalysisTasksVo.getItemId() != null) {
                PrjHazardousItemsVo prjHazardousItemsVo = prjHazardousItemsMapper.selectVoById(aiHazAnalysisTasksVo.getItemId());
                if (prjHazardousItemsVo.getDangerId() != null) {
                    String parentName = getParentName(prjHazardousItemsVo.getDangerId());
                    prjHazardousItemsVo.setParentName(parentName);
                }
                aiHazAnalysisTasksVo.setPrjHazardousItemsVo(prjHazardousItemsVo);
            }
        }

        return aiHazAnalysisTasksVo;
    }

    /**
     * 分页查询智能隐患分析任务列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 智能隐患分析任务分页列表
     */
    @Override
    public TableDataInfo<AiHazAnalysisTasksVo> queryPageList(AiHazAnalysisTasksBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiHazAnalysisTasks> lqw = buildQueryWrapper(bo);
        Page<AiHazAnalysisTasksVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的智能隐患分析任务列表
     *
     * @param bo 查询条件
     * @return 智能隐患分析任务列表
     */
    @Override
    public List<AiHazAnalysisTasksVo> queryList(AiHazAnalysisTasksBo bo) {
        LambdaQueryWrapper<AiHazAnalysisTasks> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiHazAnalysisTasks> buildQueryWrapper(AiHazAnalysisTasksBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiHazAnalysisTasks> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AiHazAnalysisTasks::getTaskId);
        lqw.eq(bo.getProjectId() != null, AiHazAnalysisTasks::getProjectId, bo.getProjectId());
        lqw.eq(bo.getExpertUserId() != null, AiHazAnalysisTasks::getExpertUserId, bo.getExpertUserId());
        lqw.eq(bo.getUploadTime() != null, AiHazAnalysisTasks::getUploadTime, bo.getUploadTime());
        lqw.eq(bo.getPhotoDocumentId() != null, AiHazAnalysisTasks::getPhotoDocumentId, bo.getPhotoDocumentId());
        lqw.eq(StringUtils.isNotBlank(bo.getGpsLocation()), AiHazAnalysisTasks::getGpsLocation, bo.getGpsLocation());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationDescription()), AiHazAnalysisTasks::getLocationDescription, bo.getLocationDescription());
        lqw.eq(StringUtils.isNotBlank(bo.getAiRecognitionRawResult()), AiHazAnalysisTasks::getAiRecognitionRawResult, bo.getAiRecognitionRawResult());
        lqw.eq(bo.getItemId() != null, AiHazAnalysisTasks::getItemId, bo.getItemId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AiHazAnalysisTasks::getStatus, bo.getStatus());
        lqw.eq(bo.getRelatedWorkOrderId() != null, AiHazAnalysisTasks::getRelatedWorkOrderId, bo.getRelatedWorkOrderId());
        return lqw;
    }

    /**
     * 新增智能隐患分析任务
     *
     * @param bo 智能隐患分析任务
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AiHazAnalysisTasksBo bo) {
        AiHazAnalysisTasks add = MapstructUtils.convert(bo, AiHazAnalysisTasks.class);
        // 设置上传时间为当前时间
        if (add.getUploadTime() == null) {
            add.setUploadTime(new Date());
        }
        validEntityBeforeSave(add);

        String enable = sysConfigService.selectConfigByKey("sys.ai.audit.enable");

        //状态开关
        if (Convert.toBool(enable, false)) {
            add.setRecheckStatus(AiHazAnalysisTasksReCheckStatus.AUDIT_RECHECK.getType());
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setTaskId(add.getTaskId());
        }
        return flag;
    }

    /**
     * 修改智能隐患分析任务
     *
     * @param bo 智能隐患分析任务
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AiHazAnalysisTasksBo bo) {
        AiHazAnalysisTasks update = MapstructUtils.convert(bo, AiHazAnalysisTasks.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiHazAnalysisTasks entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除智能隐患分析任务信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 处理AI分析回调结果
     *
     * @param aiResult AI分析结果
     * @return 是否处理成功
     */
    @Override
    public Boolean processAiAnalysisResult(AiAnalysisResultDto aiResult) {
        Long taskId = aiResult.getTaskId();
        String rawResult = aiResult.getRawResult();
        log.info("收到AI分析回调结果,任务ID: {}, 原始结果: {}", taskId, rawResult);

        // 参数校验
        if (taskId == null) {
            log.error("AI分析回调结果缺少必要参数: taskId");
            return false;
        }

        // 先查询任务是否存在
        AiHazAnalysisTasksVo task = this.queryById(taskId);
        if (task == null) {
            log.error("找不到对应的智能隐患分析任务，任务ID: {}", taskId);
            return false;
        }

        try {
            AiHazAnalysisTasksBo bo = new AiHazAnalysisTasksBo();
            bo.setTaskId(taskId);
            bo.setRemark(rawResult);
            // 解析JSON
            JSONObject jsonObj = JSONUtil.parseObj(rawResult);
            Integer code = jsonObj.getInt("code");
            String data = jsonObj.getStr("data");
            String resultImage = JSONUtil.getByPath(jsonObj, "data.resultImage", "");

            if (code == null) {
                log.error("AI分析结果缺少响应码，任务ID: {}", taskId);
                this.updateByBo(bo);
                return false;
            }

            // 根据响应码设置不同的任务状态
            String taskStatus = switch (code) {
                case 200 ->
                    // 有隐患
                    AiHazAnalysisTasksStatus.AI_ANALYSIS_COMPLETED.getType();
                case 201 ->
                    // 当前不是工地现场请重新上传照片
                    AiHazAnalysisTasksStatus.AI_ANALYSIS_NOT_CONSTRUCTION_SITE.getType();
                case 202 ->
                    // 当前没有安全隐患
                    AiHazAnalysisTasksStatus.AI_ANALYSIS_NO_SAFETY_HAZARDS.getType();
                default -> {
                    log.error("未知的AI分析响应码: {}, 任务ID: {}", code, taskId);
                    yield AiHazAnalysisTasksStatus.AI_ANALYSIS_COMPLETED.getType();
                }
            };

            // 更新任务状态和AI分析结果
            bo.setAiRecognitionRawResult(data);
            bo.setStatus(taskStatus);

            // 只有当响应码为200（有隐患）时才处理违规列表
            if (code == 200) {
                Object violationList = jsonObj.getByPath("data.violationList");
                if (violationList != null) {
                    List<AiHazAnalysisTasksResult> aiHazAnalysisTasksResults = Convert.toList(AiHazAnalysisTasksResult.class, violationList);
                    // 添加任务ID
                    aiHazAnalysisTasksResults.forEach(result -> result.setTaskId(taskId));
                    aiHazAnalysisTasksResultMapper.insertBatch(aiHazAnalysisTasksResults);
                }
            }

            // 处理结果图片 上传到OSS（如果有结果图片的话）
            if (StrUtil.isNotBlank(resultImage)) {
                try {
                    URL url = URLUtil.url(resultImage);
                    String extension = FileNameUtil.extName(resultImage);

                    // 创建临时文件
                    Path tempFile = Files.createTempFile("download-" + IdUtil.fastSimpleUUID(), "." + extension);
                    Path tempDir = tempFile.getParent();
                    log.info("临时文件目录: {}", tempDir);

                    // 下载URL内容到临时文件
                    try (InputStream in = url.openStream()) {
                        Files.copy(in, tempFile, StandardCopyOption.REPLACE_EXISTING);
                    }

                    // 转换为File对象
                    File file = tempFile.toFile();

                    log.info("下载的文件路径: {}", file.getAbsolutePath());

                    var ossResult = sysOssService.upload(file);
                    bo.setAiPhotoDocumentId(ossResult.getOssId());
                    log.info("AI分析结果图片已上传到OSS，文档ID: {}", ossResult.getOssId());

                    // 使用完后可以删除临时文件
                    boolean delete = file.delete();
                } catch (Exception e) {
                    log.error("处理结果图片失败，任务ID: {}", taskId, e);
                    // 图片处理失败不影响整体流程，继续执行
                }
            }

            // 调用更新方法
            boolean result = this.updateByBo(bo);

            if (result) {
                log.info("成功更新AI分析结果，任务ID: {}", taskId);
            } else {
                log.error("更新AI分析结果失败，任务ID: {}", taskId);
            }

            return result;
        } catch (Exception e) {
            log.error("处理AI分析回调结果时发生异常，任务ID: {}", taskId, e);
            return false;
        }
    }

    public static void main(String[] args) {
        URL url = URLUtil.url("https://dev.liamm.cn/resources/test/1.jpeg");
//            URL url = new URL("http://124.152.7.130:39528/result-image/1746846143147_result.jpg");
        File file = FileUtil.file(url);
    }

    /**
     * 提交到外部AI分析服务
     *
     * @param taskId   任务ID
     * @param imageUrl 图片URL
     * @return 分析任务详情
     */
    @Override
    public AiHazAnalysisTasksVo submitToAiAnalysis(Long taskId, String imageUrl, Long itemId) {
        log.info("提交隐患图片到外部AI分析服务，任务ID: {}, 图片URL: {}", taskId, imageUrl);

        PrjHazardousItemsVo prjHazardousItemsVo = prjHazardousItemsMapper.selectVoById(itemId);

        try {
            // 更新任务状态为"分析中"
            AiHazAnalysisTasksBo bo = new AiHazAnalysisTasksBo();
            bo.setTaskId(taskId);
            bo.setStatus(AiHazAnalysisTasksStatus.AI_ANALYSING.getType());
            this.updateByBo(bo);

            // 发送表单请求到外部AI分析服务
            log.info("发送请求到外部AI服务: {}", aiAnalysisUrl);
            String responseBody = HttpRequest.post(aiAnalysisUrl)
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .form("taskId", taskId)
                .form("dangerId", prjHazardousItemsVo.getDangerId())
                .form("checkImageUrl", imageUrl)
                .timeout(20000) // 设置超时时间为20秒
                .execute()
                .body();

            log.info("外部AI服务响应: {}", responseBody);

            // 返回更新后的任务信息
            return this.queryById(taskId);
        } catch (Exception e) {
            log.error("提交到外部AI分析服务失败，任务ID: {}", taskId, e);
            return this.queryById(taskId);
        }
    }

    /**
     * 根据dangerId获取父级名称
     *
     * @param dangerId 涉危工程清单ID
     * @return 父级名称
     */
    @Override
    public String getParentName(String dangerId) {
        if (dangerId == null) {
            return null;
        }

        // 将参数统一转换为字符串处理
        if (StringUtils.isEmpty(dangerId)) {
            return null;
        }

        // 检查是否包含逗号（多个ID的情况）
        if (dangerId.contains(",")) {
            // 取第一个ID（因为所有ID都属于同一个父级）
            String firstId = dangerId.split(",")[0].trim();
            try {
                return queryParentNameById(Convert.toLong(firstId));
            } catch (NumberFormatException e) {
                log.error("解析dangerId失败: {}", firstId, e);
                return null;
            }
        } else {
            // 单个ID
            try {
                return queryParentNameById(Convert.toLong(dangerId));
            } catch (NumberFormatException e) {
                log.error("解析dangerId失败: {}", dangerId, e);
                return null;
            }
        }
    }

    @Override
    public StartProcessReturnDTO pushWorkOrder(Long taskId, String busId) {

        //启动任务
        StartProcessBo startProcess = new StartProcessBo();
        startProcess.setFlowCode("GD-zjjyhzg-001");
        startProcess.setBusinessId(busId);
        Map<String, Object> variables = startProcess.getVariables();
        //预警变量
        variables.put("real", 1);
        variables.put("ai_task_id", String.valueOf(taskId));

        //ai业务的id
        AiHazAnalysisTasksVo tasksVo = aiHazAnalysisTasksMapper.selectVoById(taskId);

        //项目id
        Long projectId = tasksVo.getProjectId();

        PrjProjectsVo prjProjectsVo = prjProjectsMapper.selectVoById(projectId);

        List<Long> deptIds = Arrays.asList(prjProjectsVo.getSupervisingQsOrgId(), prjProjectsVo.getConstructionOrgId(), prjProjectsVo.getSupervisionOrgId());

        LambdaQueryWrapper<SysUser> wrapper = Wrappers.lambdaQuery();
        wrapper.in(SysUser::getDeptId, deptIds);

        List<SysUser> sysUsers = sysUserMapper.selectList(wrapper);

        Map<Long, List<SysUser>> deptMap = sysUsers.stream().collect(Collectors.groupingBy(SysUser::getDeptId));

        variables.put("initUser", LoginHelper.getUserId());
        //质监站接收
        String qualityId = Optional.ofNullable(deptMap.get(prjProjectsVo.getSupervisingQsOrgId()))
            .map(t -> t.get(0)).map(SysUser::getUserId).map(String::valueOf).orElse(null);
        variables.put("zhijianzhan", qualityId);
        //施工方
        String constuction = Optional.ofNullable(deptMap.get(prjProjectsVo.getConstructionOrgId()))
            .map(t -> t.get(0)).map(SysUser::getUserId).map(String::valueOf).orElse(null);
        variables.put("construction", constuction);
        //监理
        variables.put("supervisor", Optional.ofNullable(deptMap.get(prjProjectsVo.getSupervisionOrgId()))
            .map(t -> t.get(0)).map(SysUser::getUserId).map(String::valueOf).orElse(null));

        startProcess.setVariables(variables);
        startProcess.setHandle(qualityId);
        startProcess.setExt(String.valueOf(taskId));

        //开启工单
        StartProcessReturnDTO returnDTO = flwTaskService.startWorkFlow(startProcess);

        //组装工单
        CompleteTaskBo completeTaskBo = new CompleteTaskBo();
        //工单id
        completeTaskBo.setTaskId(returnDTO.getTaskId());
        //通知消息·
        completeTaskBo.setMessage("请审核隐患信息");
        //站内信通知
        completeTaskBo.setMessageType(List.of(qualityId));
        completeTaskBo.setVariables(variables);

        flwTaskService.completeTask(completeTaskBo);

        completeTaskBo.setMessage("");

        LambdaQueryWrapper<FlowTask> flowTaskWrapper = new LambdaQueryWrapper<>();
        flowTaskWrapper.eq(FlowTask::getInstanceId, returnDTO.getProcessInstanceId());
        FlowTask flowTask = flowTaskMapper.selectOne(flowTaskWrapper);

        completeTaskBo.setTaskId(flowTask.getId());

        //梅开二度
        flwTaskService.completeTask(completeTaskBo);

        return returnDTO;
    }

    @Override
    public StartProcessReturnDTO pushWorkOrder2(PrjHazardousItemsSpecialistBo bo) {

        //查询用户
        List<String> idCards = Arrays.stream(bo.getSpecialist().split(",")).toList();

        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SysUser::getUserName, idCards)
            .select(SysUser::getUserId, SysUser::getUserName);

        List<SysUser> sysUsers = sysUserMapper.selectList(wrapper);

        List<String> userIds = sysUsers.stream().map(SysUser::getUserId).map(String::valueOf).toList();

        //流程数据
        PrjHazardousItemsSpecialist add = MapstructUtils.convert(bo, PrjHazardousItemsSpecialist.class);
        add.setTaskId(IdUtil.getSnowflakeNextIdStr());
        add.setSpecialist(CollectionUtil.join(userIds, ","));

        prjHazardousItemsSpecialistMapper.insert(add);

        //启动任务
        StartProcessBo startProcess = new StartProcessBo();
        startProcess.setFlowCode("ZJT-ZJJ-YHZG");
        startProcess.setBusinessId(add.getTaskId());

        Map<String, Object> variables = startProcess.getVariables();

        //ai业务的id
        AiHazAnalysisTasksVo tasksVo = aiHazAnalysisTasksMapper.selectVoById(bo.getAiHazAnalysisId());

        PrjProjectsVo prjProjectsVo = prjProjectsMapper.selectVoById(tasksVo.getProjectId());

        //质监站部门id
        variables.put("quality_dept", prjProjectsVo.getSupervisingQsOrgId());

        variables.put("itemId", tasksVo.getItemId());
        variables.put("projectId", tasksVo.getProjectId());

        //预警变量
        variables.put("ai_task_id", String.valueOf(bo.getAiHazAnalysisId()));

        //专家组
        variables.put("specialist", CollectionUtil.join(userIds, ","));

        //发起人
        variables.put("apply_person", LoginHelper.getUserId().toString());

        //默认需要整改
        variables.put("needModified", 1);

        startProcess.setVariables(variables);
        //发起人
        startProcess.setHandle(LoginHelper.getUserId().toString());

        startProcess.setExt(String.valueOf(bo.getAiHazAnalysisId()));

        //开启工单
        StartProcessReturnDTO returnDTO = flwTaskService.startWorkFlow(startProcess);

        //组装工单
        CompleteTaskBo completeTaskBo = new CompleteTaskBo();
        //工单id
        completeTaskBo.setTaskId(returnDTO.getTaskId());
        //通知消息
        completeTaskBo.setMessage("请审核隐患信息");
        //站内信通知专家组
        completeTaskBo.setMessageType(userIds);
        completeTaskBo.setVariables(variables);

        flwTaskService.completeTask(completeTaskBo);
        return returnDTO;
    }

    /**
     * 根据单个dangerId查询父级名称
     *
     * @param dangerId 单个涉危工程清单ID
     * @return 父级名称
     */
    private String queryParentNameById(Long dangerId) {
        // 查询当前记录
        DangerListVo currentItem = dangerListService.queryById(dangerId);
        if (currentItem == null || currentItem.getPreId() == null) {
            return null;
        }
        // 查询父级记录
        DangerListVo parentItem = dangerListService.queryById(currentItem.getPreId());
        return parentItem != null ? parentItem.getName() : null;
    }

    /**
     * APP端分页查询隐患列表
     *
     * @param dto       查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @Override
    public TableDataInfo<AiHazAnalysisTasksVo> queryNewPageList(AiHazAnalysisTasksDto dto, PageQuery pageQuery) {
        IPage<AiHazAnalysisTasksVo> page = baseMapper.selectAppPageList(pageQuery.build(), dto);
        // 遍历结果集，获取每个记录的父级名称
        page.getRecords().forEach(record -> {
            if (record.getDangerId() != null) {
                String parentName = getParentName(record.getDangerId());
                record.setParentName(parentName);
            }
        });
        return TableDataInfo.build(page);
    }

    @Override
    public TableDataInfo<AiHazAnalysisTasksVo> queryAIPageList(AiHazAnalysisTasksDto dto, PageQuery pageQuery) {
        IPage<AiHazAnalysisTasksVo> page = baseMapper.selectAIPageList(pageQuery.build(), dto);
        // 遍历结果集，获取每个记录的父级名称
        page.getRecords().forEach(record -> {
            if (record.getDangerId() != null) {
                String parentName = getParentName(record.getDangerId());
                record.setParentName(parentName);
            }
        });
        return TableDataInfo.build(page);
    }
}

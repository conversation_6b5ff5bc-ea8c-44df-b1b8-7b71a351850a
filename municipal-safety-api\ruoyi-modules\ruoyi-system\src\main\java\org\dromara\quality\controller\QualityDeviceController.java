package org.dromara.quality.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.quality.domain.bo.QualityDeviceBo;
import org.dromara.quality.domain.vo.QualityDeviceVo;
import org.dromara.quality.service.IQualityDeviceService;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

/**
 * 设备管理Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/quality/device")
public class QualityDeviceController extends BaseController {

    private final IQualityDeviceService qualityDeviceService;

    /**
     * 查询设备管理列表
     */
    @SaCheckPermission("quality:device:list")
    @GetMapping("/list")
    public TableDataInfo<QualityDeviceVo> list(@Validated QualityDeviceBo bo, PageQuery pageQuery) {
        return qualityDeviceService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备管理列表
     */
    @SaCheckPermission("quality:device:export")
    @Log(title = "设备管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated QualityDeviceBo bo, HttpServletResponse response) {
        List<QualityDeviceVo> list = qualityDeviceService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备管理", QualityDeviceVo.class, response);
    }

    /**
     * 获取设备管理详细信息
     *
     * @param deviceId 设备ID
     */
    @SaCheckPermission("quality:device:query")
    @GetMapping("/{deviceId}")
    public R<QualityDeviceVo> getInfo(@NotNull(message = "设备ID不能为空")
                                      @PathVariable("deviceId") Long deviceId) {
        return R.ok(qualityDeviceService.queryById(deviceId));
    }

    /**
     * 新增设备管理
     */
    @SaCheckPermission("quality:device:add")
    @Log(title = "设备管理", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QualityDeviceBo bo) {
        return toAjax(qualityDeviceService.insertByBo(bo));
    }

    /**
     * 修改设备管理
     */
    @SaCheckPermission("quality:device:edit")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QualityDeviceBo bo) {
        return toAjax(qualityDeviceService.updateByBo(bo));
    }

    /**
     * 删除设备管理
     *
     * @param deviceIds 设备ID串
     */
    @SaCheckPermission("quality:device:remove")
    @Log(title = "设备管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deviceIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] deviceIds) {
        return toAjax(qualityDeviceService.deleteWithValidByIds(Arrays.asList(deviceIds), true));
    }

    /**
     * 生成设备编号
     *
     * @param deviceType 设备类型
     */
    @SaCheckPermission("quality:device:add")
    @GetMapping("/generateCode/{deviceType}")
    public R<String> generateDeviceCode(@PathVariable String deviceType) {
        String deviceCode = qualityDeviceService.generateDeviceCode(deviceType);
        return R.ok("生成成功", deviceCode);
    }

    /**
     * 校验设备编号是否唯一
     *
     * @param deviceCode 设备编号
     * @param deviceId   设备ID
     */
    @GetMapping("/checkDeviceCodeUnique")
    public R<Boolean> checkDeviceCodeUnique(@RequestParam String deviceCode,
                                            @RequestParam(required = false) Long deviceId) {
        Boolean isUnique = qualityDeviceService.checkDeviceCodeUnique(deviceCode, deviceId);
        return R.ok(isUnique);
    }

    /**
     * 上传设备图片
     *
     * @param file     图片文件
     * @param deviceId 设备ID（可选，新增时为空）
     */
    @SaCheckPermission("quality:device:upload")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/uploadImage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Long> uploadImage(@RequestPart("file") MultipartFile file,
                               @RequestParam(required = false) Long deviceId) {
        Long ossId = qualityDeviceService.uploadDeviceImage(deviceId, file);
        return R.ok("上传成功", ossId);
    }

    /**
     * 上传使用说明书
     *
     * @param file     说明书文件
     * @param deviceId 设备ID（可选，新增时为空）
     */
    @SaCheckPermission("quality:device:upload")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/uploadManual", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Long> uploadManual(@RequestPart("file") MultipartFile file,
                                @RequestParam(required = false) Long deviceId) {
        Long ossId = qualityDeviceService.uploadManualFile(deviceId, file);
        return R.ok("上传成功", ossId);
    }

}

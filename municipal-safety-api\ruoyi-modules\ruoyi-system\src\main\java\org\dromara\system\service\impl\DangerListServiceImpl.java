package org.dromara.system.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.system.domain.bo.DangerListBo;
import org.dromara.system.domain.vo.DangerListVo;
import org.dromara.system.domain.DangerList;
import org.dromara.system.mapper.DangerListMapper;
import org.dromara.system.service.IDangerListService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * dangerListService业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RequiredArgsConstructor
@Service
public class DangerListServiceImpl implements IDangerListService {

    private final DangerListMapper baseMapper;

    /**
     * 查询dangerList
     *
     * @param dangerId 主键
     * @return dangerList
     */
    @Override
    public DangerListVo queryById(Long dangerId) {
        return baseMapper.selectVoById(dangerId);
    }


    /**
     * 查询符合条件的dangerList列表
     *
     * @param bo 查询条件
     * @return dangerList列表
     */
    @Override
    public List<DangerListVo> queryList(DangerListBo bo) {
        LambdaQueryWrapper<DangerList> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DangerList> buildQueryWrapper(DangerListBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DangerList> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(DangerList::getDangerId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), DangerList::getName, bo.getName());
        lqw.eq(bo.getPreId() != null, DangerList::getPreId, bo.getPreId());
        lqw.eq(bo.getType() != null, DangerList::getType, bo.getType());
        return lqw;
    }

    /**
     * 新增dangerList
     *
     * @param bo dangerList
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(DangerListBo bo) {
        DangerList add = MapstructUtils.convert(bo, DangerList.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        return flag;
    }

    /**
     * 修改dangerList
     *
     * @param bo dangerList
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(DangerListBo bo) {
        DangerList update = MapstructUtils.convert(bo, DangerList.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(DangerList entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除dangerList信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}

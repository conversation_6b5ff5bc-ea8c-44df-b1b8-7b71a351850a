<template>
  <div class="expertReview">
    <el-dialog :title="expertReviewDialog.title" v-model="expertReviewDialog.visible" append-to-body
      @close="handleClose" width="70%">
      <el-divider content-position="left">评审信息</el-divider>
      <el-form ref="ruleFormRef" :model="formData.prjExpertReviews" :rules="rules">
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="会议地点" prop="meetingLocation">
              <el-input v-model="formData.prjExpertReviews.meetingLocation" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="会议时间" prop="reviewDate" label-width="105px">
              <el-date-picker v-model="formData.prjExpertReviews.reviewDate" type="date" value-format="YYYY-MM-DD"
                placeholder="请选择会议时间" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="论证结论" prop="conclusion">
              <el-select v-model="formData.prjExpertReviews.conclusion" placeholder="请选择论证结果" clearable>
                <el-option v-for="dict in expert_conclusion" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="论证报告" prop="reportDocumentId">
              <FileUpload @update:modelValue="handleReportFile" :limit="1"
                :modelValue="formData.prjExpertReviews.reportDocumentId" :isShowTip="false" :fileSize="50">
              </FileUpload>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专家意见摘要" prop="expertOpinionSummary">
              <el-input v-model="formData.prjExpertReviews.expertOpinionSummary" :rows="3" type="textarea"
                placeholder="请输入专家意见摘要/修改要求" />
            </el-form-item>
          </el-col>
          <el-col :span="8"></el-col>
        </el-row>
      </el-form>
      <el-divider content-position="left">评审专家</el-divider>
      <el-card shadow="never">
        <template #header>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" plain icon="Select" @click="handleExpertSelect"
                v-hasPermi="['projects:expertReview:select']">选择专家</el-button>
            </el-col>
          </el-row>
        </template>
        <el-table v-loading="loading" :data="expertReviewList" show-overflow-tooltip>
          <el-table-column type="index" label="序号" width="50" />
          <el-table-column label="专家主键" align="center" prop="expertId" v-if="false" />
          <el-table-column label="名称" align="center" prop="name" />
          <el-table-column label="性别" align="center" prop="sex">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.sex" />
            </template>
          </el-table-column>
          <el-table-column label="电话" align="center" prop="phone" />
          <el-table-column label="专家类型" align="center" prop="type">
            <template #default="scope">
              <dict-tag :options="expert_type" :value="scope.row.type" />
            </template>
          </el-table-column>
          <el-table-column label="职称" align="center" prop="title" />
          <el-table-column label="专业" align="center" prop="major">
            <template #default="scope">
              <dict-tag :options="expert_major" :value="scope.row.major" />
            </template>
          </el-table-column>
          <el-table-column label="行业" align="center" prop="industry">
            <template #default="scope">
              <dict-tag :options="expert_industry" :value="scope.row.industry" />
            </template>
          </el-table-column>
          <el-table-column label="角色" align="center" prop="roleInMeeting">
            <template #default="scope">
              <el-select v-model="roleInMeeting[scope.$index]" placeholder="请选择"
                @change="selectRoleChange($event, scope.$index)">
                <el-option v-for="dict in expert_role" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column v-if="false" label="是否评审专家" align="center" prop="isAttendingExpert">
            <template #default="scope">
              <el-select v-model="isAttendingExpert[scope.$index]" placeholder="请选择"
                @change="selectAttendingExpertChange($event, scope.$index)">
                <el-option v-for="dict in expert_review" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-tooltip content="删除" placement="top">
                <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row, scope.$index)"
                  v-hasPermi="['projects:expertReview:remove']"></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmChange">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 选择专家评审的列表弹框-->
    <SelectExpert :isShowModel="isShowExpertModel" @update:isShowModel="handleModelExpert"
      @selectEmit="selectEmitChange" />
  </div>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import FileUpload from '@/components/FileUpload/index.vue'
import { expertReviewSave, getExpertReviewDetail } from '@/api/expertReview/index';
import SelectExpert from '../SelectExpert/index.vue'
import { ExpertVO } from '@/api/expert/expert/types';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { expert_industry, sys_user_sex, expert_major, expert_type, expert_role, expert_review, expert_conclusion } = toRefs<any>(
  proxy?.useDict('expert_industry', 'sys_user_sex', 'expert_major', 'expert_type', 'expert_role', 'expert_review', 'expert_conclusion')
);

const props = defineProps({
  isExpertShowModel: {
    type: Boolean,
    default: false
  },
  row: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits(['update:isExpertShowModel', 'selectExpertEmit'])
const loading = ref(false);
const expertReviewDialog = reactive({
  visible: false,
  title: '专家评审'
})
const formData = ref({
  expertUser: [],
  prjExpertReviews: {
    planId: '',
    meetingLocation: '',
    reviewDate: '',
    conclusion: '',
    reportDocumentId: '',
    expertOpinionSummary: '',
  }
})
const rules = reactive<FormRules>({
  meetingLocation: [
    { required: true, message: '请输入会议地点', trigger: 'blur' }
  ],
  reviewDate: [
    { required: true, message: '请选择会议时间', trigger: 'change' }
  ],
  conclusion: [
    { required: true, message: '请选择论证结果', trigger: 'change' }
  ],
  reportDocumentId: [
    { required: true, message: '请上传论证报告', trigger: 'change' }
  ],
  expertOpinionSummary: [
    { required: true, message: '请输入专家意见摘要/修改要求', trigger: 'blur' }
  ]
})
const roleInMeeting = ref<string[]>([]);
const isAttendingExpert = ref<string[]>([]);
const expertReviewList = ref<ExpertVO[]>();
const isShowExpertModel = ref<boolean>(false);
const ruleFormRef = ref<FormInstance>()
watch(() => props.isExpertShowModel, (val) => {
  expertReviewDialog.visible = val
  getExpertDetail();
})
/** 查询专家评审详情 */
const getExpertDetail = async () => {
  loading.value = true;
  const res = await getExpertReviewDetail(props.row.planId);
  if (res.code == 200 && res.data) {
    formData.value = { ...res.data };
    expertReviewList.value = formData.value.expertUser;
    formData.value.expertUser.forEach((item, index) => {
      roleInMeeting.value[index] = item.roleInMeeting;
      isAttendingExpert.value[index] = item.isAttendingExpert.toString();
    })
  }
  loading.value = false;
};
/** 确定按钮操作 */
const confirmChange = () => {
  formData.value.prjExpertReviews.planId = props.row.planId;
  ruleFormRef.value?.validate(async (valid: boolean) => {
    if (formData.value.expertUser.length !== 5) {
      proxy?.$modal.msgError("需保留5位评审专家");
      return;
    }
    const isSelectRoleUser = formData.value.expertUser.every((item) => item.roleInMeeting !== '' && item.roleInMeeting !== undefined);
    const isSelectAttendingExpert = formData.value.expertUser.every((item) => item.isAttendingExpert !== '' && item.isAttendingExpert !== undefined);

    if (!isSelectRoleUser) {
      proxy?.$modal.msgError("请分别选择专家角色");
      return;
    }
    if (!isSelectAttendingExpert) {
      proxy?.$modal.msgError("请分别选择是否评审专家");
      return;
    }
    if (valid) {
      const res = await expertReviewSave(formData.value);
      if (res.code == 200) {
        proxy?.$modal.msgSuccess("操作成功");
      }
      expertReviewDialog.visible = false;
      emit('update:isExpertShowModel', false)
    }
  });
};
/** 取消按钮操作 */
const cancel = () => {
  emit('update:isExpertShowModel', false)
};
// 重置表单
const resetForm = () => {
  ruleFormRef.value?.resetFields();
  expertReviewDialog.visible = false;
  emit('update:isExpertShowModel', false)
}
const handleClose = () => {
  emit('update:isExpertShowModel', false)
}
/** 选择专家按钮操作 */
const handleExpertSelect = () => {
  isShowExpertModel.value = true;
}
// 选择专家组件的自定义事件方法
const handleModelExpert = (val: boolean) => {
  isShowExpertModel.value = val;
}
// 接受子组件通过emit传递过来的数据
const selectEmitChange = (val: ExpertVO[]) => {
  val.forEach((item) => {
    const isExist = expertReviewList.value.some((expert) => expert.idCard === item.idCard);
    if (!isExist) {
      expertReviewList.value.push(item);
    }
  });
  formData.value.expertUser = expertReviewList.value.map((item, index) => {
    isAttendingExpert.value.push('0');
    return {
      userId: item.idCard,
      roleInMeeting: item.roleInMeeting,
      isAttendingExpert: 0
    }
  });
}
/** 选择角色 */
const selectRoleChange = (val: string, index: number) => {
  formData.value.expertUser[index].roleInMeeting = val;
}
/** 选择是否评审专家 */
const selectAttendingExpertChange = (val: string, index: number) => {
  formData.value.expertUser[index].isAttendingExpert = parseInt(val);
}
/** 删除按钮操作 */
const handleDelete = (row: ExpertVO, index: number) => {
  proxy?.$modal.confirm('是否确认删除当前项？').then(async () => {
    expertReviewList.value = expertReviewList.value.filter((item) => item.idCard !== row.idCard);
    formData.value.expertUser = formData.value.expertUser.filter((item) => item.userId !== row.idCard);
    roleInMeeting.value.splice(index, 1);
    isAttendingExpert.value.splice(index, 1);
  }).catch(() => { });
}
// 上传论证报告回调函数
const handleReportFile = (fileOssId: string) => {
  formData.value.prjExpertReviews.reportDocumentId = fileOssId;
}
onMounted(() => {
})
</script>

<style scoped></style>
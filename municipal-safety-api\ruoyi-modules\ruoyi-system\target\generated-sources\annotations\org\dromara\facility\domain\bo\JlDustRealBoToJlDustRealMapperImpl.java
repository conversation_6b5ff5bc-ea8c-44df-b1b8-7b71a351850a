package org.dromara.facility.domain.bo;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.JlDustReal;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class JlDustRealBoToJlDustRealMapperImpl implements JlDustRealBoToJlDustRealMapper {

    @Override
    public JlDustReal convert(JlDustRealBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        JlDustReal jlDustReal = new JlDustReal();

        jlDustReal.setId( arg0.getId() );
        jlDustReal.setMn( arg0.getMn() );
        jlDustReal.setDatatime( arg0.getDatatime() );
        jlDustReal.setB03Avg( arg0.getB03Avg() );
        jlDustReal.setB03Flag( arg0.getB03Flag() );
        jlDustReal.setPm25Avg( arg0.getPm25Avg() );
        jlDustReal.setPm25Flag( arg0.getPm25Flag() );
        jlDustReal.setPm10Avg( arg0.getPm10Avg() );
        jlDustReal.setPm10Flag( arg0.getPm10Flag() );
        jlDustReal.setW02Avg( arg0.getW02Avg() );
        jlDustReal.setW02Flag( arg0.getW02Flag() );
        jlDustReal.setW01Avg( arg0.getW01Avg() );
        jlDustReal.setW01Flag( arg0.getW01Flag() );
        jlDustReal.setT01Avg( arg0.getT01Avg() );
        jlDustReal.setT01Flag( arg0.getT01Flag() );
        jlDustReal.setH01Avg( arg0.getH01Avg() );
        jlDustReal.setH01Flag( arg0.getH01Flag() );
        jlDustReal.setTspAvg( arg0.getTspAvg() );
        jlDustReal.setTspFlag( arg0.getTspFlag() );
        jlDustReal.setO3Avg( arg0.getO3Avg() );
        jlDustReal.setO3Flag( arg0.getO3Flag() );
        jlDustReal.setCoAvg( arg0.getCoAvg() );
        jlDustReal.setCoFlag( arg0.getCoFlag() );
        jlDustReal.setSo2Avg( arg0.getSo2Avg() );
        jlDustReal.setSo2Flag( arg0.getSo2Flag() );
        jlDustReal.setNo2Avg( arg0.getNo2Avg() );
        jlDustReal.setNo2Flag( arg0.getNo2Flag() );
        jlDustReal.setA01006Rtd( arg0.getA01006Rtd() );
        jlDustReal.setA01006Flag( arg0.getA01006Flag() );
        jlDustReal.setCreateTime( arg0.getCreateTime() );

        return jlDustReal;
    }

    @Override
    public JlDustReal convert(JlDustRealBo arg0, JlDustReal arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setMn( arg0.getMn() );
        arg1.setDatatime( arg0.getDatatime() );
        arg1.setB03Avg( arg0.getB03Avg() );
        arg1.setB03Flag( arg0.getB03Flag() );
        arg1.setPm25Avg( arg0.getPm25Avg() );
        arg1.setPm25Flag( arg0.getPm25Flag() );
        arg1.setPm10Avg( arg0.getPm10Avg() );
        arg1.setPm10Flag( arg0.getPm10Flag() );
        arg1.setW02Avg( arg0.getW02Avg() );
        arg1.setW02Flag( arg0.getW02Flag() );
        arg1.setW01Avg( arg0.getW01Avg() );
        arg1.setW01Flag( arg0.getW01Flag() );
        arg1.setT01Avg( arg0.getT01Avg() );
        arg1.setT01Flag( arg0.getT01Flag() );
        arg1.setH01Avg( arg0.getH01Avg() );
        arg1.setH01Flag( arg0.getH01Flag() );
        arg1.setTspAvg( arg0.getTspAvg() );
        arg1.setTspFlag( arg0.getTspFlag() );
        arg1.setO3Avg( arg0.getO3Avg() );
        arg1.setO3Flag( arg0.getO3Flag() );
        arg1.setCoAvg( arg0.getCoAvg() );
        arg1.setCoFlag( arg0.getCoFlag() );
        arg1.setSo2Avg( arg0.getSo2Avg() );
        arg1.setSo2Flag( arg0.getSo2Flag() );
        arg1.setNo2Avg( arg0.getNo2Avg() );
        arg1.setNo2Flag( arg0.getNo2Flag() );
        arg1.setA01006Rtd( arg0.getA01006Rtd() );
        arg1.setA01006Flag( arg0.getA01006Flag() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}

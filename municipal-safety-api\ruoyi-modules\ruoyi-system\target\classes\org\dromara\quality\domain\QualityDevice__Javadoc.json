{"doc": " 设备管理对象 quality_device\n\n <AUTHOR>\n @date 2024-01-01\n", "fields": [{"name": "deviceId", "doc": " 设备ID\n"}, {"name": "deviceName", "doc": " 设备名称\n"}, {"name": "specification", "doc": " 规格型号\n"}, {"name": "deviceCode", "doc": " 设备编号\n"}, {"name": "deviceType", "doc": " 设备类型\n"}, {"name": "quantity", "doc": " 数量\n"}, {"name": "deviceImageOssId", "doc": " 设备图片OSS ID\n"}, {"name": "deviceDescription", "doc": " 设备简介\n"}, {"name": "devicePurpose", "doc": " 设备用途\n"}, {"name": "usageInstructions", "doc": " 使用说明\n"}, {"name": "manualFileOssId", "doc": " 使用说明书OSS ID\n"}, {"name": "status", "doc": " 设备状态（0正常 1停用）\n"}, {"name": "version", "doc": " 版本\n"}, {"name": "delFlag", "doc": " 删除标志\n"}], "enumConstants": [], "methods": [], "constructors": []}
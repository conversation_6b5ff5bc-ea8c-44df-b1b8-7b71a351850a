<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="lnWaterList">
      <el-table-column label="累计用水量" align="center" prop="sdds">
        <template v-slot="scope">
          {{ scope.row.sdds / 1000 }}m³
        </template>
      </el-table-column>
      <el-table-column label="上报时间" align="center" prop="recordTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.recordTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上月累计用水量" align="center" prop="sylj">
        <template v-slot="scope">
          {{ scope.row.sylj / 1000 }}m³
        </template>
      </el-table-column>
      <el-table-column label="昨日累计用水量" align="center" prop="zrlj">
        <template v-slot="scope">
          {{ scope.row.zrlj / 1000 }}m³
        </template>
      </el-table-column>
      <el-table-column label="前一个小时累计用水量" align="center" prop="qyxslj">
        <template v-slot="scope">
          {{ scope.row.qyxslj / 1000 }}m³
        </template>
      </el-table-column>
      <el-table-column label="当前小时内累计用水量" align="center" prop="dqxsnlj">
        <template v-slot="scope">
          {{ scope.row.dqxsnlj / 1000 }}m³
        </template>
      </el-table-column>
      <el-table-column label="实时流速" align="center" prop="ssls">
        <template v-slot="scope">
          {{ scope.row.ssls / 1000 }}m³/s
        </template>
      </el-table-column>
      <el-table-column label="电池状态" align="center" prop="dczt">
        <template v-slot="scope">
          <span v-if="scope.row.dczt == '0'">未充电</span>
          <span v-if="scope.row.dczt == '1'">充电中</span>
        </template>
      </el-table-column>
      <el-table-column label="电池电压" align="center" prop="dcdy">
        <template v-slot="scope">
          {{ scope.row.dcdy }}V
        </template>
      </el-table-column>
      <el-table-column label="电池电量" align="center" prop="dcdl">
        <template v-slot="scope">
          {{ scope.row.dcdl }}%
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

  </div>
</template>

<script>
import { listLnWater } from "@/api/projects/facility/index";

export default {
  name: "LnWater",
  props: {
    devNo: {
      type: String,
      default: ""
    }
  },
  data () {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 绿能水表格数据
      lnWaterList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        devNo: null
      },
    };
  },
  created () {
    this.getList();
  },
  methods: {
    /** 查询绿能水列表 */
    async getList () {
      this.lnWaterList = []
      this.loading = true;
      this.queryParams.devNo = this.devNo
      const res = await listLnWater(this.queryParams)
      this.lnWaterList = res.rows;
      this.total = res.total;
      this.loading = false;
    },
  }
};
</script>

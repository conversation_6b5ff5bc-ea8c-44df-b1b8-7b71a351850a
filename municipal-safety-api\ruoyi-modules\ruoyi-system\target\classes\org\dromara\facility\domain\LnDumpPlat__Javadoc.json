{"doc": " 绿能卸料平台对象 ln_dump_plat\n\n <AUTHOR>\n @date 2025-07-24\n", "fields": [{"name": "id", "doc": " 主键\n"}, {"name": "dumpnumber", "doc": " 编号\n"}, {"name": "weightMax", "doc": " 最大载重,吨\n"}, {"name": "weight", "doc": " 重量,吨\n"}, {"name": "tilt", "doc": " 倾角,度\n"}, {"name": "batvolt", "doc": " 电池电压,v\n"}, {"name": "wightPercent", "doc": " 吊重比例 %\n"}, {"name": "tiltPercentX", "doc": " 倾斜比例X %\n"}, {"name": "tiltPercentY", "doc": " 倾斜比例Y %\n"}, {"name": "alarmInfo", "doc": " 报警信息\n"}, {"name": "status", "doc": " 设备状态\n"}, {"name": "idleWeightReal", "doc": " 重量空载实际值 kg\n"}, {"name": "loadWeightReal", "doc": " 重量负载实际值 kg\n"}, {"name": "weightWarning", "doc": " 载重预警百分比 %\n"}, {"name": "weightAlarm", "doc": " 载重报警百分比 %\n"}, {"name": "tiltWarning", "doc": " 倾斜预警值\n"}, {"name": "tiltAlarm", "doc": " 倾斜报警值\n"}, {"name": "deviceIp", "doc": " 设备ip\n"}, {"name": "realTiltX", "doc": " 实时倾斜度X\n"}, {"name": "realTiltY", "doc": " 实时倾斜度Y\n"}, {"name": "devNo", "doc": " 设备编号\n"}, {"name": "delFlag", "doc": " 删除标志 (0代表存在 1代表删除)\n"}, {"name": "createTime", "doc": " 创建时间\n"}], "enumConstants": [], "methods": [], "constructors": []}
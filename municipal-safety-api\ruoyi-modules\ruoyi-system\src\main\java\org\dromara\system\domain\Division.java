package org.dromara.system.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 行政区划对象 z_division
 * @date 2025-04-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("z_division")
public class Division extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一编号
     */
    @TableId(value = "division_id")
    private Long divisionId;

    /**
     * 区划代码
     */
    private String divisionCode;

    /**
     * 区划名称
     */
    private String divisionName;

    /**
     * 区划级别
     */
    private String level;

    /**
     * 父级区划代码
     */
    private String parentCode;

    /**
     * 0 正常 1 删除
     */
    @TableLogic
    private String delFlag;


}

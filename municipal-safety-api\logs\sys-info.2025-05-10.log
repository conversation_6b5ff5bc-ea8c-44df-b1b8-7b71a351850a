2025-05-10 08:45:56 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /],无参数
2025-05-10 08:45:56 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /],耗时:[4]毫秒
2025-05-10 08:47:11 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-10 08:47:11 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-10 08:47:11 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-10 08:47:12 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-10 08:47:12 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-10 08:47:12 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-10 08:47:12 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-10 08:47:12 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-10 08:47:16 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-10 08:47:16 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 24252 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-10 08:47:16 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-10 08:47:21 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-10 08:47:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-10 08:47:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-10 08:47:22 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@3d96b8fb
2025-05-10 08:47:22 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-10 08:47:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-10 08:47:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-10 08:47:24 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-10 08:47:24 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-10 08:47:25 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-10 08:47:25 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-10 08:47:26 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-10 08:47:33 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-10 08:47:34 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-10 08:47:34 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-10 08:47:34 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-10 08:47:34 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-10 08:47:34 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 18.743 seconds (process running for 19.857)
2025-05-10 08:47:34 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-10 08:47:34 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-10 08:47:35 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-10 08:48:15 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{
    "taskId": 1920795482277126100,
    "rawResult": "111",
    "identifiedHazards": ""
}]
2025-05-10 08:48:15 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1920795482277126100
2025-05-10 08:48:16 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[287]毫秒
2025-05-10 08:48:28 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{
    "taskId": 1920795482277126100,
    "rawResult": "111",
    "identifiedHazards": ""
}]
2025-05-10 08:48:28 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1920795482277126100
2025-05-10 08:48:28 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[44]毫秒
2025-05-10 08:49:06 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{
    "taskId": 1920795482277126146,
    "rawResult": "111",
    "identifiedHazards": ""
}]
2025-05-10 08:49:06 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1920795482277126146
2025-05-10 08:49:06 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 成功更新AI分析结果，任务ID: 1920795482277126146
2025-05-10 08:49:06 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[107]毫秒
2025-05-10 08:49:12 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{
    "taskId": 1920795482277126146,
    "rawResult": "111",
    "identifiedHazards": ""
}]
2025-05-10 08:49:12 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1920795482277126146
2025-05-10 08:49:12 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 成功更新AI分析结果，任务ID: 1920795482277126146
2025-05-10 08:49:12 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[88]毫秒
2025-05-10 08:49:55 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-10 08:49:55 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-10 08:49:55 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-10 08:49:55 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-10 08:49:55 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-10 08:49:55 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-10 08:49:55 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-10 08:49:55 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-10 08:50:00 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-10 08:50:00 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 24180 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-10 08:50:00 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-10 08:50:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-10 08:50:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-10 08:50:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-10 08:50:05 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1d208795
2025-05-10 08:50:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-10 08:50:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-10 08:50:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-10 08:50:07 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-10 08:50:07 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-10 08:50:08 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-10 08:50:08 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-10 08:50:09 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-10 08:50:16 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-10 08:50:17 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-10 08:50:17 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-10 08:50:17 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-10 08:50:17 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-10 08:50:17 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 18.59 seconds (process running for 19.693)
2025-05-10 08:50:17 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-10 08:50:18 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-10 08:50:18 [RMI TCP Connection(5)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-10 09:41:09 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /],无参数
2025-05-10 09:41:09 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /],耗时:[43]毫秒
2025-05-10 10:08:20 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-05-10 10:08:20 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-05-10 10:08:20 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[438]毫秒
2025-05-10 10:08:20 [XNIO-1 task-3] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIyWDM0Y1NBSklON3JtV3FRS252SGl6bWpPZEl3MW5vZSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiIsImVudGVycHJpc2VJbmZvIjoxOTIwNzMyMTE3NjA1NTE1MjY2fQ.d7FJFxKLrBxLiWtsH80Fl5DVXqubiAqPoxDbXnxo7d8
2025-05-10 10:08:21 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[480]毫秒
2025-05-10 10:08:21 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 10:08:21 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-10 10:08:21 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[2]毫秒
2025-05-10 10:08:21 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 10:08:21 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[203]毫秒
2025-05-10 10:08:24 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"f286db7973b14e7f85b7f27fc38b9283","code":"ik2a","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-05-10 10:08:25 [schedule-pool-1] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-05-10 10:08:25 [XNIO-1 task-3] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI1OTlUMUJnZHljdUdkZ1FZOU10ZFNjYURiTHlOOFJCMSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiIsImVudGVycHJpc2VJbmZvIjoxOTIwNzMyMTE3NjA1NTE1MjY2fQ.iC59ugfp8p8B22XK7WOTd09Dnc6_OcXpmIbqT3d2Pnc
2025-05-10 10:08:25 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[364]毫秒
2025-05-10 10:08:25 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-10 10:08:26 [XNIO-1 task-3] INFO  o.d.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-05-10 10:08:26 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[632]毫秒
2025-05-10 10:08:26 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-10 10:08:26 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[78]毫秒
2025-05-10 10:08:26 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-10 10:08:26 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-05-10 10:08:26 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI1OTlUMUJnZHljdUdkZ1FZOU10ZFNjYURiTHlOOFJCMSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiIsImVudGVycHJpc2VJbmZvIjoxOTIwNzMyMTE3NjA1NTE1MjY2fQ.iC59ugfp8p8B22XK7WOTd09Dnc6_OcXpmIbqT3d2Pnc"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-10 10:08:30 [schedule-pool-2] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-10 10:08:30 [redisson-3-2] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-10 10:08:41 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-05-10 10:08:41 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[193]毫秒
2025-05-10 10:08:42 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["3"]}]
2025-05-10 10:08:42 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[76]毫秒
2025-05-10 10:12:45 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/1920774205353095170],无参数
2025-05-10 10:12:45 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/1920774205353095170],耗时:[48]毫秒
2025-05-10 10:12:45 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-05-10 10:12:45 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[25]毫秒
2025-05-10 10:12:45 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"],"dictType":["ai_haz_analysis_tasks_status"]}]
2025-05-10 10:12:45 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/list],耗时:[82]毫秒
2025-05-10 10:13:44 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-05-10 10:13:44 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[75]毫秒
2025-05-10 10:13:46 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["3"]}]
2025-05-10 10:13:46 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[75]毫秒
2025-05-10 10:26:50 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{"taskId":"1920795482277126146","rawResult":"{\"code\":201,\"msg\":\"ENOENT: no such file or directory, open 'https://dev.liamm.cn/resources/test/1.jpeg'\"}"}]
2025-05-10 10:26:50 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1920795482277126146
2025-05-10 10:26:50 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 成功更新AI分析结果，任务ID: 1920795482277126146
2025-05-10 10:26:50 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[115]毫秒
2025-05-10 10:33:41 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{"taskId":"1920795482277126146","rawResult":"{\"code\":201,\"msg\":\"Cannot read properties of undefined (reading 'path')\"}"}]
2025-05-10 10:33:41 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1920795482277126146
2025-05-10 10:33:41 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 成功更新AI分析结果，任务ID: 1920795482277126146
2025-05-10 10:33:41 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[97]毫秒
2025-05-10 10:37:13 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{"taskId":"1920795482277126146","rawResult":"{\"code\":200,\"msg\":\"识别成功\",\"data\":{\"violationList\":[{\"violation\":\"疑似高空作业人员未佩戴安全头盔，存在高空坠落风险。\",\"regulation\":\"《中华人民共和国安全生产法》第二十一条：生产经营单位必须建立健全安全生产责任制，明确各级人员的安全生产责任，并加强对从业人员的安全教育和培训。\",\"coordinate\":\"[220, 150, 280, 200]\",\"level\":\"2\",\"measure\":\"立即要求高空作业人员佩戴符合国家标准的合格安全头盔，并加强安全教育培训。\"},{\"violation\":\"疑似起重臂未进行充分支撑，且吊载物体下方无安全防护措施，存在物体坠落风险。\",\"regulation\":\"《建设工程安全生产管理条例》第二十四条：施工单位应当建立和完善安全技术措施，采取有效的防护措施，确保高空、深基坑、临边洞口、脚手架、起重机械等危险部位的安全。\",\"coordinate\":\"[500, 200, 700, 400]\",\"level\":\"2\",\"measure\":\"立即检查起重机械的支撑情况，确保其稳定可靠。在吊载物体下方设置安全防护网或安全区域，防止物体坠落。\"},{\"violation\":\"疑似脚手架搭设不规范，连接不牢固，存在坍塌风险。\",\"regulation\":\"《安全生产法》第四十四条：生产经营单位应当对重大危险源进行辨识、评估和管理，采取有效的控制措施，防止事故发生。\",\"coordinate\":\"[100, 500, 300, 700]\",\"level\":\"1\",\"measure\":\"立即对脚手架进行全面检查，确保其搭设符合国家标准和规范，连接牢固可靠。\"},{\"violation\":\"疑似现场管理混乱，存在物料堆放不规范的现象，可能造成人员绊倒或砸伤。\",\"regulation\":\"《建设工程安全生产管理条例》第四十条：施工单位应当加强对施工现场的日常安全管理，及时排除安全隐患。\",\"coordinate\":\"[600, 600, 800, 800]\",\"level\":\"1\",\"measure\":\"立即对施工现场进行清理，规范物料堆放，确保通道畅通，消除安全隐患。\"}],\"checkImage\":\"https://dev.liamm.cn/resources/test/1.jpeg\",\"resultImage\":\"result-image/1746844633847_result.jpg\",\"allLevel\":\"2\"}}"}]
2025-05-10 10:37:13 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1920795482277126146
2025-05-10 10:37:13 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 成功更新AI分析结果，任务ID: 1920795482277126146
2025-05-10 10:37:13 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[89]毫秒
2025-05-10 10:40:35 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{"taskId":"1920795482277126146","rawResult":"{\"code\":200,\"msg\":\"识别成功\",\"data\":{\"violationList\":[{\"violation\":\"疑似起重机械存在安全距离不足的风险，可能存在与其他起重机械或建筑物碰撞的风险。\",\"regulation\":\"《建设工程安全生产管理条例》第三十一条：施工单位应当根据施工方案和有关规定，采取安全技术措施，防止高空坠落事故。高空作业场所应当设置安全防护设施，作业人员应当按照规定佩戴安全防护用品。\",\"coordinate\":\"[100, 100, 300, 400]\",\"level\":2,\"measure\":\"检查起重机械的运行轨迹，确保与周围建筑物和其他起重机械保持足够的安全距离；加强对起重机械操作人员的培训和管理，确保其具备相应的资质和技能。\"},{\"violation\":\"疑似起重机械的吊装作业存在超载的风险，可能导致起重机械倾覆或吊装物体坠落。\",\"regulation\":\"《中华人民共和国安全生产法》第五十五条　生产经营单位必须保证安全生产所需的资金投入，保证安全生产设施、安全防护用品、应急救援设备、应急物资的配备。\",\"coordinate\":\"[400, 200, 600, 500]\",\"level\":2,\"measure\":\"对起重机械进行定期检查和维护，确保其处于良好的运行状态；加强对吊装物体的重量和体积的控制，确保其不超过起重机械的额定载荷；在吊装作业过程中，加强对起重机械的监控和管理，及时发现和处理安全隐患。\"},{\"violation\":\"疑似施工现场的安全防护措施不完善，可能存在高处坠落的风险。\",\"regulation\":\"《建设工程安全生产管理条例》第三十条 施工单位应当建立健全安全生产责任制，加强对施工现场的安全管理，采取有效的安全措施，预防和制止违法违规行为。\",\"coordinate\":\"[700, 0, 900, 300]\",\"level\":1,\"measure\":\"完善施工现场的安全防护设施，如设置安全网、安全栏杆等；加强对施工人员的安全教育和培训，提高其安全意识和自我保护能力；定期进行安全检查，及时发现和处理安全隐患。\"},{\"violation\":\"疑似施工现场存在物料堆放不规范的现象，可能影响施工人员的通行和安全。\",\"regulation\":\"《中华人民共和国安全生产法》第五十三条　生产经营单位必须建立健全安全生产教育和培训制度，对从业人员进行安全生产教育和培训，提高其安全素质和技能。\",\"coordinate\":\"[0, 500, 200, 700]\",\"level\":1,\"measure\":\"规范施工现场的物料堆放，确保物料堆放的位置和方式符合安全要求；加强对施工现场的清理和维护，保持施工现场的整洁和有序。\"}],\"checkImage\":\"https://dev.liamm.cn/resources/test/1.jpeg\",\"resultImage\":\"result-image/1746844835201_result.jpg\",\"allLevel\":2}}"}]
2025-05-10 10:40:35 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1920795482277126146
2025-05-10 10:40:35 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 成功更新AI分析结果，任务ID: 1920795482277126146
2025-05-10 10:40:35 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[88]毫秒
2025-05-10 10:42:41 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-10 10:42:41 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-10 10:42:41 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-10 10:42:41 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-10 10:42:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-10 10:42:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-10 10:42:41 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-10 10:42:41 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-10 10:42:47 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-10 10:42:47 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 30516 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-10 10:42:47 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-10 10:42:51 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-10 10:42:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-10 10:42:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-10 10:42:52 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5cb64b9c
2025-05-10 10:42:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-10 10:42:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-10 10:42:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-10 10:42:55 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-10 10:42:55 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-10 10:42:55 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-10 10:42:55 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-10 10:42:56 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-10 10:43:03 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-10 10:43:04 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-10 10:43:04 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-10 10:43:04 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-10 10:43:04 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-10 10:43:04 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 17.869 seconds (process running for 18.839)
2025-05-10 10:43:04 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-10 10:43:04 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-10 10:43:05 [RMI TCP Connection(2)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-10 10:43:08 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI1OTlUMUJnZHljdUdkZ1FZOU10ZFNjYURiTHlOOFJCMSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiIsImVudGVycHJpc2VJbmZvIjoxOTIwNzMyMTE3NjA1NTE1MjY2fQ.iC59ugfp8p8B22XK7WOTd09Dnc6_OcXpmIbqT3d2Pnc"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-10 10:48:06 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-05-10 10:48:06 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI1OTlUMUJnZHljdUdkZ1FZOU10ZFNjYURiTHlOOFJCMSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiIsImVudGVycHJpc2VJbmZvIjoxOTIwNzMyMTE3NjA1NTE1MjY2fQ.iC59ugfp8p8B22XK7WOTd09Dnc6_OcXpmIbqT3d2Pnc"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-10 10:48:06 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[4]毫秒
2025-05-10 10:48:06 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-05-10 10:48:06 [XNIO-1 task-3] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI1OTlUMUJnZHljdUdkZ1FZOU10ZFNjYURiTHlOOFJCMSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiIsImVudGVycHJpc2VJbmZvIjoxOTIwNzMyMTE3NjA1NTE1MjY2fQ.iC59ugfp8p8B22XK7WOTd09Dnc6_OcXpmIbqT3d2Pnc
2025-05-10 10:48:06 [schedule-pool-1] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-05-10 10:48:06 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[159]毫秒
2025-05-10 10:48:06 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[135]毫秒
2025-05-10 10:48:07 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 10:48:07 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-10 10:48:07 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[10]毫秒
2025-05-10 10:48:07 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 10:48:07 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[180]毫秒
2025-05-10 10:48:09 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 10:48:09 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 10:48:09 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[11]毫秒
2025-05-10 10:48:38 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 10:48:38 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:124.152.7.131:'
2025-05-10 10:48:38 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[13]毫秒
2025-05-10 10:49:51 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 10:49:51 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:124.152.7.131:'
2025-05-10 10:49:51 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[16]毫秒
2025-05-10 10:50:11 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 10:50:11 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:124.152.7.131:'
2025-05-10 10:50:11 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[17]毫秒
2025-05-10 10:51:01 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 10:51:01 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:124.152.7.131:'
2025-05-10 10:51:01 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[15]毫秒
2025-05-10 10:51:05 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 10:51:05 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/auth/code:124.152.7.131:'
2025-05-10 10:51:05 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[7]毫秒
2025-05-10 10:55:04 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-10 10:55:04 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-10 10:55:04 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-10 10:55:04 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-10 10:55:04 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-10 10:55:04 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-10 10:55:04 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-10 10:55:04 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-10 10:55:10 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-10 10:55:10 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 25528 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-10 10:55:10 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-10 10:55:14 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-10 10:55:14 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-10 10:55:14 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-10 10:55:15 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1d82d35c
2025-05-10 10:55:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-10 10:55:15 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-10 10:55:15 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-10 10:55:17 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-10 10:55:17 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-10 10:55:17 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-10 10:55:17 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-10 10:55:19 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-10 10:55:25 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-10 10:55:26 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-10 10:55:26 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-10 10:55:26 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-10 10:55:26 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-10 10:55:26 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 17.838 seconds (process running for 18.801)
2025-05-10 10:55:26 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-10 10:55:27 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-10 10:55:27 [RMI TCP Connection(5)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-10 10:55:40 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{
  "tenantId" : "000000",
  "username" : "admin",
  "password" : "admin123",
  "rememberMe" : false,
  "uuid" : "c2945b7781a94c3eb5ed2336aedb0b28",
  "code" : "ouka",
  "clientId" : "e5cd7e4891bf95d1d19206ce24a7b32e",
  "grantType" : "password"
}]
2025-05-10 10:55:41 [schedule-pool-1] INFO  o.d.s.s.i.SysLogininforServiceImpl - [124.152.7.131]中国|甘肃省|兰州市|联通[admin][Error][验证码已失效]
2025-05-10 10:55:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[297]毫秒
2025-05-10 10:55:42 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{
  "tenantId" : "000000",
  "username" : "admin",
  "password" : "admin123",
  "rememberMe" : false,
  "uuid" : "c2945b7781a94c3eb5ed2336aedb0b28",
  "code" : "ouka",
  "clientId" : "e5cd7e4891bf95d1d19206ce24a7b32e",
  "grantType" : "password"
}]
2025-05-10 10:55:42 [schedule-pool-1] INFO  o.d.s.s.i.SysLogininforServiceImpl - [124.152.7.131]中国|甘肃省|兰州市|联通[admin][Error][验证码已失效]
2025-05-10 10:55:42 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[5]毫秒
2025-05-10 10:55:47 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 10:55:47 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:124.152.7.131:'
2025-05-10 10:55:47 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[127]毫秒
2025-05-10 10:56:00 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{
  "tenantId" : "000000",
  "username" : "admin",
  "password" : "admin123",
  "rememberMe" : false,
  "uuid" : "64884714dc984bd088afc5e49bf360c9",
  "code" : "rdot",
  "clientId" : "e5cd7e4891bf95d1d19206ce24a7b32e",
  "grantType" : "password"
}]
2025-05-10 10:56:01 [schedule-pool-2] INFO  o.d.s.s.i.SysLogininforServiceImpl - [124.152.7.131]中国|甘肃省|兰州市|联通[admin][Success][登录成功]
2025-05-10 10:56:02 [XNIO-1 task-2] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJabmVvazk4TXoyVnJMMEp1a3YzU2ZxWUxQSG03U0dJaSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.TTDgO7gmcptI5XE0b9YmccIK3dwPKgfGNHZN5vW1KNM
2025-05-10 10:56:02 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1869]毫秒
2025-05-10 10:56:07 [schedule-pool-1] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-10 10:56:07 [redisson-3-2] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-10 10:59:40 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /resource/oss/upload],无参数
2025-05-10 10:59:40 [XNIO-1 task-2] INFO  o.d.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-05-10 10:59:48 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /resource/oss/upload],耗时:[7802]毫秒
2025-05-10 11:01:13 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],参数类型[json],参数:[{
    "projectId": 1920027917233782786,
    "photoDocumentId": 1921037406137229313,
    "photoDocumentUrl": "https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg",
    "locationDescription": "图片描述"
}]
2025-05-10 11:01:53 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 提交隐患图片到外部AI分析服务，任务ID: 1921037883159617537, 图片URL: https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg
2025-05-10 11:02:04 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 发送请求到外部AI服务: http://*************:39528/api/v1/model/dangerousAndMajorEngineeringTask
2025-05-10 11:02:12 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 外部AI服务响应: {"code":200,"msg":"请求成功"}
2025-05-10 11:02:12 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],耗时:[58961]毫秒
2025-05-10 11:02:22 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{"taskId":"1921037883159617537","rawResult":"{\"code\":200,\"msg\":\"识别成功\",\"data\":{\"violationList\":[{\"violation\":\"疑似存在未设置必要的安全防护措施，可能存在高处坠落风险。\",\"regulation\":\"《安全生产法》第四十条：生产经营单位应当对重大危险源进行辨识、评估和监控，采取有效的控制措施。\",\"coordinate\":\"[250, 100, 350, 300]\",\"level\":\"2\",\"measure\":\"立即设置安全防护栏、安全网等必要的防护措施，防止人员或物品坠落。\"},{\"violation\":\"疑似存在临时支撑或模板结构未采取稳固措施，可能存在坍塌风险。\",\"regulation\":\"《建设工程安全生产管理条例》第二十七条：施工单位应当对施工用机械、工具、模板、支撑等进行安全检查，确保其符合安全要求。\",\"coordinate\":\"[200, 0, 400, 400]\",\"level\":\"2\",\"measure\":\"立即对临时支撑结构进行加固或更换，确保其承载能力符合要求。进行详细的结构验算，确保安全可靠。\"},{\"violation\":\"疑似施工现场管理混乱，存在杂物堆放，可能存在绊倒、滑倒等风险。\",\"regulation\":\"《安全生产法》第二十九条：生产经营单位应当对本单位的安全生产状况进行检查，及时发现并排除安全隐患。\",\"coordinate\":\"[0, 300, 200, 400]\",\"level\":\"1\",\"measure\":\"立即清理施工现场的杂物，保持现场整洁，确保人员通行安全。\"},{\"violation\":\"疑似临时用电不规范，电线、接头等裸露，可能存在触电风险。\",\"regulation\":\"《安全生产法》第四十四条：生产经营单位应当建立健全电气安全管理制度，采取有效的措施防止电气事故。\",\"coordinate\":\"[500, 0, 600, 100]\",\"level\":\"2\",\"measure\":\"立即对电气设备进行检查，确保符合安全要求。对裸露的电线、接头进行绝缘处理或更换。\"}],\"checkImage\":\"https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg\",\"resultImage\":\"result-image/1746846143147_result.jpg\",\"allLevel\":\"2\"}}"}]
2025-05-10 11:02:22 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1921037883159617537
2025-05-10 11:02:32 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 成功更新AI分析结果，任务ID: 1921037883159617537
2025-05-10 11:02:32 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[9697]毫秒
2025-05-10 11:40:54 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-10 11:40:54 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 11:40:54 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 11:40:54 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[11]毫秒
2025-05-10 11:40:54 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[19]毫秒
2025-05-10 11:40:59 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"46c9179002534da3bb39588678eae00c","code":"btzg","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-05-10 11:41:00 [schedule-pool-4] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-05-10 11:41:00 [XNIO-1 task-3] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJTTWFzalVvb0dTN3lBVmZKa0ZNdWFVcjB3MWNkY0tVUyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.qP23hvoLZGMjoti_jb55Y2jPtV4s0qf_2_mdqhn4WYs
2025-05-10 11:41:00 [XNIO-1 task-3] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJUUGlVemJRNmd2alNyYTY0Uk80eHZQTFY2R0xuQ2Y2ZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.g-E4CcRXsDlpJBo7nWaYR0v6KAq8l_4Ssdg8rw6cfes
2025-05-10 11:41:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[263]毫秒
2025-05-10 11:41:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-10 11:41:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[97]毫秒
2025-05-10 11:41:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-10 11:41:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[67]毫秒
2025-05-10 11:41:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-10 11:41:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-05-10 11:41:00 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-05-10 11:41:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJTTWFzalVvb0dTN3lBVmZKa0ZNdWFVcjB3MWNkY0tVUyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.qP23hvoLZGMjoti_jb55Y2jPtV4s0qf_2_mdqhn4WYs"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-10 11:41:00 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/list],耗时:[191]毫秒
2025-05-10 11:41:05 [schedule-pool-1] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-10 11:41:05 [redisson-3-3] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-10 11:56:54 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-10 11:56:54 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-10 11:56:54 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-10 11:56:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-10 11:56:54 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-10 11:56:54 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-10 11:56:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-10 11:56:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-10 11:57:00 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-10 11:57:00 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 30300 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-10 11:57:00 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-10 11:57:05 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-10 11:57:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-10 11:57:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-10 11:57:06 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2c376461
2025-05-10 11:57:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-10 11:57:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-10 11:57:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-10 11:57:08 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-10 11:57:08 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-10 11:57:09 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-10 11:57:09 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-10 11:57:10 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-10 11:57:17 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-10 11:57:18 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-10 11:57:18 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-10 11:57:18 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-10 11:57:18 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-10 11:57:18 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 19.022 seconds (process running for 20.082)
2025-05-10 11:57:18 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-10 11:57:19 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-10 11:57:19 [RMI TCP Connection(3)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-10 11:57:21 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJTTWFzalVvb0dTN3lBVmZKa0ZNdWFVcjB3MWNkY0tVUyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.qP23hvoLZGMjoti_jb55Y2jPtV4s0qf_2_mdqhn4WYs"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-10 11:59:13 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],参数类型[json],参数:[{
    "projectId": 1920027917233782800,
    "relatedHazardousItemId": 96,
    "photoDocumentId": 1921037406137229300,
    "photoDocumentUrl": "https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg",
    "locationDescription": "图片描述图片描述图片描述图片描述"
}]
2025-05-10 11:59:15 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 提交隐患图片到外部AI分析服务，任务ID: 1921052378783285250, 图片URL: https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg
2025-05-10 11:59:15 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 发送请求到外部AI服务: http://*************:39528/api/v1/model/dangerousAndMajorEngineeringTask
2025-05-10 11:59:15 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 外部AI服务响应: {"code":200,"msg":"请求成功"}
2025-05-10 11:59:16 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],耗时:[3008]毫秒
2025-05-10 11:59:34 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{"taskId":"1921052378783285250","rawResult":"{\"code\":200,\"msg\":\"识别成功\",\"data\":{\"violationList\":[{\"violation\":\"疑似存在未设置必要的安全防护措施，可能存在高处坠落风险。\",\"regulation\":\"《安全生产法》第四十条：生产经营单位应当对重大危险源进行辨识、评估和监控，采取有效的控制措施。\",\"coordinate\":\"[250, 100, 350, 300]\",\"level\":\"2\",\"measure\":\"立即设置安全防护栏、安全网等必要的防护措施，防止人员或物品坠落。\"},{\"violation\":\"疑似存在临时支撑或模板结构未采取稳固措施，可能存在坍塌风险。\",\"regulation\":\"《建设工程安全生产管理条例》第二十七条：施工单位应当对施工用机械、工具、模板、支撑等进行安全检查，确保其符合安全要求。\",\"coordinate\":\"[200, 0, 400, 400]\",\"level\":\"2\",\"measure\":\"立即对临时支撑结构进行加固或更换，确保其承载能力符合要求。进行详细的结构验算，确保安全可靠。\"},{\"violation\":\"疑似施工现场管理混乱，存在杂物堆放，可能存在绊倒、滑倒等风险。\",\"regulation\":\"《安全生产法》第二十九条：生产经营单位应当对本单位的安全生产状况进行检查，及时发现并排除安全隐患。\",\"coordinate\":\"[0, 300, 200, 400]\",\"level\":\"1\",\"measure\":\"立即清理施工现场的杂物，保持现场整洁，确保人员通行安全。\"},{\"violation\":\"疑似临时用电不规范，电线、接头等裸露，可能存在触电风险。\",\"regulation\":\"《安全生产法》第四十四条：生产经营单位应当建立健全电气安全管理制度，采取有效的措施防止电气事故。\",\"coordinate\":\"[500, 0, 600, 100]\",\"level\":\"2\",\"measure\":\"立即对电气设备进行检查，确保符合安全要求。对裸露的电线、接头进行绝缘处理或更换。\"}],\"checkImage\":\"https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg\",\"resultImage\":\"result-image/1746849574563_result.jpg\",\"allLevel\":\"2\"}}"}]
2025-05-10 11:59:34 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1921052378783285250
2025-05-10 11:59:34 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 成功更新AI分析结果，任务ID: 1921052378783285250
2025-05-10 11:59:34 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[102]毫秒
2025-05-10 13:58:35 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-05-10 13:58:35 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[17]毫秒
2025-05-10 13:58:35 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJTTWFzalVvb0dTN3lBVmZKa0ZNdWFVcjB3MWNkY0tVUyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.qP23hvoLZGMjoti_jb55Y2jPtV4s0qf_2_mdqhn4WYs"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-10 13:58:35 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[30]毫秒
2025-05-10 13:58:35 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-05-10 13:58:35 [XNIO-1 task-2] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJTTWFzalVvb0dTN3lBVmZKa0ZNdWFVcjB3MWNkY0tVUyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.qP23hvoLZGMjoti_jb55Y2jPtV4s0qf_2_mdqhn4WYs
2025-05-10 13:58:35 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[22]毫秒
2025-05-10 13:58:35 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 13:58:35 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-10 13:58:35 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[2]毫秒
2025-05-10 13:58:35 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 13:58:36 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[273]毫秒
2025-05-10 13:58:39 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 13:58:39 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 13:58:39 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[10]毫秒
2025-05-10 13:58:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-05-10 13:58:41 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-05-10 13:58:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[2]毫秒
2025-05-10 13:58:41 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[2]毫秒
2025-05-10 14:07:31 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 14:07:31 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 14:07:31 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[22]毫秒
2025-05-10 14:07:31 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 14:07:31 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 14:07:31 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[15]毫秒
2025-05-10 14:07:41 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 14:07:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-10 14:07:41 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 14:07:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[2]毫秒
2025-05-10 14:07:41 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[7]毫秒
2025-05-10 16:27:11 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-10 16:27:11 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-10 16:27:11 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-10 16:27:11 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-10 16:27:11 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-10 16:27:11 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-10 16:27:11 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-10 16:27:11 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-10 16:27:40 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-10 16:27:40 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 27000 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-10 16:27:40 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-10 16:27:44 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-10 16:27:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-10 16:27:45 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-10 16:27:45 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@3fe59f84
2025-05-10 16:27:45 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-10 16:27:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-10 16:27:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-10 16:27:48 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-10 16:27:48 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-10 16:27:48 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-10 16:27:48 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-10 16:27:50 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-10 16:27:59 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-10 16:28:00 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-10 16:28:00 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-10 16:28:00 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-10 16:28:00 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-10 16:28:00 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 22.175 seconds (process running for 23.448)
2025-05-10 16:28:00 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-10 16:28:00 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-10 16:28:01 [RMI TCP Connection(6)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-10 16:28:09 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 16:28:09 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:124.152.7.131:'
2025-05-10 16:28:09 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[337]毫秒
2025-05-10 16:28:36 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{
  "tenantId" : "000000",
  "username" : "admin",
  "password" : "admin123",
  "rememberMe" : false,
  "uuid" : "f901d75d990e4b69a20ad119add397ef",
  "code" : "mld7",
  "clientId" : "e5cd7e4891bf95d1d19206ce24a7b32e1",
  "grantType" : "password"
}]
2025-05-10 16:28:36 [XNIO-1 task-2] INFO  o.d.web.controller.AuthController - 客户端id: e5cd7e4891bf95d1d19206ce24a7b32e1 认证类型：password 异常!.
2025-05-10 16:28:36 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[142]毫秒
2025-05-10 16:28:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{
  "tenantId" : "000000",
  "username" : "admin",
  "password" : "admin123",
  "rememberMe" : false,
  "uuid" : "f901d75d990e4b69a20ad119add397ef",
  "code" : "mld7",
  "clientId" : "e5cd7e4891bf95d1d19206ce24a7b32e",
  "grantType" : "password"
}]
2025-05-10 16:28:41 [schedule-pool-1] INFO  o.d.s.s.i.SysLogininforServiceImpl - [124.152.7.131]中国|甘肃省|兰州市|联通[admin][Success][登录成功]
2025-05-10 16:28:41 [XNIO-1 task-2] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJEUWdqNXI1R1lGN1FpdnQzRWpQSkZRcFhYZlk4cHdkYiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiIsImVudGVycHJpc2VJbmZvIjoxOTIwNzMyNjU3NDM3NjA1ODg5fQ.g7Edr_T5GzzcvW0_b2obfe8bsxpJ0EuqXpdk5izi-zo
2025-05-10 16:28:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[745]毫秒
2025-05-10 16:28:46 [schedule-pool-2] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-10 16:28:46 [redisson-3-2] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-10 16:29:39 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],参数类型[json],参数:[{
    "projectId": 1920027917233782786,
    "photoDocumentId": 1921037406137229313,
    "photoDocumentUrl": "https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg",
    "locationDescription": "图片描述"
}]
2025-05-10 16:29:39 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],耗时:[32]毫秒
2025-05-10 16:30:11 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],参数类型[json],参数:[{
    "projectId": 1920027917233782786,
    "relatedHazardousItemId": 91,
    "photoDocumentId": 1921037406137229313,
    "photoDocumentUrl": "https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg",
    "locationDescription": "图片描述"
}]
2025-05-10 16:30:11 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 提交隐患图片到外部AI分析服务，任务ID: 1921120571275169794, 图片URL: https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg
2025-05-10 16:30:11 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 发送请求到外部AI服务: http://*************:39528/api/v1/model/dangerousAndMajorEngineeringTask
2025-05-10 16:30:11 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 外部AI服务响应: {"code":200,"msg":"请求成功"}
2025-05-10 16:30:11 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],耗时:[198]毫秒
2025-05-10 16:30:30 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{"taskId":"1921120571275169794","rawResult":"{\"code\":200,\"msg\":\"识别成功\",\"data\":{\"violationList\":[{\"violation\":\"疑似存在未设置必要的安全防护措施，可能存在高处坠落风险。\",\"regulation\":\"《安全生产法》第四十条：生产经营单位应当对重大危险源进行辨识、评估和监控，采取有效的控制措施。\",\"coordinate\":\"[250, 100, 350, 300]\",\"level\":\"2\",\"measure\":\"立即设置安全防护栏、安全网等必要的防护措施，防止人员或物品坠落。\"},{\"violation\":\"疑似存在临时支撑或模板结构未采取稳固措施，可能存在坍塌风险。\",\"regulation\":\"《建设工程安全生产管理条例》第二十七条：施工单位应当对施工用机械、工具、模板、支撑等进行安全检查，确保其符合安全要求。\",\"coordinate\":\"[200, 0, 400, 400]\",\"level\":\"2\",\"measure\":\"立即对临时支撑结构进行加固或更换，确保其承载能力符合要求。进行详细的结构验算，确保安全可靠。\"},{\"violation\":\"疑似施工现场管理混乱，存在杂物堆放，可能存在绊倒、滑倒等风险。\",\"regulation\":\"《安全生产法》第二十九条：生产经营单位应当对本单位的安全生产状况进行检查，及时发现并排除安全隐患。\",\"coordinate\":\"[0, 300, 200, 400]\",\"level\":\"1\",\"measure\":\"立即清理施工现场的杂物，保持现场整洁，确保人员通行安全。\"},{\"violation\":\"疑似临时用电不规范，电线、接头等裸露，可能存在触电风险。\",\"regulation\":\"《安全生产法》第四十四条：生产经营单位应当建立健全电气安全管理制度，采取有效的措施防止电气事故。\",\"coordinate\":\"[500, 0, 600, 100]\",\"level\":\"2\",\"measure\":\"立即对电气设备进行检查，确保符合安全要求。对裸露的电线、接头进行绝缘处理或更换。\"}],\"checkImage\":\"https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg\",\"resultImage\":\"result-image/1746865830892_result.jpg\",\"allLevel\":\"2\"}}"}]
2025-05-10 16:30:30 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1921120571275169794
2025-05-10 16:30:30 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[52]毫秒
2025-05-10 16:32:48 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-10 16:32:48 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-10 16:32:48 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-10 16:32:48 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-10 16:32:48 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-10 16:32:48 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-10 16:32:48 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-10 16:32:48 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-10 16:33:13 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-10 16:33:13 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 28076 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-10 16:33:13 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-10 16:33:17 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-10 16:33:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-10 16:33:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-10 16:33:18 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1d82d35c
2025-05-10 16:33:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-10 16:33:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-10 16:33:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-10 16:33:20 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-10 16:33:20 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-10 16:33:21 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-10 16:33:21 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-10 16:33:22 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-10 16:33:29 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-10 16:33:30 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-10 16:33:30 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-10 16:33:30 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-10 16:33:30 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-10 16:33:30 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 18.372 seconds (process running for 19.395)
2025-05-10 16:33:30 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-10 16:33:31 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-10 16:33:31 [RMI TCP Connection(7)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-10 16:33:46 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],参数类型[json],参数:[{
    "projectId": 1920027917233782786,
    "relatedHazardousItemId": 91,
    "photoDocumentId": 1921037406137229313,
    "photoDocumentUrl": "https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg",
    "locationDescription": "图片描述"
}]
2025-05-10 16:33:46 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 提交隐患图片到外部AI分析服务，任务ID: 1921121471968391170, 图片URL: https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg
2025-05-10 16:33:46 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 发送请求到外部AI服务: http://*************:39528/api/v1/model/dangerousAndMajorEngineeringTask
2025-05-10 16:33:46 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 外部AI服务响应: {"code":200,"msg":"请求成功"}
2025-05-10 16:33:46 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],耗时:[469]毫秒
2025-05-10 16:34:00 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{"taskId":"1921121471968391170","rawResult":"{\"code\":200,\"msg\":\"识别成功\",\"data\":{\"violationList\":[{\"violation\":\"疑似存在未设置必要的安全防护措施，可能存在高处坠落风险。\",\"regulation\":\"《安全生产法》第四十条：生产经营单位应当对重大危险源进行辨识、评估和监控，采取有效的控制措施。\",\"coordinate\":\"[250, 100, 350, 300]\",\"level\":\"2\",\"measure\":\"立即设置安全防护栏、安全网等必要的防护措施，防止人员或物品坠落。\"},{\"violation\":\"疑似存在临时支撑或模板结构未采取稳固措施，可能存在坍塌风险。\",\"regulation\":\"《建设工程安全生产管理条例》第二十七条：施工单位应当对施工用机械、工具、模板、支撑等进行安全检查，确保其符合安全要求。\",\"coordinate\":\"[200, 0, 400, 400]\",\"level\":\"2\",\"measure\":\"立即对临时支撑结构进行加固或更换，确保其承载能力符合要求。进行详细的结构验算，确保安全可靠。\"},{\"violation\":\"疑似施工现场管理混乱，存在杂物堆放，可能存在绊倒、滑倒等风险。\",\"regulation\":\"《安全生产法》第二十九条：生产经营单位应当对本单位的安全生产状况进行检查，及时发现并排除安全隐患。\",\"coordinate\":\"[0, 300, 200, 400]\",\"level\":\"1\",\"measure\":\"立即清理施工现场的杂物，保持现场整洁，确保人员通行安全。\"},{\"violation\":\"疑似临时用电不规范，电线、接头等裸露，可能存在触电风险。\",\"regulation\":\"《安全生产法》第四十四条：生产经营单位应当建立健全电气安全管理制度，采取有效的措施防止电气事故。\",\"coordinate\":\"[500, 0, 600, 100]\",\"level\":\"2\",\"measure\":\"立即对电气设备进行检查，确保符合安全要求。对裸露的电线、接头进行绝缘处理或更换。\"}],\"checkImage\":\"https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg\",\"resultImage\":\"result-image/1746866041249_result.jpg\",\"allLevel\":\"2\"}}"}]
2025-05-10 16:34:00 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1921121471968391170
2025-05-10 16:34:11 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[10986]毫秒
2025-05-10 16:35:46 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-10 16:35:46 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-10 16:35:46 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-10 16:35:46 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-10 16:35:46 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-10 16:35:46 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-10 16:35:46 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-10 16:35:46 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-10 16:35:52 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-10 16:35:52 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 21616 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-10 16:35:52 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-10 16:35:56 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-10 16:35:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-10 16:35:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-10 16:35:57 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2f006edf
2025-05-10 16:35:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-10 16:35:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-10 16:35:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-10 16:36:00 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-10 16:36:00 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-10 16:36:00 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-10 16:36:00 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-10 16:36:02 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-10 16:36:08 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-10 16:36:09 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-10 16:36:09 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-10 16:36:09 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-10 16:36:09 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-10 16:36:10 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 18.352 seconds (process running for 19.472)
2025-05-10 16:36:10 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-10 16:36:10 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-10 16:36:10 [RMI TCP Connection(6)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-10 16:36:23 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],参数类型[json],参数:[{
    "projectId": 1920027917233782786,
    "relatedHazardousItemId": 91,
    "photoDocumentId": 1921037406137229313,
    "photoDocumentUrl": "https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg",
    "locationDescription": "图片描述"
}]
2025-05-10 16:36:23 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 提交隐患图片到外部AI分析服务，任务ID: 1921122130029469698, 图片URL: https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg
2025-05-10 16:36:23 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 发送请求到外部AI服务: http://*************:39528/api/v1/model/dangerousAndMajorEngineeringTask
2025-05-10 16:36:23 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 外部AI服务响应: {"code":200,"msg":"请求成功"}
2025-05-10 16:36:23 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],耗时:[456]毫秒
2025-05-10 16:36:37 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{"taskId":"1921122130029469698","rawResult":"{\"code\":200,\"msg\":\"识别成功\",\"data\":{\"violationList\":[{\"violation\":\"疑似存在未设置必要的安全防护措施，可能存在高处坠落风险。\",\"regulation\":\"《安全生产法》第四十条：生产经营单位应当对重大危险源进行辨识、评估和监控，采取有效的控制措施。\",\"coordinate\":\"[250, 100, 350, 300]\",\"level\":\"2\",\"measure\":\"立即设置安全防护栏、安全网等必要的防护措施，防止人员或物品坠落。\"},{\"violation\":\"疑似存在临时支撑或模板结构未采取稳固措施，可能存在坍塌风险。\",\"regulation\":\"《建设工程安全生产管理条例》第二十七条：施工单位应当对施工用机械、工具、模板、支撑等进行安全检查，确保其符合安全要求。\",\"coordinate\":\"[200, 0, 400, 400]\",\"level\":\"2\",\"measure\":\"立即对临时支撑结构进行加固或更换，确保其承载能力符合要求。进行详细的结构验算，确保安全可靠。\"},{\"violation\":\"疑似施工现场管理混乱，存在杂物堆放，可能存在绊倒、滑倒等风险。\",\"regulation\":\"《安全生产法》第二十九条：生产经营单位应当对本单位的安全生产状况进行检查，及时发现并排除安全隐患。\",\"coordinate\":\"[0, 300, 200, 400]\",\"level\":\"1\",\"measure\":\"立即清理施工现场的杂物，保持现场整洁，确保人员通行安全。\"},{\"violation\":\"疑似临时用电不规范，电线、接头等裸露，可能存在触电风险。\",\"regulation\":\"《安全生产法》第四十四条：生产经营单位应当建立健全电气安全管理制度，采取有效的措施防止电气事故。\",\"coordinate\":\"[500, 0, 600, 100]\",\"level\":\"2\",\"measure\":\"立即对电气设备进行检查，确保符合安全要求。对裸露的电线、接头进行绝缘处理或更换。\"}],\"checkImage\":\"https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg\",\"resultImage\":\"result-image/1746866198074_result.jpg\",\"allLevel\":\"2\"}}"}]
2025-05-10 16:36:37 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1921122130029469698
2025-05-10 16:36:44 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[7122]毫秒
2025-05-10 16:43:09 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-10 16:43:09 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-10 16:43:09 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-10 16:43:09 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-10 16:43:09 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-10 16:43:09 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-10 16:43:09 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-10 16:43:09 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-10 16:43:13 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-10 16:43:13 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 30144 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-10 16:43:13 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-10 16:43:17 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-10 16:43:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-10 16:43:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-10 16:43:18 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@15ade272
2025-05-10 16:43:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-10 16:43:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-10 16:43:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-10 16:43:20 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-10 16:43:20 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-10 16:43:21 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-10 16:43:21 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-10 16:43:22 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-10 16:43:29 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-10 16:43:30 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-10 16:43:30 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-10 16:43:30 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-10 16:43:30 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-10 16:43:30 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 17.615 seconds (process running for 18.512)
2025-05-10 16:43:30 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-10 16:43:30 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-10 16:43:30 [RMI TCP Connection(5)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-10 16:52:15 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-10 16:52:15 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-10 16:52:15 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-10 16:52:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-10 16:52:15 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-10 16:52:15 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-10 16:52:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-10 16:52:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-10 16:52:20 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-10 16:52:20 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 31104 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-10 16:52:20 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-10 16:52:24 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-10 16:52:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-10 16:52:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-10 16:52:25 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@27244a88
2025-05-10 16:52:25 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-10 16:52:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-10 16:52:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-10 16:52:27 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-10 16:52:27 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-10 16:52:27 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-10 16:52:27 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-10 16:52:29 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-10 16:52:36 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-10 16:52:37 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-10 16:52:37 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-10 16:52:37 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-10 16:52:37 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-10 16:52:37 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 18.065 seconds (process running for 18.996)
2025-05-10 16:52:37 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-10 16:52:37 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-10 16:52:38 [RMI TCP Connection(4)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-10 16:54:36 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-10 16:54:36 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-10 16:54:36 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-10 16:54:36 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-10 16:54:36 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-10 16:54:36 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-10 16:54:36 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-10 16:54:36 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-10 16:55:01 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-10 16:55:01 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 7100 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-10 16:55:01 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-10 16:55:06 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-10 16:55:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-10 16:55:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-10 16:55:07 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@27244a88
2025-05-10 16:55:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-10 16:55:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-10 16:55:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-10 16:55:09 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-10 16:55:09 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-10 16:55:09 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-10 16:55:09 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-10 16:55:11 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-10 16:55:18 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-10 16:55:18 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-10 16:55:18 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-10 16:55:18 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-10 16:55:19 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-10 16:55:19 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 18.537 seconds (process running for 19.529)
2025-05-10 16:55:19 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-10 16:55:19 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-10 16:55:19 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-10 16:55:19 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],参数类型[json],参数:[{
    "projectId": 1920027917233782786,
    "relatedHazardousItemId": 91,
    "photoDocumentId": 1921037406137229313,
    "photoDocumentUrl": "https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg",
    "locationDescription": "图片描述test"
}]
2025-05-10 16:55:20 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 提交隐患图片到外部AI分析服务，任务ID: 1921126898563088386, 图片URL: https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg
2025-05-10 16:55:20 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 发送请求到外部AI服务: http://*************:39528/api/v1/model/dangerousAndMajorEngineeringTask
2025-05-10 16:55:20 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 外部AI服务响应: {"code":200,"msg":"请求成功"}
2025-05-10 16:55:20 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],耗时:[440]毫秒
2025-05-10 16:55:38 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{"taskId":"1921126898563088386","rawResult":"{\"code\":200,\"msg\":\"识别成功\",\"data\":{\"violationList\":[{\"violation\":\"疑似存在未设置必要的安全防护措施，可能存在高处坠落风险。\",\"regulation\":\"《安全生产法》第四十条：生产经营单位应当对重大危险源进行辨识、评估和监控，采取有效的控制措施。\",\"coordinate\":\"[250, 100, 350, 300]\",\"level\":\"2\",\"measure\":\"立即设置安全防护栏、安全网等必要的防护措施，防止人员或物品坠落。\"},{\"violation\":\"疑似存在临时支撑或模板结构未采取稳固措施，可能存在坍塌风险。\",\"regulation\":\"《建设工程安全生产管理条例》第二十七条：施工单位应当对施工用机械、工具、模板、支撑等进行安全检查，确保其符合安全要求。\",\"coordinate\":\"[200, 0, 400, 400]\",\"level\":\"2\",\"measure\":\"立即对临时支撑结构进行加固或更换，确保其承载能力符合要求。进行详细的结构验算，确保安全可靠。\"},{\"violation\":\"疑似施工现场管理混乱，存在杂物堆放，可能存在绊倒、滑倒等风险。\",\"regulation\":\"《安全生产法》第二十九条：生产经营单位应当对本单位的安全生产状况进行检查，及时发现并排除安全隐患。\",\"coordinate\":\"[0, 300, 200, 400]\",\"level\":\"1\",\"measure\":\"立即清理施工现场的杂物，保持现场整洁，确保人员通行安全。\"},{\"violation\":\"疑似临时用电不规范，电线、接头等裸露，可能存在触电风险。\",\"regulation\":\"《安全生产法》第四十四条：生产经营单位应当建立健全电气安全管理制度，采取有效的措施防止电气事故。\",\"coordinate\":\"[500, 0, 600, 100]\",\"level\":\"2\",\"measure\":\"立即对电气设备进行检查，确保符合安全要求。对裸露的电线、接头进行绝缘处理或更换。\"}],\"checkImage\":\"https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg\",\"resultImage\":\"result-image/1746867339190_result.jpg\",\"allLevel\":\"2\"}}"}]
2025-05-10 16:55:38 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1921126898563088386
2025-05-10 16:55:56 [XNIO-1 task-2] INFO  o.d.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-05-10 16:55:57 [XNIO-1 task-2] INFO  s.a.a.t.s.p.LoggingTransferListener - Transfer initiated...
2025-05-10 16:55:57 [XNIO-1 task-2] INFO  s.a.a.t.s.p.LoggingTransferListener - |                    | 0.0%
2025-05-10 16:55:59 [Thread-9] INFO  s.a.a.t.s.p.LoggingTransferListener - |====================| 100.0%
2025-05-10 16:55:59 [sdk-async-response-1-0] INFO  s.a.a.t.s.p.LoggingTransferListener - Transfer complete!
2025-05-10 16:56:02 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - AI分析结果图片已上传到OSS，文档ID: 1921127065647382530
2025-05-10 16:56:04 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 成功更新AI分析结果，任务ID: 1921126898563088386
2025-05-10 16:56:04 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[25698]毫秒
2025-05-10 17:00:27 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-10 17:00:27 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-10 17:00:27 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-10 17:00:27 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-10 17:00:27 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-10 17:00:27 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-10 17:00:27 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-10 17:00:27 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-10 17:00:52 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-10 17:00:52 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 16424 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-10 17:00:52 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-10 17:00:56 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-10 17:00:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-10 17:00:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-10 17:00:57 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@4df8443f
2025-05-10 17:00:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-10 17:00:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-10 17:00:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-10 17:00:59 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-10 17:00:59 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-10 17:01:00 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-10 17:01:00 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-10 17:01:01 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-10 17:01:08 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-10 17:01:09 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-10 17:01:09 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-10 17:01:09 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-10 17:01:09 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-10 17:01:09 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 18.339 seconds (process running for 19.884)
2025-05-10 17:01:09 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-10 17:01:09 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-10 17:01:10 [RMI TCP Connection(5)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-10 17:01:21 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],参数类型[json],参数:[{
    "projectId": 1920027917233782786,
    "relatedHazardousItemId": 91,
    "photoDocumentId": 1921037406137229313,
    "photoDocumentUrl": "https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg",
    "locationDescription": "图片描述test"
}]
2025-05-10 17:01:22 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 提交隐患图片到外部AI分析服务，任务ID: 1921128416867627009, 图片URL: https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg
2025-05-10 17:01:22 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 发送请求到外部AI服务: http://*************:39528/api/v1/model/dangerousAndMajorEngineeringTask
2025-05-10 17:01:22 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 外部AI服务响应: {"code":200,"msg":"请求成功"}
2025-05-10 17:01:22 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],耗时:[476]毫秒
2025-05-10 17:01:40 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{"taskId":"1921128416867627009","rawResult":"{\"code\":200,\"msg\":\"识别成功\",\"data\":{\"violationList\":[{\"violation\":\"疑似存在未设置必要的安全防护措施，可能存在高处坠落风险。\",\"regulation\":\"《安全生产法》第四十条：生产经营单位应当对重大危险源进行辨识、评估和监控，采取有效的控制措施。\",\"coordinate\":\"[250, 100, 350, 300]\",\"level\":\"2\",\"measure\":\"立即设置安全防护栏、安全网等必要的防护措施，防止人员或物品坠落。\"},{\"violation\":\"疑似存在临时支撑或模板结构未采取稳固措施，可能存在坍塌风险。\",\"regulation\":\"《建设工程安全生产管理条例》第二十七条：施工单位应当对施工用机械、工具、模板、支撑等进行安全检查，确保其符合安全要求。\",\"coordinate\":\"[200, 0, 400, 400]\",\"level\":\"2\",\"measure\":\"立即对临时支撑结构进行加固或更换，确保其承载能力符合要求。进行详细的结构验算，确保安全可靠。\"},{\"violation\":\"疑似施工现场管理混乱，存在杂物堆放，可能存在绊倒、滑倒等风险。\",\"regulation\":\"《安全生产法》第二十九条：生产经营单位应当对本单位的安全生产状况进行检查，及时发现并排除安全隐患。\",\"coordinate\":\"[0, 300, 200, 400]\",\"level\":\"1\",\"measure\":\"立即清理施工现场的杂物，保持现场整洁，确保人员通行安全。\"},{\"violation\":\"疑似临时用电不规范，电线、接头等裸露，可能存在触电风险。\",\"regulation\":\"《安全生产法》第四十四条：生产经营单位应当建立健全电气安全管理制度，采取有效的措施防止电气事故。\",\"coordinate\":\"[500, 0, 600, 100]\",\"level\":\"2\",\"measure\":\"立即对电气设备进行检查，确保符合安全要求。对裸露的电线、接头进行绝缘处理或更换。\"}],\"checkImage\":\"https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg\",\"resultImage\":\"result-image/1746867701207_result.jpg\",\"allLevel\":\"2\"}}"}]
2025-05-10 17:01:40 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1921128416867627009
2025-05-10 17:01:51 [XNIO-1 task-2] INFO  o.d.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-05-10 17:01:53 [XNIO-1 task-2] INFO  s.a.a.t.s.p.LoggingTransferListener - Transfer initiated...
2025-05-10 17:01:53 [XNIO-1 task-2] INFO  s.a.a.t.s.p.LoggingTransferListener - |                    | 0.0%
2025-05-10 17:01:55 [Thread-17] INFO  s.a.a.t.s.p.LoggingTransferListener - |====================| 100.0%
2025-05-10 17:01:55 [sdk-async-response-1-0] INFO  s.a.a.t.s.p.LoggingTransferListener - Transfer complete!
2025-05-10 17:01:55 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - AI分析结果图片已上传到OSS，文档ID: 1921128556575698946
2025-05-10 17:01:55 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 成功更新AI分析结果，任务ID: 1921128416867627009
2025-05-10 17:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[14892]毫秒
2025-05-10 17:05:47 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-10 17:05:47 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-10 17:05:47 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-10 17:05:47 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-10 17:05:47 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-10 17:05:47 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-10 17:05:47 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-10 17:05:47 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-10 17:06:12 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-10 17:06:12 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 20708 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-10 17:06:12 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-10 17:06:16 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-10 17:06:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-10 17:06:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-10 17:06:17 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@27244a88
2025-05-10 17:06:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-10 17:06:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-10 17:06:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-10 17:06:19 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-10 17:06:20 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-10 17:06:20 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-10 17:06:20 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-10 17:06:21 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-10 17:06:28 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-10 17:06:29 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-10 17:06:29 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-10 17:06:29 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-10 17:06:29 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-10 17:06:29 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 17.751 seconds (process running for 19.539)
2025-05-10 17:06:29 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-10 17:06:29 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-10 17:06:30 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-10 17:06:54 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],参数类型[json],参数:[{
    "projectId": 1920027917233782786,
    "relatedHazardousItemId": 91,
    "photoDocumentId": 1921037406137229313,
    "photoDocumentUrl": "https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg",
    "locationDescription": "图片描述test"
}]
2025-05-10 17:06:54 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 提交隐患图片到外部AI分析服务，任务ID: 1921129812304113665, 图片URL: https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg
2025-05-10 17:06:54 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 发送请求到外部AI服务: http://*************:39528/api/v1/model/dangerousAndMajorEngineeringTask
2025-05-10 17:06:54 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 外部AI服务响应: {"code":200,"msg":"请求成功"}
2025-05-10 17:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],耗时:[457]毫秒
2025-05-10 17:07:13 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],参数类型[json],参数:[{"taskId":"1921129812304113665","rawResult":"{\"code\":200,\"msg\":\"识别成功\",\"data\":{\"violationList\":[{\"violation\":\"疑似存在未设置必要的安全防护措施，可能存在高处坠落风险。\",\"regulation\":\"《安全生产法》第四十条：生产经营单位应当对重大危险源进行辨识、评估和监控，采取有效的控制措施。\",\"coordinate\":\"[250, 100, 350, 300]\",\"level\":\"2\",\"measure\":\"立即设置安全防护栏、安全网等必要的防护措施，防止人员或物品坠落。\"},{\"violation\":\"疑似存在临时支撑或模板结构未采取稳固措施，可能存在坍塌风险。\",\"regulation\":\"《建设工程安全生产管理条例》第二十七条：施工单位应当对施工用机械、工具、模板、支撑等进行安全检查，确保其符合安全要求。\",\"coordinate\":\"[200, 0, 400, 400]\",\"level\":\"2\",\"measure\":\"立即对临时支撑结构进行加固或更换，确保其承载能力符合要求。进行详细的结构验算，确保安全可靠。\"},{\"violation\":\"疑似施工现场管理混乱，存在杂物堆放，可能存在绊倒、滑倒等风险。\",\"regulation\":\"《安全生产法》第二十九条：生产经营单位应当对本单位的安全生产状况进行检查，及时发现并排除安全隐患。\",\"coordinate\":\"[0, 300, 200, 400]\",\"level\":\"1\",\"measure\":\"立即清理施工现场的杂物，保持现场整洁，确保人员通行安全。\"},{\"violation\":\"疑似临时用电不规范，电线、接头等裸露，可能存在触电风险。\",\"regulation\":\"《安全生产法》第四十四条：生产经营单位应当建立健全电气安全管理制度，采取有效的措施防止电气事故。\",\"coordinate\":\"[500, 0, 600, 100]\",\"level\":\"2\",\"measure\":\"立即对电气设备进行检查，确保符合安全要求。对裸露的电线、接头进行绝缘处理或更换。\"}],\"checkImage\":\"https://ms-static.gsjtsz.cn/2025/05/10/92dad3d7552a494ba5d269a66c181dbc.jpg\",\"resultImage\":\"result-image/1746868033985_result.jpg\",\"allLevel\":\"2\"}}"}]
2025-05-10 17:07:13 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 收到AI分析回调结果，任务ID: 1921129812304113665
2025-05-10 17:07:21 [XNIO-1 task-2] INFO  o.d.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-05-10 17:07:23 [XNIO-1 task-2] INFO  s.a.a.t.s.p.LoggingTransferListener - Transfer initiated...
2025-05-10 17:07:23 [XNIO-1 task-2] INFO  s.a.a.t.s.p.LoggingTransferListener - |                    | 0.0%
2025-05-10 17:07:24 [Thread-13] INFO  s.a.a.t.s.p.LoggingTransferListener - |====================| 100.0%
2025-05-10 17:07:25 [sdk-async-response-1-0] INFO  s.a.a.t.s.p.LoggingTransferListener - Transfer complete!
2025-05-10 17:07:25 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - AI分析结果图片已上传到OSS，文档ID: 1921129939534131201
2025-05-10 17:07:25 [XNIO-1 task-2] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 成功更新AI分析结果，任务ID: 1921129812304113665
2025-05-10 17:07:25 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/callback],耗时:[11812]毫秒
2025-05-10 17:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 17:07:55 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-10 17:07:55 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-05-10 17:07:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 17:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[109]毫秒
2025-05-10 17:07:58 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"f58de474761649a4a9094958ac9ccf07","code":"sezx","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-05-10 17:07:58 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[245]毫秒
2025-05-10 17:07:58 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 17:07:58 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 17:07:58 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[9]毫秒
2025-05-10 17:08:05 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"4086074ee8cb4336a4ddb4cc641f4907","code":"36pf","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-05-10 17:08:05 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[192]毫秒
2025-05-10 17:08:05 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 17:08:05 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 17:08:05 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[6]毫秒
2025-05-10 17:08:17 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"30a40abd492045d1b0a4668828f36808","code":"pe6b","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-05-10 17:08:17 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[191]毫秒
2025-05-10 17:08:17 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 17:08:17 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 6, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 17:08:17 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[19]毫秒
2025-05-10 17:10:27 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 17:10:27 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 17:10:27 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[15]毫秒
2025-05-10 17:10:29 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"da2e15ca60c14f0099b7e63de946b536","code":"xuh6","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-05-10 17:10:30 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[185]毫秒
2025-05-10 17:10:30 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 17:10:30 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 17:10:30 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[11]毫秒
2025-05-10 17:10:49 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"2d59cb1f01554a13b07bb6b8365e9661","code":"9po5","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-05-10 17:10:50 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[186]毫秒
2025-05-10 17:10:50 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 17:10:50 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 17:10:50 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[8]毫秒
2025-05-10 17:14:09 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-10 17:14:09 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-10 17:14:09 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-10 17:14:09 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-10 17:14:09 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-10 17:14:09 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-10 17:14:09 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-10 17:14:09 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-10 17:15:18 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-10 17:15:18 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 29936 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-10 17:15:18 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-10 17:15:24 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-10 17:15:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-10 17:15:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-10 17:15:25 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@79aa675b
2025-05-10 17:15:25 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-10 17:15:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-10 17:15:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-10 17:15:28 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-10 17:15:28 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-10 17:15:29 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-10 17:15:29 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-10 17:15:31 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-10 17:15:38 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-10 17:15:39 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-10 17:15:39 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-10 17:15:39 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-10 17:15:39 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-10 17:15:39 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 22.533 seconds (process running for 23.711)
2025-05-10 17:15:39 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-10 17:15:40 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-10 17:15:40 [RMI TCP Connection(16)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-10 17:15:53 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-10 17:15:53 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-10 17:15:53 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-10 17:15:54 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[176]毫秒
2025-05-10 17:15:54 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[211]毫秒
2025-05-10 17:15:57 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"7caac9d7e0104343a0e0b2ce516811da","code":"mz2e","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-05-10 17:15:57 [schedule-pool-1] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-05-10 17:15:57 [XNIO-1 task-3] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJ2TW9sQ0VsVEIyQTI0cHYzc1RDYnVFNms3cTVQUWJ6SSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.ew7asPTEujw6t-mC9LYAT7Ej8Xt1iMxds3ZaSwzOkX8
2025-05-10 17:15:57 [XNIO-1 task-3] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJtWjdXMGFrMjhnU0k4ak1QeEhTYzVsczVnSzVpemx5byIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.9rvsas5b_EUyeSVos5u7U6uTOVW94BMM8JG3LNQZHt8
2025-05-10 17:15:57 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[440]毫秒
2025-05-10 17:15:57 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-10 17:15:58 [XNIO-1 task-3] INFO  o.d.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-05-10 17:15:58 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[689]毫秒
2025-05-10 17:15:58 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-10 17:15:58 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[64]毫秒
2025-05-10 17:15:59 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-10 17:15:59 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[1]毫秒
2025-05-10 17:15:59 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJ2TW9sQ0VsVEIyQTI0cHYzc1RDYnVFNms3cTVQUWJ6SSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.ew7asPTEujw6t-mC9LYAT7Ej8Xt1iMxds3ZaSwzOkX8"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-10 17:16:02 [schedule-pool-2] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-10 17:16:02 [redisson-3-2] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-10 17:16:07 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.oss.previewListResource],无参数
2025-05-10 17:16:07 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.oss.previewListResource],耗时:[16]毫秒
2025-05-10 17:16:07 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/oss/list],参数类型[param],参数:[{"pageSize":["10"],"isAsc":["ascending"],"orderByColumn":["createTime"],"pageNum":["1"]}]
2025-05-10 17:16:07 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/oss/list],耗时:[202]毫秒
2025-05-10 17:16:10 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.oss.previewListResource],无参数
2025-05-10 17:16:10 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.oss.previewListResource],耗时:[0]毫秒
2025-05-10 17:16:10 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/oss/list],参数类型[param],参数:[{"pageSize":["10"],"isAsc":["ascending"],"orderByColumn":["createTime"],"pageNum":["9"]}]
2025-05-10 17:16:10 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/oss/list],耗时:[99]毫秒
2025-05-10 17:16:13 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[DELETE /resource/oss/1921127065647382530],无参数
2025-05-10 17:16:14 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[DELETE /resource/oss/1921127065647382530],耗时:[408]毫秒
2025-05-10 17:16:14 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.oss.previewListResource],无参数
2025-05-10 17:16:14 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.oss.previewListResource],耗时:[0]毫秒
2025-05-10 17:16:14 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/oss/list],参数类型[param],参数:[{"pageSize":["10"],"isAsc":["ascending"],"orderByColumn":["createTime"],"pageNum":["9"]}]
2025-05-10 17:16:14 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/oss/list],耗时:[91]毫秒
2025-05-10 17:16:17 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[DELETE /resource/oss/1921128556575698946],无参数
2025-05-10 17:16:17 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[DELETE /resource/oss/1921128556575698946],耗时:[88]毫秒
2025-05-10 17:16:17 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.oss.previewListResource],无参数
2025-05-10 17:16:17 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.oss.previewListResource],耗时:[0]毫秒
2025-05-10 17:16:17 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/oss/list],参数类型[param],参数:[{"pageSize":["10"],"isAsc":["ascending"],"orderByColumn":["createTime"],"pageNum":["9"]}]
2025-05-10 17:16:17 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/oss/list],耗时:[82]毫秒

package org.dromara.special.domain.sync;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SyncSpecialEquipment {


    /** 施工许可证编号 */
    private String CONSTRUCTIONPERMITNUM;

    /** 设备备案编号 */
    private String SBBABH;

    /** 设备类型 */
    private String SBLX;

    /** 设备名称 */
    private String SBMC;

    /** 规格型号 */
    private String GGXH;

    /** 生产厂商 */
    private String SCCS;

    /** 生产厂商统一社会信用代码 */
    private String SCCSTYSHXYDM;

    /** 出厂编号 */
    private String CCBH;

    /** 出厂日期 */
    private Date CCRQ;

    /** 特种设备生产许可证编号 */
    private String ZZXKZH;

    /** 使用年限 */
    private String SYNX;

    /** 产权单位 */
    private String CQDW;

    /** 产权单位统一社会信用代码 */
    private String CQDWTYSHXYDM;

    /** 产权单地址 */
    private String CQDWDZ;

    /** 企业法人代表 */
    private String QYFRDB;

    /** 法定代表人身份证件号 */
    private String FRSFZH;

    /** 联系人 */
    private String LXR;

    /** 联系电话 */
    private String LXDH;

    /**  出厂价格（万元） */
    private BigDecimal CCJG;

    /** 购置年月 */
    private Date GZNY;

    /** 设备备案机关 */
    private String SBBAJG;

    /** 发证机关代码*/
    private String FZJGTYSHDM;

    /** 机械所在地市 */
    private String JXSZDS;

    /** 机械所在区县 */
    private String JXSZQX;

    /** 设备所在区域 */
    private String JXQY;

    //  机器数据字段

    /** 额定起重量（T） */
    private BigDecimal EDQZL;

    /** 额定起重力矩（T·M） */
    private BigDecimal EDQZLJ;

    /** 起重臂长度（M） */
    private BigDecimal TSQZJQZCB;

    /**  最大工作幅度（M） */
    private BigDecimal ZDGZFD;

    /** 最大幅度额定起重量（T） */
    private BigDecimal TSQZJZDFDQZL;

    /** 独起升高度（M） */
    private BigDecimal ZDDLQSGD;

    /** 最大起升高度（M） */
    private BigDecimal ZDQSGD;

    /** 主要结构件唯一编号 */
    private String ZYJGJWYBH;

    /** 塔式起重机拟安装最大高度（m） */
    private BigDecimal TSQZJNAZZDGD;

    /** 标准节主要结构件规格 */
    private String ZYJGJGG;

    /** 加强节参数（长×宽×高）mm */
    private String TAQZJJQJCS;

    /** 标准节参数（长×宽×高）mm */
    private String TSQZJBZJCS;

    /** 施工升降机用途类型 */
    private String SGSJJYTLX;

    /** 电动机总功率（kW） */
    private BigDecimal DDJZGL;

    /** 额定提升速度（m/min） */
    private BigDecimal EDTSSD;

    /** 防坠安全器型号 */
    private String FZAQQXH;

    /** 运载装置（吊笼）净空尺寸（长×宽×高）m */
    private String JKCC;

    /** 设备类别 */
    private String SBLB;

    /** 门式起重机跨度（m） */
    private String MSQZJKD;

}

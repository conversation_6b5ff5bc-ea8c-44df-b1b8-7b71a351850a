package org.dromara.special.mapper;

import org.dromara.special.domain.SpecialEquipment;
import org.dromara.special.domain.vo.SpecialEquipmentVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 特种设备Mapper接口
 *
 * <AUTHOR> Li
 * @date 2025-05-14
 */
public interface SpecialEquipmentMapper extends BaseMapperPlus<SpecialEquipment, SpecialEquipmentVo> {

    List<SpecialEquipmentVo> getAuthorityList();

}

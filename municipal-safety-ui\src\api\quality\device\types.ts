/**
 * 设备管理查询对象类型
 */
export interface DeviceQuery extends PageQuery {
  deviceName?: string;
  specification?: string;
  deviceCode?: string;
  status?: string;
  createTime?: string[];
}

/**
 * 设备管理返回对象
 */
export interface DeviceVO extends BaseEntity {
  deviceId: number;
  deviceName: string;
  specification: string;
  deviceCode: string;
  quantity: number;
  deviceImageOssId?: number;
  deviceImageUrl?: string;
  deviceDescription?: string;
  devicePurpose?: string;
  usageInstructions?: string;
  manualFileOssId?: number;
  manualFileUrl?: string;
  manualFileName?: string;
  status: string;
  createByName?: string;
  updateByName?: string;
}

/**
 * 设备管理表单类型
 */
export interface DeviceForm {
  deviceId?: number;
  deviceName: string;
  specification: string;
  deviceCode: string;
  deviceType: string;
  quantity: number;
  deviceImageOssId?: number;
  deviceDescription?: string;
  devicePurpose?: string;
  usageInstructions?: string;
  manualFileOssId?: number;
  status: string;
}

/**
 * 文件上传响应
 */
export interface FileUploadResponse {
  ossId: number;
  url: string;
  fileName?: string;
}

/**
 * 设备统计数据
 */
export interface DeviceStatistics {
  total: number;
  normalCount: number;
  disabledCount: number;
  increase: number;
  increaseRate: number;
}

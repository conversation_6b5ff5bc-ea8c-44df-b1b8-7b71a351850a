{"doc": " 项目人员关联表视图对象 prj_personnel\n\n <AUTHOR>\n @date 2025-05-09\n", "fields": [{"name": "projectPersonnelId", "doc": " 项目人员关联ID\n"}, {"name": "projectId", "doc": " 项目ID (逻辑外键至 prj_projects.project_id)\n"}, {"name": "personId", "doc": " 人员表id\n"}, {"name": "userId", "doc": " 用户ID (逻辑外键至 sys_users.user_id)\n"}, {"name": "orgId", "doc": " 该人员在项目中的所属单位ID (逻辑外键至 sys_organizations.org_id)\n"}, {"name": "roleOnProject", "doc": " 在本项目中的具体角色/岗位（取字典值）\n"}, {"name": "isSpecialOps", "doc": " 是否特种作业人员 (0:否, 1:是)\n"}, {"name": "startDateOnProject", "doc": " 进入项目日期\n"}, {"name": "endDateOnProject", "doc": " 离开项目日期\n"}, {"name": "name", "doc": " 人员信息\n"}, {"name": "idCard", "doc": " 身份证号码\n"}, {"name": "phone", "doc": " 手机号码\n"}, {"name": "gender", "doc": " 性别\n"}, {"name": "education", "doc": " 文化程度\n"}, {"name": "enterpriseName", "doc": " 企业名称\n"}, {"name": "unifiedSocialCreditCode", "doc": " 企业统一社会信用代码\n"}], "enumConstants": [], "methods": [], "constructors": []}
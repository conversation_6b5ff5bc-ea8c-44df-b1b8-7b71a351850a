package org.dromara.attendance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.attendance.domain.MAttSn;

import java.io.Serial;
import java.io.Serializable;



/**
 * 考勤设备视图对象 m_att_sn
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MAttSn.class)
public class MAttSnVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    @ExcelProperty(value = "设备id")
    private Long snId;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long projectId;

    /**
     * 设备sn号
     */
    @ExcelProperty(value = "设备sn号")
    private String sn;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String snName;

    /**
     * 方向。0-进，1-出
     */
    @ExcelProperty(value = "方向。0-进，1-出")
    private Long direction;

    /**
     * 设备状态。0-在线，1-掉线
     */
    @ExcelProperty(value = "设备状态。0-在线，1-掉线")
    private Long status;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 返回相同的值为true
     */
    private Boolean disabled;

    private Long personSnId;
}

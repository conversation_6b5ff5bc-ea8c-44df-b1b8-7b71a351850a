package org.dromara.projects.domain.bo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/14 9:38
 * @Description TODO
 * @Version 1.0
 */
@Data
public class PrjHazardousItemsAppBo {

    /**
     * 分页当前页
     */
    private Integer pageNum;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 查询类型 1项目 2工程
     */
    private Integer queryType;

    /**
     * 查询数据
     */
    private String queryData;

    /**
     *  projectIdList
     */
    private List<String> projectIdList;

    /**
     *  projectType  根据项目  查询工层  为空时返回 0
     */
    private String projectType;
}

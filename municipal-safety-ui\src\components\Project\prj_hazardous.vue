<template>
  <div class="p-2">
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="60%" append-to-body @close="handleClose">
      <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
        :leave-active-class="proxy?.animate.searchAnimate.leave">
        <div v-show="showSearch" class="mb-[10px]">
          <el-card shadow="hover">
            <el-form ref="queryFormRef" :model="queryParams" :inline="true">
              <el-form-item label="危大工程名称" prop="itemName" label-width="96px">
                <el-input v-model="queryParams.itemName" placeholder="请输入危大工程名称/描述" clearable
                  @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="危大类型" prop="dangerListType">
                <el-select v-model="queryParams.dangerListType" placeholder="请选择危大类型" clearable>
                  <el-option v-for="dict in danger_list_type" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>

      <el-card shadow="never">
        <el-table v-loading="loading" :data="prj_hazardous_itemsList" show-overflow-tooltip class="expertTable4"
          highlight-current-row row-key="itemId" @row-click="handleSelectionProject">
          <el-table-column type="index" label="序号" width="55" align="center" />
          <el-table-column label="项目名称" align="center" prop="projectName" min-width="200px" />
          <el-table-column label="涉危工程" align="center" prop="parentName" min-width="200px" />
          <el-table-column label="危大工程名称" align="center" prop="itemName" min-width="130px" />
          <el-table-column label="具体范围" align="center" prop="scopeDetails" min-width="130px" />
          <el-table-column label="危大类型" align="center" prop="dangerListType">
            <template #default="scope">
              <dict-tag :options="danger_list_type" :value="scope.row.dangerListType" />
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-card>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmChange">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { listPrj_hazardous_items } from '@/api/projects/prj_hazardous_items';
import { Prj_hazardous_itemsVO, Prj_hazardous_itemsQuery } from '@/api/projects/prj_hazardous_items/types';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { danger_list_type } = toRefs<any>(proxy?.useDict('danger_list_type', 'project_item_status', 'plan_ai_check_status'));

const route = useRoute();
const prj_hazardous_itemsList = ref<Prj_hazardous_itemsVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const queryFormRef = ref<ElFormInstance>();
const queryParams = reactive<Prj_hazardous_itemsQuery>({
  pageNum: 1,
  pageSize: 10,
  projectId: '',
  dangerId: undefined,
  itemName: undefined,
  scopeDetails: undefined,
  dangerListType: undefined,
  startDate: '',
  plannedEndDate: '',
  actualEndDate: '',
  actualStartDate: '',
  status: undefined,
  params: {
  }
})
const selectedData1 = ref<{ itemId: string | number, itemName: string }>({
  itemId: '',
  itemName: ''
});
const props = defineProps({
  isShowModel1: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: String,
    default: '',
  }
});
const dialog = reactive({
  visible: false,
  title: '添加项目工程',
})
const emit = defineEmits(['update:isShowModel1', 'selectionProjectData1']);
watch([() => props.isShowModel1, () => props.itemId], (newVal) => {
  queryParams.projectId = newVal[1];
  dialog.visible = newVal[0];
  if (newVal[0]) {
    resetQuery();
    getList();
  }
}
);
/** 查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPrj_hazardous_items(queryParams);
  prj_hazardous_itemsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  if (route.params.id) {
    queryParams.projectId = (route.params.id as string | number);
  }
  handleQuery();
}
const confirmChange = () => {
  console.log('selectedData1.value', selectedData1.value);

  emit('selectionProjectData1', selectedData1.value);
  handleClose();
}
// 选中表格数据的点击事件
const handleSelectionProject = (val: Prj_hazardous_itemsQuery) => {
  selectedData1.value.itemId = val.itemId;
  selectedData1.value.itemName = val.itemName;
}
const cancel = () => {
  dialog.visible = false
}
const handleClose = () => {
  emit('update:isShowModel1', false)
}
onMounted(() => {
  // 接收动态路由的参数
  if (route.params.id) {
    queryParams.projectId = (route.params.id as string | number);
    getList();
  }
});
</script>

<style lang="scss">
.expertTable4 {

  .el-popper,
  .is-dark {
    max-width: 300px;
  }
}
</style>
import request from '@/utils/request';
import { AxiosPromise } from 'axios';
/**
 * 保存
 * @param query
 * @returns {*}
 */

export const expertReviewSave = (data: any): AxiosPromise<any> => {
  return request({
    url: '/system/expertReview/save',
    method: 'post',
    data
  });
};
// 获取详情
export const getExpertReviewDetail = (planId: string | number): AxiosPromise<any> => {
  return request({
    url: `/system/expertReview/detail/${planId}`,
    method: 'get',
  });
}
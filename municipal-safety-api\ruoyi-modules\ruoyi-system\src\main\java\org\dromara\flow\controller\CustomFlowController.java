package org.dromara.flow.controller;

import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.flow.service.CustomFlowService;
import org.dromara.projects.domain.vo.ItemsAiDetailVO;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.service.ISysUserService;
import org.dromara.warm.flow.orm.mapper.FlowInstanceMapper;
import org.dromara.workflow.domain.bo.FlowInstanceBo;
import org.dromara.workflow.domain.bo.FlowTaskBo;
import org.dromara.workflow.domain.vo.FlowInstanceVo;
import org.dromara.workflow.domain.vo.FlowTaskVo;
import org.dromara.workflow.service.IFlwInstanceService;
import org.dromara.workflow.service.IFlwTaskService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> Zu Da
 * @date 2025/5/28 15:02
 * @Description TODO
 * @Version 1.0
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/custom/flow")
public class CustomFlowController {

    @Resource
    private CustomFlowService customFlowService;

    @Resource
    private IFlwInstanceService flwInstanceService;

    @Resource
    private ISysUserService sysUserService;

    /**
     * 获取任务变量
     *
     * @param busId
     * @return
     */
    @GetMapping("/getVariable/{busId}")
    public R<JSONObject> getVariable(@PathVariable String busId) {
        return R.ok(customFlowService.getVariable(busId));
    }

    /**
     * ai隐患获取详情信息
     *
     * @param id
     * @return
     */
    @GetMapping("/aiDangerDetail/{id}")
    public R<ItemsAiDetailVO> aiDetailVOR(@PathVariable Long id) {
        return R.ok(customFlowService.getAiHazAnalysisTaskDetail(id));
    }


    /**
     * 查询当前用户的待办任务
     *
     * @param flowTaskBo 参数
     * @param pageQuery  分页
     */
    @GetMapping("/pageByTaskWait")
    public TableDataInfo<FlowTaskVo> pageByTaskWait(FlowTaskBo flowTaskBo, PageQuery pageQuery) {
        return customFlowService.pageByTaskWaitZjj(flowTaskBo, pageQuery);
    }

    /**
     * 按照实例id删除流程实例
     *
     * @param instanceIds 实例id
     */
    @DeleteMapping("/deleteByInstanceIds/{instanceIds}")
    public R<Boolean> deleteByInstanceIds(@PathVariable List<Long> instanceIds) {
        // 额外删除流程
        customFlowService.removeByInstanceIds(instanceIds);
        return R.ok(flwInstanceService.deleteByInstanceIds(instanceIds));
    }

    /**
     * 获取用户列表
     */
    @GetMapping("/selectUserPage")
    public TableDataInfo<SysUserVo> list(SysUserBo user, PageQuery pageQuery) {
        return sysUserService.selectUserBySelect(user, pageQuery);
    }

    /**
     * 获取用户列表
     */
    @GetMapping("/makeCopyUserPage")
    public TableDataInfo<SysUserVo> makeCopyUserPage(SysUserBo user, PageQuery pageQuery) {
        return sysUserService.selectMekeCopyUserPage(user, pageQuery);
    }
}

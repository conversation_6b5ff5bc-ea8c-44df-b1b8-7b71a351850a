{"doc": " 巡检计划\n\n <AUTHOR>\n @date 2025-06-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.plan.domain.bo.PrjPatrolPlanBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询巡检计划列表\n"}, {"name": "export", "paramTypes": ["org.dromara.plan.domain.bo.PrjPatrolPlanBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出巡检计划列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取巡检计划详细信息\n\n @param planId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.plan.domain.bo.PrjPatrolPlanBo"], "doc": " 新增巡检计划\n"}, {"name": "edit", "paramTypes": ["org.dromara.plan.domain.bo.PrjPatrolPlanBo"], "doc": " 修改巡检计划\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除巡检计划\n\n @param planIds 主键串\n"}, {"name": "AIProblemList", "paramTypes": ["org.dromara.ai.domain.dto.AiHazAnalysisTasksDto", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "  本次巡检计划  隐患信息\n"}], "constructors": []}
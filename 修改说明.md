# 绑定人员功能修改说明

## 修改概述
将原有的简单"绑定管理员"功能升级为完整的"项目管理员"管理功能，支持查看、删除和新增当前项目的管理员。**只显示角色为"项目管理员"的人员**。

## 主要修改内容

### 1. 界面修改 (municipal-safety-ui/src/views/projects/prj_projects/index.vue)

#### 对话框标题和宽度调整
- 将对话框标题从"绑定项目管理员"改为"项目管理员"
- 将对话框宽度从500px调整为1000px以容纳更多内容

#### 新增项目管理员列表视图
- 添加项目管理员列表表格，**只显示角色为"PROJECT_ADMIN"的人员**，包含以下字段：
  - 姓名 (name)
  - 身份证号 (idCard) 
  - 手机号 (phone)
  - 所属单位 (enterpriseName)
  - 项目角色 (roleOnProject)
  - 操作按钮（删除）

#### 新增表单视图切换
- 添加"新增项目管理员"按钮
- 实现列表视图和新增表单视图的切换
- 新增表单保持原有的字段：姓名、身份证号、手机号、所属单位
- **新增的人员自动设置为"PROJECT_ADMIN"角色**

#### 底部按钮优化
- 在列表视图显示"关闭"按钮
- 在新增表单视图显示"返回"和"确定"按钮

### 2. 数据管理优化

#### 新增状态变量
```javascript
const adminList = ref<any[]>([]);           // 项目管理员列表
const showAddAdminForm = ref(false);        // 是否显示新增管理员表单
const adminListLoading = ref(false);        // 管理员列表加载状态
```

#### 新增核心功能函数
- `loadAdminList()`: 加载项目管理员列表，**只显示roleOnProject === 'PROJECT_ADMIN'的人员**
- `showAddForm()`: 显示新增项目管理员表单
- `deleteAdmin()`: 删除项目管理员
- `cancelAdminBind()`: 优化取消逻辑，支持表单返回和对话框关闭

### 3. API接口扩展 (municipal-safety-ui/src/api/projects/prj_projects/index.ts)

#### 新增API接口
```javascript
// 获取项目管理人员列表
export const getProjectAdmins = (projectId: number | string) => {
  return request({
    url: `/projects/personnel/listByProjectId/${projectId}`,
    method: 'get'
  });
};
```

### 4. 业务逻辑优化

#### 绑定人员流程改进
1. 点击"绑定人员"按钮 → 打开项目管理人员对话框
2. 默认显示当前项目的管理人员列表
3. 可以点击"新增管理人员"进入新增表单
4. 新增成功后自动返回列表视图并刷新数据
5. 支持删除现有管理人员

#### 数据加载优化
- 在打开对话框时自动加载项目管理人员列表
- 新增/删除操作后自动刷新列表
- 保持原有的单位选择逻辑

## 功能特点

### 1. 完整的CRUD操作
- **查看**: 列表展示所有项目管理人员
- **新增**: 通过表单添加新的管理人员
- **删除**: 支持单个删除操作
- **修改**: 保留原有的快速绑定功能

### 2. 用户体验优化
- 清晰的视图切换（列表 ↔ 表单）
- 直观的操作按钮和确认提示
- 实时的数据刷新和状态反馈

### 3. 数据完整性
- 显示完整的人员信息（姓名、证件、联系方式、单位、角色）
- 保持与现有数据结构的兼容性
- 支持角色字典值的正确显示

## 使用说明

1. 在项目列表中点击"更多" → "绑定人员"
2. 查看当前项目的所有管理人员
3. 点击"新增管理人员"添加新人员
4. 点击"删除"按钮移除不需要的人员
5. 所有操作都会实时更新列表数据

## 技术实现

- 基于Vue 3 + Element Plus
- 使用响应式数据管理
- 支持异步数据加载和错误处理
- 保持与现有代码架构的一致性

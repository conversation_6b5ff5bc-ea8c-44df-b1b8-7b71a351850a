{"doc": " 特种作业人员信息Service接口\n\n <AUTHOR>\n @date 2025-05-13\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询特种作业人员信息\n\n @param sopId 主键\n @return 特种作业人员信息\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.special.domain.bo.SpecialOperationPersonnelBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询特种作业人员信息列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 特种作业人员信息分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.special.domain.bo.SpecialOperationPersonnelBo"], "doc": " 查询符合条件的特种作业人员信息列表\n\n @param bo 查询条件\n @return 特种作业人员信息列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.special.domain.bo.SpecialOperationPersonnelBo"], "doc": " 新增特种作业人员信息\n\n @param bo 特种作业人员信息\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.special.domain.bo.SpecialOperationPersonnelBo"], "doc": " 修改特种作业人员信息\n\n @param bo 特种作业人员信息\n @return 是否修改成功\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除特种作业人员信息信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
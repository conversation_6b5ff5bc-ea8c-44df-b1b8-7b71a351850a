package org.dromara.flow.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.flow.domain.PrjHazardousItemsSpecialist;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsSpecialistBoToPrjHazardousItemsSpecialistMapperImpl implements PrjHazardousItemsSpecialistBoToPrjHazardousItemsSpecialistMapper {

    @Override
    public PrjHazardousItemsSpecialist convert(PrjHazardousItemsSpecialistBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsSpecialist prjHazardousItemsSpecialist = new PrjHazardousItemsSpecialist();

        prjHazardousItemsSpecialist.setSearchValue( arg0.getSearchValue() );
        prjHazardousItemsSpecialist.setCreateDept( arg0.getCreateDept() );
        prjHazardousItemsSpecialist.setCreateBy( arg0.getCreateBy() );
        prjHazardousItemsSpecialist.setCreateTime( arg0.getCreateTime() );
        prjHazardousItemsSpecialist.setUpdateBy( arg0.getUpdateBy() );
        prjHazardousItemsSpecialist.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            prjHazardousItemsSpecialist.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        prjHazardousItemsSpecialist.setId( arg0.getId() );
        prjHazardousItemsSpecialist.setSpecialist( arg0.getSpecialist() );
        prjHazardousItemsSpecialist.setInstruction( arg0.getInstruction() );
        prjHazardousItemsSpecialist.setDownPushFile( arg0.getDownPushFile() );
        prjHazardousItemsSpecialist.setTaskId( arg0.getTaskId() );

        return prjHazardousItemsSpecialist;
    }

    @Override
    public PrjHazardousItemsSpecialist convert(PrjHazardousItemsSpecialistBo arg0, PrjHazardousItemsSpecialist arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setSpecialist( arg0.getSpecialist() );
        arg1.setInstruction( arg0.getInstruction() );
        arg1.setDownPushFile( arg0.getDownPushFile() );
        arg1.setTaskId( arg0.getTaskId() );

        return arg1;
    }
}

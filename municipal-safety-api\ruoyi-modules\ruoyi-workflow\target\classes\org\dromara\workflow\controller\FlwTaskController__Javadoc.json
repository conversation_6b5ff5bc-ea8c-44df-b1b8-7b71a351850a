{"doc": " 任务管理 控制层\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "startWorkFlow", "paramTypes": ["org.dromara.workflow.domain.bo.StartProcessBo"], "doc": " 启动任务\n\n @param startProcessBo 启动流程参数\n"}, {"name": "completeTask", "paramTypes": ["org.dromara.workflow.domain.bo.CompleteTaskBo"], "doc": " 办理任务\n\n @param completeTaskBo 办理任务参数\n"}, {"name": "pageByTaskWait", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询当前用户的待办任务\n\n @param flowTaskBo 参数\n @param pageQuery  分页\n"}, {"name": "pageByTaskFinish", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询当前用户的已办任务\n\n @param flowTaskBo 参数\n @param pageQuery  分页\n"}, {"name": "pageByAllTaskWait", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询待办任务\n\n @param flowTaskBo 参数\n @param pageQuery  分页\n"}, {"name": "pageByAllTaskFinish", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询已办任务\n\n @param flowTaskBo 参数\n @param pageQuery  分页\n"}, {"name": "pageByTaskCopy", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询当前用户的抄送\n\n @param flowTaskBo 参数\n @param pageQuery  分页\n"}, {"name": "getTask", "paramTypes": ["java.lang.Long"], "doc": " 根据taskId查询代表任务\n\n @param taskId 任务id\n"}, {"name": "getNextNodeList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowNextNodeBo"], "doc": " 获取下一节点信息\n\n @param bo 参数\n"}, {"name": "terminationTask", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTerminationBo"], "doc": " 终止任务\n\n @param bo 参数\n"}, {"name": "taskOperation", "paramTypes": ["org.dromara.workflow.domain.bo.TaskOperationBo", "java.lang.String"], "doc": " 任务操作\n\n @param bo            参数\n @param taskOperation 操作类型，委派 delegateTask、转办 transferTask、加签 addSignature、减签 reductionSignature\n"}, {"name": "updateAssignee", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 修改任务办理人\n\n @param taskIdList 任务id\n @param userId     办理人id\n"}, {"name": "backProcess", "paramTypes": ["org.dromara.workflow.domain.bo.BackProcessBo"], "doc": " 驳回审批\n\n @param bo 参数\n"}, {"name": "getBackTaskNode", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 获取可驳回的前置节点\n\n @param definitionId 流程定义id\n @param nowNodeCode  当前节点\n"}, {"name": "currentTaskAllUser", "paramTypes": ["java.lang.Long"], "doc": " 获取当前任务的所有办理人\n\n @param taskId 任务id\n"}], "constructors": []}
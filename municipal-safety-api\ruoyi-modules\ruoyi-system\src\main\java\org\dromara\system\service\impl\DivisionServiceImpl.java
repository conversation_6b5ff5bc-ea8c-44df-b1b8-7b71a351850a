package org.dromara.system.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.system.domain.bo.DivisionBo;
import org.dromara.system.domain.vo.DivisionVo;
import org.dromara.system.domain.Division;
import org.dromara.system.mapper.DivisionMapper;
import org.dromara.system.service.IDivisionService;

import java.util.*;

/**
 * 行政区划Service业务层处理
 *
 * @date 2025-04-30
 */
@RequiredArgsConstructor
@Service
public class DivisionServiceImpl implements IDivisionService {

    private final DivisionMapper baseMapper;

    /**
     * 查询行政区划
     *
     * @param divisionId 主键
     * @return 行政区划
     */
    @Override
    public DivisionVo queryById(Long divisionId){
        return baseMapper.selectVoById(divisionId);
    }


    /**
     * 查询符合条件的行政区划列表
     *
     * @param bo 查询条件
     * @return 行政区划列表
     */
    @Override
    public List<DivisionVo> queryList(DivisionBo bo) {
        LambdaQueryWrapper<Division> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Division> buildQueryWrapper(DivisionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Division> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Division::getDivisionId);
        lqw.eq(StringUtils.isNotBlank(bo.getDivisionCode()), Division::getDivisionCode, bo.getDivisionCode());
        lqw.like(StringUtils.isNotBlank(bo.getDivisionName()), Division::getDivisionName, bo.getDivisionName());
        lqw.eq(StringUtils.isNotBlank(bo.getLevel()), Division::getLevel, bo.getLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getParentCode()), Division::getParentCode, bo.getParentCode());
        lqw.orderByAsc(Division::getDivisionId);
        return lqw;
    }

    /**
     * 新增行政区划
     *
     * @param bo 行政区划
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(DivisionBo bo) {
        bo.setDelFlag("0");
        Division add = MapstructUtils.convert(bo, Division.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setDivisionId(add.getDivisionId());
        }
        return flag;
    }

    /**
     * 修改行政区划
     *
     * @param bo 行政区划
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(DivisionBo bo) {
        Division update = MapstructUtils.convert(bo, Division.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Division entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除行政区划信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {

        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


    @Override
    public List<DivisionVo> getTreeList(DivisionBo bo) {
        List<DivisionVo> nodeList = queryList(bo);
        if (nodeList == null || nodeList.isEmpty()) {
            return Collections.emptyList();
        }

        // 使用 Map 提高查找效率
        Map<String, DivisionVo> nodeMap = new HashMap<>();
        for (DivisionVo node : nodeList) {
            nodeMap.put(node.getDivisionCode(), node);
        }

        List<DivisionVo> rootNodes = new ArrayList<>();

        for (DivisionVo node : nodeList) {
            // 只保留 level=1 的作为根节点
            if ( "1".equals(node.getLevel())) {
                rootNodes.add(node);
            } else {
                // 查找父节点
                DivisionVo parentNode = nodeMap.get(node.getDivisionCode());
                if (parentNode != null) {
                    if (parentNode.getChildren() == null) {
                        parentNode.setChildren(new ArrayList<>());
                    }
                    parentNode.getChildren().add(node);
                }
            }
        }

        return rootNodes;
    }

}

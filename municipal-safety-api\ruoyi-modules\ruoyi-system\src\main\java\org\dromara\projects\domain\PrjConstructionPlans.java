package org.dromara.projects.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * [项目管理] 存储危大工程专项施工方案信息及其审批状态对象 prj_construction_plans
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prj_construction_plans")
public class PrjConstructionPlans extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 方案ID (主键)
     */
    @TableId(value = "plan_id")
    private Long planId;

    /**
     * 危大工程项ID
     */
    private Long itemId;

    /**
     * 专项施工方案名称
     */
    private String planName;

    /**
     * 方案版本号
     */
    private String planVersion;

    /**
     * 专项施工方案电子版文件ID
     */
    private Long planDocumentId;

    /**
     * 专项施工方案审批表文件ID
     */
    private Long approvalFormDocId;

    /**
     * AI校验状态
     */
    private String reviewStatus;

    /**
     * AI对比方案缺陷预警提示内容
     */
    private String aiDefectWarningDetails;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;
}

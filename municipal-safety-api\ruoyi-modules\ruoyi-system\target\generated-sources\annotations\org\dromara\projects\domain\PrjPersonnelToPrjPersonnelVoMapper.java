package org.dromara.projects.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.attendance.domain.MAttSnToMAttSnVoMapper;
import org.dromara.attendance.domain.vo.MAttSnVoToMAttSnMapper;
import org.dromara.projects.domain.bo.PrjPersonnelBoToPrjPersonnelMapper;
import org.dromara.projects.domain.vo.PrjPersonnelVo;
import org.dromara.projects.domain.vo.PrjPersonnelVoToPrjPersonnelMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {MAttSnVoToMAttSnMapper.class,MAttSnToMAttSnVoMapper.class,PrjPersonnelBoToPrjPersonnelMapper.class,PrjPersonnelVoToPrjPersonnelMapper.class},
    imports = {}
)
public interface PrjPersonnelToPrjPersonnelVoMapper extends BaseMapper<PrjPersonnel, PrjPersonnelVo> {
}

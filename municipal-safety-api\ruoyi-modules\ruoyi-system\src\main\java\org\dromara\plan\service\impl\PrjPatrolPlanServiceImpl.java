package org.dromara.plan.service.impl;

import org.dromara.ai.domain.AiHazAnalysisTasks;
import org.dromara.ai.mapper.AiHazAnalysisTasksMapper;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.expert.domain.vo.ExpertVo;
import org.dromara.expert.mapper.ExpertMapper;
import org.dromara.plan.domain.PrjPatrolPlan;
import org.dromara.plan.domain.bo.PrjPatrolPlanBo;
import org.dromara.plan.domain.vo.PrjPatrolPlanVo;
import org.dromara.plan.mapper.PrjPatrolPlanMapper;
import org.dromara.plan.service.IPrjPatrolPlanService;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.service.ISysUserService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 巡检计划Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-06-18
 */
@RequiredArgsConstructor
@Service
public class PrjPatrolPlanServiceImpl implements IPrjPatrolPlanService {

    private final PrjPatrolPlanMapper baseMapper;



    /**
     * 查询巡检计划
     *
     * @param planId 主键
     * @return 巡检计划
     */
    @Override
    public PrjPatrolPlanVo queryById(Long planId){
        return baseMapper.selectVoById(planId);
    }

    /**
     * 分页查询巡检计划列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 巡检计划分页列表
     */
    @Override
    public TableDataInfo<PrjPatrolPlanVo> queryPageList(PrjPatrolPlanBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PrjPatrolPlan> lqw = buildQueryWrapper(bo);
        Page<PrjPatrolPlanVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的巡检计划列表
     *
     * @param bo 查询条件
     * @return 巡检计划列表
     */
    @Override
    public List<PrjPatrolPlanVo> queryList(PrjPatrolPlanBo bo) {
        LambdaQueryWrapper<PrjPatrolPlan> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PrjPatrolPlan> buildQueryWrapper(PrjPatrolPlanBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PrjPatrolPlan> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBeginTime() != null, PrjPatrolPlan::getBeginTime, bo.getBeginTime());
        lqw.eq(bo.getEndTime() != null, PrjPatrolPlan::getEndTime, bo.getEndTime());
        lqw.like(StringUtils.isNotBlank(bo.getDeptIds()), PrjPatrolPlan::getDeptIds, bo.getDeptIds());
        lqw.eq(StringUtils.isNotBlank(bo.getProjectIds()), PrjPatrolPlan::getProjectIds, bo.getProjectIds());
        lqw.eq(StringUtils.isNotBlank(bo.getRemarks()), PrjPatrolPlan::getRemarks, bo.getRemarks());
        lqw.like(StringUtils.isNotBlank(bo.getExpertIds()), PrjPatrolPlan::getExpertIds, bo.getExpertIds());
        lqw.orderByDesc(PrjPatrolPlan::getCreateTime);
        // 专家查询
        lqw.like(bo.getExpert() != null, PrjPatrolPlan::getExpertIds, bo.getExpert());
        // 机构查询
        lqw.like(bo.getDeptId() != null, PrjPatrolPlan::getDeptIds, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增巡检计划
     *
     * @param bo 巡检计划
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PrjPatrolPlanBo bo) {
        PrjPatrolPlan add = MapstructUtils.convert(bo, PrjPatrolPlan.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setPlanId(add.getPlanId());
        }
        return flag;
    }

    /**
     * 修改巡检计划
     *
     * @param bo 巡检计划
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PrjPatrolPlanBo bo) {
        PrjPatrolPlan update = MapstructUtils.convert(bo, PrjPatrolPlan.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PrjPatrolPlan entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除巡检计划信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

}

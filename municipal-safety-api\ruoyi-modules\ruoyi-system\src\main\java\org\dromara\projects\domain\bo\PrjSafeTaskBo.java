package org.dromara.projects.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.projects.domain.PrjSafeTask;

import java.util.Date;


/**
 * 【项目管理】安拆任务业务对象 prj_safe_task
 *
 * <AUTHOR> Li
 * @date 2025-08-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjSafeTask.class, reverseConvertGenerate = false)
public class PrjSafeTaskBo extends BaseEntity {

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String projectName;

    /**
     * 项目地址
     */
    private String projectAddress;

    /**
     * 项目经度
     */
    private String projectLongitude;

    /**
     * 项目维度
     */
    private String projectLatitude;

    /**
     * 塔机现场编号
     */
    @NotBlank(message = "塔机现场编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String projectCraneNum;

    /**
     * 塔机类型
     * 平臂:FlatArm
     * 动臂:SwingArm
     * 塔头:Tower
     * 缺少该字段默认传：
     * FlatArm
     */
    @NotBlank(message = "塔机类型 平臂:FlatArm 动臂:SwingArm 塔头:Tower 缺少该字段默认传： FlatArm不能为空", groups = {AddGroup.class, EditGroup.class})
    private String craneType;

    /**
     * 塔机规格型号
     */
    @NotBlank(message = "塔机规格型号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String craneModel;

    /**
     * 塔机出厂编号
     */
    @NotBlank(message = "塔机出厂编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String craneSn;

    /**
     * 塔机出厂日期
     */
    @NotNull(message = "塔机出厂日期不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date craneProductionDate;

    /**
     * 产权单位名称
     */
    @NotBlank(message = "产权单位名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String propertyCompanyName;

    /**
     * 塔机生产厂商名称
     */
    @NotBlank(message = "塔机生产厂商名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String factoryName;

    /**
     * 顶升降节作业类型
     * 顶升:JackingUp
     * 降节:Disassembly
     */
    @NotBlank(message = "顶升降节作业类型 顶升:JackingUp 降节:Disassembly不能为空", groups = {AddGroup.class, EditGroup.class})
    private String jackingType;

    /**
     * 执行日期
     */
    @NotNull(message = "执行日期不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date executioinDate;

    /**
     * 升/降节数
     */
    private Long sectionNum;

    /**
     * 加降节后
     * 塔机高度
     */
    private Double modifiedCraneHeight;

    /**
     * 加降节前
     * 塔机高度
     */
    private Double initialCraneHeight;

    /**
     * 安拆单位名称
     */
    @NotBlank(message = "安拆单位名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String installationUnitName;

    /**
     * 安拆单位
     * 资质地址
     */
    private String installationUnitQualification;

    /**
     * 安全生产
     * 许可证地
     * 址
     */
    private String safetyProductionPermit;

    /**
     * 安拆任务状态
     */
    private String status;
}

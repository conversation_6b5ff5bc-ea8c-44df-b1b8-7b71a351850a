package org.dromara.attendance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.attendance.domain.MAttRule;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 考勤规则视图对象 m_att_rule
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MAttRule.class)
public class MAttRuleVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 规则类型（0：模板规则，1：普通规则）
     */
    @ExcelProperty(value = "规则类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=：模板规则，1：普通规则")
    private Long ruleType;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID")
    private Long projectId;

    /**
     * 人员类型
     */
    @ExcelProperty(value = "人员类型")
    private String personType;

    /**
     * 是否全部人员通用
     */
    private Integer isAll;

    /**
     * 设置打卡时间（支持多时段，如午休分段）。
     */
    @ExcelProperty(value = "设置打卡时间", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "支=持多时段，如午休分段")
    private String checkTime;

    /**
     * 弹性时间：允许迟到/早退的宽限时间（如上班后30分钟内打卡不算迟到）
     */
    @ExcelProperty(value = "弹性时间：允许迟到/早退的宽限时间", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=上班后30分钟内打卡不算迟到")
    private Long elasticTime;

    /**
     * 预警机制：根据对应的漏卡次数设置（黄色、橙色、红色）
     */
    @ExcelProperty(value = "预警机制：根据对应的漏卡次数设置", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "黄=色、橙色、红色")
    private String warning;

    /**
     * 外勤打卡：0：关，1：开
     */
    @ExcelProperty(value = "外勤打卡：0：关，1：开")
    private Long fieldCheck;

    /**
     * 自定义内容
     */
    @ExcelProperty(value = "自定义内容")
    private String content;

    /**
     * 人员类型名称
     */
    private List<String> personTypeName;

    private String projectName;

    // 解析打卡时段
    public List<PunchTimeSlot> getPunchTimeSlots() {
        List<PunchTimeSlot> slots = new ArrayList<>();
        String[] parts = checkTime.split("startTime\\*");

        for (int i = 1; i < parts.length; i++) {
            String part = parts[i];
            int numEnd = part.indexOf("'");
            int num = Integer.parseInt(part.substring(0, numEnd));
            String time = part.substring(numEnd + 1, part.indexOf("'", numEnd + 1));
            slots.add(new PunchTimeSlot(num, time));
        }
        return slots;
    }

    // 打卡时段内部类
    public static class PunchTimeSlot {
        private int punchNumber; // 打卡次数编号(对应which_time)
        private String time;     // 时间

        public PunchTimeSlot(int punchNumber, String time) {
            this.punchNumber = punchNumber;
            this.time = time;
        }

        // getters

        public int getPunchNumber() {
            return punchNumber;
        }

        public void setPunchNumber(int punchNumber) {
            this.punchNumber = punchNumber;
        }

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }
    }

}

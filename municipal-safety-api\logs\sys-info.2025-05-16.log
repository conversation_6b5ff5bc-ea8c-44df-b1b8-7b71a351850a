2025-05-16 08:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-05-16 08:34:55 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-05-16 08:34:55 [XNIO-1 task-7] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJlaGhJeFY1WWdXVmNTekxFa3BxNHUwbmtKSklWWHNvNCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9._MhXuud5rBzTnPuZyUmlJJcya71zLYrHVkpyWQTxccw
2025-05-16 08:34:55 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[4]毫秒
2025-05-16 08:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[7]毫秒
2025-05-16 08:34:55 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJlaGhJeFY1WWdXVmNTekxFa3BxNHUwbmtKSklWWHNvNCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9._MhXuud5rBzTnPuZyUmlJJcya71zLYrHVkpyWQTxccw"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-16 08:34:55 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[13]毫秒
2025-05-16 08:34:55 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-16 08:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-16 08:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-05-16 08:34:55 [XNIO-1 task-7] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-05-16 08:34:55 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[20]毫秒
2025-05-16 08:34:58 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":true,"uuid":"7d5ed13da73148299e34970c17ea4aaa","code":"3248","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-05-16 08:34:59 [schedule-pool-6] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-05-16 08:34:59 [XNIO-1 task-7] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJSdjVXUUR4OHQ0bG9iR0g4RVZtN25wNDM1N1IyU0VvTyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.C7JlmflmWlbPCu7fTh9yQS1qzHs2Yh5OOSOK3pJd6sk
2025-05-16 08:35:00 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1018]毫秒
2025-05-16 08:35:00 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 08:35:00 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[52]毫秒
2025-05-16 08:35:00 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 08:35:00 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[110]毫秒
2025-05-16 08:35:01 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/prj_projects_status],无参数
2025-05-16 08:35:01 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/educational_level_code],无参数
2025-05-16 08:35:01 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-05-16 08:35:01 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/educational_level_code],耗时:[0]毫秒
2025-05-16 08:35:01 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/prj_projects_status],耗时:[0]毫秒
2025-05-16 08:35:01 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[0]毫秒
2025-05-16 08:35:01 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/certificate_level],无参数
2025-05-16 08:35:01 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_dept_type],无参数
2025-05-16 08:35:01 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/politics_status],无参数
2025-05-16 08:35:01 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/certificate_level],耗时:[0]毫秒
2025-05-16 08:35:01 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_dept_type],耗时:[0]毫秒
2025-05-16 08:35:01 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/politics_status],耗时:[0]毫秒
2025-05-16 08:35:01 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/enterprise_type],无参数
2025-05-16 08:35:01 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/personnel_position],无参数
2025-05-16 08:35:01 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-16 08:35:01 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-05-16 08:35:01 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/enterprise_type],耗时:[0]毫秒
2025-05-16 08:35:01 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/personnel_position],耗时:[0]毫秒
2025-05-16 08:35:01 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/area/tree],无参数
2025-05-16 08:35:01 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/list],参数类型[param],参数:[{"params[dateRange]":[""],"pageSize":["10"],"pageNum":["1"]}]
2025-05-16 08:35:01 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJSdjVXUUR4OHQ0bG9iR0g4RVZtN25wNDM1N1IyU0VvTyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.C7JlmflmWlbPCu7fTh9yQS1qzHs2Yh5OOSOK3pJd6sk"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-16 08:35:01 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/area/tree],耗时:[55]毫秒
2025-05-16 08:35:02 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/list],耗时:[464]毫秒
2025-05-16 08:35:05 [schedule-pool-2] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-16 08:35:05 [redisson-3-3] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-16 09:03:03 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-05-16 09:03:03 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-05-16 09:03:03 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[2]毫秒
2025-05-16 09:03:03 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[2]毫秒
2025-05-16 09:03:03 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-05-16 09:03:03 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[268]毫秒
2025-05-16 09:03:18 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:03:18 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[86]毫秒
2025-05-16 09:03:19 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:03:19 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[153]毫秒
2025-05-16 09:03:19 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-05-16 09:03:19 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-05-16 09:03:19 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[0]毫秒
2025-05-16 09:03:19 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[0]毫秒
2025-05-16 09:03:19 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-16 09:03:19 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-05-16 09:03:19 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-05-16 09:03:19 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJSdjVXUUR4OHQ0bG9iR0g4RVZtN25wNDM1N1IyU0VvTyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.C7JlmflmWlbPCu7fTh9yQS1qzHs2Yh5OOSOK3pJd6sk"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-16 09:03:19 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[321]毫秒
2025-05-16 09:03:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:03:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[67]毫秒
2025-05-16 09:03:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:03:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[48]毫秒
2025-05-16 09:03:21 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-05-16 09:03:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-05-16 09:03:21 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[0]毫秒
2025-05-16 09:03:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[0]毫秒
2025-05-16 09:03:21 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-16 09:03:21 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-05-16 09:03:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-05-16 09:03:21 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJSdjVXUUR4OHQ0bG9iR0g4RVZtN25wNDM1N1IyU0VvTyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.C7JlmflmWlbPCu7fTh9yQS1qzHs2Yh5OOSOK3pJd6sk"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-16 09:03:23 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[1932]毫秒
2025-05-16 09:03:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:03:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[155]毫秒
2025-05-16 09:03:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:03:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[97]毫秒
2025-05-16 09:03:31 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:03:31 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[64]毫秒
2025-05-16 09:03:31 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:03:31 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[43]毫秒
2025-05-16 09:04:33 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:04:33 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[75]毫秒
2025-05-16 09:04:33 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:04:33 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[533]毫秒
2025-05-16 09:09:15 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:09:15 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[79]毫秒
2025-05-16 09:09:15 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:09:16 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[201]毫秒
2025-05-16 09:09:27 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:09:27 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[78]毫秒
2025-05-16 09:09:27 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:09:28 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[1072]毫秒
2025-05-16 09:09:29 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:09:29 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[47]毫秒
2025-05-16 09:09:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:09:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[399]毫秒
2025-05-16 09:09:55 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-05-16 09:09:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-05-16 09:09:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[1]毫秒
2025-05-16 09:09:55 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[1]毫秒
2025-05-16 09:09:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-16 09:09:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-05-16 09:09:55 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-05-16 09:09:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJSdjVXUUR4OHQ0bG9iR0g4RVZtN25wNDM1N1IyU0VvTyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.C7JlmflmWlbPCu7fTh9yQS1qzHs2Yh5OOSOK3pJd6sk"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-16 09:09:57 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[2131]毫秒
2025-05-16 09:09:59 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:09:59 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 09:10:02 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:10:03 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[375]毫秒
2025-05-16 09:10:03 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["632800"]}]
2025-05-16 09:10:03 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[27]毫秒
2025-05-16 09:10:04 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["632801"]}]
2025-05-16 09:10:04 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[49]毫秒
2025-05-16 09:10:07 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:10:08 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[553]毫秒
2025-05-16 09:11:45 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:11:45 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[52]毫秒
2025-05-16 09:11:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:11:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[78]毫秒
2025-05-16 09:11:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:11:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[262]毫秒
2025-05-16 09:11:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:11:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[27]毫秒
2025-05-16 09:12:01 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:12:01 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[74]毫秒
2025-05-16 09:12:01 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:12:01 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[100]毫秒
2025-05-16 09:12:01 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:12:01 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[31]毫秒
2025-05-16 09:12:13 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:12:13 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[376]毫秒
2025-05-16 09:12:41 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:12:41 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[52]毫秒
2025-05-16 09:12:44 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:12:44 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[383]毫秒
2025-05-16 09:12:49 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:12:49 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[78]毫秒
2025-05-16 09:12:49 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:12:50 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[608]毫秒
2025-05-16 09:12:50 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:12:50 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[37]毫秒
2025-05-16 09:12:52 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:12:52 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[45]毫秒
2025-05-16 09:14:26 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:14:26 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[54]毫秒
2025-05-16 09:14:30 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:14:30 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 09:14:30 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:14:30 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 09:14:34 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:14:34 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[77]毫秒
2025-05-16 09:14:34 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:14:34 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[99]毫秒
2025-05-16 09:14:34 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:14:34 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[28]毫秒
2025-05-16 09:14:36 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:14:36 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 09:15:27 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:15:27 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[66]毫秒
2025-05-16 09:15:29 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:15:29 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[191]毫秒
2025-05-16 09:15:29 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:15:29 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[207]毫秒
2025-05-16 09:15:30 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:15:30 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 09:15:30 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:15:30 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 09:15:31 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:15:32 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[534]毫秒
2025-05-16 09:15:32 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:15:32 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[23]毫秒
2025-05-16 09:15:37 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:15:37 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[64]毫秒
2025-05-16 09:15:37 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:15:37 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[44]毫秒
2025-05-16 09:15:37 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:15:37 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 09:15:39 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:15:39 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 09:17:38 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:17:38 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[65]毫秒
2025-05-16 09:17:42 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:17:42 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[76]毫秒
2025-05-16 09:17:42 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:17:42 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[261]毫秒
2025-05-16 09:17:43 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:17:43 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[28]毫秒
2025-05-16 09:17:44 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["510000"]}]
2025-05-16 09:17:44 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 09:19:02 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:19:02 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[54]毫秒
2025-05-16 09:19:41 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:19:41 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 09:19:52 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:19:52 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[78]毫秒
2025-05-16 09:19:52 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:19:52 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[99]毫秒
2025-05-16 09:19:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:19:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[23]毫秒
2025-05-16 09:19:54 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:19:54 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[45]毫秒
2025-05-16 09:20:55 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:20:55 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[53]毫秒
2025-05-16 09:20:59 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:20:59 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[663]毫秒
2025-05-16 09:20:59 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:21:00 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[930]毫秒
2025-05-16 09:21:01 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:21:01 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[28]毫秒
2025-05-16 09:21:02 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:21:02 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[52]毫秒
2025-05-16 09:21:34 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:21:34 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[53]毫秒
2025-05-16 09:23:24 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:23:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[409]毫秒
2025-05-16 09:23:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:23:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[101]毫秒
2025-05-16 09:23:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:23:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[29]毫秒
2025-05-16 09:23:26 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 09:23:26 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[44]毫秒
2025-05-16 09:25:33 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:25:33 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 09:25:38 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:25:38 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[326]毫秒
2025-05-16 09:25:38 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:25:38 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[51]毫秒
2025-05-16 09:25:38 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:25:38 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 09:25:39 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 09:25:40 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[620]毫秒
2025-05-16 09:25:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 09:25:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 09:26:10 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:26:10 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 09:26:14 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:26:14 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[190]毫秒
2025-05-16 09:26:14 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:26:15 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[475]毫秒
2025-05-16 09:26:15 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:26:15 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[28]毫秒
2025-05-16 09:26:16 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 09:26:16 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[52]毫秒
2025-05-16 09:26:51 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:26:51 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[57]毫秒
2025-05-16 09:27:02 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:27:02 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 09:27:04 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:27:04 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[76]毫秒
2025-05-16 09:27:04 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:27:04 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[76]毫秒
2025-05-16 09:27:05 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:27:05 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 09:27:06 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 09:27:06 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 09:27:45 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:27:45 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[298]毫秒
2025-05-16 09:27:48 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:27:48 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 09:27:52 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:27:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[293]毫秒
2025-05-16 09:28:04 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:28:04 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[65]毫秒
2025-05-16 09:28:09 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:28:09 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[76]毫秒
2025-05-16 09:28:09 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:28:09 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[98]毫秒
2025-05-16 09:28:09 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:28:09 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 09:28:10 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 09:28:10 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 09:28:34 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:28:34 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 09:30:07 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:30:07 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 09:30:11 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:30:12 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 09:30:20 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:30:20 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[63]毫秒
2025-05-16 09:30:20 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:30:20 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[62]毫秒
2025-05-16 09:30:20 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:30:20 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 09:30:47 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:30:47 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[74]毫秒
2025-05-16 09:30:47 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:30:47 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[99]毫秒
2025-05-16 09:30:48 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:30:48 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 09:30:50 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 09:30:50 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 09:30:51 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["632800"]}]
2025-05-16 09:30:51 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[303]毫秒
2025-05-16 09:30:51 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["632801"]}]
2025-05-16 09:30:51 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 09:30:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:30:53 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 09:30:54 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 09:30:54 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 09:30:57 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620100"]}]
2025-05-16 09:30:57 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 09:30:58 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620129"]}]
2025-05-16 09:30:58 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 09:31:03 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620100"]}]
2025-05-16 09:31:03 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[44]毫秒
2025-05-16 09:31:18 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620129"]}]
2025-05-16 09:31:18 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[314]毫秒
2025-05-16 09:31:20 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620100"]}]
2025-05-16 09:31:20 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[54]毫秒
2025-05-16 09:31:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620129"]}]
2025-05-16 09:31:21 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 09:31:22 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620100"]}]
2025-05-16 09:31:22 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[49]毫秒
2025-05-16 09:31:22 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620129"]}]
2025-05-16 09:31:22 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[25]毫秒
2025-05-16 09:31:23 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620100"]}]
2025-05-16 09:31:23 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 09:31:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620129"]}]
2025-05-16 09:31:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[44]毫秒
2025-05-16 09:31:26 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620100"]}]
2025-05-16 09:31:27 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[56]毫秒
2025-05-16 09:31:27 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620129"]}]
2025-05-16 09:31:27 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 09:31:28 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620100"]}]
2025-05-16 09:31:28 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[49]毫秒
2025-05-16 09:31:28 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620129"]}]
2025-05-16 09:31:28 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[24]毫秒
2025-05-16 09:31:29 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:31:29 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[49]毫秒
2025-05-16 09:34:27 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:34:27 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[398]毫秒
2025-05-16 09:34:27 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:34:27 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[85]毫秒
2025-05-16 09:34:27 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:34:27 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[23]毫秒
2025-05-16 09:34:32 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:34:33 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[75]毫秒
2025-05-16 09:34:33 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:34:33 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[489]毫秒
2025-05-16 09:34:33 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:34:33 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[27]毫秒
2025-05-16 09:44:05 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:44:06 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[66]毫秒
2025-05-16 09:44:06 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:44:06 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[64]毫秒
2025-05-16 09:44:06 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-05-16 09:44:06 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/prj_projects_status],无参数
2025-05-16 09:44:06 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/educational_level_code],无参数
2025-05-16 09:44:06 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/politics_status],无参数
2025-05-16 09:44:06 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[1]毫秒
2025-05-16 09:44:06 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/educational_level_code],耗时:[1]毫秒
2025-05-16 09:44:06 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/politics_status],耗时:[1]毫秒
2025-05-16 09:44:06 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/prj_projects_status],耗时:[1]毫秒
2025-05-16 09:44:06 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/certificate_level],无参数
2025-05-16 09:44:06 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_dept_type],无参数
2025-05-16 09:44:06 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/certificate_level],耗时:[1]毫秒
2025-05-16 09:44:06 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_dept_type],耗时:[1]毫秒
2025-05-16 09:44:07 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-16 09:44:07 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/personnel_position],无参数
2025-05-16 09:44:07 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/enterprise_type],无参数
2025-05-16 09:44:07 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-05-16 09:44:07 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/personnel_position],耗时:[0]毫秒
2025-05-16 09:44:07 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/enterprise_type],耗时:[0]毫秒
2025-05-16 09:44:07 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/area/tree],无参数
2025-05-16 09:44:07 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/list],参数类型[param],参数:[{"params[dateRange]":[""],"pageSize":["10"],"pageNum":["1"]}]
2025-05-16 09:44:07 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJSdjVXUUR4OHQ0bG9iR0g4RVZtN25wNDM1N1IyU0VvTyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.C7JlmflmWlbPCu7fTh9yQS1qzHs2Yh5OOSOK3pJd6sk"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-16 09:44:07 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/list],耗时:[83]毫秒
2025-05-16 09:44:07 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/area/tree],耗时:[96]毫秒
2025-05-16 09:44:40 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:44:40 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[64]毫秒
2025-05-16 09:44:40 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:44:40 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[43]毫秒
2025-05-16 09:44:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/prj_projects_status],无参数
2025-05-16 09:44:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/politics_status],无参数
2025-05-16 09:44:41 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/educational_level_code],无参数
2025-05-16 09:44:41 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-05-16 09:44:41 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/educational_level_code],耗时:[0]毫秒
2025-05-16 09:44:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/politics_status],耗时:[0]毫秒
2025-05-16 09:44:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/prj_projects_status],耗时:[0]毫秒
2025-05-16 09:44:41 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[0]毫秒
2025-05-16 09:44:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/certificate_level],无参数
2025-05-16 09:44:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_dept_type],无参数
2025-05-16 09:44:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/certificate_level],耗时:[0]毫秒
2025-05-16 09:44:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_dept_type],耗时:[0]毫秒
2025-05-16 09:44:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/personnel_position],无参数
2025-05-16 09:44:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/enterprise_type],无参数
2025-05-16 09:44:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/enterprise_type],耗时:[0]毫秒
2025-05-16 09:44:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/personnel_position],耗时:[0]毫秒
2025-05-16 09:44:41 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-16 09:44:41 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-05-16 09:44:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/list],参数类型[param],参数:[{"params[dateRange]":[""],"pageSize":["10"],"pageNum":["1"]}]
2025-05-16 09:44:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/area/tree],无参数
2025-05-16 09:44:41 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJSdjVXUUR4OHQ0bG9iR0g4RVZtN25wNDM1N1IyU0VvTyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.C7JlmflmWlbPCu7fTh9yQS1qzHs2Yh5OOSOK3pJd6sk"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-16 09:44:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/area/tree],耗时:[59]毫秒
2025-05-16 09:44:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/list],耗时:[67]毫秒
2025-05-16 09:44:46 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:44:46 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[63]毫秒
2025-05-16 09:44:46 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:44:46 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[42]毫秒
2025-05-16 09:44:46 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:44:46 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 09:44:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["610000"]}]
2025-05-16 09:44:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 09:46:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:46:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[64]毫秒
2025-05-16 09:46:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:46:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[62]毫秒
2025-05-16 09:46:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:46:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[22]毫秒
2025-05-16 09:47:53 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:47:53 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[64]毫秒
2025-05-16 09:47:53 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:47:53 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[46]毫秒
2025-05-16 09:47:54 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:47:54 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[22]毫秒
2025-05-16 09:49:30 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:49:30 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[63]毫秒
2025-05-16 09:49:30 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:49:30 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[45]毫秒
2025-05-16 09:49:30 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:49:30 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[22]毫秒
2025-05-16 09:49:59 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:49:59 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[63]毫秒
2025-05-16 09:49:59 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:49:59 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[58]毫秒
2025-05-16 09:50:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:50:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[66]毫秒
2025-05-16 09:50:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:50:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[51]毫秒
2025-05-16 09:50:52 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:50:52 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[44]毫秒
2025-05-16 09:52:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:52:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[64]毫秒
2025-05-16 09:52:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:52:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[42]毫秒
2025-05-16 09:52:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:52:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[22]毫秒
2025-05-16 09:52:47 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:52:47 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[63]毫秒
2025-05-16 09:52:47 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:52:47 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[42]毫秒
2025-05-16 09:52:48 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:52:48 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[261]毫秒
2025-05-16 09:53:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:53:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[65]毫秒
2025-05-16 09:53:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:53:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[44]毫秒
2025-05-16 09:53:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:53:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[23]毫秒
2025-05-16 09:53:30 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:53:30 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[67]毫秒
2025-05-16 09:53:30 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:53:30 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[45]毫秒
2025-05-16 09:53:31 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:53:31 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 09:54:46 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:54:46 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[63]毫秒
2025-05-16 09:54:46 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:54:46 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[44]毫秒
2025-05-16 09:54:46 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:54:46 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[22]毫秒
2025-05-16 09:55:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:55:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[65]毫秒
2025-05-16 09:55:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:55:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[48]毫秒
2025-05-16 09:55:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:55:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 09:56:18 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 09:56:18 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 09:56:19 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620100"]}]
2025-05-16 09:56:19 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 09:56:24 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:56:24 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[64]毫秒
2025-05-16 09:56:24 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:56:24 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[23]毫秒
2025-05-16 09:56:24 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:56:24 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[23]毫秒
2025-05-16 09:56:26 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 09:56:26 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 09:56:31 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:56:31 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[279]毫秒
2025-05-16 09:56:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:56:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[63]毫秒
2025-05-16 09:56:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:56:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[42]毫秒
2025-05-16 09:56:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:56:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[23]毫秒
2025-05-16 09:56:33 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 09:56:33 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 09:56:35 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620100"]}]
2025-05-16 09:56:35 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 09:56:42 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:56:42 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 09:56:50 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["650000"]}]
2025-05-16 09:56:50 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[44]毫秒
2025-05-16 09:56:55 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["652800"]}]
2025-05-16 09:56:55 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[44]毫秒
2025-05-16 09:56:59 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:56:59 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 09:57:02 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["460000"]}]
2025-05-16 09:57:02 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[45]毫秒
2025-05-16 09:57:19 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:57:19 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 09:58:48 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 09:58:48 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[48]毫秒
2025-05-16 09:58:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620100"]}]
2025-05-16 09:58:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 09:58:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620122"]}]
2025-05-16 09:58:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 09:58:54 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620100"]}]
2025-05-16 09:58:54 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[44]毫秒
2025-05-16 09:58:55 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620129"]}]
2025-05-16 09:58:55 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[44]毫秒
2025-05-16 09:59:15 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 09:59:15 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[65]毫秒
2025-05-16 09:59:15 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 09:59:15 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[45]毫秒
2025-05-16 09:59:15 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:59:15 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[25]毫秒
2025-05-16 09:59:20 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 09:59:20 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 09:59:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620600"]}]
2025-05-16 09:59:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[46]毫秒
2025-05-16 09:59:28 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:59:28 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 09:59:33 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 09:59:33 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 09:59:35 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620100"]}]
2025-05-16 09:59:35 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[44]毫秒
2025-05-16 09:59:42 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620102"]}]
2025-05-16 09:59:42 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 09:59:59 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 09:59:59 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 10:00:01 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:00:01 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 10:00:34 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:00:34 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 10:00:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:00:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[45]毫秒
2025-05-16 10:04:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:04:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[86]毫秒
2025-05-16 10:04:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:04:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[26]毫秒
2025-05-16 10:04:14 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:04:14 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 10:05:48 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:05:48 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[49]毫秒
2025-05-16 10:05:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:05:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[332]毫秒
2025-05-16 10:05:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:05:52 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[201]毫秒
2025-05-16 10:05:52 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:05:52 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[27]毫秒
2025-05-16 10:05:54 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:05:54 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[49]毫秒
2025-05-16 10:05:56 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:05:57 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[1069]毫秒
2025-05-16 10:05:58 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["640000"]}]
2025-05-16 10:05:58 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[284]毫秒
2025-05-16 10:06:15 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:06:15 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[49]毫秒
2025-05-16 10:06:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:06:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 10:06:18 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:06:18 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 10:06:19 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:06:19 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 10:06:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:06:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 10:07:28 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:07:28 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[412]毫秒
2025-05-16 10:07:28 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:07:28 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[99]毫秒
2025-05-16 10:07:29 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:07:29 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 10:07:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:07:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[87]毫秒
2025-05-16 10:07:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:07:37 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[509]毫秒
2025-05-16 10:07:37 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:07:37 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 10:07:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:07:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[75]毫秒
2025-05-16 10:07:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:07:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[73]毫秒
2025-05-16 10:07:52 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:07:52 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[27]毫秒
2025-05-16 10:07:53 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:07:53 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[293]毫秒
2025-05-16 10:07:59 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:07:59 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[303]毫秒
2025-05-16 10:08:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:08:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[48]毫秒
2025-05-16 10:08:23 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:08:23 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 10:08:42 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:08:42 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[330]毫秒
2025-05-16 10:08:42 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:08:42 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[108]毫秒
2025-05-16 10:08:43 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:08:43 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[25]毫秒
2025-05-16 10:08:44 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:08:44 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 10:10:04 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:10:04 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[67]毫秒
2025-05-16 10:10:04 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:10:04 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[64]毫秒
2025-05-16 10:10:05 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:10:05 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[74]毫秒
2025-05-16 10:10:05 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:10:05 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[159]毫秒
2025-05-16 10:10:16 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:10:16 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[82]毫秒
2025-05-16 10:10:16 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:10:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[460]毫秒
2025-05-16 10:10:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:10:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[52]毫秒
2025-05-16 10:10:19 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:10:19 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 10:10:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:10:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 10:10:37 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:10:37 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[323]毫秒
2025-05-16 10:10:37 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:10:37 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[238]毫秒
2025-05-16 10:10:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:10:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[23]毫秒
2025-05-16 10:10:39 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:10:39 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 10:10:40 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:10:40 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[49]毫秒
2025-05-16 10:11:02 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:11:02 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[76]毫秒
2025-05-16 10:11:02 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:11:02 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[82]毫秒
2025-05-16 10:11:02 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:11:02 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[27]毫秒
2025-05-16 10:11:03 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:11:03 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 10:11:05 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:11:05 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[49]毫秒
2025-05-16 10:11:39 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:11:39 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 10:12:20 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:12:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1288]毫秒
2025-05-16 10:12:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:12:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[430]毫秒
2025-05-16 10:12:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:12:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[27]毫秒
2025-05-16 10:12:23 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:12:23 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[48]毫秒
2025-05-16 10:12:31 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:12:31 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[327]毫秒
2025-05-16 10:12:31 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:12:31 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[98]毫秒
2025-05-16 10:12:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:12:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[269]毫秒
2025-05-16 10:12:35 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:12:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[73]毫秒
2025-05-16 10:12:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:12:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[73]毫秒
2025-05-16 10:12:37 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:12:37 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[284]毫秒
2025-05-16 10:12:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:12:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[52]毫秒
2025-05-16 10:13:10 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:13:10 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[74]毫秒
2025-05-16 10:13:10 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:13:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[165]毫秒
2025-05-16 10:13:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:13:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 10:13:14 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:13:14 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[49]毫秒
2025-05-16 10:14:20 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:14:20 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[76]毫秒
2025-05-16 10:14:20 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:14:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[235]毫秒
2025-05-16 10:14:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:14:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[27]毫秒
2025-05-16 10:14:26 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:14:26 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[48]毫秒
2025-05-16 10:15:33 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:15:33 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[68]毫秒
2025-05-16 10:15:33 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:15:33 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[66]毫秒
2025-05-16 10:15:34 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:15:34 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[29]毫秒
2025-05-16 10:15:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:15:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 10:16:12 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:16:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[655]毫秒
2025-05-16 10:16:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:16:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[97]毫秒
2025-05-16 10:16:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:16:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[25]毫秒
2025-05-16 10:16:14 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:16:14 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[44]毫秒
2025-05-16 10:16:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:16:50 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[960]毫秒
2025-05-16 10:16:50 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:16:50 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[244]毫秒
2025-05-16 10:16:50 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:16:50 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[277]毫秒
2025-05-16 10:16:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:16:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[44]毫秒
2025-05-16 10:18:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:18:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[75]毫秒
2025-05-16 10:18:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:18:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[80]毫秒
2025-05-16 10:18:43 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:18:43 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[73]毫秒
2025-05-16 10:18:43 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:18:43 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[486]毫秒
2025-05-16 10:18:44 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:18:44 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[72]毫秒
2025-05-16 10:18:44 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:18:44 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[79]毫秒
2025-05-16 10:18:44 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:18:44 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 10:18:45 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:18:46 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 10:19:10 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:19:10 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[72]毫秒
2025-05-16 10:19:10 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:19:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[75]毫秒
2025-05-16 10:19:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:19:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[30]毫秒
2025-05-16 10:19:12 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:19:12 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[294]毫秒
2025-05-16 10:19:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:19:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[73]毫秒
2025-05-16 10:19:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:19:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[62]毫秒
2025-05-16 10:19:37 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:19:37 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[25]毫秒
2025-05-16 10:19:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:19:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 10:20:05 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:20:05 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[74]毫秒
2025-05-16 10:20:05 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:20:05 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[145]毫秒
2025-05-16 10:20:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:20:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[78]毫秒
2025-05-16 10:20:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:20:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[74]毫秒
2025-05-16 10:20:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:20:14 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[535]毫秒
2025-05-16 10:20:14 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:20:14 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[25]毫秒
2025-05-16 10:20:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:20:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[465]毫秒
2025-05-16 10:20:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:20:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[202]毫秒
2025-05-16 10:20:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:20:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[27]毫秒
2025-05-16 10:20:24 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:20:24 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[49]毫秒
2025-05-16 10:20:42 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:20:42 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[75]毫秒
2025-05-16 10:20:42 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:20:42 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[365]毫秒
2025-05-16 10:20:43 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:20:43 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[27]毫秒
2025-05-16 10:20:44 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:20:44 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 10:22:12 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:22:12 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[206]毫秒
2025-05-16 10:22:12 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:22:12 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[99]毫秒
2025-05-16 10:22:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:22:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 10:22:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:22:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[79]毫秒
2025-05-16 10:22:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:22:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[80]毫秒
2025-05-16 10:22:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:22:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[38]毫秒
2025-05-16 10:22:35 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:22:35 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[45]毫秒
2025-05-16 10:24:56 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:24:56 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[74]毫秒
2025-05-16 10:24:56 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:24:56 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[214]毫秒
2025-05-16 10:24:56 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:24:56 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[22]毫秒
2025-05-16 10:24:58 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:24:59 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[293]毫秒
2025-05-16 10:25:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:25:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[63]毫秒
2025-05-16 10:25:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:25:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[256]毫秒
2025-05-16 10:25:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:25:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 10:25:15 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:25:15 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[303]毫秒
2025-05-16 10:25:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:25:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[330]毫秒
2025-05-16 10:25:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:25:36 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[96]毫秒
2025-05-16 10:25:37 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:25:37 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 10:25:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:25:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[48]毫秒
2025-05-16 10:26:30 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:26:30 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[328]毫秒
2025-05-16 10:26:30 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:26:31 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[512]毫秒
2025-05-16 10:26:31 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:26:31 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[26]毫秒
2025-05-16 10:26:33 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:26:33 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[298]毫秒
2025-05-16 10:26:43 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:26:43 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[71]毫秒
2025-05-16 10:26:43 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:26:43 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[338]毫秒
2025-05-16 10:26:44 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:26:44 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[273]毫秒
2025-05-16 10:26:45 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["710000"]}]
2025-05-16 10:26:45 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[47]毫秒
2025-05-16 10:27:10 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:27:10 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[197]毫秒
2025-05-16 10:27:10 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:27:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[97]毫秒
2025-05-16 10:27:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:27:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[25]毫秒
2025-05-16 10:27:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 10:27:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[51]毫秒
2025-05-16 10:27:15 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620600"]}]
2025-05-16 10:27:15 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[48]毫秒
2025-05-16 10:27:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:27:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[296]毫秒
2025-05-16 10:27:20 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 10:27:20 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[41]毫秒
2025-05-16 10:27:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620300"]}]
2025-05-16 10:27:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[41]毫秒
2025-05-16 10:31:54 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:31:55 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[58]毫秒
2025-05-16 10:31:55 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 10:31:56 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[391]毫秒
2025-05-16 10:35:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:35:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[64]毫秒
2025-05-16 10:35:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:35:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[67]毫秒
2025-05-16 10:35:50 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:35:50 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[22]毫秒
2025-05-16 10:35:53 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:35:53 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[62]毫秒
2025-05-16 10:35:53 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:35:53 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[45]毫秒
2025-05-16 10:35:54 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:35:54 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[22]毫秒
2025-05-16 10:35:58 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620000"]}]
2025-05-16 10:35:58 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 10:36:05 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["620900"]}]
2025-05-16 10:36:05 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 10:36:16 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:36:16 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 10:36:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:36:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[47]毫秒
2025-05-16 10:36:27 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["152500"]}]
2025-05-16 10:36:27 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[44]毫秒
2025-05-16 10:36:28 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:36:28 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 10:36:29 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:36:29 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[22]毫秒
2025-05-16 10:36:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:36:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 10:36:43 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:36:43 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[45]毫秒
2025-05-16 10:36:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:36:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 10:36:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:36:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[22]毫秒
2025-05-16 10:36:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:36:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 10:36:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["650000"]}]
2025-05-16 10:36:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[45]毫秒
2025-05-16 10:36:53 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:36:53 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 10:36:53 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:36:53 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[22]毫秒
2025-05-16 10:36:56 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:36:56 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[42]毫秒
2025-05-16 10:36:56 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:36:56 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[23]毫秒
2025-05-16 10:37:04 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:37:04 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 10:37:06 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:37:06 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[44]毫秒
2025-05-16 10:37:28 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:37:28 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 10:37:31 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["650000"]}]
2025-05-16 10:37:31 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 10:40:16 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:40:16 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[67]毫秒
2025-05-16 10:40:16 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:40:16 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[47]毫秒
2025-05-16 10:40:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:40:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[23]毫秒
2025-05-16 10:40:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:40:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[78]毫秒
2025-05-16 10:40:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:40:38 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[208]毫秒
2025-05-16 10:40:39 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:40:39 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[27]毫秒
2025-05-16 10:40:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:40:41 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[549]毫秒
2025-05-16 10:42:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:42:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[65]毫秒
2025-05-16 10:42:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:42:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[44]毫秒
2025-05-16 10:42:49 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:42:50 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[290]毫秒
2025-05-16 10:42:50 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:42:51 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[363]毫秒
2025-05-16 10:43:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:43:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[400]毫秒
2025-05-16 10:43:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:43:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[84]毫秒
2025-05-16 10:43:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:43:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[23]毫秒
2025-05-16 10:43:25 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:43:25 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[49]毫秒
2025-05-16 10:45:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:45:17 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[40]毫秒
2025-05-16 10:45:18 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:45:18 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[41]毫秒
2025-05-16 10:45:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["152500"]}]
2025-05-16 10:45:21 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[177]毫秒
2025-05-16 10:45:27 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:45:27 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[304]毫秒
2025-05-16 10:45:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:45:32 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[41]毫秒
2025-05-16 10:46:18 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:46:19 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[441]毫秒
2025-05-16 10:46:19 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:46:19 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[97]毫秒
2025-05-16 10:46:19 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:46:19 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[25]毫秒
2025-05-16 10:46:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:46:22 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[366]毫秒
2025-05-16 10:47:03 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:47:04 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[396]毫秒
2025-05-16 10:47:04 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:47:04 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[257]毫秒
2025-05-16 10:47:04 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:47:04 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[25]毫秒
2025-05-16 10:47:09 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:47:09 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[41]毫秒
2025-05-16 10:47:57 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:47:57 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[567]毫秒
2025-05-16 10:47:57 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:47:57 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[96]毫秒
2025-05-16 10:47:58 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:47:58 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[25]毫秒
2025-05-16 10:48:00 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:48:01 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[43]毫秒
2025-05-16 10:50:02 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:50:02 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[72]毫秒
2025-05-16 10:50:02 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:50:03 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[478]毫秒
2025-05-16 10:50:03 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:50:03 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[24]毫秒
2025-05-16 10:50:04 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["150000"]}]
2025-05-16 10:50:04 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[41]毫秒
2025-05-16 10:50:05 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:50:05 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[57]毫秒
2025-05-16 10:50:06 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 10:50:07 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[49]毫秒
2025-05-16 10:50:07 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 10:50:07 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[29]毫秒
2025-05-16 10:50:08 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:50:08 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[48]毫秒
2025-05-16 10:50:09 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["630000"]}]
2025-05-16 10:50:09 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[298]毫秒
2025-05-16 10:50:09 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:50:09 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 10:50:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["610000"]}]
2025-05-16 10:50:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[41]毫秒
2025-05-16 10:50:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:50:11 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[23]毫秒
2025-05-16 10:50:12 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["410000"]}]
2025-05-16 10:50:13 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[50]毫秒
2025-05-16 10:50:14 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:50:14 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[48]毫秒
2025-05-16 10:50:27 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-16 10:50:27 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[67]毫秒
2025-05-16 10:50:27 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-16 10:50:27 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[65]毫秒
2025-05-16 10:50:27 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/list],参数类型[param],参数:[{"parentCode":["100000"]}]
2025-05-16 10:50:27 [XNIO-1 task-7] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/list],耗时:[25]毫秒
2025-05-16 12:50:05 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-16 12:50:05 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-16 12:50:05 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-16 12:50:05 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-16 12:50:06 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-16 12:50:06 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-16 12:50:06 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-16 12:50:06 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye

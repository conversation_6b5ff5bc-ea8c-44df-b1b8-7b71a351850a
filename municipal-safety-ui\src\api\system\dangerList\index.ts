import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DangerListForm, DangerListQuery, DangerListVO } from '@/api/system/dangerList/types';

/**
 * 查询dangerList列表
 * @param query
 * @returns {*}
 */

export const listDangerList = (query?: DangerListQuery): AxiosPromise<DangerListVO[]> => {
  return request({
    url: '/system/dangerList/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询dangerList详细
 * @param dangerId
 */
export const getDangerList = (dangerId: string | number): AxiosPromise<DangerListVO> => {
  return request({
    url: '/system/dangerList/' + dangerId,
    method: 'get'
  });
};

/**
 * 新增dangerList
 * @param data
 */
export const addDangerList = (data: DangerListForm) => {
  return request({
    url: '/system/dangerList',
    method: 'post',
    data: data
  });
};

/**
 * 修改dangerList
 * @param data
 */
export const updateDangerList = (data: DangerListForm) => {
  return request({
    url: '/system/dangerList',
    method: 'put',
    data: data
  });
};

/**
 * 删除dangerList
 * @param dangerId
 */
export const delDangerList = (dangerId: string | number | Array<string | number>) => {
  return request({
    url: '/system/dangerList/' + dangerId,
    method: 'delete'
  });
};

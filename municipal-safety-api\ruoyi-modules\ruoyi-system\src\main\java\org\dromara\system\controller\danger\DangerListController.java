package org.dromara.system.controller.danger;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.lang.tree.Tree;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.TreeBuildUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.bo.DangerListBo;
import org.dromara.system.domain.vo.DangerListVo;
import org.dromara.system.service.IDangerListService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.List;

/**
 * dangerList
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/dangerList")
public class DangerListController extends BaseController {

    private final IDangerListService dangerListService;

    /**
     * Redis缓存键前缀
     */
    private static final String REDIS_CACHE_KEY_PREFIX = "danger:list:tree:";

    /**
     * 查询dangerList列表
     */
    @GetMapping("/list")
    public R<List<DangerListVo>> list(DangerListBo bo) {
        List<DangerListVo> list = dangerListService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 查询dangerList树形列表
     */
    @GetMapping("/tree_list")
    @SaIgnore
    public R<List<Tree<Long>>> treeList(DangerListBo bo) {
        // 使用固定缓存键
        String cacheKey = REDIS_CACHE_KEY_PREFIX + "all";

        // 尝试从Redis缓存获取
        List<Tree<Long>> treeList = RedisUtils.getCacheObject(cacheKey);
        if (treeList != null) {
            return R.ok(treeList);
        }

        // 缓存未命中，从数据库查询
        List<DangerListVo> list = dangerListService.queryList(bo);

        // 构建树形结构
        treeList = TreeBuildUtils.build(list, 0L, (dangerListVo, tree) -> {
            tree.setId(dangerListVo.getDangerId());
            tree.setParentId(dangerListVo.getPreId());
            tree.setName(dangerListVo.getName());
            tree.putExtra("type", dangerListVo.getType());
            tree.putExtra("remark", dangerListVo.getRemark());
        });

        // 存入Redis缓存
        RedisUtils.setCacheList(cacheKey, treeList);
        RedisUtils.setCacheObject(cacheKey, treeList, Duration.ofDays(30L));
        return R.ok(treeList);
    }

    /**
     * 导出dangerList列表
     */
    @SaCheckPermission("system:dangerList:export")
    @Log(title = "dangerList", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(DangerListBo bo, HttpServletResponse response) {
        List<DangerListVo> list = dangerListService.queryList(bo);
        ExcelUtil.exportExcel(list, "dangerList", DangerListVo.class, response);
    }

    /**
     * 获取dangerList详细信息
     *
     * @param dangerId 主键
     */
    @SaCheckPermission("system:dangerList:query")
    @GetMapping("/{dangerId}")
    public R<DangerListVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long dangerId) {
        return R.ok(dangerListService.queryById(dangerId));
    }

    /**
     * 新增dangerList
     */
    @SaCheckPermission("system:dangerList:add")
    @Log(title = "dangerList", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DangerListBo bo) {
        boolean result = dangerListService.insertByBo(bo);
        if (result) {
            // 清除缓存
            clearCache();
        }
        return toAjax(result);
    }

    /**
     * 修改dangerList
     */
    @SaCheckPermission("system:dangerList:edit")
    @Log(title = "dangerList", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DangerListBo bo) {
        boolean result = dangerListService.updateByBo(bo);
        if (result) {
            // 清除缓存
            clearCache();
        }
        return toAjax(result);
    }

    /**
     * 删除dangerList
     *
     * @param dangerIds 主键串
     */
    @SaCheckPermission("system:dangerList:remove")
    @Log(title = "dangerList", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dangerIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] dangerIds) {
        boolean result = dangerListService.deleteWithValidByIds(List.of(dangerIds), true);
        if (result) {
            // 清除缓存
            clearCache();
        }
        return toAjax(result);
    }

    /**
     * 清除危大工程缓存
     */
    private void clearCache() {
        // 删除所有以REDIS_CACHE_KEY_PREFIX开头的缓存
        RedisUtils.deleteKeys(REDIS_CACHE_KEY_PREFIX + "*");
    }
}

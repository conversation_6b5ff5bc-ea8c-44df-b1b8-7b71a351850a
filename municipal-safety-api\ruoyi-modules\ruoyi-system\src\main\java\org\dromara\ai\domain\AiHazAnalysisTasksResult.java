package org.dromara.ai.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 隐患AI分析结果对象 ai_haz_analysis_tasks_result
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_haz_analysis_tasks_result")
public class AiHazAnalysisTasksResult extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 隐患结果主键
     */
    @TableId(value = "result_id")
    private Long resultId;

    /**
     * 隐患任务id
     */
    private Long taskId;

    /**
     * 隐患描述
     */
    private String violation;

    /**
     * 涉及条款
     */
    private String regulation;

    /**
     * 位置坐标
     */
    private String coordinate;

    /**
     * 危险评估等级（2：重大隐患，1：一般隐患）
     */
    private String level;

    /**
     * 整改措施建议
     */
    private String measure;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

}

package org.dromara.facility.service.impl;

import com.alibaba.fastjson2.JSON;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.facility.domain.bo.LnSmokeBo;
import org.dromara.facility.domain.vo.LnSmokeVo;
import org.dromara.facility.domain.LnSmoke;
import org.dromara.facility.mapper.LnSmokeMapper;
import org.dromara.facility.service.ILnSmokeService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 绿能烟感Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@RequiredArgsConstructor
@Service
public class LnSmokeServiceImpl implements ILnSmokeService {

    private final LnSmokeMapper baseMapper;

    /**
     * 查询绿能烟感
     *
     * @param id 主键
     * @return 绿能烟感
     */
    @Override
    public LnSmokeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询绿能烟感列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能烟感分页列表
     */
    @Override
    public TableDataInfo<LnSmokeVo> queryPageList(LnSmokeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LnSmoke> lqw = buildQueryWrapper(bo);
        Page<LnSmokeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的绿能烟感列表
     *
     * @param bo 查询条件
     * @return 绿能烟感列表
     */
    @Override
    public List<LnSmokeVo> queryList(LnSmokeBo bo) {
        LambdaQueryWrapper<LnSmoke> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LnSmoke> buildQueryWrapper(LnSmokeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LnSmoke> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LnSmoke::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceNo()), LnSmoke::getDeviceNo, bo.getDeviceNo());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), LnSmoke::getDeviceName, bo.getDeviceName());
        lqw.eq(StringUtils.isNotBlank(bo.getEventCode()), LnSmoke::getEventCode, bo.getEventCode());
        lqw.eq(StringUtils.isNotBlank(bo.getEventContent()), LnSmoke::getEventContent, bo.getEventContent());
        lqw.eq(bo.getEventTime() != null, LnSmoke::getEventTime, bo.getEventTime());
        lqw.eq(StringUtils.isNotBlank(bo.getDevNo()), LnSmoke::getDevNo, bo.getDevNo());
        return lqw;
    }

    /**
     * 新增绿能烟感
     *
     * @param bo 绿能烟感
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LnSmokeBo bo) {
        LnSmoke add = MapstructUtils.convert(bo, LnSmoke.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public void insertByJson(String jsonString) {
        LnSmoke add = MapstructUtils.convert(JSON.parseObject(jsonString, LnSmokeBo.class), LnSmoke.class);
        assert add != null;
        add.setCreateTime(new Date());
        baseMapper.insert(add);
    }

    /**
     * 修改绿能烟感
     *
     * @param bo 绿能烟感
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LnSmokeBo bo) {
        LnSmoke update = MapstructUtils.convert(bo, LnSmoke.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LnSmoke entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除绿能烟感信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


}

package org.dromara.facility.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.LnEnergy;
import org.dromara.facility.domain.LnEnergyToLnEnergyVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {LnEnergyToLnEnergyVoMapper.class},
    imports = {}
)
public interface LnEnergyVoToLnEnergyMapper extends BaseMapper<LnEnergyVo, LnEnergy> {
}

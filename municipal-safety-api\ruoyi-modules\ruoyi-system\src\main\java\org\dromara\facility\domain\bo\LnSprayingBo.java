package org.dromara.facility.domain.bo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.facility.domain.LnSpraying;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.Date;

/**
 * 绿能喷淋设备业务对象 ln_spraying
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LnSpraying.class, reverseConvertGenerate = false)
public class LnSprayingBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 继电器状态
     */
    private String relayStatus;

    /**
     * 工作模式 0-自动 1-手动
     */
    private Long workMode;

    /**
     * 水位传感器 0-正常 1-下限位 2- 上限位 3-异常
     */
    private Long waterSensor;

    /**
     * 设备状态
     */
    private Long status;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

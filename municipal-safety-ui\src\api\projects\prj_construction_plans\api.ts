import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ConstructionPlansVO, ConstructionPlansForm, ConstructionPlansQuery } from '@/api/projects/prj_construction_plans/types';

/**
 * 查询[项目管理] 存储危大工程专项施工方案信息及其审批状态列表
 * @param query
 * @returns {*}
 */

export const listConstructionPlans = (query?: ConstructionPlansQuery): AxiosPromise<ConstructionPlansVO[]> => {
  return request({
    url: '/system/constructionPlans/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询[项目管理] 存储危大工程专项施工方案信息及其审批状态详细
 * @param planId
 */
export const getConstructionPlans = (planId: string | number): AxiosPromise<ConstructionPlansVO> => {
  return request({
    url: '/system/constructionPlans/' + planId,
    method: 'get'
  });
};

/**
 * 新增[项目管理] 存储危大工程专项施工方案信息及其审批状态
 * @param data
 */
export const addConstructionPlans = (data: ConstructionPlansForm) => {
  return request({
    url: '/system/constructionPlans',
    method: 'post',
    data: data
  });
};

/**
 * 修改[项目管理] 存储危大工程专项施工方案信息及其审批状态
 * @param data
 */
export const updateConstructionPlans = (data: ConstructionPlansForm) => {
  return request({
    url: '/system/constructionPlans',
    method: 'put',
    data: data
  });
};

/**
 * 删除[项目管理] 存储危大工程专项施工方案信息及其审批状态
 * @param planId
 */
export const delConstructionPlans = (planId: string | number | Array<string | number>) => {
  return request({
    url: '/system/constructionPlans/' + planId,
    method: 'delete'
  });
};
// 发起工单
export const startWorkOrder = (taskId: string, busId: string) => {
  return request({
    url: `/ai/ai_haz_analysis_tasks/pushWorkOrder/${taskId}/${busId}`,
    method: 'post'
  });
}

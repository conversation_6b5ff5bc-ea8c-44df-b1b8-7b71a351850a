package org.dromara.special.domain.vo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import org.dromara.common.translation.annotation.Translation;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.special.domain.SpecialOperationPersonnel;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 特种作业人员信息视图对象 special_operation_personnel
 *
 * <AUTHOR> Li
 * @date 2025-05-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SpecialOperationPersonnel.class)
public class SpecialOperationPersonnelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** ID */
    @ExcelProperty(value = "ID")
    private Long sopId;

    /** 证书编号 */
    @ExcelProperty(value = "证书编号")
    private String certificateNumber;

    /** 姓名 */
    @ExcelProperty(value = "姓名")
    private String name;

    /** 身份证号 */
    @ExcelProperty(value = "身份证号")
    private String idCard;

    /** 性别 */
    @ExcelProperty(value = "性别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_user_sex")
    private String gender;

    /** 出生日期 */
    @ExcelProperty(value = "出生日期")
    private Date birthdate;

    /** 操作类别 */
    @ExcelProperty(value = "操作类别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "special_operation_type")
    private String operationCategory;

    /** 发证机关 */
    @ExcelProperty(value = "发证机关")
    private String issuer;

    /** 初次领证日期 */
    @ExcelProperty(value = "初次领证日期")
    private Date firstIssueDate;

    /** 最近发证日期 */
    @ExcelProperty(value = "最近发证日期")
    private Date lastIssueDate;

    /** 有效期开始 */
    @ExcelProperty(value = "有效期开始")
    private Date validityStart;

    /** 有效期截止 */
    @ExcelProperty(value = "有效期截止")
    private Date validityEnd;

    /** * 证书状态(有效,无效,挂失,注销) */
    @ExcelProperty(value = "证书状态(有效,无效,挂失,注销)", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "special_operation_license_status")
    private String status;

    /** 电子证照链接 */
    @ExcelProperty(value = "电子证照链接")
    private String electronicLicenseUrl;

    /** 电子证照文件ID */
    @ExcelProperty(value = "电子证照文件ID")
    private Long electronicLicenseId;

    /** 电子证照文件IDUrl */
    @Translation(type = TransConstant.OSS_ID_TO_URL, mapper = "electronicLicenseId")
    private String electronicLicenseIdUrl;

    /** 项目ID */
    private Long projectId;

    /** 项目名称 */
    private String projectName;

    /** 删除标志 (0代表存在 1代表删除) */
    @TableLogic
    private String delFlag;

    /** 所属单位名称 */
    private String companyName;

    /** 单位统一社会信用代码 */
    private String companyCode;

    /**  单位的类别分类 */
    private String companyType;

    /**  人员类别 */
    private String personType;

}

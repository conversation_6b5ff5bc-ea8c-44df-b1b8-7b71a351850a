package org.dromara.monito.handle;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.annotation.Resource;
import org.dromara.monito.domain.DeviceMonito;
import org.dromara.monito.mapper.DeviceMonitoMapper;
import org.dromara.util.YsyClient;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @date 2025/5/20 11:04
 * @Description TODO
 * @Version 1.0
 */
@Component
@JobExecutor(name = "deviceStatusTask")
public class DeviceStatusTask {

    @Resource
    private YsyClient ysyClient;

    @Resource
    private DeviceMonitoMapper deviceMonitoMapper;

    public ExecuteResult jobExecute(JobArgs jobArgs) {
        List<String> deviceSerials = ysyClient.onLineDeviceSerials();

        System.out.println(deviceSerials);

        if (!deviceSerials.isEmpty()) {

            LambdaUpdateWrapper<DeviceMonito> updateStatus = Wrappers.lambdaUpdate();

            updateStatus.set(DeviceMonito::getDeviceStatus, "1")
                .eq(DeviceMonito::getDeviceStatus, "0")
                .in(DeviceMonito::getDeviceCode, deviceSerials);

            LambdaUpdateWrapper<DeviceMonito> updateNoStatus = Wrappers.lambdaUpdate();

            updateNoStatus.set(DeviceMonito::getDeviceStatus, "0")
                .eq(DeviceMonito::getDeviceStatus, "1")
                .notIn(DeviceMonito::getDeviceCode, deviceSerials);

            deviceMonitoMapper.update(updateStatus);
            deviceMonitoMapper.update(updateNoStatus);
        }

        return ExecuteResult.success("状态更新成功!");
    }
}

{"doc": " 字符串工具类\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "blankToDefault", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取参数不为空值\n\n @param str defaultValue 要判断的value\n @return value 返回值\n"}, {"name": "isEmpty", "paramTypes": ["java.lang.String"], "doc": " * 判断一个字符串是否为空串\n\n @param str String\n @return true：为空 false：非空\n"}, {"name": "isNotEmpty", "paramTypes": ["java.lang.String"], "doc": " * 判断一个字符串是否为非空串\n\n @param str String\n @return true：非空串 false：空串\n"}, {"name": "trim", "paramTypes": ["java.lang.String"], "doc": " 去空格\n"}, {"name": "substring", "paramTypes": ["java.lang.String", "int"], "doc": " 截取字符串\n\n @param str   字符串\n @param start 开始\n @return 结果\n"}, {"name": "substring", "paramTypes": ["java.lang.String", "int", "int"], "doc": " 截取字符串\n\n @param str   字符串\n @param start 开始\n @param end   结束\n @return 结果\n"}, {"name": "format", "paramTypes": ["java.lang.String", "java.lang.Object[]"], "doc": " 格式化文本, {} 表示占位符<br>\n 此方法只是简单将占位符 {} 按照顺序替换为参数<br>\n 如果想输出 {} 使用 \\\\转义 { 即可，如果想输出 {} 之前的 \\ 使用双转义符 \\\\\\\\ 即可<br>\n 例：<br>\n 通常使用：format(\"this is {} for {}\", \"a\", \"b\") -> this is a for b<br>\n 转义{}： format(\"this is \\\\{} for {}\", \"a\", \"b\") -> this is {} for a<br>\n 转义\\： format(\"this is \\\\\\\\{} for {}\", \"a\", \"b\") -> this is \\a for b<br>\n\n @param template 文本模板，被替换的部分用 {} 表示\n @param params   参数值\n @return 格式化后的文本\n"}, {"name": "ishttp", "paramTypes": ["java.lang.String"], "doc": " 是否为http(s)://开头\n\n @param link 链接\n @return 结果\n"}, {"name": "str2Set", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 字符串转set\n\n @param str 字符串\n @param sep 分隔符\n @return set集合\n"}, {"name": "str2List", "paramTypes": ["java.lang.String", "java.lang.String", "boolean", "boolean"], "doc": " 字符串转list\n\n @param str         字符串\n @param sep         分隔符\n @param filterBlank 过滤纯空白\n @param trim        去掉首尾空白\n @return list集合\n"}, {"name": "containsAnyIgnoreCase", "paramTypes": ["java.lang.CharSequence", "java.lang.CharSequence[]"], "doc": " 查找指定字符串是否包含指定字符串列表中的任意一个字符串同时串忽略大小写\n\n @param cs                  指定字符串\n @param searchCharSequences 需要检查的字符串数组\n @return 是否包含任意一个字符串\n"}, {"name": "toUnderScoreCase", "paramTypes": ["java.lang.String"], "doc": " 驼峰转下划线命名\n"}, {"name": "inStringIgnoreCase", "paramTypes": ["java.lang.String", "java.lang.String[]"], "doc": " 是否包含字符串\n\n @param str  验证字符串\n @param strs 字符串组\n @return 包含返回true\n"}, {"name": "convertToCamelCase", "paramTypes": ["java.lang.String"], "doc": " 将下划线大写方式命名的字符串转换为驼峰式。如果转换前的下划线大写方式命名的字符串为空，则返回空字符串。 例如：HELLO_WORLD->HelloWorld\n\n @param name 转换前的下划线大写方式命名的字符串\n @return 转换后的驼峰式命名的字符串\n"}, {"name": "toCamelCase", "paramTypes": ["java.lang.String"], "doc": " 驼峰式命名法 例如：user_name->userName\n"}, {"name": "matches", "paramTypes": ["java.lang.String", "java.util.List"], "doc": " 查找指定字符串是否匹配指定字符串列表中的任意一个字符串\n\n @param str  指定字符串\n @param strs 需要检查的字符串数组\n @return 是否匹配\n"}, {"name": "isMatch", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 判断url是否与规则配置:\n ? 表示单个字符;\n * 表示一层路径内的任意字符串，不可跨层级;\n ** 表示任意层路径;\n\n @param pattern 匹配规则\n @param url     需要匹配的url\n"}, {"name": "padl", "paramTypes": ["java.lang.Number", "int"], "doc": " 数字左边补齐0，使之达到指定长度。注意，如果数字转换为字符串后，长度大于size，则只保留 最后size个字符。\n\n @param num  数字对象\n @param size 字符串指定长度\n @return 返回数字的字符串格式，该字符串为指定长度。\n"}, {"name": "padl", "paramTypes": ["java.lang.String", "int", "char"], "doc": " 字符串左补齐。如果原始字符串s长度大于size，则只保留最后size个字符。\n\n @param s    原始字符串\n @param size 字符串指定长度\n @param c    用于补齐的字符\n @return 返回指定长度的字符串，由原字符串左补齐或截取得到。\n"}, {"name": "splitList", "paramTypes": ["java.lang.String"], "doc": " 切分字符串(分隔符默认逗号)\n\n @param str 被切分的字符串\n @return 分割后的数据列表\n"}, {"name": "splitList", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 切分字符串\n\n @param str       被切分的字符串\n @param separator 分隔符\n @return 分割后的数据列表\n"}, {"name": "splitTo", "paramTypes": ["java.lang.String", "java.util.function.Function"], "doc": " 切分字符串自定义转换(分隔符默认逗号)\n\n @param str    被切分的字符串\n @param mapper 自定义转换\n @return 分割后的数据列表\n"}, {"name": "splitTo", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.function.Function"], "doc": " 切分字符串自定义转换\n\n @param str       被切分的字符串\n @param separator 分隔符\n @param mapper    自定义转换\n @return 分割后的数据列表\n"}, {"name": "startWithAnyIgnoreCase", "paramTypes": ["java.lang.CharSequence", "java.lang.CharSequence[]"], "doc": " 不区分大小写检查 CharSequence 是否以指定的前缀开头。\n\n @param str     要检查的 CharSequence 可能为 null\n @param prefixs 要查找的前缀可能为 null\n @return 是否包含\n"}], "constructors": []}
{"doc": " 部门管理 服务实现\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPageDeptList", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询部门管理数据\n\n @param dept      部门信息\n @param pageQuery 分页对象\n @return 部门信息集合\n"}, {"name": "selectDeptList", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": " 查询部门管理数据\n\n @param dept 部门信息\n @return 部门信息集合\n"}, {"name": "selectDeptTreeList", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": " 查询部门树结构信息\n\n @param bo 部门信息\n @return 部门树信息集合\n"}, {"name": "buildDeptTreeSelect", "paramTypes": ["java.util.List"], "doc": " 构建前端所需要下拉树结构\n\n @param depts 部门列表\n @return 下拉树结构列表\n"}, {"name": "selectDeptListByRoleId", "paramTypes": ["java.lang.Long"], "doc": " 根据角色ID查询部门树信息\n\n @param roleId 角色ID\n @return 选中部门列表\n"}, {"name": "selectDeptById", "paramTypes": ["java.lang.Long"], "doc": " 根据部门ID查询信息\n\n @param deptId 部门ID\n @return 部门信息\n"}, {"name": "selectDeptNameByIds", "paramTypes": ["java.lang.String"], "doc": " 通过部门ID查询部门名称\n\n @param deptIds 部门ID串逗号分隔\n @return 部门名称串逗号分隔\n"}, {"name": "selectDeptLeaderById", "paramTypes": ["java.lang.Long"], "doc": " 根据部门ID查询部门负责人\n\n @param deptId 部门ID，用于指定需要查询的部门\n @return 返回该部门的负责人ID\n"}, {"name": "selectDeptsByList", "paramTypes": [], "doc": " 查询部门\n\n @return 部门列表\n"}, {"name": "selectNormalChildrenDeptById", "paramTypes": ["java.lang.Long"], "doc": " 根据ID查询所有子部门数（正常状态）\n\n @param deptId 部门ID\n @return 子部门数\n"}, {"name": "hasChildByDeptId", "paramTypes": ["java.lang.Long"], "doc": " 是否存在子节点\n\n @param deptId 部门ID\n @return 结果\n"}, {"name": "checkDeptExistUser", "paramTypes": ["java.lang.Long"], "doc": " 查询部门是否存在用户\n\n @param deptId 部门ID\n @return 结果 true 存在 false 不存在\n"}, {"name": "checkDeptNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": " 校验部门名称是否唯一\n\n @param dept 部门信息\n @return 结果\n"}, {"name": "checkDeptDataScope", "paramTypes": ["java.lang.Long"], "doc": " 校验部门是否有数据权限\n\n @param deptId 部门id\n"}, {"name": "insertDept", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": " 新增保存部门信息\n\n @param bo 部门信息\n @return 结果\n"}, {"name": "updateDept", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": " 修改保存部门信息\n\n @param bo 部门信息\n @return 结果\n"}, {"name": "updateParentDeptStatusNormal", "paramTypes": ["org.dromara.system.domain.SysDept"], "doc": " 修改该部门的父级部门状态\n\n @param dept 当前部门\n"}, {"name": "updateDept<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 修改子元素关系\n\n @param deptId       被修改的部门ID\n @param newAncestors 新的父ID集合\n @param oldAncestors 旧的父ID集合\n"}, {"name": "deleteDeptById", "paramTypes": ["java.lang.Long"], "doc": " 删除部门管理信息\n\n @param deptId 部门ID\n @return 结果\n"}], "constructors": []}
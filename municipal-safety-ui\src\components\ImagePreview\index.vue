<template>
  <el-image :src="`${realSrc}`" fit="cover" :style="`width:${realWidth};height:${realHeight};`"
    :preview-src-list="realSrcList" preview-teleported>
    <template #error>
      <div class="image-slot">
        <el-icon><picture-filled /></el-icon>
      </div>
    </template>
    <!-- 自定义预览图底部按钮 -->
    <template v-if="isShowToolbar" #toolbar="{ actions }">
      <el-icon @click="actions('zoomOut')">
        <ZoomOut />
      </el-icon>
      <el-icon @click="actions('zoomIn', { enableTransition: true, zoomRate: 2 })">
        <ZoomIn />
      </el-icon>
      <span style="font-size: 18px;cursor: pointer;" @click="handleScreenShotChange">标注</span>
      <span style="font-size: 18px;cursor: pointer;" @click="handleOriginalPhotoChange">{{ isOriginalPhoto ? 'AI图标注' :
        '原图标注' }}</span>
      <el-icon @click="
        actions('clockwise', { rotateDeg: 180, enableTransition: true })
        ">
        <RefreshRight />
      </el-icon>
      <el-icon @click="actions('anticlockwise')">
        <RefreshLeft />
      </el-icon>
    </template>
  </el-image>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes';
import { ElIcon } from 'element-plus'
import { ZoomOut, ZoomIn, RefreshRight, RefreshLeft } from '@element-plus/icons-vue'

const emit = defineEmits(['screenShotChange', 'OriginalPhotoChange']);
const props = defineProps({
  src: propTypes.string.def(''),
  width: {
    type: [Number, String],
    default: ''
  },
  height: {
    type: [Number, String],
    default: ''
  },
  isShowToolbar: {
    type: Boolean,
    default: false
  }
});

// AI图替换原图的判断标识
const isOriginalPhoto = ref<boolean>(false);

const realSrc = computed(() => {
  if (!props.src) {
    return;
  }
  let real_src = props.src.split(',')[0];
  return real_src;
});

const realSrcList = computed(() => {
  if (!props.src) {
    return [];
  }
  let real_src_list = props.src.split(',');
  let srcList: string[] = [];
  real_src_list.forEach((item: string) => {
    if (item.trim() === '') {
      return;
    }
    return srcList.push(item);
  });
  return srcList;
});

const realWidth = computed(() => (typeof props.width == 'string' ? props.width : `${props.width}px`));

const realHeight = computed(() => (typeof props.height == 'string' ? props.height : `${props.height}px`));

// 将原图替换成标注图的事件
const handleOriginalPhotoChange = () => {
  isOriginalPhoto.value = !isOriginalPhoto.value;
  emit('OriginalPhotoChange', isOriginalPhoto.value);
}
// 开启自定义截图的事件
const handleScreenShotChange = () => {
  emit('screenShotChange', true);
}
</script>

<style lang="scss" scoped>
.el-image {
  border-radius: 5px;
  background-color: #ebeef5;
  box-shadow: 0 0 5px 1px #ccc;

  :deep(.el-image__inner) {
    transition: all 0.3s;
    cursor: pointer;

    &:hover {
      transform: scale(1.2);
    }
  }

  :deep(.image-slot) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 30px;
  }
}
</style>

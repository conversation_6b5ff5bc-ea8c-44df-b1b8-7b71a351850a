package org.dromara.facility.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.LnSpraying;
import org.dromara.facility.domain.LnSprayingToLnSprayingVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {LnSprayingToLnSprayingVoMapper.class},
    imports = {}
)
public interface LnSprayingVoToLnSprayingMapper extends BaseMapper<LnSprayingVo, LnSpraying> {
}

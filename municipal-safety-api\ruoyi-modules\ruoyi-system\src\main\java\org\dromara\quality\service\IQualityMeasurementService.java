package org.dromara.quality.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.quality.domain.bo.QualityMeasurementBo;
import org.dromara.quality.domain.vo.QualityMeasurementVo;

import java.util.Collection;
import java.util.List;

/**
 * 实测实量Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IQualityMeasurementService {

    /**
     * 查询实测实量
     *
     * @param measurementId 测量ID
     * @return 实测实量
     */
    QualityMeasurementVo queryById(Long measurementId);

    /**
     * 查询实测实量列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 实测实量集合
     */
    TableDataInfo<QualityMeasurementVo> queryPageList(QualityMeasurementBo bo, PageQuery pageQuery);

    /**
     * 查询实测实量列表
     *
     * @param bo 查询条件
     * @return 实测实量集合
     */
    List<QualityMeasurementVo> queryList(QualityMeasurementBo bo);

    /**
     * 新增实测实量
     *
     * @param bo 实测实量
     * @return 结果
     */
    Boolean insertByBo(QualityMeasurementBo bo);

    /**
     * 修改实测实量
     *
     * @param bo 实测实量
     * @return 结果
     */
    Boolean updateByBo(QualityMeasurementBo bo);

    /**
     * 校验并批量删除实测实量信息
     *
     * @param ids     需要删除的实测实量主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 推送测量信息
     *
     * @param measurementIds 测量ID集合
     * @return 结果
     */
    Boolean pushMeasurementInfo(Collection<Long> measurementIds);

    /**
     * 标记隐患
     *
     * @param measurementId     测量ID
     * @param hazardDescription 隐患描述
     * @return 结果
     */
    Boolean markHazard(Long measurementId, String hazardDescription);

    /**
     * 取消标记隐患
     *
     * @param measurementId 测量ID
     * @return 结果
     */
    Boolean unmarkHazard(Long measurementId);

    /**
     * 根据设备ID获取设备信息并填充到测量记录
     *
     * @param deviceId 设备ID
     * @param bo       测量业务对象
     */
    void fillDeviceInfo(Long deviceId, QualityMeasurementBo bo);

} 
<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="realList">
      <el-table-column :width="commonWidth" label="设备编号" align="center" prop="devNo" v-if="columns[0].visible" />
      <el-table-column :width="commonWidth" label="塔机编号" align="center" prop="tcNo" v-if="columns[1].visible" />
      <el-table-column :width="commonWidth" label="时间" align="center" prop="addTime" width="180"
        v-if="columns[2].visible">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.addTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="厂家及设备类型" align="center" prop="lockMachineStatus"
        v-if="columns[3].visible" />
      <el-table-column label="高度" align="center" prop="height" v-if="columns[4].visible">
        <template v-slot="scope"> {{ scope.row.height }}米 </template>
      </el-table-column>
      <el-table-column label="幅度" align="center" prop="realRange" v-if="columns[5].visible">
        <template v-slot="scope"> {{ scope.row.realRange }}米 </template>
      </el-table-column>
      <el-table-column label="回转" align="center" prop="rotary" v-if="columns[6].visible">
        <template v-slot="scope"> {{ scope.row.rotary }}度 </template>
      </el-table-column>
      <el-table-column label="起始重量" align="center" prop="startWeight" v-if="columns[7].visible">
        <template v-slot="scope"> {{ scope.row.startWeight }}吨 </template>
      </el-table-column>
      <el-table-column label="风速数据" align="center" prop="windSpeed" v-if="columns[8].visible">
        <template v-slot="scope"> {{ scope.row.windSpeed }}米/秒 </template>
      </el-table-column>
      <el-table-column label="倾角数据" align="center" prop="slant" v-if="columns[9].visible">
        <template v-slot="scope"> {{ scope.row.slant }}度 </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="重量百分比" align="center" prop="weightPct" v-if="columns[10].visible">
        <template v-slot="scope"> {{ scope.row.weightPct }}% </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="力矩百分比" align="center" prop="mofPct" v-if="columns[11].visible">
        <template v-slot="scope"> {{ scope.row.mofPct }}% </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="风速百分比" align="center" prop="windSpeedPct" v-if="columns[12].visible">
        <template v-slot="scope"> {{ scope.row.windSpeedPct }}% </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="倾斜百分比" align="center" prop="slantPct" v-if="columns[13].visible">
        <template v-slot="scope"> {{ scope.row.slantPct }}% </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="报警原因" align="center" prop="alarmCause" v-if="columns[14].visible" />
      <el-table-column :width="commonWidth" label="报警原因中文" align="center" prop="alarmCauseZh"
        v-if="columns[15].visible" />
      <el-table-column :width="commonWidth" label="制动状态上" align="center" prop="brakingStatusUp"
        v-if="columns[16].visible" />
      <el-table-column :width="commonWidth" label="制动状态下" align="center" prop="brakingStatusDown"
        v-if="columns[17].visible" />
      <el-table-column :width="commonWidth" label="制动状态前" align="center" prop="brakingStatusFront"
        v-if="columns[18].visible" />
      <el-table-column :width="commonWidth" label="制动状态后" align="center" prop="brakingStatusBack"
        v-if="columns[19].visible" />
      <el-table-column :width="commonWidth" label="制动状态左" align="center" prop="brakingStatusLeft"
        v-if="columns[20].visible" />
      <el-table-column :width="commonWidth" label="制动状态右" align="center" prop="brakingStatusRight"
        v-if="columns[21].visible" />
      <el-table-column :width="commonWidth" label="工作状态" align="center" prop="workingStatus" v-if="columns[22].visible">
        <template v-slot="scope">
          <span v-if="scope.row.workingStatus == '1'">工作</span>
          <span v-if="scope.row.workingStatus == '2'">空闲</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script>
import { getTowerRealTimeData } from "@/api/special/equipment/index";
export default {
  name: "tower_real",
  props: {
    devNo: {
      type: String,
      default: "",
    }
  },
  data () {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 塔机实时数据表格数据
      realList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        devNo: ''
      },
      //隐藏列
      columns: [
        { key: 2, label: `设备编号`, visible: true },
        { key: 3, label: `塔机编号`, visible: true },
        { key: 4, label: `时间`, visible: true },
        { key: 5, label: `厂家及设备类型`, visible: true },
        { key: 6, label: `高度`, visible: true },
        { key: 7, label: `幅度`, visible: true },
        { key: 8, label: `回转`, visible: true },
        { key: 9, label: `起始重量`, visible: true },
        { key: 10, label: `风速数据`, visible: true },
        { key: 11, label: `倾角数据`, visible: true },
        { key: 12, label: `重量百分比`, visible: true },
        { key: 13, label: `力矩百分比`, visible: true },
        { key: 14, label: `风速百分比`, visible: true },
        { key: 15, label: `倾斜百分比`, visible: true },
        { key: 16, label: `报警原因`, visible: true },
        { key: 17, label: `报警原因中文`, visible: true },
        { key: 18, label: `制动状态上`, visible: true },
        { key: 19, label: `制动状态下`, visible: true },
        { key: 20, label: `制动状态前`, visible: true },
        { key: 21, label: `制动状态后`, visible: true },
        { key: 22, label: `制动状态左`, visible: true },
        { key: 23, label: `制动状态右`, visible: true },
        { key: 24, label: `工作状态 1工作 2空闲`, visible: true },
      ],
      commonWidth: "120",
      stype: 11,
    };
  },
  created () {
    this.getList();
  },
  methods: {
    SearchList (val) {
      console.log("SearchList", val);
      this.queryParams.tn = val;
      this.getList();
    },
    /** 查询塔机实时数据列表 */
    async getList () {
      this.loading = true;
      this.queryParams.devNo = this.devNo;
      const response = await getTowerRealTimeData(this.queryParams);
      this.realList = response.rows;
      this.total = response.total;
      this.loading = false;
    }
  }
};
</script>

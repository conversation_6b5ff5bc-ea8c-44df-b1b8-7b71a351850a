package org.dromara.facility.service;

import org.dromara.facility.domain.vo.LnHighFormworkRealVo;
import org.dromara.facility.domain.bo.LnHighFormworkRealBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 绿能高支模实时数据Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-25
 */
public interface ILnHighFormworkRealService extends BaseFacilityHandle {

    /**
     * 查询绿能高支模实时数据
     *
     * @param id 主键
     * @return 绿能高支模实时数据
     */
    LnHighFormworkRealVo queryById(Long id);

    /**
     * 分页查询绿能高支模实时数据列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能高支模实时数据分页列表
     */
    TableDataInfo<LnHighFormworkRealVo> queryPageList(LnHighFormworkRealBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的绿能高支模实时数据列表
     *
     * @param bo 查询条件
     * @return 绿能高支模实时数据列表
     */
    List<LnHighFormworkRealVo> queryList(LnHighFormworkRealBo bo);

    /**
     * 新增绿能高支模实时数据
     *
     * @param bo 绿能高支模实时数据
     * @return 是否新增成功
     */
    Boolean insertByBo(LnHighFormworkRealBo bo);

    /**
     * 修改绿能高支模实时数据
     *
     * @param bo 绿能高支模实时数据
     * @return 是否修改成功
     */
    Boolean updateByBo(LnHighFormworkRealBo bo);

    /**
     * 校验并批量删除绿能高支模实时数据信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

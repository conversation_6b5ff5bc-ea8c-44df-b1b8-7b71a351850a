package org.dromara.facility.domain;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 绿能临边防护对象 ln_edge_guard
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ln_edge_guard")
public class LnEdgeGuard {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 编号
     */
    private Long dumpnumber;

    /**
     * 开关状态
     */
    private Long checkSensor;

    /**
     * 经度
     */
    private Long longitude;

    /**
     * 纬度
     */
    private Long latitude;

    /**
     * 电池电压
     */
    private Long batvolt;

    /**
     * 电池电量
     */
    private Long batPercent;

    /**
     * 报警信息
     */
    private Long alarmInfo;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

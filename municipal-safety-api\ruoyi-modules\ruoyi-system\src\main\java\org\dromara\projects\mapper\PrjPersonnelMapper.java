package org.dromara.projects.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.projects.domain.PrjPersonnel;
import org.dromara.projects.domain.vo.PrjPersonnelVo;

import java.util.List;

/**
 * 项目人员关联表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
public interface PrjPersonnelMapper extends BaseMapperPlus<PrjPersonnel, PrjPersonnelVo> {

    /**
     * 查询项目人员列表
     *
     * @param projectId 项目ID
     * @return 项目人员列表（包含人员基本信息和企业信息）
     */
    List<PrjPersonnelVo> selectPersonnelListByProjectId(@Param("projectId") Long projectId);

    List<PrjPersonnelVo> selectPrjPersonByPersonId(@Param("projectId") String projectId, @Param("personId") Long personId);
}

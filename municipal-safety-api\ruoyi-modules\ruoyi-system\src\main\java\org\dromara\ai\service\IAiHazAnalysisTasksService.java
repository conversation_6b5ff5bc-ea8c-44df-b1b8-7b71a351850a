package org.dromara.ai.service;

import org.dromara.ai.domain.bo.AiHazAnalysisTasksBo;
import org.dromara.ai.domain.dto.AiAnalysisResultDto;
import org.dromara.ai.domain.dto.AiHazAnalysisTasksDto;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksVo;
import org.dromara.common.core.domain.dto.StartProcessReturnDTO;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistBo;

import java.util.Collection;
import java.util.List;

/**
 * 智能隐患分析任务Service接口
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
public interface IAiHazAnalysisTasksService {

    /**
     * 查询智能隐患分析任务
     *
     * @param taskId 主键
     * @return 智能隐患分析任务
     */
    AiHazAnalysisTasksVo queryById(Long taskId);

    AiHazAnalysisTasksVo queryByIdNew(Long taskId);

    /**
     * 分页查询智能隐患分析任务列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 智能隐患分析任务分页列表
     */
    TableDataInfo<AiHazAnalysisTasksVo> queryPageList(AiHazAnalysisTasksBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的智能隐患分析任务列表
     *
     * @param bo 查询条件
     * @return 智能隐患分析任务列表
     */
    List<AiHazAnalysisTasksVo> queryList(AiHazAnalysisTasksBo bo);

    /**
     * 新增智能隐患分析任务
     *
     * @param bo 智能隐患分析任务
     * @return 是否新增成功
     */
    Boolean insertByBo(AiHazAnalysisTasksBo bo);

    /**
     * 修改智能隐患分析任务
     *
     * @param bo 智能隐患分析任务
     * @return 是否修改成功
     */
    Boolean updateByBo(AiHazAnalysisTasksBo bo);

    /**
     * 校验并批量删除智能隐患分析任务信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 处理AI分析回调结果
     *
     * @param aiResult AI分析结果
     * @return 是否处理成功
     */
    Boolean processAiAnalysisResult(AiAnalysisResultDto aiResult);

    /**
     * 提交到外部AI分析服务
     *
     * @param taskId     任务ID
     * @param imageUrl   图片URL
     * @param itemId     隐患ID
     * @return 分析任务详情
     */
    AiHazAnalysisTasksVo submitToAiAnalysis(Long taskId, String imageUrl, Long itemId);

    /**
     * APP端分页查询隐患列表
     *
     * @param dto       查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    TableDataInfo<AiHazAnalysisTasksVo> queryNewPageList(AiHazAnalysisTasksDto dto, PageQuery pageQuery);

    /**
     * AI端分页查询隐患列表
     * @param dto
     * @param pageQuery
     * @return
     */
    TableDataInfo<AiHazAnalysisTasksVo> queryAIPageList(AiHazAnalysisTasksDto dto, PageQuery pageQuery);

    /**
     * 根据dangerId获取父级名称
     *
     * @param dangerId 涉危工程清单ID（可以是Long类型或逗号分隔的字符串）
     * @return 父级名称
     */
    String getParentName(String dangerId);

    /**
     * 发起质监站工单
     * @param taskId
     * @param busId
     * @return
     */
    StartProcessReturnDTO pushWorkOrder(Long taskId, String busId);

    /**
     * 发起省厅自动工单
     * @param bo
     * @return
     */
    StartProcessReturnDTO pushWorkOrder2(PrjHazardousItemsSpecialistBo bo);
}

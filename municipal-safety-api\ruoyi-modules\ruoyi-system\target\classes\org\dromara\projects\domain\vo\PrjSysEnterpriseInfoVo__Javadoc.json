{"doc": " 企业信息视图对象\n\n <AUTHOR>\n @date 2025-05-08\n", "fields": [{"name": "enterpriseId", "doc": " 企业ID\n"}, {"name": "enterpriseName", "doc": " 企业名称\n"}, {"name": "unifiedSocialCreditCode", "doc": " 统一社会信用代码\n"}, {"name": "enterpriseType", "doc": " 企业类型\n"}, {"name": "businessAddress", "doc": " 企业地址\n"}, {"name": "legalRepresentative", "doc": " 法定代表人\n"}, {"name": "registrationRegionProvince", "doc": " 注册省份\n"}, {"name": "registrationRegionCity", "doc": " 注册城市\n"}, {"name": "registrationRegionArea", "doc": " 注册区域\n"}, {"name": "registrationDate", "doc": " 注册日期\n"}, {"name": "officePhone", "doc": " 办公电话\n"}, {"name": "enterpriseStatus", "doc": " 企业状态\n"}, {"name": "deptId", "doc": " 部门ID\n"}, {"name": "enterpriseRole", "doc": " 企业类型 (1:建设单位 2:施工单位 3:监理单位 4:设计单位 5:勘察单位)\n"}], "enumConstants": [], "methods": [], "constructors": []}
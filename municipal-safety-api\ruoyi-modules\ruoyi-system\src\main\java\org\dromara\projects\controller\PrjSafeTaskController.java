package org.dromara.projects.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.projects.domain.PrjSafeTask;
import org.dromara.projects.domain.vo.PrjSafeTaskVo;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.projects.domain.bo.PrjSafeTaskBo;
import org.dromara.projects.service.IPrjSafeTaskService;

/**
 * 【项目管理】安拆任务
 *
 * <AUTHOR> Li
 * @date 2025-08-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/projects/safeTask")
public class PrjSafeTaskController extends BaseController {

    private final IPrjSafeTaskService prjSafeTaskService;

    /**
     * 查询【项目管理】安拆任务列表
     */
    @SaCheckPermission("safeTask:safeTask:list")
    @GetMapping("/list")
    public TableDataInfo<PrjSafeTaskVo> list(PrjSafeTaskBo bo, PageQuery pageQuery) {
        return prjSafeTaskService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出【项目管理】安拆任务列表
     */
    @SaCheckPermission("safeTask:safeTask:export")
    @Log(title = "【项目管理】安拆任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PrjSafeTaskBo bo, HttpServletResponse response) {
        List<PrjSafeTaskVo> list = prjSafeTaskService.queryList(bo);
        ExcelUtil.exportExcel(list, "【项目管理】安拆任务", PrjSafeTaskVo.class, response);
    }

    /**
     * 获取【项目管理】安拆任务详细信息
     *
     * @param openTaskId 主键
     */
    @SaCheckPermission("safeTask:safeTask:query")
    @GetMapping("/{openTaskId}")
    public R<PrjSafeTaskVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long openTaskId) {
        return R.ok(prjSafeTaskService.queryById(openTaskId));
    }

    /**
     * 新增【项目管理】安拆任务
     */
    @SaCheckPermission("safeTask:safeTask:add")
    @Log(title = "【项目管理】安拆任务", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PrjSafeTaskBo bo) {
        return toAjax(prjSafeTaskService.insertByBo(bo));
    }

    /**
     * 修改【项目管理】安拆任务
     */
    @SaCheckPermission("safeTask:safeTask:edit")
    @Log(title = "【项目管理】安拆任务", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PrjSafeTaskBo bo) {
        return toAjax(prjSafeTaskService.updateByBo(bo));
    }

    /**
     * 删除【项目管理】安拆任务
     *
     * @param openTaskIds 主键串
     */
    @SaCheckPermission("safeTask:safeTask:remove")
    @Log(title = "【项目管理】安拆任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{openTaskIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] openTaskIds) {
        return toAjax(prjSafeTaskService.deleteWithValidByIds(List.of(openTaskIds), true));
    }

    @GetMapping("/getToPrj/{projectId}")
    public R<PrjSafeTask> prjSafeTask(@PathVariable Long projectId) {
        return R.ok(prjSafeTaskService.getToProjectId(projectId));
    }
}

package org.dromara.quality.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.quality.domain.bo.QualityDeviceBoToQualityDeviceMapper;
import org.dromara.quality.domain.vo.QualityDeviceVo;
import org.dromara.quality.domain.vo.QualityDeviceVoToQualityDeviceMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {QualityDeviceVoToQualityDeviceMapper.class,QualityDeviceBoToQualityDeviceMapper.class},
    imports = {}
)
public interface QualityDeviceToQualityDeviceVoMapper extends BaseMapper<QualityDevice, QualityDeviceVo> {
}

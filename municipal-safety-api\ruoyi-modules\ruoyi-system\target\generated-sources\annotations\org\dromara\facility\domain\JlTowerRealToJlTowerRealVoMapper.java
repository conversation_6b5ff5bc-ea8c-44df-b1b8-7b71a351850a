package org.dromara.facility.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.bo.JlTowerRealBoToJlTowerRealMapper;
import org.dromara.facility.domain.vo.JlTowerRealVo;
import org.dromara.facility.domain.vo.JlTowerRealVoToJlTowerRealMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {JlTowerRealVoToJlTowerRealMapper.class,JlTowerRealBoToJlTowerRealMapper.class},
    imports = {}
)
public interface JlTowerRealToJlTowerRealVoMapper extends BaseMapper<JlTowerReal, JlTowerRealVo> {
}

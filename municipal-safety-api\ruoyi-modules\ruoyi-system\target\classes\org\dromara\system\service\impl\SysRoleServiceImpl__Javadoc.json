{"doc": " 角色 业务层处理\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectRoleList", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": " 根据条件分页查询角色数据\n\n @param role 角色信息\n @return 角色数据集合信息\n"}, {"name": "selectRolesByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询角色\n\n @param userId 用户ID\n @return 角色列表\n"}, {"name": "selectRolesAuthByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询角色列表(包含被授权状态)\n\n @param userId 用户ID\n @return 角色列表\n"}, {"name": "selectRolePermissionByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询权限\n\n @param userId 用户ID\n @return 权限列表\n"}, {"name": "selectRoleAll", "paramTypes": [], "doc": " 查询所有角色\n\n @return 角色列表\n"}, {"name": "selectRoleListByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID获取角色选择框列表\n\n @param userId 用户ID\n @return 选中角色ID列表\n"}, {"name": "selectRoleById", "paramTypes": ["java.lang.Long"], "doc": " 通过角色ID查询角色\n\n @param roleId 角色ID\n @return 角色对象信息\n"}, {"name": "selectRoleByIds", "paramTypes": ["java.util.List"], "doc": " 通过角色ID串查询角色\n\n @param roleIds 角色ID串\n @return 角色列表信息\n"}, {"name": "checkRoleNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": " 校验角色名称是否唯一\n\n @param role 角色信息\n @return 结果\n"}, {"name": "checkRoleKeyUnique", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": " 校验角色权限是否唯一\n\n @param role 角色信息\n @return 结果\n"}, {"name": "checkRoleAllowed", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": " 校验角色是否允许操作\n\n @param role 角色信息\n"}, {"name": "checkRoleDataScope", "paramTypes": ["java.lang.Long"], "doc": " 校验角色是否有数据权限\n\n @param roleId 角色id\n"}, {"name": "countUserRoleByRoleId", "paramTypes": ["java.lang.Long"], "doc": " 通过角色ID查询角色使用数量\n\n @param roleId 角色ID\n @return 结果\n"}, {"name": "insertRole", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": " 新增保存角色信息\n\n @param bo 角色信息\n @return 结果\n"}, {"name": "updateRole", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": " 修改保存角色信息\n\n @param bo 角色信息\n @return 结果\n"}, {"name": "updateRoleStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 修改角色状态\n\n @param roleId 角色ID\n @param status 角色状态\n @return 结果\n"}, {"name": "authDataScope", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": " 修改数据权限信息\n\n @param bo 角色信息\n @return 结果\n"}, {"name": "insertRoleMenu", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": " 新增角色菜单信息\n\n @param role 角色对象\n"}, {"name": "insertRoleDept", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": " 新增角色部门信息(数据权限)\n\n @param role 角色对象\n"}, {"name": "deleteRoleById", "paramTypes": ["java.lang.Long"], "doc": " 通过角色ID删除角色\n\n @param roleId 角色ID\n @return 结果\n"}, {"name": "deleteRoleByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除角色信息\n\n @param roleIds 需要删除的角色ID\n @return 结果\n"}, {"name": "deleteAuthUser", "paramTypes": ["org.dromara.system.domain.SysUserRole"], "doc": " 取消授权用户角色\n\n @param userRole 用户和角色关联信息\n @return 结果\n"}, {"name": "deleteAuthUsers", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": " 批量取消授权用户角色\n\n @param roleId  角色ID\n @param userIds 需要取消授权的用户数据ID\n @return 结果\n"}, {"name": "insertAuthUsers", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": " 批量选择授权用户角色\n\n @param roleId  角色ID\n @param userIds 需要授权的用户数据ID\n @return 结果\n"}], "constructors": []}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.expert.mapper.ExpertMapper">

    <select id="selectListByUserIds" resultType="org.dromara.expert.domain.Expert">
        select *
        from z_expert
        where id_card in (
            select user_name from sys_user
                where user_id in
                <foreach collection="ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
        )
        and del_flag = 0
    </select>
</mapper>

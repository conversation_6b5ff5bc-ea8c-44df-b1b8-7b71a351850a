package org.dromara.facility.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.LnHighFormworkReal;
import org.dromara.facility.domain.LnHighFormworkRealToLnHighFormworkRealVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {LnHighFormworkRealToLnHighFormworkRealVoMapper.class},
    imports = {}
)
public interface LnHighFormworkRealVoToLnHighFormworkRealMapper extends BaseMapper<LnHighFormworkRealVo, LnHighFormworkReal> {
}

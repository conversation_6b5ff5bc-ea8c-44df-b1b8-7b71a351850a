package org.dromara.facility.service;

import org.dromara.facility.domain.vo.LnDumpPlatVo;
import org.dromara.facility.domain.bo.LnDumpPlatBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 绿能卸料平台Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
public interface ILnDumpPlatService extends BaseFacilityHandle{

    /**
     * 查询绿能卸料平台
     *
     * @param id 主键
     * @return 绿能卸料平台
     */
    LnDumpPlatVo queryById(Long id);

    /**
     * 分页查询绿能卸料平台列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能卸料平台分页列表
     */
    TableDataInfo<LnDumpPlatVo> queryPageList(LnDumpPlatBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的绿能卸料平台列表
     *
     * @param bo 查询条件
     * @return 绿能卸料平台列表
     */
    List<LnDumpPlatVo> queryList(LnDumpPlatBo bo);

    /**
     * 新增绿能卸料平台
     *
     * @param bo 绿能卸料平台
     * @return 是否新增成功
     */
    Boolean insertByBo(LnDumpPlatBo bo);

    /**
     * 修改绿能卸料平台
     *
     * @param bo 绿能卸料平台
     * @return 是否修改成功
     */
    Boolean updateByBo(LnDumpPlatBo bo);

    /**
     * 校验并批量删除绿能卸料平台信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

package org.dromara.flow.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.flow.domain.PrjHazardousItemsSpecialistQuestion;

/**
 * 省厅自动工单专家建议业务对象 prj_hazardous_items_specialist_question
 *
 * <AUTHOR>
 * @date 2025-06-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjHazardousItemsSpecialistQuestion.class, reverseConvertGenerate = false)
public class PrjHazardousItemsSpecialistQuestionBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 专家名称
     */
    private String name;

    /**
     * 专家意见
     */
    private String detail;

    /**
     * 问题id(ai_haz_analysis_tasks_result.id)
     */
    private Long resultId;

    /**
     * 省厅工单数据id(prj_hazardous_items_specialist.id)
     */
    private Long specialist;
}

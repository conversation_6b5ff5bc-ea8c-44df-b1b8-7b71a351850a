package org.dromara.attendance.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.attendance.domain.MAttSn;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class MAttSnBoToMAttSnMapperImpl implements MAttSnBoToMAttSnMapper {

    @Override
    public MAttSn convert(MAttSnBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MAttSn mAttSn = new MAttSn();

        mAttSn.setSearchValue( arg0.getSearchValue() );
        mAttSn.setCreateDept( arg0.getCreateDept() );
        mAttSn.setCreateBy( arg0.getCreateBy() );
        mAttSn.setCreateTime( arg0.getCreateTime() );
        mAttSn.setUpdateBy( arg0.getUpdateBy() );
        mAttSn.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            mAttSn.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        mAttSn.setSnId( arg0.getSnId() );
        mAttSn.setProjectId( arg0.getProjectId() );
        mAttSn.setSn( arg0.getSn() );
        mAttSn.setSnName( arg0.getSnName() );
        mAttSn.setDirection( arg0.getDirection() );
        mAttSn.setStatus( arg0.getStatus() );

        return mAttSn;
    }

    @Override
    public MAttSn convert(MAttSnBo arg0, MAttSn arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSnId( arg0.getSnId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setSn( arg0.getSn() );
        arg1.setSnName( arg0.getSnName() );
        arg1.setDirection( arg0.getDirection() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}

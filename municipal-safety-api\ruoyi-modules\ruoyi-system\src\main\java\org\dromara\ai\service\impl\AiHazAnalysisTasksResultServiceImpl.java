package org.dromara.ai.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.ai.domain.AiHazAnalysisTasksResult;
import org.dromara.ai.domain.bo.AiHazAnalysisTasksResultBo;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksResultVo;
import org.dromara.ai.mapper.AiHazAnalysisTasksResultMapper;
import org.dromara.ai.service.IAiHazAnalysisTasksResultService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 隐患AI分析结果Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@RequiredArgsConstructor
@Service
public class AiHazAnalysisTasksResultServiceImpl implements IAiHazAnalysisTasksResultService {

    private final AiHazAnalysisTasksResultMapper baseMapper;

    /**
     * 查询隐患AI分析结果
     *
     * @param resultId 主键
     * @return 隐患AI分析结果
     */
    @Override
    public AiHazAnalysisTasksResultVo queryById(Long resultId) {
        return baseMapper.selectVoById(resultId);
    }

    /**
     * 分页查询隐患AI分析结果列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 隐患AI分析结果分页列表
     */
    @Override
    public TableDataInfo<AiHazAnalysisTasksResultVo> queryPageList(AiHazAnalysisTasksResultBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiHazAnalysisTasksResult> lqw = buildQueryWrapper(bo);
        Page<AiHazAnalysisTasksResultVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的隐患AI分析结果列表
     *
     * @param bo 查询条件
     * @return 隐患AI分析结果列表
     */
    @Override
    public List<AiHazAnalysisTasksResultVo> queryList(AiHazAnalysisTasksResultBo bo) {
        LambdaQueryWrapper<AiHazAnalysisTasksResult> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiHazAnalysisTasksResult> buildQueryWrapper(AiHazAnalysisTasksResultBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiHazAnalysisTasksResult> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AiHazAnalysisTasksResult::getResultId);
        lqw.eq(bo.getTaskId() != null, AiHazAnalysisTasksResult::getTaskId, bo.getTaskId());
        lqw.eq(StringUtils.isNotBlank(bo.getViolation()), AiHazAnalysisTasksResult::getViolation, bo.getViolation());
        lqw.eq(StringUtils.isNotBlank(bo.getRegulation()), AiHazAnalysisTasksResult::getRegulation, bo.getRegulation());
        lqw.eq(StringUtils.isNotBlank(bo.getCoordinate()), AiHazAnalysisTasksResult::getCoordinate, bo.getCoordinate());
        lqw.eq(StringUtils.isNotBlank(bo.getLevel()), AiHazAnalysisTasksResult::getLevel, bo.getLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getMeasure()), AiHazAnalysisTasksResult::getMeasure, bo.getMeasure());
        return lqw;
    }

    /**
     * 新增隐患AI分析结果
     *
     * @param bo 隐患AI分析结果
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AiHazAnalysisTasksResultBo bo) {
        AiHazAnalysisTasksResult add = MapstructUtils.convert(bo, AiHazAnalysisTasksResult.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setResultId(add.getResultId());
        }
        return flag;
    }

    /**
     * 修改隐患AI分析结果
     *
     * @param bo 隐患AI分析结果
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AiHazAnalysisTasksResultBo bo) {
        AiHazAnalysisTasksResult update = MapstructUtils.convert(bo, AiHazAnalysisTasksResult.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiHazAnalysisTasksResult entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除隐患AI分析结果信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}

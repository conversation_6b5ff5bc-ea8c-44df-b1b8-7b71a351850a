package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.DangerListBoToDangerListMapper;
import org.dromara.system.domain.vo.DangerListVo;
import org.dromara.system.domain.vo.DangerListVoToDangerListMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {DangerListVoToDangerListMapper.class,DangerListBoToDangerListMapper.class},
    imports = {}
)
public interface DangerListToDangerListVoMapper extends BaseMapper<DangerList, DangerListVo> {
}

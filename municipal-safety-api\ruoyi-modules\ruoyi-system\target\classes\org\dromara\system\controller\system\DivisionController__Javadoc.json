{"doc": " 行政区划\n @date 2025-04-30\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.DivisionBo"], "doc": " 查询行政区划列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.DivisionBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出行政区划列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取行政区划详细信息\n\n @param divisionId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.DivisionBo"], "doc": " 新增行政区划\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.DivisionBo"], "doc": " 修改行政区划\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除行政区划\n\n @param divisionIds 主键串\n"}, {"name": "getProvinceList", "paramTypes": [], "doc": "  获取省列表\n"}, {"name": "getCityList", "paramTypes": ["java.lang.String"], "doc": "  获取市列表\n"}, {"name": "getAreaList", "paramTypes": ["java.lang.String"], "doc": "  获取区列表\n"}], "constructors": []}
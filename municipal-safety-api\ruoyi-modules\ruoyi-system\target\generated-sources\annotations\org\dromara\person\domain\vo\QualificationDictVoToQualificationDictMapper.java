package org.dromara.person.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.person.domain.QualificationDict;
import org.dromara.person.domain.QualificationDictToQualificationDictVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {QualificationDictToQualificationDictVoMapper.class},
    imports = {}
)
public interface QualificationDictVoToQualificationDictMapper extends BaseMapper<QualificationDictVo, QualificationDict> {
}

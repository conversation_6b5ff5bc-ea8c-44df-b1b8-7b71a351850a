import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PersonVO, PersonForm, PersonQuery, QualificationVO, QualificationForm, QualificationQuery } from '@/api/person/basic/types';


/**
 * 查询人员基本信息列表
 * @param query
 * @returns {*}
 */

export const listPerson = (query?: PersonQuery): AxiosPromise<PersonVO[]> => {
  return request({
    url: '/system/person/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询人员基本信息详细
 * @param personId
 */
export const getPerson = (personId: string | number): AxiosPromise<any> => {
  return request({
    url: '/system/person/' + personId,
    method: 'get'
  });
};

/**
 * 新增人员基本信息
 * @param data
 */
export const addPerson = (data: PersonForm) => {
  return request({
    url: '/system/person',
    method: 'post',
    data: data
  });
};

/**
 * 修改人员基本信息
 * @param data
 */
export const updatePerson = (data: PersonForm) => {
  return request({
    url: '/system/person',
    method: 'put',
    data: data
  });
};

/**
 * 删除人员基本信息
 * @param personId
 */
export const delPerson = (personId: string | number | Array<string | number>) => {
  return request({
    url: '/system/person/' + personId,
    method: 'delete'
  });
};




/**
 * 查询人员资格证书列表
 * @param query
 * @returns {*}
 */

export const listQualification = (query?: QualificationQuery): AxiosPromise<QualificationQuery[]> => {
  return request({
    url: '/system/qualification/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询人员资格证书详细
 * @param qualificationId
 */
export const getQualification = (qualificationId: string | number): AxiosPromise<QualificationVO> => {
  return request({
    url: '/system/qualification/' + qualificationId,
    method: 'get'
  });
};

/**
 * 新增人员资格证书
 * @param data
 */
export const addQualification = (data: QualificationForm) => {
  return request({
    url: '/system/qualification',
    method: 'post',
    data: data
  });
};

/**
 * 修改人员资格证书
 * @param data
 */
export const updateQualification = (data: QualificationForm) => {
  return request({
    url: '/system/qualification',
    method: 'put',
    data: data
  });
};

/**
 * 删除人员资格证书
 * @param qualificationId
 */
export const delQualification = (qualificationId: string | number | Array<string | number>) => {
  return request({
    url: '/system/qualification/' + qualificationId,
    method: 'delete'
  });
};

export const signOut = (data : any) => {
  return request({
    url: `/system/person/moveOut`,
    method: 'post',
    data
  });
};

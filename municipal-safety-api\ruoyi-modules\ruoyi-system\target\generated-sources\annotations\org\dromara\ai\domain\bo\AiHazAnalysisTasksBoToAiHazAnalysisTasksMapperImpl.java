package org.dromara.ai.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.ai.domain.AiHazAnalysisTasks;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class AiHazAnalysisTasksBoToAiHazAnalysisTasksMapperImpl implements AiHazAnalysisTasksBoToAiHazAnalysisTasksMapper {

    @Override
    public AiHazAnalysisTasks convert(AiHazAnalysisTasksBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AiHazAnalysisTasks aiHazAnalysisTasks = new AiHazAnalysisTasks();

        aiHazAnalysisTasks.setSearchValue( arg0.getSearchValue() );
        aiHazAnalysisTasks.setCreateDept( arg0.getCreateDept() );
        aiHazAnalysisTasks.setCreateBy( arg0.getCreateBy() );
        aiHazAnalysisTasks.setCreateTime( arg0.getCreateTime() );
        aiHazAnalysisTasks.setUpdateBy( arg0.getUpdateBy() );
        aiHazAnalysisTasks.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            aiHazAnalysisTasks.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        aiHazAnalysisTasks.setTaskId( arg0.getTaskId() );
        aiHazAnalysisTasks.setProjectId( arg0.getProjectId() );
        aiHazAnalysisTasks.setItemId( arg0.getItemId() );
        aiHazAnalysisTasks.setSourceType( arg0.getSourceType() );
        aiHazAnalysisTasks.setExpertUserId( arg0.getExpertUserId() );
        aiHazAnalysisTasks.setUploadTime( arg0.getUploadTime() );
        aiHazAnalysisTasks.setPhotoDocumentId( arg0.getPhotoDocumentId() );
        aiHazAnalysisTasks.setGpsLocation( arg0.getGpsLocation() );
        aiHazAnalysisTasks.setLocationDescription( arg0.getLocationDescription() );
        aiHazAnalysisTasks.setAiPhotoDocumentId( arg0.getAiPhotoDocumentId() );
        aiHazAnalysisTasks.setAiRecognitionRawResult( arg0.getAiRecognitionRawResult() );
        aiHazAnalysisTasks.setStatus( arg0.getStatus() );
        aiHazAnalysisTasks.setRecheckStatus( arg0.getRecheckStatus() );
        aiHazAnalysisTasks.setRelatedWorkOrderId( arg0.getRelatedWorkOrderId() );
        aiHazAnalysisTasks.setRemark( arg0.getRemark() );

        return aiHazAnalysisTasks;
    }

    @Override
    public AiHazAnalysisTasks convert(AiHazAnalysisTasksBo arg0, AiHazAnalysisTasks arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setSourceType( arg0.getSourceType() );
        arg1.setExpertUserId( arg0.getExpertUserId() );
        arg1.setUploadTime( arg0.getUploadTime() );
        arg1.setPhotoDocumentId( arg0.getPhotoDocumentId() );
        arg1.setGpsLocation( arg0.getGpsLocation() );
        arg1.setLocationDescription( arg0.getLocationDescription() );
        arg1.setAiPhotoDocumentId( arg0.getAiPhotoDocumentId() );
        arg1.setAiRecognitionRawResult( arg0.getAiRecognitionRawResult() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRecheckStatus( arg0.getRecheckStatus() );
        arg1.setRelatedWorkOrderId( arg0.getRelatedWorkOrderId() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}

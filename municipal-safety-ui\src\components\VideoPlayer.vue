<template>
  <div ref="playerContainer" class="video-player-container"></div>
</template>

<script>
import Player from 'xgplayer'
// import DanmuPlugin from 'xgplayer-plugin-danmu'
import DemoBasePlugin from './playComponents/demoBasePlugin'
import DemoPlugin from './playComponents/demoPlugin'

export default {
  name: 'VideoPlayer',
  props: {
    videoUrl: {
      type: String,
      required: true
    },
    videoId: {
      type: String,
      default: ''
    },
    tokens: {
      type: String,
      default: ''
    },
    danmus: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      player: null,
      video_id: '',
    }
  },
  mounted() {
    this.initPlayer()
  },
  methods: {
    initPlayer() {
      this.video_id = this.videoId
      
      // 初始化播放器
      this.player = new Player({
        id: this.$refs.playerContainer,
        url: require('../assets/tst.mp4'),
        width: '100%',
        height: '100%',
        autoplay: false,
        plugins: [DemoBasePlugin],
        danmu: {
          comments: this.formatDanmus(), // 转换弹幕格式
          area: {
            start: 0,
            end: 1
          }
        }
      })
      
      // 注册弹幕发送插件
      this.player.registerPlugin(DemoPlugin)
      
      // 将Vue实例挂载到播放器root节点上，以便插件获取
      this.player.root.__vue__ = this
    },
    
    formatDanmus() {
      // 根据你的弹幕数据格式转换成西瓜播放器需要的格式
      return this.danmus.map(item => {
        return {
          id: item.id,
          txt: item.content,
          duration: 15000,
          start: item.time,
          color: true,
          style: {
            color: '#ff9500',
            fontSize: '20px',
            border: 'solid 1px #ff9500',
            borderRadius: '50px',
            padding: '5px 11px',
            backgroundColor: 'rgba(255, 255, 255, 0.1)'
          }
        }
      })
    }
  },
  beforeDestroy() {
    if (this.player) {
      this.player.destroy()
      this.player = null
    }
  }
}
</script>

<style scoped>
.video-player-container {
  width: 100%;
  height: 500px;
}

/* 自定义弹幕发送组件样式 */
:deep(.danmu-container) {
  display: flex;
  align-items: center;
  margin-left: 10px;
}
</style> 
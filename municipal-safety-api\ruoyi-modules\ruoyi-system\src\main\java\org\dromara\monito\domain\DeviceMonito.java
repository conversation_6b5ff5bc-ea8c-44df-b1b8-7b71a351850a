package org.dromara.monito.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 监控管理对象 device_monito
 *
 * <AUTHOR> Li
 * @date 2025-05-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("device_monito")
public class DeviceMonito extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 监控ID
     */
    @TableId(value = "monito_id")
    private Long monitoId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目工程ID
     */
    private Long itemId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备类型(001:萤石云)
     */
    private String deviceType;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备在线状态(在线、不在线)
     */
    private String deviceStatus;

    /**
     * 是否启用抓拍
     */
    private String enableSnapshot;

    /**
     * 抓拍时间间隔(秒)
     */
    private Long snapshotTime;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

    /**  通道编号 */
    private Integer channelNo;

    /** 定时任务 */
    private Long jobId;
}

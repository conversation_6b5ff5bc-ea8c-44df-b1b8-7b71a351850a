export interface Prj_hazardous_itemsVO {
  /**
   * 危大工程项ID
   */
  itemId: string | number;

  /**
   * 所属项目ID (逻辑外键至 prj_projects.project_id)
   */
  projectId: string | number;

  /**
   * 涉危工程清单ID（支持多选逗号隔开）
   */
  dangerId: string | number;

  /**
   * 危大工程名称/描述
   */
  itemName: string;

  /**
   * 具体范围详情
   */
  scopeDetails: string;

  /**
   * 危大类型 (1:危大, 2:超危大)
   */
  dangerListType: number;

  /**
   * 计划开工日期
   */
  startDate: string;

  /**
   * 计划竣工日期
   */
  plannedEndDate: string;

  /**
   * 实际竣工日期
   */
  actualEndDate: string;

  /**
   * 实际开工日期
   */
  actualStartDate: string;

  /**
   * 状态
   */
  status: string;

}

export interface Prj_hazardous_itemsForm extends BaseEntity {
  dangerGcVal: string;
  dangerGcTypeVal: string[];
  /**
   * 危大工程项ID
   */
  itemId?: string | number;

  /**
   * 所属项目ID (逻辑外键至 prj_projects.project_id)
   */
  projectId?: string | number;

  /**
   * 涉危工程清单ID（支持多选逗号隔开）
   */
  dangerId?: string | number;

  /**
   * 危大工程名称/描述
   */
  itemName?: string;

  /**
   * 具体范围详情
   */
  scopeDetails?: string;

  /**
   * 危大类型 (1:危大, 2:超危大)
   */
  dangerListType?: number;

  /**
   * 计划开工日期
   */
  startDate?: string;

  /**
   * 计划竣工日期
   */
  plannedEndDate?: string;

  /**
   * 实际竣工日期
   */
  actualEndDate?: string;

  /**
   * 实际开工日期
   */
  actualStartDate?: string;

  /**
   * 状态
   */
  status?: string;

}

export interface Prj_hazardous_itemsQuery extends PageQuery {

  /**
   * 所属项目ID (逻辑外键至 prj_projects.project_id)
   */
  enterpriseId?: string | number;
  projectId?: string | number;
  itemId?: string | number;

  /**
   * 涉危工程清单ID（支持多选逗号隔开）
   */
  dangerId?: string | number;

  /**
   * 危大工程名称/描述
   */
  itemName?: string;

  /**
   * 具体范围详情
   */
  scopeDetails?: string;

  /**
   * 危大类型 (1:危大, 2:超危大)
   */
  dangerListType?: number;

  /**
   * 计划开工日期
   */
  startDate?: string;

  /**
   * 计划竣工日期
   */
  plannedEndDate?: string;

  /**
   * 实际竣工日期
   */
  actualEndDate?: string;

  /**
   * 实际开工日期
   */
  actualStartDate?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}




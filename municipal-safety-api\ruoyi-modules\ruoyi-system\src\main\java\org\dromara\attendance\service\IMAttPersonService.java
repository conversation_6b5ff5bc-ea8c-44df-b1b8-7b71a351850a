package org.dromara.attendance.service;

import org.dromara.attendance.domain.vo.MAttPersonVo;
import org.dromara.attendance.domain.bo.MAttPersonBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * attPersonService接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface IMAttPersonService {

    /**
     * 查询attPerson
     *
     * @param id 主键
     * @return attPerson
     */
    MAttPersonVo queryById(Long id);

    /**
     * 分页查询attPerson列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return attPerson分页列表
     */
    TableDataInfo<MAttPersonVo> queryPageList(MAttPersonBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的attPerson列表
     *
     * @param bo 查询条件
     * @return attPerson列表
     */
    List<MAttPersonVo> queryList(MAttPersonBo bo);

    /**
     * 新增attPerson
     *
     * @param bo attPerson
     * @return 是否新增成功
     */
    Boolean insertByBo(MAttPersonBo bo);

    /**
     * 修改attPerson
     *
     * @param bo attPerson
     * @return 是否修改成功
     */
    Boolean updateByBo(MAttPersonBo bo);

    /**
     * 校验并批量删除attPerson信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<MAttPersonVo> selectMAttPersonByPersonId(Long personId);

    List<MAttPersonVo>  selectMAttPersonBySnId(Long snId);

    MAttPersonVo selectMAttPersonByPersonIdAndSnId(String snId, Long personId);
}

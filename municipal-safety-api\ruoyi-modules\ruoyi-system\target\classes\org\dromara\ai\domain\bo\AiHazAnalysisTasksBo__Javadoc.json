{"doc": " 智能隐患分析任务业务对象 ai_haz_analysis_tasks\n\n <AUTHOR>\n @date 2025-05-09\n", "fields": [{"name": "taskId", "doc": " 分析任务ID\n"}, {"name": "projectId", "doc": " 关联项目ID\n"}, {"name": "sourceType", "doc": " 预警来源类型 CAMERA[摄像头] APP[用户App上报]\n"}, {"name": "expertUserId", "doc": " 提交分析的专家用户ID\n"}, {"name": "uploadTime", "doc": " 照片上传时间\n"}, {"name": "photoDocumentId", "doc": " 上传的照片文档ID\n"}, {"name": "gpsLocation", "doc": " 拍照时GPS坐标\n"}, {"name": "locationDescription", "doc": " 拍照位置文字描述\n"}, {"name": "aiRecognitionRawResult", "doc": " AI模型识别输出结果\n"}, {"name": "aiPhotoDocumentId", "doc": " AI分析后返回的带标注的照片文档ID\n"}, {"name": "itemId", "doc": " 关联的已知危大工程项ID\n"}, {"name": "status", "doc": " 任务状态\n"}, {"name": "recheckStatus", "doc": " 复检状态（PENDING_RECHECK[待复检]、FINISH_RECHECK[复检完成]）\n"}, {"name": "relatedWorkOrderId", "doc": " 因此次分析发现问题而生成的工单ID\n"}], "enumConstants": [], "methods": [], "constructors": []}
{"doc": " 考勤规则\n\n <AUTHOR>\n @date 2025-05-06\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRuleBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询考勤规则列表\n"}, {"name": "selectAll", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRuleBo"], "doc": " 查询考勤规则列表\n"}, {"name": "export", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRuleBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出考勤规则列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取考勤规则详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRuleBo"], "doc": " 新增考勤规则\n"}, {"name": "edit", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRuleBo"], "doc": " 修改考勤规则\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除考勤规则\n\n @param ids 主键串\n"}, {"name": "roleList", "paramTypes": ["java.lang.Long"], "doc": " 获取项目的角色规则\n @return\n"}], "constructors": []}
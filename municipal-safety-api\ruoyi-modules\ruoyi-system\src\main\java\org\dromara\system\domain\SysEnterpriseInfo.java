package org.dromara.system.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 企业信息对象 sys_enterprise_info
 *
 * <AUTHOR> Li
 * @date 2025-05-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_enterprise_info")
public class SysEnterpriseInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 企业ID
     */
    @TableId(value = "enterprise_id")
    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 统一社会信用代码
     */
    private String unifiedSocialCreditCode;

    /**
     * 企业类型（下拉选择）
     */
    private String enterpriseType;

    /**
     * 企业地址
     */
    private String businessAddress;

    /**
     * 法定代表人
     */
    private String legalRepresentative;

    /**
     * 省
     */
    private String registrationRegionProvince;

    /**
     * 市
     */
    private String registrationRegionCity;

    /**
     * 区
     */
    private String registrationRegionArea;

    /**
     * 注册日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registrationDate;

    /**
     * 办公电话
     */
    private String officePhone;

    /**
     * 营业执照路径
     */
    private Long businessLicensePath;

    /**
     * 法人代表身份证扫描件路径
     */
    private Long legalIdCardPath;

    /**
     * 资质证书文件路径
     */
    private Long qualificationCertificatePath;

    /**
     * 安全生产许可证路径
     */
    private Long safetyProductionLicensePath;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

    /** 状态 0待审核 1通过 2不通过 */
    private String enterpriseStatus;

    /**  审核人 */
    private Long enterpriseUserId;

    /**  审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date enterpriseTime;

    /** 原因 */
    private String enterpriseReason;

    /**  验证码id  */
    @TableField(exist = false)
    private String uuid;

    /**  验证码 code  */
    @TableField(exist = false)
    private String code;

    /**  审核人名称 */
    @TableField(exist = false)
    private String enterpriseUserName;

    /** 行政区划名称  */
    @TableField(exist = false)
    private String provinceName;
    @TableField(exist = false)
    private String cityName;
    @TableField(exist = false)
    private String areaName;
    @TableField(exist = false)
    private Integer pageNum;
    @TableField(exist = false)
    private Integer pageSize;

}

package org.dromara.quality.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.quality.domain.QualityDevice;
import org.dromara.quality.domain.QualityMeasurement;
import org.dromara.quality.domain.bo.QualityMeasurementBo;
import org.dromara.quality.domain.vo.QualityMeasurementVo;
import org.dromara.quality.mapper.QualityDeviceMapper;
import org.dromara.quality.mapper.QualityMeasurementMapper;
import org.dromara.quality.service.IQualityMeasurementService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 实测实量Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RequiredArgsConstructor
@Service
public class QualityMeasurementServiceImpl implements IQualityMeasurementService {

    private final QualityMeasurementMapper baseMapper;
    private final QualityDeviceMapper qualityDeviceMapper;

    /**
     * 查询实测实量
     */
    @Override
    public QualityMeasurementVo queryById(Long measurementId) {
        return baseMapper.selectVoById(measurementId);
    }

    /**
     * 查询实测实量列表
     */
    @Override
    public TableDataInfo<QualityMeasurementVo> queryPageList(QualityMeasurementBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QualityMeasurement> lqw = buildQueryWrapper(bo);
        Page<QualityMeasurementVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询实测实量列表
     */
    @Override
    public List<QualityMeasurementVo> queryList(QualityMeasurementBo bo) {
        LambdaQueryWrapper<QualityMeasurement> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QualityMeasurement> buildQueryWrapper(QualityMeasurementBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QualityMeasurement> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getMeasurementItem()), QualityMeasurement::getMeasurementItem, bo.getMeasurementItem());
        lqw.eq(ObjectUtil.isNotNull(bo.getDeviceId()), QualityMeasurement::getDeviceId, bo.getDeviceId());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), QualityMeasurement::getDeviceName, bo.getDeviceName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceCode()), QualityMeasurement::getDeviceCode, bo.getDeviceCode());
        lqw.eq(StringUtils.isNotBlank(bo.getIsCompliant()), QualityMeasurement::getIsCompliant, bo.getIsCompliant());
        lqw.like(StringUtils.isNotBlank(bo.getProjectName()), QualityMeasurement::getProjectName, bo.getProjectName());
        lqw.eq(StringUtils.isNotBlank(bo.getIsHazardMarked()), QualityMeasurement::getIsHazardMarked, bo.getIsHazardMarked());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), QualityMeasurement::getStatus, bo.getStatus());
        lqw.between(params.get("beginMeasurementTime") != null && params.get("endMeasurementTime") != null,
            QualityMeasurement::getMeasurementTime, params.get("beginMeasurementTime"), params.get("endMeasurementTime"));
        lqw.between(params.get("beginCreateTime") != null && params.get("endCreateTime") != null,
            QualityMeasurement::getCreateTime, params.get("beginCreateTime"), params.get("endCreateTime"));
        lqw.orderByDesc(QualityMeasurement::getMeasurementTime);
        return lqw;
    }

    /**
     * 新增实测实量
     */
    @Override
    public Boolean insertByBo(QualityMeasurementBo bo) {
        QualityMeasurement add = MapstructUtils.convert(bo, QualityMeasurement.class);
        validEntityBeforeSave(add);
        // 根据设备ID填充设备信息
        fillDeviceInfo(bo.getDeviceId(), bo);
        add.setDeviceName(bo.getDeviceName());
        add.setDeviceCode(bo.getDeviceCode());
        add.setMeasurementPerson(LoginHelper.getLoginUser().getNickname());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMeasurementId(add.getMeasurementId());
        }
        return flag;
    }

    /**
     * 修改实测实量
     */
    @Override
    public Boolean updateByBo(QualityMeasurementBo bo) {
        QualityMeasurement update = MapstructUtils.convert(bo, QualityMeasurement.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QualityMeasurement entity) {
        // 校验设备是否存在
        if (ObjectUtil.isNotNull(entity.getDeviceId())) {
            QualityDevice device = qualityDeviceMapper.selectById(entity.getDeviceId());
            if (ObjectUtil.isNull(device)) {
                throw new ServiceException("设备不存在");
            }
        }
    }

    /**
     * 批量删除实测实量
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 推送测量信息
     */
    @Override
    public Boolean pushMeasurementInfo(Collection<Long> measurementIds) {
        LambdaUpdateWrapper<QualityMeasurement> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.in(QualityMeasurement::getMeasurementId, measurementIds)
                    .set(QualityMeasurement::getStatus, "1"); // 1-已推送
        
        int updateCount = baseMapper.update(null, updateWrapper);
        
        // TODO: 这里可以添加实际的推送逻辑，比如发送消息到消息队列、调用第三方接口等
        
        return updateCount > 0;
    }

    /**
     * 标记隐患
     */
    @Override
    public Boolean markHazard(Long measurementId, String hazardDescription) {
        LambdaUpdateWrapper<QualityMeasurement> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(QualityMeasurement::getMeasurementId, measurementId)
                    .set(QualityMeasurement::getIsHazardMarked, "1") // 1-是
                    .set(QualityMeasurement::getHazardDescription, hazardDescription);
        
        return baseMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 取消标记隐患
     */
    @Override
    public Boolean unmarkHazard(Long measurementId) {
        LambdaUpdateWrapper<QualityMeasurement> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(QualityMeasurement::getMeasurementId, measurementId)
                    .set(QualityMeasurement::getIsHazardMarked, "0") // 0-否
                    .set(QualityMeasurement::getHazardDescription, null);
        
        return baseMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 根据设备ID获取设备信息并填充到测量记录
     */
    @Override
    public void fillDeviceInfo(Long deviceId, QualityMeasurementBo bo) {
        if (ObjectUtil.isNotNull(deviceId)) {
            QualityDevice device = qualityDeviceMapper.selectById(deviceId);
            if (ObjectUtil.isNotNull(device)) {
                bo.setDeviceName(device.getDeviceName());
                bo.setDeviceCode(device.getDeviceCode());
            }
        }
    }

} 
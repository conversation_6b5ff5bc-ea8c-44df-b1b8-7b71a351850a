package org.dromara.flow.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.flow.domain.PrjHazardousItemsSpecialWarning;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsSpecialWarningBoToPrjHazardousItemsSpecialWarningMapperImpl implements PrjHazardousItemsSpecialWarningBoToPrjHazardousItemsSpecialWarningMapper {

    @Override
    public PrjHazardousItemsSpecialWarning convert(PrjHazardousItemsSpecialWarningBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsSpecialWarning prjHazardousItemsSpecialWarning = new PrjHazardousItemsSpecialWarning();

        prjHazardousItemsSpecialWarning.setSearchValue( arg0.getSearchValue() );
        prjHazardousItemsSpecialWarning.setCreateDept( arg0.getCreateDept() );
        prjHazardousItemsSpecialWarning.setCreateBy( arg0.getCreateBy() );
        prjHazardousItemsSpecialWarning.setCreateTime( arg0.getCreateTime() );
        prjHazardousItemsSpecialWarning.setUpdateBy( arg0.getUpdateBy() );
        prjHazardousItemsSpecialWarning.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            prjHazardousItemsSpecialWarning.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        prjHazardousItemsSpecialWarning.setWarningId( arg0.getWarningId() );
        prjHazardousItemsSpecialWarning.setTaskId( arg0.getTaskId() );
        prjHazardousItemsSpecialWarning.setReason( arg0.getReason() );
        prjHazardousItemsSpecialWarning.setReasonType( arg0.getReasonType() );

        return prjHazardousItemsSpecialWarning;
    }

    @Override
    public PrjHazardousItemsSpecialWarning convert(PrjHazardousItemsSpecialWarningBo arg0, PrjHazardousItemsSpecialWarning arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setWarningId( arg0.getWarningId() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setReason( arg0.getReason() );
        arg1.setReasonType( arg0.getReasonType() );

        return arg1;
    }
}

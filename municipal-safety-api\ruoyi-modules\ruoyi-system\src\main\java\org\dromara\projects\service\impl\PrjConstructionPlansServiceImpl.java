package org.dromara.projects.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.projects.domain.PrjConstructionPlans;
import org.dromara.projects.domain.PrjExpertReviews;
import org.dromara.projects.domain.bo.AnalyseResultBo;
import org.dromara.projects.domain.bo.PrjConstructionPlansBo;
import org.dromara.projects.domain.vo.PrjConstructionPlansVo;
import org.dromara.projects.domain.vo.PrjExpertReviewsVo;
import org.dromara.projects.mapper.PrjConstructionPlansMapper;
import org.dromara.projects.mapper.PrjExpertReviewsMapper;
import org.dromara.projects.service.IPrjConstructionPlansService;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.mapper.SysOssMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * [项目管理] 存储危大工程专项施工方案信息及其审批状态Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@RequiredArgsConstructor
@Service
public class PrjConstructionPlansServiceImpl implements IPrjConstructionPlansService {

    private final PrjConstructionPlansMapper baseMapper;
    private final SysOssMapper sysOssMapper;
    private final PrjExpertReviewsMapper prjExpertReviewsMapper;

    @Value("${ai.analysis.document-url}")
    private String documentUrl;

    /**
     * 查询[项目管理] 存储危大工程专项施工方案信息及其审批状态
     *
     * @param planId 主键
     * @return [项目管理] 存储危大工程专项施工方案信息及其审批状态
     */
    @Override
    public PrjConstructionPlansVo queryById(Long planId) {
        return baseMapper.selectVoById(planId);
    }

    /**
     * 分页查询[项目管理] 存储危大工程专项施工方案信息及其审批状态列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return [项目管理] 存储危大工程专项施工方案信息及其审批状态分页列表
     */
    @Override
    public TableDataInfo<PrjConstructionPlansVo> queryPageList(PrjConstructionPlansBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PrjConstructionPlans> lqw = buildQueryWrapper(bo);
        Page<PrjConstructionPlansVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        List<PrjConstructionPlansVo> records = result.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {

            List<Long> planIds = records.stream().map(PrjConstructionPlansVo::getPlanId).toList();
            LambdaQueryWrapper<PrjExpertReviews> wrapper = Wrappers.lambdaQuery();
            wrapper.in(PrjExpertReviews::getPlanId, planIds);

            List<PrjExpertReviewsVo> reviewsVos = prjExpertReviewsMapper.selectVoList(wrapper);

            Map<Long, PrjExpertReviewsVo> reviewsVoMap = reviewsVos.stream().collect(Collectors.toMap(PrjExpertReviewsVo::getPlanId, t -> t));

            for (PrjConstructionPlansVo record : records) {
                PrjExpertReviewsVo reviewsVo = reviewsVoMap.get(record.getPlanId());
                record.setConclusion(reviewsVo == null ? null : reviewsVo.getConclusion());
            }
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的[项目管理] 存储危大工程专项施工方案信息及其审批状态列表
     *
     * @param bo 查询条件
     * @return [项目管理] 存储危大工程专项施工方案信息及其审批状态列表
     */
    @Override
    public List<PrjConstructionPlansVo> queryList(PrjConstructionPlansBo bo) {
        LambdaQueryWrapper<PrjConstructionPlans> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PrjConstructionPlans> buildQueryWrapper(PrjConstructionPlansBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PrjConstructionPlans> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PrjConstructionPlans::getPlanId);
        lqw.like(StringUtils.isNotBlank(bo.getPlanName()), PrjConstructionPlans::getPlanName, bo.getPlanName());
        lqw.eq(StringUtils.isNotBlank(bo.getPlanVersion()), PrjConstructionPlans::getPlanVersion, bo.getPlanVersion());
        lqw.eq(StringUtils.isNotBlank(bo.getReviewStatus()), PrjConstructionPlans::getReviewStatus, bo.getReviewStatus());
        lqw.eq(PrjConstructionPlans::getItemId, bo.getItemId());
        return lqw;
    }

    /**
     * 新增[项目管理] 存储危大工程专项施工方案信息及其审批状态
     *
     * @param bo [项目管理] 存储危大工程专项施工方案信息及其审批状态
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PrjConstructionPlansBo bo) {
        PrjConstructionPlans add = MapstructUtils.convert(bo, PrjConstructionPlans.class);
        validEntityBeforeSave(add);

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            //推送ai分析
            this.pushAiAnalyse(add.getPlanId());
            bo.setPlanId(add.getPlanId());
        }
        return flag;
    }

    /**
     * 修改[项目管理] 存储危大工程专项施工方案信息及其审批状态
     *
     * @param bo [项目管理] 存储危大工程专项施工方案信息及其审批状态
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PrjConstructionPlansBo bo) {
        PrjConstructionPlans update = MapstructUtils.convert(bo, PrjConstructionPlans.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PrjConstructionPlans entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除[项目管理] 存储危大工程专项施工方案信息及其审批状态信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 接收文档分析回调接口
     *
     * @param resultBo
     */
    @Override
    public void analyse(AnalyseResultBo resultBo) {
        PrjConstructionPlans plans = baseMapper.selectById(Long.valueOf(resultBo.getTaskId()));

        if (plans != null) {
            if (resultBo.getRawResult().getCode() == 200) {
                plans.setReviewStatus("3");
            } else {
                plans.setReviewStatus("2");
            }
            plans.setAiDefectWarningDetails(resultBo.getRawResult().getData());
            baseMapper.updateById(plans);
        }
    }

    /**
     * 推送ai分析
     *
     * @param plansId
     * @return
     */
    @Override
    public Boolean pushAiAnalyse(Long plansId) {

        PrjConstructionPlans plans = this.baseMapper.selectById(plansId);

        if (plans != null) {

            plans.setReviewStatus("1");

            SysOssVo sysOssVo = sysOssMapper.selectVoById(plans.getPlanDocumentId());

            HttpRequest.post(documentUrl)
                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                .form("taskId", String.valueOf(plans.getPlanId()))
                .form("typeId", "")
                .form("fileUrl", sysOssVo.getUrl())
                // 设置超时时间为20秒
                .timeout(20000)
                .execute()
                .body();

            this.baseMapper.updateById(plans);

            return true;
        }

        return false;
    }
}

package org.dromara.plan.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.github.linpeilie.utils.CollectionUtils;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.ai.domain.AiHazAnalysisTasks;
import org.dromara.ai.domain.dto.AiHazAnalysisTasksDto;
import org.dromara.ai.mapper.AiHazAnalysisTasksMapper;
import org.dromara.ai.service.IAiHazAnalysisTasksService;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.expert.domain.Expert;
import org.dromara.expert.domain.vo.ExpertVo;
import org.dromara.expert.mapper.ExpertMapper;
import org.dromara.expert.service.IExpertService;
import org.dromara.plan.domain.PrjPatrolPlan;
import org.dromara.plan.domain.bo.PrjPatrolPlanBo;
import org.dromara.plan.domain.vo.PrjPatrolPlanVo;
import org.dromara.plan.mapper.PrjPatrolPlanMapper;
import org.dromara.plan.service.IPrjPatrolPlanService;
import org.dromara.projects.domain.PrjProjects;
import org.dromara.projects.domain.bo.PrjHazardousItemsAppBo;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.service.IPrjProjectsService;
import org.dromara.system.domain.SysRole;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.SysUserRole;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.mapper.SysRoleMapper;
import org.dromara.system.mapper.SysUserMapper;
import org.dromara.system.mapper.SysUserRoleMapper;
import org.dromara.system.service.ISysDeptService;
import org.dromara.system.service.ISysUserService;
import org.ehcache.core.util.CollectionUtil;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 巡检计划
 *
 * <AUTHOR> Li
 * @date 2025-06-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/plan/patrolPlan")
public class PrjPatrolPlanController extends BaseController {

    private final IPrjPatrolPlanService prjPatrolPlanService;
    private final IPrjProjectsService prjProjectsService;
    private final IExpertService expertService;
    private final ISysDeptService sysDeptService;
    private final ExpertMapper expertMapper;
    private final ISysUserService sysUserService;
    private final AiHazAnalysisTasksMapper aiHazAnalysisTasksMapper;
    private final SysRoleMapper sysRoleMapper;
    private final PrjPatrolPlanMapper patrolPlanMapper;
    private final SysUserMapper sysUserMapper;
    private final SysUserRoleMapper sysUserRoleMapper;
    private final IAiHazAnalysisTasksService aiHazAnalysisTasksService;


    /**
     * 查询巡检计划列表
     */
    @GetMapping("/list")
    public TableDataInfo<PrjPatrolPlanVo> list(PrjPatrolPlanBo bo, PageQuery pageQuery) {
        PrjPatrolPlanBo roleBo = role(bo);
        TableDataInfo<PrjPatrolPlanVo> prjPatrolPlanVoTableDataInfo = prjPatrolPlanService.queryPageList(roleBo, pageQuery);
        for (PrjPatrolPlanVo row : prjPatrolPlanVoTableDataInfo.getRows()) {
            ArrayList<PrjProjectsVo> prjProjects = new ArrayList<>();
            ArrayList<ExpertVo> expertVos = new ArrayList<>();
            ArrayList<SysDeptVo> deptVos = new ArrayList<>();
            if (StringUtils.isNotBlank(row.getProjectIds())){
                for (String project : row.getProjectIds().split(",")) {
                    PrjProjectsVo prjProjectsVo = prjProjectsService.queryById(Long.valueOf(project));
                    if (!Objects.isNull(prjProjectsVo)){
                        prjProjects.add(prjProjectsVo);
                    }
                }
            }
            if (StringUtils.isNotEmpty(row.getExpertIds())){
                for (String expert : row.getExpertIds().split(",")) {
                    ExpertVo expertVo = expertService.queryById(Long.valueOf(expert));
                    if (!Objects.isNull(expertVo)){
                        expertVos.add(expertVo);
                    }
                }
            }
            if (StringUtils.isNotBlank(row.getDeptIds())){
                for (String deptId : row.getDeptIds().split(",")) {
                    SysDeptVo sysDeptVo = sysDeptService.selectDeptById(Long.valueOf(deptId));
                    if (!Objects.isNull(sysDeptVo)){
                        deptVos.add(sysDeptVo);
                    }
                }
            }
            row.setDeptList(deptVos);
            row.setProjectList(prjProjects);
            row.setExpertList(expertVos);
        }
        return prjPatrolPlanVoTableDataInfo;
    }

    public PrjPatrolPlanBo role(PrjPatrolPlanBo bo){
            // 获取用户id
            Long userId = LoginHelper.getUserId();
            SysUser sysUser = sysUserMapper.selectById(userId);
            // 查询关联角色
            SysUserRole sysUserRole = sysUserRoleMapper.selectOne(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
            SysRoleVo sysRoleVo = sysRoleMapper.selectVoOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleId,sysUserRole.getRoleId()));
            switch (sysRoleVo.getRoleKey()) {
                case "expert": {
                    Expert expert = expertMapper.selectOne(new LambdaQueryWrapper<Expert>().eq(Expert::getIdCard, sysUser.getUserName()));
                    bo.setExpert(expert.getExpertId());
                    return bo;
                }
                case "superadmin":  return bo;
                case "gov_province":  return bo;
                default: {
                    bo.setDeptId(sysUser.getDeptId());
                    return bo;
                }
            }
    }

    /**
     * 导出巡检计划列表
     */
    @Log(title = "巡检计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PrjPatrolPlanBo bo, HttpServletResponse response) {
        List<PrjPatrolPlanVo> list = prjPatrolPlanService.queryList(bo);
        ExcelUtil.exportExcel(list, "巡检计划", PrjPatrolPlanVo.class, response);
    }

    /**
     * 获取巡检计划详细信息
     *
     * @param planId 主键
     */
    @GetMapping("/{planId}")
    public R<PrjPatrolPlanVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long planId) {
        PrjPatrolPlanVo prjPatrolPlanVo = prjPatrolPlanService.queryById(planId);
        ArrayList<PrjProjectsVo> prjProjects = new ArrayList<>();
        ArrayList<ExpertVo> expertVos = new ArrayList<>();
        ArrayList<SysDeptVo> deptVos = new ArrayList<>();
        //  参与部门
        if (StringUtils.isNotBlank(prjPatrolPlanVo.getDeptIds())){
            for (String deptId : prjPatrolPlanVo.getDeptIds().split(",")) {
                SysDeptVo sysDeptVo = sysDeptService.selectDeptById(Long.valueOf(deptId));
                if (!Objects.isNull(sysDeptVo)){
                    deptVos.add(sysDeptVo);
                }
            }
        }
        //  专家信息
        if (StringUtils.isNotEmpty(prjPatrolPlanVo.getExpertIds())){
            for (String expert : prjPatrolPlanVo.getExpertIds().split(",")) {
                ExpertVo expertVo = expertService.queryById(Long.valueOf(expert));
                if (!Objects.isNull(expertVo)){
                    expertVos.add(expertVo);
                }
            }
        }
        // 项目
        if (StringUtils.isNotBlank(prjPatrolPlanVo.getProjectIds())){
            for (String deptId : prjPatrolPlanVo.getProjectIds().split(",")) {
                PrjProjectsVo prjProjectsVo = prjProjectsService.queryById(Long.valueOf(deptId));
                if (!Objects.isNull(prjProjectsVo)){
                    prjProjects.add(prjProjectsVo);
                }
            }
        }
        prjPatrolPlanVo.setDeptList(deptVos);
        prjPatrolPlanVo.setProjectList(prjProjects);
        prjPatrolPlanVo.setExpertList(expertVos);
        return R.ok(prjPatrolPlanVo);
    }

    /**
     * 新增巡检计划
     */
    @Log(title = "巡检计划", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PrjPatrolPlanBo bo) {
        return toAjax(prjPatrolPlanService.insertByBo(bo));
    }

    /**
     * 修改巡检计划
     */
    @Log(title = "巡检计划", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PrjPatrolPlanBo bo) {
        return toAjax(prjPatrolPlanService.updateByBo(bo));
    }

    /**
     * 删除巡检计划
     *
     * @param planIds 主键串
     */
    @Log(title = "巡检计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{planIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] planIds) {
        return toAjax(prjPatrolPlanService.deleteWithValidByIds(List.of(planIds), true));
    }

    /**
     *  本次巡检计划  隐患信息
     */
    @GetMapping("/AIProblem")
    public TableDataInfo AIProblemList(AiHazAnalysisTasksDto dto, PageQuery pageQuery) {
        PrjPatrolPlanVo prjPatrolPlanVo = prjPatrolPlanService.queryById(dto.getPlanId());
        // 部门
        List<String> collect = Arrays.stream(prjPatrolPlanVo.getDeptIds().split(",")).collect(Collectors.toList());
        //  专家
        Arrays.stream(prjPatrolPlanVo.getExpertIds().split(",")).forEach(item -> {
            ExpertVo expertVo = expertMapper.selectVoById(item);
            SysUserVo sysUserVo = sysUserService.selectUserByUserName(expertVo.getIdCard());
            collect.add(String.valueOf(sysUserVo.getUserId()));
        });
        dto.setCreatePeopleList(collect);
        return  aiHazAnalysisTasksService.queryAIPageList(dto, pageQuery);
    }
}

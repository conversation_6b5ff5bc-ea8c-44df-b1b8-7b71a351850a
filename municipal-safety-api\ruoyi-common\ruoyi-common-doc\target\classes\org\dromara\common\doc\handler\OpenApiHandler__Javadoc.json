{"doc": " 自定义 openapi 处理器\n 对源码功能进行修改 增强使用\n", "fields": [{"name": "basicErrorController", "doc": " The Basic error controller.\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": " The Security parser.\n"}, {"name": "mappingsMap", "doc": " The Mappings map.\n"}, {"name": "springdocTags", "doc": " The Springdoc tags.\n"}, {"name": "openApiBuilderCustomisers", "doc": " The Open api builder customisers.\n"}, {"name": "serverBaseUrlCustomizers", "doc": " The server base URL customisers.\n"}, {"name": "springDocConfigProperties", "doc": " The Spring doc config properties.\n"}, {"name": "cachedOpenAPI", "doc": " The Cached open api map.\n"}, {"name": "propertyResolverUtils", "doc": " The Property resolver utils.\n"}, {"name": "javadocProvider", "doc": " The javadoc provider.\n"}, {"name": "context", "doc": " The Context.\n"}, {"name": "openAPI", "doc": " The Open api.\n"}, {"name": "isServersPresent", "doc": " The Is servers present.\n"}, {"name": "serverBaseUrl", "doc": " The Server base url.\n"}], "enumConstants": [], "methods": [], "constructors": [{"name": "<init>", "paramTypes": ["java.util.Optional", "org.springdoc.core.service.SecurityService", "org.springdoc.core.properties.SpringDocConfigProperties", "org.springdoc.core.utils.PropertyResolverUtils", "java.util.Optional", "java.util.Optional", "java.util.Optional"], "doc": " Instantiates a new Open api builder.\n\n @param openAPI                   the open api\n @param securityParser            the security parser\n @param springDocConfigProperties the spring doc config properties\n @param propertyResolverUtils     the property resolver utils\n @param openApiBuilderCustomizers the open api builder customisers\n @param serverBaseUrlCustomizers  the server base url customizers\n @param javadocProvider           the javadoc provider\n"}]}
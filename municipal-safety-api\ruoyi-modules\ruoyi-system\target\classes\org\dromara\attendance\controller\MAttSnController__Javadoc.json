{"doc": " 考勤设备\n\n <AUTHOR>\n @date 2025-05-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.attendance.domain.bo.MAttSnBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询考勤设备列表\n"}, {"name": "selectAll", "paramTypes": ["org.dromara.attendance.domain.bo.MAttSnBo"], "doc": " 查询考勤设备列表\n"}, {"name": "selectMAttSnByProjectId", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 查询设备\n @param projectId\n @param personId\n @return\n"}, {"name": "export", "paramTypes": ["org.dromara.attendance.domain.bo.MAttSnBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出考勤设备列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取考勤设备详细信息\n\n @param snId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.attendance.domain.bo.MAttSnBo"], "doc": " 新增考勤设备(第三方接口)\n"}, {"name": "edit", "paramTypes": ["org.dromara.attendance.domain.bo.MAttSnBo"], "doc": " 修改考勤设备\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除考勤设备\n\n @param snIds 主键串\n"}, {"name": "sendPostRequest", "paramTypes": ["org.dromara.attendance.domain.bo.MAttSnBo", "java.lang.String"], "doc": " 发送POST请求到指定URL\n @return 响应状态码\n @throws Exception 如果请求过程中发生错误\n"}], "constructors": []}
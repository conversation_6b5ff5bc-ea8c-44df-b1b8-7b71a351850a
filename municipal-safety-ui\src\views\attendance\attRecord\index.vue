<template>
    <div class="p-2">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
                    :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item label="规则" prop="ruleId">
                            <el-input v-model="queryParams.ruleId"
                                 placeholder="请输入规则" clearable @keyup.enter="handleQuery"/>
                        </el-form-item>
                        <el-form-item label="身份证号" prop="idNumber">
                            <el-input v-model="queryParams.idNumber"
                                 placeholder="请输入身份证号" clearable @keyup.enter="handleQuery"/>
                        </el-form-item>
                        <el-form-item label="设备号" prop="sn">
                            <el-input v-model="queryParams.sn"
                                placeholder="请输入设备号" clearable @keyup.enter="handleQuery"/>
                        </el-form-item>
<!--                        <el-form-item label="考勤时间" prop="attTime">-->
<!--                            <el-date-picker clearable-->
<!--                                v-model="queryParams.attTime"-->
<!--                                type="date"-->
<!--                                value-format="YYYY-MM-DD"-->
<!--                                placeholder="请选择考勤时间"-->
<!--                            />-->
<!--                        </el-form-item>-->
<!--                        <el-form-item label="考勤日期" prop="attDate">-->
<!--                            <el-date-picker clearable-->
<!--                                v-model="queryParams.attDate"-->
<!--                                type="date"-->
<!--                                value-format="YYYY-MM-DD"-->
<!--                                placeholder="请选择考勤日期"-->
<!--                            />-->
<!--                        </el-form-item>-->
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>
        <el-card shadow="never">
            <template #header>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd"
                                   v-hasPermi="['attendance:attRecord:add']">新增
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
                                   v-hasPermi="['attendance:attRecord:edit']">修改
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
                                   v-hasPermi="['attendance:attRecord:remove']">删除
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="warning" plain icon="Download" @click="handleExport"
                                   v-hasPermi="['attendance:attRecord:export']">导出
                        </el-button>
                    </el-col>
                    <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
            </template>
            <el-table v-loading="loading" :data="attRecordList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center"/>
              <el-table-column label="实时人脸" align="center" prop="realTimeFace" >
                <template #default="scope">
                  <el-image
                    style="width: 80px; height: 80px"
                    :src="'data:image/jpeg;base64,'+ scope.row.realTimeFace"
                    :preview-src-list="['data:image/jpeg;base64,'+ scope.row.realTimeFace]"
                    preview-teleported="true"
                  />
                </template>
              </el-table-column>
                <el-table-column label="姓名" align="center" prop="realName"/>
                <el-table-column label="身份证号" align="center" prop="idNumber"/>
                <el-table-column label="设备号" align="center" prop="sn"/>
                <el-table-column label="考勤时间" align="center" prop="attTime" width="180">
                    <template #default="scope">
                        <span>{{ scope.row.attTime }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="考勤状态" align="center" prop="attResult" width="180">
                    <template #default="scope">
                        <span>{{ scope.row.attResult == '0' ? '正常' : '异常' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="打卡次数" align="center" prop="whichTime" width="180">
                    <template #default="scope">
                        <span>{{ scope.row.whichTime ? scope.row.whichTime : '--' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="打卡来源" align="center" prop="source">
                  <template #default="scope">
                    {{ scope.row.source === 0 ? '考勤机' : '人脸识别' }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-tooltip content="修改" placement="top">
                            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                                       v-hasPermi="['attendance:attRecord:edit']"></el-button>
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top">
                            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                                       v-hasPermi="['attendance:attRecord:remove']"></el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                        v-model:limit="queryParams.pageSize" @pagination="getList"/>
        </el-card>
        <!-- 添加或修改考勤记录对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="50vw" append-to-body>
            <el-form ref="attRecordFormRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="规则" prop="ruleId">
                    <el-input v-model="form.ruleId" placeholder="请输入规则id"/>
                </el-form-item>
                <el-form-item label="考勤时间" prop="attTime">
                  <el-date-picker clearable
                                  v-model="form.attTime"
                                  type="datetime"
                                  value-format="YYYY-MM-DD HH:mm:ss"
                                  placeholder="请选择考勤时间">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="考勤日期" prop="attDate">
                  <el-date-picker clearable
                                  v-model="form.attDate"
                                  type="datetime"
                                  value-format="YYYY-MM-DD HH:mm:ss"
                                  placeholder="请选择考勤日期">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="身份证号" prop="idNumber">
                    <el-input v-model="form.idNumber" placeholder="请输入身份证号"/>
                </el-form-item>
                <el-form-item label="实时人脸" prop="realTimeFace">
                    <el-input v-model="form.realTimeFace" type="textarea" placeholder="请输入内容"/>
                </el-form-item>
                <el-form-item label="设备号" prop="sn">
                    <el-input v-model="form.sn" placeholder="请输入设备号"/>
                </el-form-item>
                <el-form-item label="备注">
                    <editor v-model="form.content" :min-height="192"/>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="AttRecord" lang="ts">
    import {addAttRecord, getAttRecord, listAttRecord, updateAttRecord,delAttRecord} from '@/api/attendance/attRecord';
    import { AttRecordVO, AttRecordForm, AttRecordQuery } from '@/api/attendance/attRecord/types';
    import { TreeVO } from '@/api/demo/tree/types';
    import { delTree } from '@/api/demo/tree';
    import { delExpert } from '@/api/expert/expert';
    const {proxy} = getCurrentInstance() as ComponentInternalInstance;
    const attRecordList = ref<AttRecordVO[]>([]);
    const buttonLoading = ref(false);
    const loading = ref(true);
    const showSearch = ref(true);
    const ids = ref<Array<string | number>>([]);
    const single = ref(true);
    const multiple = ref(true);
    const total = ref(0);
    const queryFormRef = ref<ElFormInstance>();
    const attRecordFormRef = ref<ElFormInstance>();
    const dialog = reactive<DialogOption>({
        visible: false,
        title: ''
    });

    const initFormData: AttRecordForm = {
        id: undefined,
        ruleId: undefined,
        idNumber: undefined,
        realTimeFace: undefined,
        sn: undefined,
        content: undefined,
        attTime: undefined,
        attDate: undefined,
        deleted: undefined
    }
    const data = reactive<PageData<AttRecordForm, AttRecordQuery>>({
        form: {...initFormData},
        queryParams: {
            pageNum: 1,
            pageSize: 10,
            ruleId: undefined,
            idNumber: undefined,
            realTimeFace: undefined,
            sn: undefined,
            content: undefined,
            attTime: undefined,
            attDate: undefined,
            deleted: undefined,
            params: {
            }
        },
        rules: {
            id: [
              {required: true, message: "id不能为空", trigger: "blur" }
            ],
            deleted: [
              {required: true, message: "是否删除。", trigger: "blur" }
            ]
        }
    });

    const {queryParams, form, rules} = toRefs(data);

    /** 查询考勤记录列表 */
    const getList = async () => {
        loading.value = true;
        const res = await listAttRecord(queryParams.value);
            attRecordList.value = res.rows;
        total.value = res.total;
        loading.value = false;
    }

    /** 取消按钮 */
    const cancel = () => {
        reset();
        dialog.visible = false;
    }

    /** 表单重置 */
    const reset = () => {
        form.value = {...initFormData};
            attRecordFormRef.value?.resetFields();
    }

    /** 搜索按钮操作 */
    const handleQuery = () => {
        queryParams.value.pageNum = 1;
        getList();
    }

    /** 重置按钮操作 */
    const resetQuery = () => {
        queryFormRef.value?.resetFields();
        handleQuery();
    }

    /** 多选框选中数据 */
    const handleSelectionChange = (selection: AttRecordVO[]) => {
        ids.value = selection.map(item => item.id);
        single.value = selection.length != 1;
        multiple.value = !selection.length;
    }

    /** 新增按钮操作 */
    const handleAdd = () => {
        reset();
        dialog.visible = true;
        dialog.title = "添加考勤记录";
    }

    /** 修改按钮操作 */
    const handleUpdate = async (row?: AttRecordVO) => {
        reset();
        const _id = row?.id || ids.value[0]
        const res = await getAttRecord(_id);
        Object.assign(form.value, res.data);
        dialog.visible = true;
        dialog.title = "修改考勤记录";
    }

    /** 提交按钮 */
    const submitForm = () => {
            attRecordFormRef.value?.validate(async (valid: boolean) => {
            if (valid) {
                buttonLoading.value = true;
                if (form.value.id) {
                    await updateAttRecord(form.value).finally(() => buttonLoading.value = false);
                } else {
                    await addAttRecord(form.value).finally(() => buttonLoading.value = false);
                }
                proxy?.$modal.msgSuccess("操作成功");
                dialog.visible = false;
                await getList();
            }
        });
    }
    /** 删除按钮操作 */
    const handleDelete = async (row?: AttRecordVO) => {
      console.log(row , 'rowww');
      return
      const _expertIds = row?.expertId || ids.value;
      await proxy?.$modal.confirm('是否确认删除当前数据项？').finally(() => loading.value = false);
      await delAttRecord(_expertIds);
      proxy?.$modal.msgSuccess("删除成功");
      await getList();
    }
    /** 导出按钮操作 */
    const handleExport = () => {
        proxy?.download('attendance/attRecord/export', {
            ...queryParams.value
        }, `attRecord_${new Date().getTime()}.xlsx`)
    }

    onMounted(() => {
        getList();
    });
</script>

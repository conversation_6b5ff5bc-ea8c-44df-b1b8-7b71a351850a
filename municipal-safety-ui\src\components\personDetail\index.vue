<template>
  <div class="personDetail" v-if="personDetail?.personId">
    <el-dialog title="" v-model="detailDialogVisible.visible" @close="handleClose" width="70%">
      <el-descriptions border title="人员基本信息" :column="4">
        <el-descriptions-item label="头像" :rowspan="2" align="center">
          <HeaderPrewiew :src="personDetail.headImgUrl" :width="50" :height="50"
            :preview-src-list="[personDetail.headImgUrl]"></HeaderPrewiew>
        </el-descriptions-item>
        <el-descriptions-item label="姓名" align="center">{{ personDetail.name }}</el-descriptions-item>
        <el-descriptions-item label="性别" align="center">
          <dict-tag :options="sys_user_sex" :value="personDetail.gender" />
        </el-descriptions-item>
        <el-descriptions-item label="身份证号" align="center">{{ personDetail.idCard }}</el-descriptions-item>
        <el-descriptions-item label="手机号" align="center">{{ personDetail.phone }}</el-descriptions-item>
        <el-descriptions-item label="籍贯" align="center">{{ personDetail.nativePlace }}</el-descriptions-item>
        <el-descriptions-item label="文化程度" align="center">
          <dict-tag :options="educational_level_code" :value="personDetail.education" />
        </el-descriptions-item>
        <el-descriptions-item label="政治面貌" align="center">
          <dict-tag :options="politics_status" :value="personDetail.politicalStatus" />
        </el-descriptions-item>
        <el-descriptions-item label="企业名称" align="center">{{ personDetail.enterpriseName
        }}</el-descriptions-item>
        <el-descriptions-item label="企业统一信用代码" align="center">{{ personDetail.unifiedSocialCreditCode
        }}</el-descriptions-item>
        <el-descriptions-item align="center"></el-descriptions-item>
      </el-descriptions>
      <p style="margin: 20px 0;color: #303133;font-size: 16px;font-weight: bold;">资格证书管理</p>
      <el-table v-loading="certLoading" :data="personDetail.qualificationDictVos" show-overflow-tooltip>
        <el-table-column label="专业" align="center" prop="correspondingPosition" min-width="90px" />
        <el-table-column label="证书名称" align="center" prop="certificateType" min-width="130px" />
        <el-table-column label="证书图片" align="center" prop="uploadPhotoUrl" min-width="90px">
          <template #default="scope">
            <header-prewiew :src="scope.row.uploadPhotoUrl" :width="50" :height="50"
              :preview-src-list="[scope.row.uploadPhotoUrl]" />
          </template>
        </el-table-column>
        <el-table-column label="证书编号" align="center" prop="certificateNumber" min-width="130px" />
        <el-table-column label="证书种类" align="center" prop="certificateName" min-width="130px" />
        <el-table-column label="证书等级" align="center" prop="certificateLevel" min-width="90px">
          <template #default="scope">
            <dict-tag :options="certificate_level" :value="scope.row.certificateLevel" />
          </template>
        </el-table-column>
        <el-table-column label="发证机关" align="center" prop="issuingAuthority" min-width="120px" />
        <el-table-column label="证书获取时间" align="center" prop="acquisitionTime" min-width="100px" />
        <el-table-column label="证书有效期" align="center" prop="validTo" min-width="100px" />
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getPerson } from '@/api/person/basic/api'
import HeaderPrewiew from '@/components/ImagePreview/index.vue';
const emit = defineEmits(['update:isShowModel'])
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
import { listQualificationDict } from '@/api/person/qualificationDict/api';
import { QualificationDictVO } from '@/api/person/qualificationDict/types';
const { educational_level_code, sys_user_sex, politics_status, certificate_level } = toRefs<any>(proxy?.useDict('educational_level_code', 'sys_user_sex', 'politics_status', 'certificate_level'));
const props = defineProps({
  isShowModel: {
    type: Boolean,
    default: false,
  },
  personId: {
    type: String,
    default: '',
  }
})
// 控制dialog弹框显隐的变量
const detailDialogVisible = reactive({
  visible: false,
  title: '',
})
// 存放人员详细信息的变量
const personDetail = ref()
const certLoading = ref(false)
// 存放证书类型的所有数据的数组
const qualificationDict = ref<QualificationDictVO[]>([]);
// 新增证书弹框中的证书种类级联选择框的options配置
const QualificationDictOptions = ref<QualificationDictVO[]>([])

watch(() => props.isShowModel, (newVal) => {
  detailDialogVisible.visible = newVal;
  if (newVal) {
    getPersonDetail()
  }
})
// 获取人员详细信息
const getPersonDetail = async () => {
  certLoading.value = true
  const res = await getPerson(props.personId)
  if (res.code === 200) {
    personDetail.value = res.data
    for (let i = 0; i < qualificationDict.value.length; i++) {
      for (let n = 0; n < res.data.qualificationDictVos.length; n++) {
        if (qualificationDict.value[i].id == res.data.qualificationDictVos[n].certificateName) {
          res.data.qualificationDictVos[n].certificateName = qualificationDict.value[i].name;
        }
        if (qualificationDict.value[i].id == res.data.qualificationDictVos[n].certificateType) {
          res.data.qualificationDictVos[n].certificateType = qualificationDict.value[i].name;
        }
      }
    }
  }
  certLoading.value = false
}
// 获取证书种类字典数据
const getQualificationDict = async () => {
  const res = await listQualificationDict();
  const data = proxy?.handleTree<QualificationDictVO>(res.data, "id", "preId");
  if (data) {
    QualificationDictOptions.value = data;
  }
  qualificationDict.value = res.data;
}
const handleClose = () => {
  emit('update:isShowModel', false)
}
onMounted(() => {
  getQualificationDict();
})
</script>

<style lang="scss" scoped></style>
package org.dromara.facility.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.facility.domain.bo.LnSmokeBo;
import org.dromara.facility.domain.vo.LnSmokeVo;
import org.dromara.facility.service.ILnSmokeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 绿能烟感
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/lnSmoke")
public class LnSmokeController extends BaseController {

    private final ILnSmokeService lnSmokeService;

    /**
     * 查询绿能烟感列表
     */
    @GetMapping("/list")
    public TableDataInfo<LnSmokeVo> list(LnSmokeBo bo, PageQuery pageQuery) {
        return lnSmokeService.queryPageList(bo, pageQuery);
    }
}

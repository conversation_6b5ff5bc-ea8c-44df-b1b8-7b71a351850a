{"doc": " 考勤规则Service接口\n\n <AUTHOR>\n @date 2025-05-06\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询考勤规则\n\n @param id 主键\n @return 考勤规则\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRuleBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询考勤规则列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 考勤规则分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRuleBo"], "doc": " 查询符合条件的考勤规则列表\n\n @param bo 查询条件\n @return 考勤规则列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRuleBo"], "doc": " 新增考勤规则\n\n @param bo 考勤规则\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRuleBo"], "doc": " 修改考勤规则\n\n @param bo 考勤规则\n @return 是否修改成功\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除考勤规则信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.projects.mapper.PrjPersonnelMapper">

    <resultMap id="PrjPersonnelResult" type="org.dromara.projects.domain.vo.PrjPersonnelVo">
        <id property="projectPersonnelId" column="project_personnel_id"/>
        <result property="projectId" column="project_id"/>
        <result property="personId" column="person_id"/>
        <result property="userId" column="user_id"/>
        <result property="orgId" column="org_id"/>
        <result property="roleOnProject" column="role_on_project"/>
        <result property="isSpecialOps" column="is_special_ops"/>
        <result property="startDateOnProject" column="start_date_on_project"/>
        <result property="endDateOnProject" column="end_date_on_project"/>
        <!-- 人员基本信息 -->
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="phone" column="phone"/>
        <result property="gender" column="gender"/>
        <result property="education" column="education"/>
        <result property="headImgId" column="head_img_id"/>
        <!-- 企业信息 -->
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="unifiedSocialCreditCode" column="unified_social_credit_code"/>
        <result property="deptId" column="dept_id"/>
        <!-- 头像信息 -->
        <result property="url" column="url"/>
    </resultMap>

    <select id="selectPrjPersonByPersonId" resultMap="PrjPersonnelResult">
        select pp.* from prj_personnel pp
        where pp.project_id = #{projectId}
          and pp.person_id = #{personId}
          and pp.del_flag = 0
    </select>

    <select id="selectPersonnelListByProjectId" resultMap="PrjPersonnelResult">
        SELECT
            pp.*,
            sp.name,
            sp.id_card,
            sp.phone,
            sp.gender,
            sp.education,
            sp.head_img_id,
            se.enterprise_name,
            se.unified_social_credit_code,
            sd.dept_id,
            so.url
        FROM
            prj_personnel pp
        LEFT JOIN
            sys_person sp ON pp.person_id = sp.person_id
        LEFT JOIN
            sys_oss so ON so.oss_id = sp.head_img_mini
        LEFT JOIN
            sys_enterprise_info se ON sp.enterprise_id = se.enterprise_id
        LEFT JOIN
            sys_dept sd ON pp.org_id = sd.dept_id
        WHERE
            pp.project_id = #{projectId}
        and pp.del_flag = 0
    </select>
</mapper>

package org.dromara.facility.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import org.dromara.common.core.domain.R;
import org.dromara.facility.service.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/24 9:55
 * @Description TODO
 * @Version 1.0
 */
@Validated
@RestController
@RequestMapping("/system/baseFacility")
public class BaseFacilityController {

    @Resource
    private IJlTowerRealService jlTowerRealService;

    @Resource
    private IJlLifterRealService jlLifterRealService;

    @Resource
    private IJlDustRealService jlDustRealService;

    @Resource
    private ILnEdgeGuardService lnEdgeGuardService;

    @Resource
    private ILnDumpPlatService lnDumpPlatService;

    @Resource
    private ILnSprayingService lnSprayingService;

    @Resource
    private ILnEnergyService lnEnergyService;

    @Resource
    private ILnNutService lnNutService;

    @Resource
    private ILnWaterService lnWaterService;

    @Resource
    private ILnSmokeService lnSmokeService;

    @Resource
    private ILnHighFormworkRealService lnHighFormworkRealService;

    /**
     * 统一接口
     */
    @SaIgnore
    @PostMapping("/only")
    public R<Void> only(@RequestBody Map<String, Object> objectMap) {

        Object object = objectMap.get("deviceType");

        String str = Convert.toStr(object);

        String jsonString = JSON.toJSONString(objectMap);

        switch (str) {
            case "jl_tower": {
                jlTowerRealService.insertByJson(jsonString);
                break;
            }
            case "jl_lifter": {
                jlLifterRealService.insertByJson(jsonString);
                break;
            }
            case "jl_dust": {
                jlDustRealService.insertByJson(jsonString);
                break;
            }
            case "ln_edgeGuardReal": {
                lnEdgeGuardService.insertByJson(jsonString);
                break;
            }
            case "ln_dump": {
                lnDumpPlatService.insertByJson(jsonString);
                break;
            }
            case "ln_spraying": {
                lnSprayingService.insertByJson(jsonString);
                break;
            }
            case "ln_energy": {
                lnEnergyService.insertByJson(jsonString);
                break;
            }
            case "ln_nut": {
                lnNutService.insertByJson(jsonString);
                break;
            }
            case "ln_water": {
                lnWaterService.insertByJson(jsonString);
                break;
            }
            case "ln_smoke": {
                lnSmokeService.insertByJson(jsonString);
                break;
            }
            case "ln_highFormWork": {
                lnHighFormworkRealService.insertByJson(jsonString);
                break;
            }
        }
        return R.ok();
    }

}

package org.dromara.person.domain.vo;

import java.util.Date;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.person.domain.SysQualification;

import java.io.Serial;
import java.io.Serializable;


/**
 * 人员资格证书视图对象 sys_qualification
 *
 * <AUTHOR> zu <PERSON>
 * @date 2025-05-10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysQualification.class)
public class SysQualificationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long qualificationId;

    /**
     * 关联人员ID
     */
    @ExcelProperty(value = "关联人员ID")
    private Long personId;

    /**
     * 证书种类
     */
    @ExcelProperty(value = "证书种类")
    private String certificateType;

    /**
     * 证书名称
     */
    @ExcelProperty(value = "证书名称")
    private String certificateName;

    /**
     * 证书编号
     */
    @ExcelProperty(value = "证书编号")
    private String certificateNumber;

    /**
     * 获取时间
     */
    @ExcelProperty(value = "获取时间")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date acquisitionTime;

    /**
     * 发证机关
     */
    @ExcelProperty(value = "发证机关")
    private String issuingAuthority;

    /**
     * 证书等级
     */
    @ExcelProperty(value = "证书等级")
    private String certificateLevel;

    /**
     * 资质对应岗位
     */
    @ExcelProperty(value = "资质对应岗位", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "personnel_position")
    private String correspondingPosition;

    /**
     * 有效期起始
     */
    @ExcelProperty(value = "有效期起始")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date validFrom;

    /**
     * 有效期结束
     */
    @ExcelProperty(value = "有效期结束")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date validTo;

    /**
     * 证件照片路径
     */
    @ExcelProperty(value = "证件照片路径")
    private Long uploadPhoto;

    /**
     * 证书地址
     */
    private String uploadPhotoUrl;
}

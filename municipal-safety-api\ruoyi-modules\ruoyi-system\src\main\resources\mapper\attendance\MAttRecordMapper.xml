<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.attendance.mapper.MAttRecordMapper">

    <resultMap type="org.dromara.attendance.domain.MAttRecord" id="MAttRecordResult">
        <result property="id" column="id"/>
        <result property="personId" column="person_id"/>
        <result property="personType" column="person_type"/>
        <result property="realName" column="real_name"/>
        <result property="idNumber" column="id_number"/>
        <result property="realTimeFace" column="real_time_face"/>
        <result property="sn" column="sn"/>
        <result property="source" column="source"/>
        <result property="content" column="content"/>
        <result property="attTime" column="att_time"/>
        <result property="attDate" column="att_date"/>
        <result property="attResult" column="att_result"/>
        <result property="whichTime" column="which_time"/>
        <result property="ruleId" column="rule_id"/>
        <result property="createDept" column="create_dept"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectMAttRuleByAttDate" resultType="org.dromara.attendance.domain.vo.MAttRecordVo">
        select id, real_name, att_date, which_time
        from m_att_record
        where att_date = #{attDate}
            and which_time = #{whichTime}
            and person_id = #{personId}
            and att_result = 0
    </select>

    <select id="selectMAttRecordByPersonIdAndAttDate" resultType="org.dromara.attendance.domain.vo.MAttRecordVo">
        select a.*, ma.sn_name from m_att_record a
        left join m_att_sn ma on ma.sn = a.sn
        where a.person_id = #{personId} and a.att_date = #{attDate}
        order by a.att_time asc
    </select>

    <select id="selectMAttRecordByPersonId" resultType="org.dromara.attendance.domain.vo.MAttRecordVo">
        select * from m_att_record
        where person_id = #{personId}
    </select>

    <insert id="insertMAttRecord" parameterType="org.dromara.attendance.domain.MAttRecord" useGeneratedKeys="true" keyProperty="id">
        insert into m_att_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="personId != null">person_id,</if>
            <if test="personType != null">person_type,</if>
            <if test="realName != null">real_name,</if>
            <if test="idNumber != null">id_number,</if>
            <if test="realTimeFace != null">real_time_face,</if>
            <if test="sn != null">sn,</if>
            <if test="source != null">source,</if>
            <if test="content != null">content,</if>
            <if test="attTime != null">att_time,</if>
            <if test="attDate != null">att_date,</if>
            <if test="attResult != null">att_result,</if>
            <if test="whichTime != null">which_time,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="createDept != null">create_dept,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="personId != null">#{personId},</if>
            <if test="personType != null">#{personType},</if>
            <if test="realName != null">#{realName},</if>
            <if test="idNumber != null">#{idNumber},</if>
            <if test="realTimeFace != null">#{realTimeFace},</if>
            <if test="sn != null">#{sn},</if>
            <if test="source != null">#{source},</if>
            <if test="content != null">#{content},</if>
            <if test="attTime != null">#{attTime},</if>
            <if test="attDate != null">#{attDate},</if>
            <if test="attResult != null">#{attResult},</if>
            <if test="whichTime != null">#{whichTime},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="createDept != null">#{createDept},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
</mapper>

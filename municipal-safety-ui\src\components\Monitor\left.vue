<template>
    <div class="left_box">
        <el-input v-model="filterText" placeholder="查询项目" style="margin-bottom: 10px;" prefix-icon="Search" clearable />
        <el-tree ref="treeRef" :data="leftData" :props="defaultProps" default-expand-all node-key="monitoId"
            :current-node-key="nodeKey" :highlight-current="true" :filter-node-method="filterNode" class="monitorTree"
            @node-click="handleNodeClick" />
    </div>
</template>
<script setup lang="ts">
import type { TreeInstance } from 'element-plus'
import { leftTreeData } from '@/api/monito/monito/index'

interface Tree {
    [key: string]: any
}
const emit = defineEmits(['getMonitorChangeUrl'])
const filterText = ref('')
const nodeKey = ref('1924817279703908353')
const treeRef = ref<TreeInstance>()

const defaultProps = {
    children: 'list',
    label: 'name'
}

const leftData = ref<Tree[]>([])

onMounted(() => {
    getLeftTreeData()
})
watch(filterText, (val) => {
    treeRef.value!.filter(val)
})
// 获取左侧树结构数据的请求
const getLeftTreeData = async () => {
    const res = await leftTreeData()
    if (res.code === 200) {
        leftData.value = res.data
        if (res.data[0].list[0].list[0].list[0].list[0].list[0].monitoId) {
            emit('getMonitorChangeUrl', { deviceCode: res.data[0].list[0].list[0].list[0].list[0].list[0].deviceCode, channelNo: res.data[0].list[0].list[0].list[0].list[0].list[0].channelNo, monitoId: res.data[0].list[0].list[0].list[0].list[0].list[0].monitoId })
        }
    }
}

const filterNode = (value: string, data: Tree, node: any) => {
    if (!value) return true
    let _array = [];//这里使用数组存储 只是为了存储值。
    getReturnNode(node, _array, value);
    let result = false;
    _array.forEach((item) => {
        result = result || item;
    });
    return result;
}
const getReturnNode = (node: any, _array: any[], value: any) => {
    let isPass = node.data && node.data.name && node.data.name.indexOf(value) !== -1;
    isPass ? _array.push(isPass) : '';
    if (!isPass && node.level != 1 && node.parent) {
        getReturnNode(node.parent, _array, value);
    }
}
const handleNodeClick = (data: Tree) => {
    if (data.monitoId) {
        emit('getMonitorChangeUrl', { deviceCode: data.deviceCode, channelNo: data.channelNo })
    }
}
</script>

<style lang="scss">
.left_box {
    width: 100%;
    height: calc(100vh - 160px);
    border: 1px solid #ccc;
    padding: 10px;
    overflow: auto;
}

.monitorTree .el-tree-node .el-tree-node__children,
.el-tree-node__content {
    padding-left: 5px !important;
}
</style>
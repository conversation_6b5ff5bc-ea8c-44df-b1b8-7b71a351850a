package org.dromara.system.service.impl;

import com.alibaba.excel.EasyExcel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.excel.core.ExcelResult;
import org.dromara.system.domain.vo.GovUserImportVo;
import org.dromara.system.listener.GovUserImportListener;
import org.dromara.system.service.IGovUserImportService;
import org.dromara.system.utils.CsvToExcelConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 政府用户导入服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class GovUserImportServiceImpl implements IGovUserImportService {

    @Override
    public R<String> importGovUsers(MultipartFile file, boolean updateSupport) throws IOException {
        try {
            GovUserImportListener listener = new GovUserImportListener(updateSupport);
            EasyExcel.read(file.getInputStream(), GovUserImportVo.class, listener).sheet().doRead();
            ExcelResult<GovUserImportVo> result = listener.getExcelResult();
            return R.ok(result.getAnalysis());
        } catch (Exception e) {
            log.error("导入政府用户失败", e);
            return R.fail("导入失败：" + e.getMessage());
        }
    }

    @Override
    public R<String> batchImportGovUsers(List<Object> govUsers, boolean updateSupport) {
        try {
            GovUserImportListener listener = new GovUserImportListener(updateSupport);

            // 将Object转换为GovUserImportVo
            for (Object obj : govUsers) {
                if (obj instanceof GovUserImportVo) {
                    listener.invoke((GovUserImportVo) obj, null);
                }
            }

            ExcelResult<GovUserImportVo> result = listener.getExcelResult();
            return R.ok(result.getAnalysis());
        } catch (Exception e) {
            log.error("批量导入政府用户失败", e);
            return R.fail("批量导入失败：" + e.getMessage());
        }
    }

    @Override
    public R<String> importFromCsv(String csvContent, boolean updateSupport) {
        try {
            // 将CSV内容转换为政府用户列表
            List<GovUserImportVo> govUsers = CsvToExcelConverter.convertCsvToGovUsers(csvContent);

            if (govUsers.isEmpty()) {
                return R.fail("CSV数据解析失败或数据为空");
            }

            // 使用批量导入方法
            return batchImportGovUsers(govUsers.stream().map(user -> (Object) user).toList(), updateSupport);

        } catch (Exception e) {
            log.error("从CSV导入政府用户失败", e);
            return R.fail("CSV导入失败：" + e.getMessage());
        }
    }

    @Override
    public byte[] downloadTemplate() {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            EasyExcel.write(outputStream, GovUserImportVo.class)
                .sheet("政府用户")
                .doWrite(new ArrayList<>());  // 使用空列表替代null
            return outputStream.toByteArray();
        } catch (IOException e) {
            log.error("下载模板失败", e);
            throw new ServiceException("下载模板失败");
        }
    }
}

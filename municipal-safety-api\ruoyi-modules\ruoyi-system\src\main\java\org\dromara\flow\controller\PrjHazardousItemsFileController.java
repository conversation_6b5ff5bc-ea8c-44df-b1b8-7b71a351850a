package org.dromara.flow.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.flow.domain.PrjHazardousItemsFile;
import org.dromara.flow.domain.bo.PrjHazardousItemsFileBo;
import org.dromara.flow.domain.vo.PrjHazardousItemsFileVo;
import org.dromara.flow.service.IPrjHazardousItemsFileService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;

/**
 * 通用流程附件
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/hazardousItemsFile")
public class PrjHazardousItemsFileController extends BaseController {

    private final IPrjHazardousItemsFileService prjHazardousItemsFileService;
    /**
     * 查询通用流程附件列表
     */
    @SaCheckPermission("system:hazardousItemsFile:list")
    @GetMapping("/list")
    public TableDataInfo<PrjHazardousItemsFileVo> list(PrjHazardousItemsFileBo bo, PageQuery pageQuery) {
        return prjHazardousItemsFileService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出通用流程附件列表
     */
    @SaCheckPermission("system:hazardousItemsFile:export")
    @Log(title = "通用流程附件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PrjHazardousItemsFileBo bo, HttpServletResponse response) {
        List<PrjHazardousItemsFileVo> list = prjHazardousItemsFileService.queryList(bo);
        ExcelUtil.exportExcel(list, "通用流程附件", PrjHazardousItemsFileVo.class, response);
    }

    /**
     * 获取通用流程附件详细信息
     *
     * @param itemFileId 主键
     */
    @SaCheckPermission("system:hazardousItemsFile:query")
    @GetMapping("/{itemFileId}")
    public R<PrjHazardousItemsFileVo> getInfo(@NotNull(message = "主键不能为空")
                                              @PathVariable Long itemFileId) {
        return R.ok(prjHazardousItemsFileService.queryById(itemFileId));
    }

    /**
     * 新增通用流程附件
     */
    @SaCheckPermission("system:hazardousItemsFile:add")
    @Log(title = "通用流程附件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PrjHazardousItemsFileBo bo) {
        return toAjax(prjHazardousItemsFileService.insertByBo(bo));
    }

    /**
     * 修改通用流程附件
     */
    @SaCheckPermission("system:hazardousItemsFile:edit")
    @Log(title = "通用流程附件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PrjHazardousItemsFileBo bo) {
        return toAjax(prjHazardousItemsFileService.updateByBo(bo));
    }

    /**
     * 修改通用流程附件
     */
    @Log(title = "通用流程附件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/updBat")
    public R<Void> edit(@RequestBody List<PrjHazardousItemsFile> bos) {
        return toAjax(prjHazardousItemsFileService.updateByBos(bos));
    }

    /**
     * 删除通用流程附件
     *
     * @param itemFileIds 主键串
     */
    @SaCheckPermission("system:hazardousItemsFile:remove")
    @Log(title = "通用流程附件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemFileIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemFileIds) {
        return toAjax(prjHazardousItemsFileService.deleteWithValidByIds(List.of(itemFileIds), true));
    }

}

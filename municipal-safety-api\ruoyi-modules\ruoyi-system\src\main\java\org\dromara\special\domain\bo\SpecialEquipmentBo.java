package org.dromara.special.domain.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.dromara.special.domain.SpecialEquipment;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 特种设备业务对象 special_equipment
 *
 * <AUTHOR> Li
 * @date 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SpecialEquipment.class, reverseConvertGenerate = false)
public class SpecialEquipmentBo extends BaseEntity {

    /**
     * 特种设备ID
     */
    @NotNull(message = "特种设备ID不能为空", groups = { EditGroup.class })
    private Long equipmentId;

    /** 施工许可证编号 */
    private String constructionPermitNum;

    /** 备案编号 */
    private String recordNumber;

    /** 设备类型 */
    private String equipmentCategory;

    /**  设备名称 */
    private String equipmentName;

    /**
     * 规格型号
     */
    private String modelSpec;

    /**
     * 生产厂商
     */
    private String manufacturer;

    /**
     * 生产厂商社会统一信用代码
     */
    private String manufacturerCode;

    /**
     * 出厂编号
     */
    private String factoryNumber;

    /** 出厂日期 */
    private Date factoryDate;

    /** 特种设备生产许可证编号 */
    private String productionLicense;

    /** 使用年限 */
    private String useYears;

    /**
     * 产权单位
     */
    private String propertyOwner;

    /** 产权单位统一社会代码 */
    private String propertyOwnerCode;

    /** 产权单位地址 */
    private String propertyOwnerAddress;

    /** 企业法人 */
    private String legalPerson;

    /** 企业法人证件号 */
    private String legalPersonLicense;

    /** 联系人 */
    private String contacts;

    /** 联系人电话 */
    private String contactsPhone;

    /** 价格 */
    private BigDecimal price;

    /** 购买日期 */
    private Date purchaseDate;

    /**  备案机关 */
    private String authority;

    /** 发证机关代码 */
    private String authorityCode;

    /** 机械所在地市 */
    private String locationCity;

    /**  机械所在区县 */
    private String locationCounty;

    /** 机械所在区域 */
    private String locationArea;


    // 机器 数据字段
    /** 额定起重量 T */
    private BigDecimal towerCraneWeight;

    /** 额定起重力矩（T·M） */
    private BigDecimal weightTorque;

    /** 起重臂长度（M） */
    private BigDecimal weightLength;

    /**  最大工作幅度（M） */
    private BigDecimal workRange;

    /** 最大幅度额定起重量（T） */
    private BigDecimal workRangeWeight;

    /** 独起升高度（M） */
    private BigDecimal improveHeight;

    /** 最大起升高度（M） */
    private BigDecimal liftingHeight;

    /** 主要结构件唯一编号 */
    private String structureNumber;

    /** 塔式起重机拟安装最大高度（m） */
    private BigDecimal maxHeight;

    /** 标准节主要结构件规格 */
    private String standardSectionSpecifications;

    /** 加强节参数（长×宽×高）mm */
    private String strengthenSection;

    /** 标准节参数（长×宽×高）mm */
    private String standardSection;

    /** 电动机总功率（kW） */
    private BigDecimal elevatorPower;

    /** 额定提升速度（m/min） */
    private BigDecimal improveSpeed;

    /** 防坠安全器型号 */
    private String safetyModel;

    /** 运载装置（吊笼）净空尺寸（长×宽×高）m */
    private String constructionElevatorSize;

    /** 门式起重机跨度（m） */
    private String maxSpan;

    /**
     * 使用登记证书发证机关
     */
    private String issuer;

    /**
     * 使用登记证书编号
     */
    private String certificateNumber;

    /**
     * 使用登记证书发证日期
     */
    private Date issueDate;

    /**
     * 使用登记证书id
     */
    private Long useRegistrationCertificate;

    /**
     * 工程名称
     */
    private String projectName;

    /**
     * 工程项目地址
     */
    private String projectAddress;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目工程ID
     */
    private Long itemId;

    /**
     * 使用单位
     */
    private String usageUnit;

    /**
     * 维保单位
     */
    private String maintenanceUnit;

    /**
     * 安装单位
     */
    private String installationUnit;

    /**
     * 检测单位
     */
    private String inspectionUnit;

    /**
     * 项目负责人
     */
    private String projectManager;

    /**
     * 安装日期
     */
    private Date installationDate;

    /**
     * 检测日期
     */
    private Date inspectionDate;

    /**
     * 进场日期
     */
    private Date enterDate;

    /**
     * 退场日期
     */
    private Date exitDate;

    /**
     * 存放地点（使用部位）
     */
    private String location;

    /**
     * 特种作业人员ID(操作人员)
     */
    private Long sopId;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

    /**
     * 设备编号
     */
    private String devNo;

    /** 设备现场编号 */
    private String projectCraneNum;

    /** 塔机类型 */
    private String craneType;
}

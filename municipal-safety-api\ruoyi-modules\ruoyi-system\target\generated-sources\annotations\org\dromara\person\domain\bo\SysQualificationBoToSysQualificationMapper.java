package org.dromara.person.domain.bo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.person.domain.SysQualification;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {},
    imports = {}
)
public interface SysQualificationBoToSysQualificationMapper extends BaseMapper<SysQualificationBo, SysQualification> {
}

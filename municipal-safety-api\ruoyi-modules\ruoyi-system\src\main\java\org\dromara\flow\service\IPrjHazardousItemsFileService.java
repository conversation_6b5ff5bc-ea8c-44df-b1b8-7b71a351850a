package org.dromara.flow.service;

import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.flow.domain.PrjHazardousItemsFile;
import org.dromara.flow.domain.bo.PrjHazardousItemsFileBo;
import org.dromara.flow.domain.vo.PrjHazardousItemsFileVo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 通用流程附件Service接口
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface IPrjHazardousItemsFileService {

    /**
     * 查询通用流程附件
     *
     * @param itemFileId 主键
     * @return 通用流程附件
     */
    PrjHazardousItemsFileVo queryById(Long itemFileId);

    /**
     * 分页查询通用流程附件列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 通用流程附件分页列表
     */
    TableDataInfo<PrjHazardousItemsFileVo> queryPageList(PrjHazardousItemsFileBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的通用流程附件列表
     *
     * @param bo 查询条件
     * @return 通用流程附件列表
     */
    List<PrjHazardousItemsFileVo> queryList(PrjHazardousItemsFileBo bo);

    /**
     * 新增通用流程附件
     *
     * @param bo 通用流程附件
     * @return 是否新增成功
     */
    Boolean insertByBo(PrjHazardousItemsFileBo bo);

    /**
     * 修改通用流程附件
     *
     * @param bo 通用流程附件
     * @return 是否修改成功
     */
    Boolean updateByBo(PrjHazardousItemsFileBo bo);

    /**
     * 校验并批量删除通用流程附件信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量更新文件
     *
     * @return 是否更新成功
     */
    boolean updateByBos(List<PrjHazardousItemsFile> bos);
}

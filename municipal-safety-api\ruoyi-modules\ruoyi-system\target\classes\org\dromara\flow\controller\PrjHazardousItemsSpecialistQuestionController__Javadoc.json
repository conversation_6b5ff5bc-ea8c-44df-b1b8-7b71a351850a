{"doc": " 省厅自动工单专家建议\n\n <AUTHOR>\n @date 2025-06-21\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistQuestionBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询省厅自动工单专家建议列表\n"}, {"name": "export", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistQuestionBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出省厅自动工单专家建议列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取省厅自动工单专家建议详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistQuestionBo"], "doc": " 新增省厅自动工单专家建议\n"}, {"name": "edit", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistQuestionBo"], "doc": " 修改省厅自动工单专家建议\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除省厅自动工单专家建议\n\n @param ids 主键串\n"}, {"name": "addBatch", "paramTypes": ["java.util.List"], "doc": " 批量新增省厅自动工单专家建议\n"}, {"name": "detail", "paramTypes": ["java.lang.String"], "doc": " 查询专家意见\n"}, {"name": "detailAll", "paramTypes": ["java.lang.String"], "doc": " 查询所有专家意见\n"}], "constructors": []}
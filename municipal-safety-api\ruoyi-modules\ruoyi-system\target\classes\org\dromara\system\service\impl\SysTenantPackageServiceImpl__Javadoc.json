{"doc": " 租户套餐Service业务层处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询租户套餐\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询租户套餐列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": " 查询租户套餐列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": " 新增租户套餐\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": " 修改租户套餐\n"}, {"name": "checkPackageNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": " 校验套餐名称是否唯一\n"}, {"name": "updatePackageStatus", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": " 修改套餐状态\n\n @param bo 套餐信息\n @return 结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 批量删除租户套餐\n"}], "constructors": []}
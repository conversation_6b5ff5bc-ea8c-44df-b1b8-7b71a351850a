package org.dromara.flow.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.flow.domain.PrjHazardousItemsSpecialWarning;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 特殊预警视图对象 prj_hazardous_items_special_warning
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjHazardousItemsSpecialWarning.class)
public class PrjHazardousItemsSpecialWarningVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 特殊预警id
     */
    @ExcelProperty(value = "特殊预警id")
    private Long warningId;

    /**
     * 流程业务id
     */
    @ExcelProperty(value = "流程业务id")
    private String taskId;

    /**
     * 预警原因（质监站可补充）
     */
    @ExcelProperty(value = "预警原因", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "质=监站可补充")
    private String reason;

    /**
     * 预警类型(字典special_warning_type)
     */
    @ExcelProperty(value = "预警类型(字典special_warning_type)")
    private String reasonType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 工程id
     */
    private Long itemId;

    /**
     * 工程名称
     */
    private String itemName;

    /**
     * ai识别id
     */
    private Long hazAnalysisId;

    /**
     * 业务流程id
     */
    private String businessId;
}

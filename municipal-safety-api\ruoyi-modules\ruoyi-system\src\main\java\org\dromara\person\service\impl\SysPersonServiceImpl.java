package org.dromara.person.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.person.domain.QualificationDict;
import org.dromara.person.domain.SysPerson;
import org.dromara.person.domain.SysQualification;
import org.dromara.person.domain.bo.SysPersonBo;
import org.dromara.person.domain.vo.QualificationDictVo;
import org.dromara.person.domain.vo.SysPersonVo;
import org.dromara.person.domain.vo.SysQualificationVo;
import org.dromara.person.mapper.QualificationDictMapper;
import org.dromara.person.mapper.SysPersonMapper;
import org.dromara.person.mapper.SysQualificationMapper;
import org.dromara.person.service.ISysPersonService;
import org.dromara.system.domain.SysEnterpriseInfo;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.mapper.SysEnterpriseInfoMapper;
import org.dromara.system.service.ISysOssService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 人员基本信息Service业务层处理
 *
 * <AUTHOR> zu da
 * @date 2025-05-09
 */
@RequiredArgsConstructor
@Service
public class SysPersonServiceImpl implements ISysPersonService {

    private final SysPersonMapper baseMapper;
    private final ISysOssService iSysOssService;
    private final SysEnterpriseInfoMapper sysEnterpriseInfoMapper;
    private final SysQualificationMapper sysQualificationMapper;
    private final QualificationDictMapper qualificationDictMapper;

    /**
     * 查询人员基本信息
     *
     * @param personId 主键
     * @return 人员基本信息
     */
    @Override
    public SysPersonVo queryById(Long personId) {
        SysPersonVo sysPersonVo = baseMapper.selectVoById(personId);

        //查询头像 - 添加null检查
        if (sysPersonVo.getHeadImgId() != null) {
            SysOssVo oss = iSysOssService.getById(sysPersonVo.getHeadImgId());
            if (oss != null) {
                sysPersonVo.setHeadImgUrl(oss.getUrl());
            }
        }

        //企业名称和信用代码
        if (sysPersonVo.getEnterpriseId() != null) {
            SysEnterpriseInfo enterpriseInfo = sysEnterpriseInfoMapper.selectById(sysPersonVo.getEnterpriseId());
            if (enterpriseInfo != null) {
                sysPersonVo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                sysPersonVo.setUnifiedSocialCreditCode(enterpriseInfo.getUnifiedSocialCreditCode());
            }
        }

        List<SysQualificationVo> qualificationVos = sysQualificationMapper.selectVoList(new LambdaQueryWrapper<SysQualification>()
            .eq(SysQualification::getPersonId, sysPersonVo.getPersonId()));

        if (!qualificationVos.isEmpty()) {
            List<Long> ossIds = qualificationVos.stream()
                .map(SysQualificationVo::getUploadPhoto)
                .filter(Objects::nonNull) // 过滤掉null值
                .toList();

            if (!ossIds.isEmpty()) {
                List<SysOssVo> sysOssVos = iSysOssService.listByIds(ossIds);
                Map<Long, String> ossMap = sysOssVos.stream().collect(Collectors.toMap(SysOssVo::getOssId, SysOssVo::getUrl));
                qualificationVos.forEach(s -> s.setUploadPhotoUrl(ossMap.getOrDefault(s.getUploadPhoto(), null)));
            }
        }

        sysPersonVo.setQualificationDictVos(qualificationVos);

        return sysPersonVo;
    }

    /**
     * 分页查询人员基本信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 人员基本信息分页列表
     */
    @Override
    public TableDataInfo<SysPersonVo> queryPageList(SysPersonBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysPerson> lqw = buildQueryWrapper(bo);

        Page<SysPersonVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        TableDataInfo<SysPersonVo> tableDataInfo = TableDataInfo.build(result);

        if (tableDataInfo.getRows().isEmpty()) {
            return tableDataInfo;
        }

        List<SysPersonVo> rows = tableDataInfo.getRows();

        List<Long> imgIds = rows.stream().map(SysPersonVo::getHeadImgId).filter(ObjectUtils::isNotEmpty).collect(Collectors.toList());

        //头像地址
        List<SysOssVo> sysOssVos = iSysOssService.listByIds(imgIds);
        Map<Long, SysOssVo> ossVoMap = sysOssVos.stream().collect(Collectors.toMap(SysOssVo::getOssId, t -> t));

        //企业名称和信用代码
        List<Long> enterPriseId = rows.stream().map(SysPersonVo::getEnterpriseId).toList();

        List<SysEnterpriseInfo> sysEnterpriseInfos = sysEnterpriseInfoMapper.selectByIds(enterPriseId);
        Map<Long, SysEnterpriseInfo> enterpriseInfoMap = sysEnterpriseInfos.stream().collect(Collectors.toMap(SysEnterpriseInfo::getEnterpriseId, t -> t));

        //查询证书
        List<Long> personIds = rows.stream().map(SysPersonVo::getPersonId).toList();
        List<QualificationDictVo> dictVos = qualificationDictMapper.selectVoList(new LambdaQueryWrapper<QualificationDict>().select(QualificationDict::getId, QualificationDict::getName));
        Map<Long, String> dictMap = dictVos.stream().collect(Collectors.toMap(QualificationDictVo::getId, QualificationDictVo::getName));

        List<SysQualificationVo> voList = sysQualificationMapper.selectVoList(new LambdaQueryWrapper<SysQualification>()
            .in(SysQualification::getPersonId, personIds)
            .select(SysQualification::getPersonId, SysQualification::getCertificateType, SysQualification::getCertificateName));
        Map<Long, List<SysQualificationVo>> qualificationMap = voList.stream().collect(Collectors.groupingBy(SysQualificationVo::getPersonId));

        rows.forEach(r -> {
            r.setHeadImgUrl(Optional.ofNullable(ossVoMap.get(r.getHeadImgId())).map(SysOssVo::getUrl).orElse(null));
            r.setEnterpriseName(Optional.ofNullable(enterpriseInfoMap.get(r.getEnterpriseId())).map(SysEnterpriseInfo::getEnterpriseName).orElse(null));
            r.setUnifiedSocialCreditCode(Optional.ofNullable(enterpriseInfoMap.get(r.getEnterpriseId())).map(SysEnterpriseInfo::getUnifiedSocialCreditCode).orElse(null));

            List<SysQualificationVo> qualificationVos = qualificationMap.get(r.getPersonId());
            if (qualificationVos != null && !qualificationVos.isEmpty()) {
                StringBuilder builder = new StringBuilder();
                for (SysQualificationVo qualificationVo : qualificationVos) {
                    String certificateType = qualificationVo.getCertificateType();
                    String certificateName = qualificationVo.getCertificateName();
                    builder.append(Optional.ofNullable(certificateType).map(s -> dictMap.get(Long.valueOf(s))).orElse(""))
                        .append("/")
                        .append(Optional.ofNullable(certificateName).map(s -> dictMap.get(Long.valueOf(s))).orElse(""))
                        .append("<br>");
                }
                r.setQualificationDetail(builder.toString());
            }
        });

        return tableDataInfo;
    }

    /**
     * 查询符合条件的人员基本信息列表
     *
     * @param bo 查询条件
     * @return 人员基本信息列表
     */
    @Override
    public List<SysPersonVo> queryList(SysPersonBo bo) {
        LambdaQueryWrapper<SysPerson> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysPerson> buildQueryWrapper(SysPersonBo bo) {
        LambdaQueryWrapper<SysPerson> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SysPerson::getPersonId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), SysPerson::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getIdCard()), SysPerson::getIdCard, bo.getIdCard());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), SysPerson::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getNativePlace()), SysPerson::getNativePlace, bo.getNativePlace());
        lqw.eq(StringUtils.isNotBlank(bo.getGender()), SysPerson::getGender, bo.getGender());
        lqw.eq(StringUtils.isNotBlank(bo.getPoliticalStatus()), SysPerson::getPoliticalStatus, bo.getPoliticalStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getEducation()), SysPerson::getEducation, bo.getEducation());

        lqw.eq(ObjectUtils.isNotEmpty(bo.getEnterpriseId()), SysPerson::getEnterpriseId, bo.getEnterpriseId());

        //不是超管限制
        if (!LoginHelper.isSuperAdmin()) {
            lqw.eq(SysPerson::getEnterpriseId, LoginHelper.getEnterpriseId());
        } else if (LoginHelper.isSuperAdmin()) {
            lqw.eq(ObjectUtils.isNotEmpty(bo.getEnterpriseId()), SysPerson::getEnterpriseId, bo.getEnterpriseId());
        }

        //证书类型
        if (StringUtils.isNotEmpty(bo.getCertificateType()) || StringUtils.isNotEmpty(bo.getCertificateName())) {
            //查询指定证书类型的人员
            LambdaQueryWrapper<SysQualification> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StringUtils.isNotBlank(bo.getCertificateType()), SysQualification::getCertificateType, bo.getCertificateType())
                .eq(StringUtils.isNotBlank(bo.getCertificateName()), SysQualification::getCertificateName, bo.getCertificateName())
                .select(SysQualification::getPersonId);

            List<SysQualificationVo> qualificationVos = sysQualificationMapper.selectVoList(wrapper);
            List<Long> personIds = qualificationVos.stream().map(SysQualificationVo::getPersonId).toList();
            if (personIds.isEmpty()) {
                personIds = new ArrayList<>();
                personIds.add(0L);
            }
            lqw.in(SysPerson::getPersonId, personIds);
        }

        return lqw;
    }

    /**
     * 新增人员基本信息
     *
     * @param bo 人员基本信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SysPersonBo bo) {
        SysPerson add = MapstructUtils.convert(bo, SysPerson.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setPersonId(add.getPersonId());
        }
        return flag;
    }

    /**
     * 修改人员基本信息
     *
     * @param bo 人员基本信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SysPersonBo bo) {
        SysPerson update = MapstructUtils.convert(bo, SysPerson.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean moveOutByBo(SysPersonBo sysPersonBo) {
        SysPerson update = MapstructUtils.convert(sysPersonBo, SysPerson.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysPerson entity) {
        //TODO 做一些数据校验,如唯一约束
        if (!LoginHelper.isEnterPrise() && !LoginHelper.isSuperAdmin()) {
            throw new ServiceException("您不是企业用户!");
        }
        // 如果entity中已经设置了enterpriseId，则使用设置的值
        // 否则使用当前登录用户的企业ID
        if (entity.getEnterpriseId() == null) {
            entity.setEnterpriseId(LoginHelper.getEnterpriseId());
        }
    }

    /**
     * 校验并批量删除人员基本信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 是否还有挂载的证书
            LambdaQueryWrapper<SysQualification> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(SysQualification::getPersonId, ids)
                .select(SysQualification::getPersonId);

            List<SysQualificationVo> qualificationVos = sysQualificationMapper.selectVoList(wrapper);
            List<Long> list = qualificationVos.stream().map(SysQualificationVo::getPersonId).distinct().toList();

            for (Long id : ids) {
                if (list.contains(id)) {
                    throw new ServiceException("人员还存在证书，无法删除!");
                }
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public Long getPersonIdByIdCard(String idCard) {
        if (StringUtils.isEmpty(idCard)) {
            return null;
        }
        return baseMapper.selectPersonIdByIdCard(idCard);
    }

    @Override
    public SysPersonVo queryByIdCard(String idCard) {
        return baseMapper.queryByIdCard(idCard);
    }
}

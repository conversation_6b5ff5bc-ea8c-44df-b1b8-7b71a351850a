package org.dromara.quality.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备统计详细数据VO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeviceStatisticsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备总数
     */
    private Integer total;

    /**
     * 正常状态设备数量
     */
    private Integer normalCount;

    /**
     * 停用状态设备数量
     */
    private Integer disabledCount;

    /**
     * 设备数量较上月增长百分比
     */
    private Integer increase;

    /**
     * 设备数量较上月增长率
     */
    private Double increaseRate;
} 
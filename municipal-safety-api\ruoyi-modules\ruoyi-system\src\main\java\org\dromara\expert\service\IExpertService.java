package org.dromara.expert.service;

import org.dromara.common.core.domain.R;
import org.dromara.expert.domain.vo.ExpertVo;
import org.dromara.expert.domain.bo.ExpertBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 *  专家主Service接口
 * @date 2025-05-03
 */
public interface IExpertService {

    /**
     * 查询 专家主
     *
     * @param expertId 主键
     * @return  专家主
     */
    ExpertVo queryById(Long expertId);

    /**
     * 分页查询 专家主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return  专家主分页列表
     */
    TableDataInfo<ExpertVo> queryPageList(ExpertBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的 专家主列表
     *
     * @param bo 查询条件
     * @return  专家主列表
     */
    List<ExpertVo> queryList(ExpertBo bo);

    /**
     * 新增 专家主
     *
     * @param bo  专家主
     * @return 是否新增成功
     */
    R insertByBo(ExpertBo bo);

    /**
     * 修改 专家主
     *
     * @param bo  专家主
     * @return 是否修改成功
     */
    Boolean updateByBo(ExpertBo bo);

    /**
     * 校验并批量删除 专家主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

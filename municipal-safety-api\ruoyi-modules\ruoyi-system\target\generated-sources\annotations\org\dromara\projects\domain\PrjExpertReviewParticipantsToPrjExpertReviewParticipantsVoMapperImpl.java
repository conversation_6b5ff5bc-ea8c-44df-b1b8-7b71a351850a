package org.dromara.projects.domain;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.vo.PrjExpertReviewParticipantsVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:15+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjExpertReviewParticipantsToPrjExpertReviewParticipantsVoMapperImpl implements PrjExpertReviewParticipantsToPrjExpertReviewParticipantsVoMapper {

    @Override
    public PrjExpertReviewParticipantsVo convert(PrjExpertReviewParticipants arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjExpertReviewParticipantsVo prjExpertReviewParticipantsVo = new PrjExpertReviewParticipantsVo();

        prjExpertReviewParticipantsVo.setReviewId( arg0.getReviewId() );
        prjExpertReviewParticipantsVo.setUserId( arg0.getUserId() );
        prjExpertReviewParticipantsVo.setRoleInMeeting( arg0.getRoleInMeeting() );
        prjExpertReviewParticipantsVo.setIsAttendingExpert( arg0.getIsAttendingExpert() );

        return prjExpertReviewParticipantsVo;
    }

    @Override
    public PrjExpertReviewParticipantsVo convert(PrjExpertReviewParticipants arg0, PrjExpertReviewParticipantsVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setReviewId( arg0.getReviewId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setRoleInMeeting( arg0.getRoleInMeeting() );
        arg1.setIsAttendingExpert( arg0.getIsAttendingExpert() );

        return arg1;
    }
}

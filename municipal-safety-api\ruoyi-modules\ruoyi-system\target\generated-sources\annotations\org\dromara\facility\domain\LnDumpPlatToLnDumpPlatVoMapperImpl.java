package org.dromara.facility.domain;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.vo.LnDumpPlatVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:15+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnDumpPlatToLnDumpPlatVoMapperImpl implements LnDumpPlatToLnDumpPlatVoMapper {

    @Override
    public LnDumpPlatVo convert(LnDumpPlat arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnDumpPlatVo lnDumpPlatVo = new LnDumpPlatVo();

        lnDumpPlatVo.setId( arg0.getId() );
        lnDumpPlatVo.setDumpnumber( arg0.getDumpnumber() );
        lnDumpPlatVo.setWeightMax( arg0.getWeightMax() );
        lnDumpPlatVo.setWeight( arg0.getWeight() );
        lnDumpPlatVo.setTilt( arg0.getTilt() );
        lnDumpPlatVo.setBatvolt( arg0.getBatvolt() );
        lnDumpPlatVo.setWightPercent( arg0.getWightPercent() );
        lnDumpPlatVo.setTiltPercentX( arg0.getTiltPercentX() );
        lnDumpPlatVo.setTiltPercentY( arg0.getTiltPercentY() );
        lnDumpPlatVo.setAlarmInfo( arg0.getAlarmInfo() );
        lnDumpPlatVo.setStatus( arg0.getStatus() );
        lnDumpPlatVo.setIdleWeightReal( arg0.getIdleWeightReal() );
        lnDumpPlatVo.setLoadWeightReal( arg0.getLoadWeightReal() );
        lnDumpPlatVo.setWeightWarning( arg0.getWeightWarning() );
        lnDumpPlatVo.setWeightAlarm( arg0.getWeightAlarm() );
        lnDumpPlatVo.setTiltWarning( arg0.getTiltWarning() );
        lnDumpPlatVo.setTiltAlarm( arg0.getTiltAlarm() );
        lnDumpPlatVo.setDeviceIp( arg0.getDeviceIp() );
        lnDumpPlatVo.setRealTiltX( arg0.getRealTiltX() );
        lnDumpPlatVo.setRealTiltY( arg0.getRealTiltY() );
        lnDumpPlatVo.setDevNo( arg0.getDevNo() );
        lnDumpPlatVo.setCreateTime( arg0.getCreateTime() );

        return lnDumpPlatVo;
    }

    @Override
    public LnDumpPlatVo convert(LnDumpPlat arg0, LnDumpPlatVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setDumpnumber( arg0.getDumpnumber() );
        arg1.setWeightMax( arg0.getWeightMax() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setTilt( arg0.getTilt() );
        arg1.setBatvolt( arg0.getBatvolt() );
        arg1.setWightPercent( arg0.getWightPercent() );
        arg1.setTiltPercentX( arg0.getTiltPercentX() );
        arg1.setTiltPercentY( arg0.getTiltPercentY() );
        arg1.setAlarmInfo( arg0.getAlarmInfo() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setIdleWeightReal( arg0.getIdleWeightReal() );
        arg1.setLoadWeightReal( arg0.getLoadWeightReal() );
        arg1.setWeightWarning( arg0.getWeightWarning() );
        arg1.setWeightAlarm( arg0.getWeightAlarm() );
        arg1.setTiltWarning( arg0.getTiltWarning() );
        arg1.setTiltAlarm( arg0.getTiltAlarm() );
        arg1.setDeviceIp( arg0.getDeviceIp() );
        arg1.setRealTiltX( arg0.getRealTiltX() );
        arg1.setRealTiltY( arg0.getRealTiltY() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}

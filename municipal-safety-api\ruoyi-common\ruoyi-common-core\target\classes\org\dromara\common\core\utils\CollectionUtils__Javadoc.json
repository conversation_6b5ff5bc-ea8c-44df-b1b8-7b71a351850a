{"doc": " Collection 工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "diffList", "paramTypes": ["java.util.Collection", "java.util.Collection", "java.util.function.BiFunction"], "doc": " 对比老、新两个列表，找出新增、修改、删除的数据\n\n @param oldList  老列表\n @param newList  新列表\n @param sameFunc 对比函数，返回 true 表示相同，返回 false 表示不同\n                 注意，same 是通过每个元素的“标识”，判断它们是不是同一个数据\n @return [新增列表、修改列表、删除列表]\n"}, {"name": "toLinkedHashSet", "paramTypes": ["java.lang.Class", "java.lang.Object"], "doc": " 转换为 LinkedHashSet\n\n @param <T>         元素类型\n @param elementType 集合中元素类型\n @param value       被转换的值\n @return {@link LinkedHashSet}\n"}], "constructors": []}
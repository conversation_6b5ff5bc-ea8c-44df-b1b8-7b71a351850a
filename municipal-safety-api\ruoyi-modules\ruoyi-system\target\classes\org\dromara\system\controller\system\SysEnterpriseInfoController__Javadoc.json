{"doc": " 企业信息\n\n <AUTHOR>\n @date 2025-05-08\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.SysEnterpriseInfo"], "doc": " 查询企业信息列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.SysEnterpriseInfo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出企业信息列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取企业信息详细信息\n\n @param enterpriseId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.SysEnterpriseInfo"], "doc": " 新增企业信息\n"}, {"name": "webAdd", "paramTypes": ["org.dromara.system.domain.SysEnterpriseInfo"], "doc": " 新增企业信息  (web 注册)\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.SysEnterpriseInfo"], "doc": " 修改企业信息\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除企业信息\n\n @param enterpriseIds 主键串\n"}, {"name": "audit", "paramTypes": ["org.dromara.system.domain.SysEnterpriseInfo"], "doc": " 企业注册审核\n"}, {"name": "getSearchData", "paramTypes": [], "doc": " 查询所有企业的名称和id\n @return\n"}, {"name": "resetPwd", "paramTypes": ["org.dromara.expert.domain.bo.ExpertBo"], "doc": " 重置密码\n"}], "constructors": []}
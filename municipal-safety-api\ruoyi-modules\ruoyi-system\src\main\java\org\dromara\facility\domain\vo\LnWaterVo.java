package org.dromara.facility.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.facility.domain.LnWater;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 绿能水表视图对象 ln_water
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LnWater.class)
public class LnWaterVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 0.001m³ (例：30m³传30000)
     */
    @ExcelProperty(value = "0.001m³ (例：30m³传30000)")
    private Long sdds;

    /**
     * 上报时间
     */
    @ExcelProperty(value = "上报时间")
    private Date recordTime;

    /**
     * 上月累计用水量 0.001m³
     */
    @ExcelProperty(value = "上月累计用水量 0.001m³")
    private Long sylj;

    /**
     * 昨日累计用水量 0.001m³
     */
    @ExcelProperty(value = "昨日累计用水量 0.001m³")
    private Long zrlj;

    /**
     * 前一个小时累计用水量 0.001m³
     */
    @ExcelProperty(value = "前一个小时累计用水量 0.001m³")
    private Long qyxslj;

    /**
     * 当前小时内累计用水量 0.001m³
     */
    @ExcelProperty(value = "当前小时内累计用水量 0.001m³")
    private Long dqxsnlj;

    /**
     * 实时流速 0.001m³/s
     */
    @ExcelProperty(value = "实时流速 0.001m³/s")
    private Long ssls;

    /**
     * 电池状态 dczt
     */
    @ExcelProperty(value = "电池状态 dczt")
    private Long dczt;

    /**
     * 电池电压 0.1V
     */
    @ExcelProperty(value = "电池电压 0.1V")
    private String dcdy;

    /**
     * 电池电量 1%
     */
    @ExcelProperty(value = "电池电量 1%")
    private String dcdl;

    /**
     * 保留
     */
    @ExcelProperty(value = "保留")
    private String bl;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String devNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}

<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="energyList">
      <el-table-column label="采集时间" align="center" prop="recordTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.recordTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="A相电压" align="center" prop="ua">
        <template v-slot="scope">
          {{ scope.row.ua }}V
        </template>
      </el-table-column>
      <el-table-column label="B相电压" align="center" prop="ub">
        <template v-slot="scope">
          {{ scope.row.ub }}V
        </template>
      </el-table-column>
      <el-table-column label="C相电压" align="center" prop="uc">
        <template v-slot="scope">
          {{ scope.row.uc }}V
        </template>
      </el-table-column>
      <el-table-column label="A相电流" align="center" prop="ia">
        <template v-slot="scope">
          {{ scope.row.ia }}V
        </template>
      </el-table-column>
      <el-table-column label="B相电流" align="center" prop="ib">
        <template v-slot="scope">
          {{ scope.row.ib }}V
        </template>
      </el-table-column>
      <el-table-column label="C相电流" align="center" prop="ic">
        <template v-slot="scope">
          {{ scope.row.ic }}V
        </template>
      </el-table-column>
      <el-table-column label="漏电流" align="center" prop="il">
        <template v-slot="scope">
          {{ scope.row.il }}V
        </template>
      </el-table-column>
      <el-table-column label="A相温度" align="center" prop="ta">
        <template v-slot="scope">
          {{ scope.row.ta }}°
        </template>
      </el-table-column>
      <el-table-column label="B相温度" align="center" prop="tb">
        <template v-slot="scope">
          {{ scope.row.tb }}°
        </template>
      </el-table-column>
      <el-table-column label="C相温度" align="center" prop="tc">
        <template v-slot="scope">
          {{ scope.row.tc }}°
        </template>
      </el-table-column>
      <el-table-column label="N相温度" align="center" prop="tn">
        <template v-slot="scope">
          {{ scope.row.tn }}°
        </template>
      </el-table-column>
      <el-table-column label="设备编号" align="center" prop="deviceNo" />
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

  </div>
</template>

<script>
import { listEnergy } from "@/api/projects/facility/index";

export default {
  name: "Energy",
  props: {
    devNo: {
      type: String,
      default: ""
    }
  },
  data () {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 绿能用电监测表格数据
      energyList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        facilityId: null
      },
    };
  },
  created () {
    this.getList();
  },
  methods: {
    /** 查询绿能用电监测列表 */
    async getList () {
      this.energyList = []
      this.loading = true;
      this.queryParams.devNo = this.devNo
      const res = await listEnergy(this.queryParams)
      this.energyList = res.rows;
      this.total = res.total;
      this.loading = false;
    },
  }
};
</script>

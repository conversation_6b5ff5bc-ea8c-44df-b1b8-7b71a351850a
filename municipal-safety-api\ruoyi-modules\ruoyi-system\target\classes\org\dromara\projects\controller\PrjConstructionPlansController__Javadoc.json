{"doc": " [项目管理] 存储危大工程专项施工方案信息及其审批状态\n\n <AUTHOR>\n @date 2025-05-15\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.projects.domain.bo.PrjConstructionPlansBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询[项目管理] 存储危大工程专项施工方案信息及其审批状态列表\n"}, {"name": "export", "paramTypes": ["org.dromara.projects.domain.bo.PrjConstructionPlansBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出[项目管理] 存储危大工程专项施工方案信息及其审批状态列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取[项目管理] 存储危大工程专项施工方案信息及其审批状态详细信息\n\n @param planId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.projects.domain.bo.PrjConstructionPlansBo"], "doc": " 新增[项目管理] 存储危大工程专项施工方案信息及其审批状态\n"}, {"name": "edit", "paramTypes": ["org.dromara.projects.domain.bo.PrjConstructionPlansBo"], "doc": " 修改[项目管理] 存储危大工程专项施工方案信息及其审批状态\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除[项目管理] 存储危大工程专项施工方案信息及其审批状态\n\n @param planIds 主键串\n"}, {"name": "analyseResult", "paramTypes": ["org.dromara.projects.domain.bo.AnalyseResultBo"], "doc": " 接收文档分析回调接口\n\n @param resultBo\n @return\n"}], "constructors": []}
{"doc": " 政府用户导入\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "boolean"], "doc": " 政府用户导入\n"}, {"name": "batchImport", "paramTypes": ["java.util.List", "boolean"], "doc": " 批量导入政府用户\n"}, {"name": "importFromCsv", "paramTypes": ["java.lang.String", "boolean"], "doc": " 从CSV导入政府用户\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载政府用户导入模板\n"}, {"name": "smartImport", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "boolean"], "doc": " 智能分析导入政府用户\n"}], "constructors": []}
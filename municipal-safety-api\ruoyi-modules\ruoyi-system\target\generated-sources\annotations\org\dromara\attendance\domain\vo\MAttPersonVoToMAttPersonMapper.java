package org.dromara.attendance.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.attendance.domain.MAttPerson;
import org.dromara.attendance.domain.MAttPersonToMAttPersonVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {MAttPersonToMAttPersonVoMapper.class},
    imports = {}
)
public interface MAttPersonVoToMAttPersonMapper extends BaseMapper<MAttPersonVo, MAttPerson> {
}

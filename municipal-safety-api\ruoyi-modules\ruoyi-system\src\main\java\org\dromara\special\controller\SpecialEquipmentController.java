package org.dromara.special.controller;

import java.util.List;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.service.IPrjProjectsService;
import org.dromara.special.domain.sync.SyncDate;
import org.dromara.special.domain.sync.SyncEntity;
import org.dromara.special.domain.sync.SyncReceive;
import org.dromara.special.domain.sync.SyncSpecialPersonnel;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.special.domain.vo.SpecialEquipmentVo;
import org.dromara.special.domain.bo.SpecialEquipmentBo;
import org.dromara.special.service.ISpecialEquipmentService;

/**
 * 特种设备
 *
 * <AUTHOR> Li
 * @date 2025-05-14
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/special/equipment")
public class SpecialEquipmentController extends BaseController {

    private final ISpecialEquipmentService specialEquipmentService;
    private final IPrjProjectsService prjProjectsService;

    /**
     * 查询特种设备列表
     */
    @GetMapping("/list")
    public TableDataInfo<SpecialEquipmentVo> list(SpecialEquipmentBo bo, PageQuery pageQuery) {
        TableDataInfo<SpecialEquipmentVo> specialEquipmentVoTableDataInfo = specialEquipmentService.queryPageList(bo, pageQuery);
        for (SpecialEquipmentVo specialEquipmentVo : specialEquipmentVoTableDataInfo.getRows()) {
            PrjProjectsVo prjProjectsVo = prjProjectsService.queryById(specialEquipmentVo.getProjectId());
            if (prjProjectsVo != null) {
                specialEquipmentVo.setProjectName(prjProjectsVo.getProjectName());
            }
        }
        return specialEquipmentVoTableDataInfo;
    }

    /**
     * 导出特种设备列表
     */
    @Log(title = "特种设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SpecialEquipmentBo bo, HttpServletResponse response) {
        List<SpecialEquipmentVo> list = specialEquipmentService.queryList(bo);
        ExcelUtil.exportExcel(list, "特种设备", SpecialEquipmentVo.class, response);
    }

    /**
     * 获取特种设备详细信息
     *
     * @param equipmentId 主键
     */
    @GetMapping("/{equipmentId}")
    public R<SpecialEquipmentVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long equipmentId) {
        SpecialEquipmentVo specialEquipmentVo = specialEquipmentService.queryById(equipmentId);
        PrjProjectsVo prjProjectsVo = prjProjectsService.queryById(specialEquipmentVo.getProjectId());
        if (prjProjectsVo != null) {
            specialEquipmentVo.setProjectName(prjProjectsVo.getProjectName());
        }
        return R.ok(specialEquipmentVo);
    }

    /**
     * 新增特种设备
     */
    @Log(title = "特种设备", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SpecialEquipmentBo bo) {
        return toAjax(specialEquipmentService.insertByBo(bo));
    }

    /**
     * 修改特种设备
     */
    @Log(title = "特种设备", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SpecialEquipmentBo bo) {
        return toAjax(specialEquipmentService.updateByBo(bo));
    }

    /**
     * 删除特种设备
     *
     * @param equipmentIds 主键串
     */
    @Log(title = "特种设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{equipmentIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] equipmentIds) {
        return toAjax(specialEquipmentService.deleteWithValidByIds(List.of(equipmentIds), true));
    }


    /**
     * 同步信息到特种设备或特种人员信息
     * @param syncEntity 同步实体
     */
    @PostMapping("/sync")
    public R<Object> syncProject(@Validated @RequestBody SyncEntity syncEntity) {
        specialEquipmentService.syncProject(syncEntity);
        return R.ok("同步成功");
    }

    /**
     *  查询备案机关数据
     */
    @GetMapping("/getAuthorityData")
    public R getAuthorityList() {
        return R.ok(specialEquipmentService.getAuthorityList());
    }
}

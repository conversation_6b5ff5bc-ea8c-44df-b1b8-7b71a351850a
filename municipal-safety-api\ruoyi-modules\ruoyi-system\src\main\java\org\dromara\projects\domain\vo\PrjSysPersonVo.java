package org.dromara.projects.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 人员基本信息对象 VO
 */
@Data
public class PrjSysPersonVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 人员ID
     */
    private Long personId;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 性别
     */
    private String gender;

    /**
     * 政治面貌（如群众、党员）
     */
    private String politicalStatus;

    /**
     * 文化程度
     */
    private String education;

    /**
     * 头像图片id
     */
    private Long headImgId;
}

export interface MonitorFacilityVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 设备编号
   */
  devNo: string;

  /**
   * 设备类型
   */
  deviceType: string;

  /**
   * 生产厂家
   */
  manufacturers: string;

  /**
   * 数据来源
   */
  dataSources: string;

  /**
   * 项目id
   */
  projectId: string | number;

  /**
   * 工程id
   */
  itemId: string | number;

  /**
   * 备注
   */
  remark: string;

}

export interface MonitorFacilityForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 设备编号
   */
  devNo?: string;

  /**
   * 设备类型
   */
  deviceType?: string;

  /**
   * 生产厂家
   */
  manufacturers?: string;

  /**
   * 数据来源
   */
  dataSources?: string;

  /**
   * 项目id
   */
  projectId?: string | number;

  /**
   * 工程id
   */
  itemId?: string | number;

  /**
   * 备注
   */
  remark?: string;

}

export interface MonitorFacilityQuery extends PageQuery {

  /**
   * 设备编号
   */
  devNo?: string;

  /**
   * 设备类型
   */
  deviceType?: string;

  /**
   * 生产厂家
   */
  manufacturers?: string;

  /**
   * 数据来源
   */
  dataSources?: string;

  /**
   * 项目id
   */
  projectId?: string | number;

  /**
   * 工程id
   */
  itemId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}

package org.dromara.ai.domain;

import javax.annotation.processing.Generated;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:15+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class AiHazAnalysisTasksToAiHazAnalysisTasksVoMapperImpl implements AiHazAnalysisTasksToAiHazAnalysisTasksVoMapper {

    @Override
    public AiHazAnalysisTasksVo convert(AiHazAnalysisTasks arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AiHazAnalysisTasksVo aiHazAnalysisTasksVo = new AiHazAnalysisTasksVo();

        aiHazAnalysisTasksVo.setTaskId( arg0.getTaskId() );
        aiHazAnalysisTasksVo.setProjectId( arg0.getProjectId() );
        aiHazAnalysisTasksVo.setSourceType( arg0.getSourceType() );
        aiHazAnalysisTasksVo.setExpertUserId( arg0.getExpertUserId() );
        aiHazAnalysisTasksVo.setUploadTime( arg0.getUploadTime() );
        aiHazAnalysisTasksVo.setPhotoDocumentId( arg0.getPhotoDocumentId() );
        aiHazAnalysisTasksVo.setAiPhotoDocumentId( arg0.getAiPhotoDocumentId() );
        aiHazAnalysisTasksVo.setGpsLocation( arg0.getGpsLocation() );
        aiHazAnalysisTasksVo.setLocationDescription( arg0.getLocationDescription() );
        aiHazAnalysisTasksVo.setAiRecognitionRawResult( arg0.getAiRecognitionRawResult() );
        aiHazAnalysisTasksVo.setItemId( arg0.getItemId() );
        aiHazAnalysisTasksVo.setStatus( arg0.getStatus() );
        aiHazAnalysisTasksVo.setRecheckStatus( arg0.getRecheckStatus() );
        aiHazAnalysisTasksVo.setRelatedWorkOrderId( arg0.getRelatedWorkOrderId() );

        return aiHazAnalysisTasksVo;
    }

    @Override
    public AiHazAnalysisTasksVo convert(AiHazAnalysisTasks arg0, AiHazAnalysisTasksVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setTaskId( arg0.getTaskId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setSourceType( arg0.getSourceType() );
        arg1.setExpertUserId( arg0.getExpertUserId() );
        arg1.setUploadTime( arg0.getUploadTime() );
        arg1.setPhotoDocumentId( arg0.getPhotoDocumentId() );
        arg1.setAiPhotoDocumentId( arg0.getAiPhotoDocumentId() );
        arg1.setGpsLocation( arg0.getGpsLocation() );
        arg1.setLocationDescription( arg0.getLocationDescription() );
        arg1.setAiRecognitionRawResult( arg0.getAiRecognitionRawResult() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRecheckStatus( arg0.getRecheckStatus() );
        arg1.setRelatedWorkOrderId( arg0.getRelatedWorkOrderId() );

        return arg1;
    }
}

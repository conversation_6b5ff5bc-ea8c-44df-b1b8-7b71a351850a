package org.dromara.monito.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.monito.domain.DeviceMonito;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class DeviceMonitoVoToDeviceMonitoMapperImpl implements DeviceMonitoVoToDeviceMonitoMapper {

    @Override
    public DeviceMonito convert(DeviceMonitoVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        DeviceMonito deviceMonito = new DeviceMonito();

        deviceMonito.setMonitoId( arg0.getMonitoId() );
        deviceMonito.setProjectId( arg0.getProjectId() );
        deviceMonito.setItemId( arg0.getItemId() );
        deviceMonito.setDeviceName( arg0.getDeviceName() );
        deviceMonito.setDeviceType( arg0.getDeviceType() );
        deviceMonito.setDeviceCode( arg0.getDeviceCode() );
        deviceMonito.setDeviceStatus( arg0.getDeviceStatus() );
        deviceMonito.setEnableSnapshot( arg0.getEnableSnapshot() );
        deviceMonito.setSnapshotTime( arg0.getSnapshotTime() );
        deviceMonito.setRemarks( arg0.getRemarks() );
        deviceMonito.setChannelNo( arg0.getChannelNo() );

        return deviceMonito;
    }

    @Override
    public DeviceMonito convert(DeviceMonitoVo arg0, DeviceMonito arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setMonitoId( arg0.getMonitoId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setDeviceName( arg0.getDeviceName() );
        arg1.setDeviceType( arg0.getDeviceType() );
        arg1.setDeviceCode( arg0.getDeviceCode() );
        arg1.setDeviceStatus( arg0.getDeviceStatus() );
        arg1.setEnableSnapshot( arg0.getEnableSnapshot() );
        arg1.setSnapshotTime( arg0.getSnapshotTime() );
        arg1.setRemarks( arg0.getRemarks() );
        arg1.setChannelNo( arg0.getChannelNo() );

        return arg1;
    }
}

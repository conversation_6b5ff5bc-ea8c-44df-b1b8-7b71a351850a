package org.dromara.flow.domain.bo;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.flow.domain.PrjHazardousItemsFile;

/**
 * 通用流程附件业务对象 prj_hazardous_items_file
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjHazardousItemsFile.class, reverseConvertGenerate = false)
public class PrjHazardousItemsFileBo extends BaseEntity {

    /**
     * 附件id
     */
    @NotNull(message = "附件id不能为空", groups = {EditGroup.class})
    private Long itemFileId;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 取sys_oss.oss_id,文件id
     */
    private Long fileId;

    /**
     * 业务id，取 雪花||UUID
     */
    private String taskId;

    /**
     * 服务类型，取字典表flow_service_type
     */
    private String serviceType;

    /**
     * 取sys_oss.oss_id,反馈文件id（多个逗号隔开）
     */
    private String callFileId;
}

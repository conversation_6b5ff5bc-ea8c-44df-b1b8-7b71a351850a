package org.dromara.person.service;

import org.dromara.person.domain.vo.QualificationDictVo;
import org.dromara.person.domain.bo.QualificationDictBo;

import java.util.Collection;
import java.util.List;

/**
 * 人员证书属性类型Service接口
 *
 * <AUTHOR> Li
 * @date 2025-05-10
 */
public interface IQualificationDictService {

    /**
     * 查询人员证书属性类型
     *
     * @param id 主键
     * @return 人员证书属性类型
     */
    QualificationDictVo queryById(Long id);


    /**
     * 查询符合条件的人员证书属性类型列表
     *
     * @param bo 查询条件
     * @return 人员证书属性类型列表
     */
    List<QualificationDictVo> queryList(QualificationDictBo bo);

    /**
     * 新增人员证书属性类型
     *
     * @param bo 人员证书属性类型
     * @return 是否新增成功
     */
    Boolean insertByBo(QualificationDictBo bo);

    /**
     * 修改人员证书属性类型
     *
     * @param bo 人员证书属性类型
     * @return 是否修改成功
     */
    Boolean updateByBo(QualificationDictBo bo);

    /**
     * 校验并批量删除人员证书属性类型信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

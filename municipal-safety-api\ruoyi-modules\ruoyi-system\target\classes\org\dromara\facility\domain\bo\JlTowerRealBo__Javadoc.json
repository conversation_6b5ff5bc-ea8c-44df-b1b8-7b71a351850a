{"doc": " 塔机实时数据业务对象 jl_tower_real\n\n <AUTHOR>\n @date 2025-07-24\n", "fields": [{"name": "id", "doc": " 主键\n"}, {"name": "devNo", "doc": " 设备编号\n"}, {"name": "tcNo", "doc": " 塔机编号\n"}, {"name": "addTime", "doc": " 时间\n"}, {"name": "lockMachineStatus", "doc": " 厂家及设备类型\n"}, {"name": "height", "doc": " 高度\n"}, {"name": "realRange", "doc": " 幅度\n"}, {"name": "rotary", "doc": " 回转\n"}, {"name": "startWeight", "doc": " 起始重量\n"}, {"name": "windSpeed", "doc": " 风速数据\n"}, {"name": "slant", "doc": " 倾角数据\n"}, {"name": "weightPct", "doc": " 重量百分比\n"}, {"name": "mofPct", "doc": " 力矩百分比\n"}, {"name": "windSpeedPct", "doc": " 风速百分比\n"}, {"name": "slantPct", "doc": " 倾斜百分比\n"}, {"name": "alarmCause", "doc": " 报警原因\n"}, {"name": "alarmCauseZh", "doc": " 报警原因中文\n"}, {"name": "brakingStatusUp", "doc": " 制动状态上\n"}, {"name": "brakingStatusDown", "doc": " 制动状态下\n"}, {"name": "brakingStatusFront", "doc": " 制动状态前\n"}, {"name": "brakingStatusBack", "doc": " 制动状态后\n"}, {"name": "brakingStatusLeft", "doc": " 制动状态左\n"}, {"name": "brakingStatusRight", "doc": " 制动状态右\n"}, {"name": "workingStatus", "doc": " 工作状态 1工作 2空闲\n"}, {"name": "createTime", "doc": " 创建时间\n"}], "enumConstants": [], "methods": [], "constructors": []}
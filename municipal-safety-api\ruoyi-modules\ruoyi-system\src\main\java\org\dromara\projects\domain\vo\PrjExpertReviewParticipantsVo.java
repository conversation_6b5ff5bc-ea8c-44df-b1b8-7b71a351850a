package org.dromara.projects.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.projects.domain.PrjExpertReviewParticipants;

import java.io.Serial;
import java.io.Serializable;


/**
 * [项目管理] 列出专家论证会议的参会人员视图对象 prj_expert_review_participants
 *
 * <AUTHOR> brother
 * @date 2025-06-05
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjExpertReviewParticipants.class)
public class PrjExpertReviewParticipantsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 专家论证会议ID (逻辑外键至 prj_expert_reviews.review_id)
     */
    @ExcelProperty(value = "专家论证会议ID (逻辑外键至 prj_expert_reviews.review_id)")
    private Long reviewId;

    /**
     * 参与者用户ID (逻辑外键至 sys_users.user_id)
     */
    @ExcelProperty(value = "参与者用户ID (逻辑外键至 sys_users.user_id)")
    private Long userId;

    /**
     * 会议中的角色
     */
    @ExcelProperty(value = "会议中的角色", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "expert_role")
    private String roleInMeeting;

    /**
     * 是否评审专家 (0:否, 1:是)
     */
    @ExcelProperty(value = "是否评审专家 (0:否, 1:是)")
    private Long isAttendingExpert;
}

package org.dromara.person.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 人员基本信息对象 sys_person
 *
 * <AUTHOR> zu da
 * @date 2025-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_person")
public class SysPerson extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 人员ID
     */
    @TableId(value = "person_id")
    private Long personId;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 性别
     */
    private String gender;

    /**
     * 政治面貌（如群众、党员）
     */
    private String politicalStatus;

    /**
     * 文化程度
     */
    private String education;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

    /**
     * 头像图片id
     */
    private Long headImgId;

    /**
     * 头像压缩图片id
     */
    private Long headImgMini;
}

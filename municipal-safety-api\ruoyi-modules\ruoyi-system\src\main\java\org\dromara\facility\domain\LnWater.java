package org.dromara.facility.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 绿能水表对象 ln_water
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ln_water")
public class LnWater {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 0.001m³ (例：30m³传30000)
     */
    private Long sdds;

    /**
     * 上报时间
     */
    private Date recordTime;

    /**
     * 上月累计用水量 0.001m³
     */
    private Long sylj;

    /**
     * 昨日累计用水量 0.001m³
     */
    private Long zrlj;

    /**
     * 前一个小时累计用水量 0.001m³
     */
    private Long qyxslj;

    /**
     * 当前小时内累计用水量 0.001m³
     */
    private Long dqxsnlj;

    /**
     * 实时流速 0.001m³/s
     */
    private Long ssls;

    /**
     * 电池状态 dczt
     */
    private Long dczt;

    /**
     * 电池电压 0.1V
     */
    private String dcdy;

    /**
     * 电池电量 1%
     */
    private String dcdl;

    /**
     * 保留
     */
    private String bl;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}

package org.dromara.ai.domain.bo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.ai.domain.AiHazAnalysisTasksResult;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {},
    imports = {}
)
public interface AiHazAnalysisTasksResultBoToAiHazAnalysisTasksResultMapper extends BaseMapper<AiHazAnalysisTasksResultBo, AiHazAnalysisTasksResult> {
}

package org.dromara.attendance.service.impl;

import jakarta.annotation.Resource;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.SysEnterpriseInfo;
import org.dromara.system.mapper.SysEnterpriseInfoMapper;
import org.springframework.stereotype.Service;
import org.dromara.attendance.domain.bo.MAttSnBo;
import org.dromara.attendance.domain.vo.MAttSnVo;
import org.dromara.attendance.domain.MAttSn;
import org.dromara.attendance.mapper.MAttSnMapper;
import org.dromara.attendance.service.IMAttSnService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 考勤设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@RequiredArgsConstructor
@Service
public class MAttSnServiceImpl implements IMAttSnService {

    private final MAttSnMapper baseMapper;
    @Resource
    private SysEnterpriseInfoMapper sysEnterpriseInfoMapper;

    /**
     * 查询考勤设备
     *
     * @param snId 主键
     * @return 考勤设备
     */
    @Override
    public MAttSnVo queryById(Long snId) {
        return baseMapper.selectVoById(snId);
    }

    /**
     * 分页查询考勤设备列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 考勤设备分页列表
     */
    @Override
    public TableDataInfo<MAttSnVo> queryPageList(MAttSnBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MAttSn> lqw = buildQueryWrapper(bo);
        Page<MAttSnVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<MAttSnVo> selectMAttSnList(MAttSnBo bo) {
        SysEnterpriseInfo sysEnterpriseInfo = sysEnterpriseInfoMapper.selectById(LoginHelper.getEnterpriseId());
        bo.setCreateDept(LoginHelper.isSuperAdmin() ? null : sysEnterpriseInfo.getDeptId());
        return baseMapper.selectMAttSnList(bo);
    }

    /**
     * 查询符合条件的考勤设备列表
     *
     * @param bo 查询条件
     * @return 考勤设备列表
     */
    @Override
    public List<MAttSnVo> queryList(MAttSnBo bo) {
        LambdaQueryWrapper<MAttSn> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MAttSn> buildQueryWrapper(MAttSnBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MAttSn> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MAttSn::getSnId);
        lqw.eq(bo.getProjectId() != null, MAttSn::getProjectId, bo.getProjectId());
        lqw.eq(StringUtils.isNotBlank(bo.getSn()), MAttSn::getSn, bo.getSn());
        lqw.like(StringUtils.isNotBlank(bo.getSnName()), MAttSn::getSnName, bo.getSnName());
        lqw.eq(bo.getDirection() != null, MAttSn::getDirection, bo.getDirection());
        lqw.eq(bo.getStatus() != null, MAttSn::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增考勤设备
     *
     * @param bo 考勤设备
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MAttSnBo bo) {
        MAttSn add = MapstructUtils.convert(bo, MAttSn.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setSnId(add.getSnId());
        }
        return flag;
    }

    /**
     * 修改考勤设备
     *
     * @param bo 考勤设备
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MAttSnBo bo) {
        MAttSn update = MapstructUtils.convert(bo, MAttSn.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MAttSn entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除考勤设备信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public MAttSnVo selectMAttSnBySn(String sn) {
        return baseMapper.selectMAttSnBySn(sn);
    }

    @Override
    public List<MAttSnVo> selectMAttSnByProjectId(Long projectId) {
        return baseMapper.selectMAttSnByProjectId(projectId);
    }
}

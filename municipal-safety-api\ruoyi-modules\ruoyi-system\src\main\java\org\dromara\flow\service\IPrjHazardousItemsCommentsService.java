package org.dromara.flow.service;

import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.flow.domain.PrjHazardousItemsComments;
import org.dromara.flow.domain.bo.PrjHazardousItemsCommentsBo;
import org.dromara.flow.domain.vo.PrjHazardousItemsCommentsVo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 质监站隐患清单整改Service接口
 *
 * <AUTHOR> zu <PERSON>
 * @date 2025-05-28
 */
public interface IPrjHazardousItemsCommentsService {

    /**
     * 修改质监站隐患清单整改
     *
     * @param bo 质监站隐患清单整改
     * @return 是否修改成功
     */
    Boolean updateByBo(PrjHazardousItemsCommentsBo bo);

    /**
     * 修改清单
     * @param comments
     * @return
     */
    boolean updateById(PrjHazardousItemsComments comments);

    /**
     * 保存清单
     * @param comments
     * @return
     */
    String save(PrjHazardousItemsComments comments);

    /**
     * 获取详情
     * @param taskId
     * @return
     */
    PrjHazardousItemsCommentsVo getDetail(String taskId);

    Boolean consturctionUpBackFiles(PrjHazardousItemsComments comments);
}

package org.dromara.facility.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.facility.domain.bo.LnEdgeGuardBo;
import org.dromara.facility.domain.vo.LnEdgeGuardVo;
import org.dromara.facility.service.ILnEdgeGuardService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 绿能临边防护
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/edgeGuard")
public class LnEdgeGuardController extends BaseController {

    private final ILnEdgeGuardService lnEdgeGuardService;

    /**
     * 查询绿能临边防护列表
     */
    @GetMapping("/list")
    public TableDataInfo<LnEdgeGuardVo> list(LnEdgeGuardBo bo, PageQuery pageQuery) {
        return lnEdgeGuardService.queryPageList(bo, pageQuery);
    }
}

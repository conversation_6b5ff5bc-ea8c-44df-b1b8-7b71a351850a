package org.dromara.projects.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 企业信息视图对象
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
public class PrjSysEnterpriseInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 统一社会信用代码
     */
    private String unifiedSocialCreditCode;

    /**
     * 企业类型
     */
    private String enterpriseType;

    /**
     * 企业地址
     */
    private String businessAddress;

    /**
     * 法定代表人
     */
    private String legalRepresentative;

    /**
     * 注册省份
     */
    private String registrationRegionProvince;

    /**
     * 注册城市
     */
    private String registrationRegionCity;

    /**
     * 注册区域
     */
    private String registrationRegionArea;

    /**
     * 注册日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registrationDate;

    /**
     * 办公电话
     */
    private String officePhone;

    /**
     * 企业状态
     */
    private String enterpriseStatus;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 企业类型 (1:建设单位 2:施工单位 3:监理单位 4:设计单位 5:勘察单位)
     */
    private String enterpriseRole;
}

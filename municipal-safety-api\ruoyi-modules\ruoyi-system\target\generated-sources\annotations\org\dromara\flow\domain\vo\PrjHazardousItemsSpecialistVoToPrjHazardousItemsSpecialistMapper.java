package org.dromara.flow.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.PrjHazardousItemsSpecialist;
import org.dromara.flow.domain.PrjHazardousItemsSpecialistToPrjHazardousItemsSpecialistVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjHazardousItemsSpecialistToPrjHazardousItemsSpecialistVoMapper.class},
    imports = {}
)
public interface PrjHazardousItemsSpecialistVoToPrjHazardousItemsSpecialistMapper extends BaseMapper<PrjHazardousItemsSpecialistVo, PrjHazardousItemsSpecialist> {
}

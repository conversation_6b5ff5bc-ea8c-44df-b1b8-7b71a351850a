package org.dromara.expert.domain.bo;

import org.dromara.expert.domain.Expert;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.expert.domain.Field;
import org.dromara.expert.domain.Project;

import java.util.List;

/**
 *  专家主业务对象 expert
 * @date 2025-05-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Expert.class, reverseConvertGenerate = false)
public class ExpertBo extends BaseEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * 身份证件号
     */
    private String idCard;

    /**
     * 性别
     */
    private String sex;

    /**
     * 工作单位
     */
    private String workUnit;

    /**
     * 电话
     */
    private String phone;

    /**
     * 简介
     */
    private String introduce;

    /**
     * 职称
     */
    private String title;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在市
     */
    private String city;

    /**
     * 所在区
     */
    private String area;

    /**
     * 专业
     */
    private String major;

    /**
     * 行业
     */
    private String industry;

    /**
     * 专家主键
     */
    private Long expertId;

    /**
     *  删除标识
     */
    private String delFlag;

    /** 类型 */
    private String type;


    /**
     *  所属项目
     */
    private List<Project> expertProjectList;


    /**
     *  专业领域
     */
    private List<Field> expertFieldList;

    /** 头像 */
    private String avatar;
}

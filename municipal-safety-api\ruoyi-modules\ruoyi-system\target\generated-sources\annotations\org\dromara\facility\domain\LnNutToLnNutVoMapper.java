package org.dromara.facility.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.bo.LnNutBoToLnNutMapper;
import org.dromara.facility.domain.vo.LnNutVo;
import org.dromara.facility.domain.vo.LnNutVoToLnNutMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {LnNutBoToLnNutMapper.class,LnNutVoToLnNutMapper.class},
    imports = {}
)
public interface LnNutToLnNutVoMapper extends BaseMapper<LnNut, LnNutVo> {
}

package org.dromara.person.domain;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import java.io.Serial;

/**
 * 人员资格证书对象 sys_qualification
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_qualification")
public class SysQualification extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "qualification_id", type = IdType.ASSIGN_ID)
    private Long qualificationId;

    /**
     * 关联人员ID
     */
    private Long personId;

    /**
     * 证书种类
     */
    private String certificateType;

    /**
     * 证书名称
     */
    private String certificateName;

    /**
     * 证书编号
     */
    private String certificateNumber;

    /**
     * 获取时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date acquisitionTime;

    /**
     * 发证机关
     */
    private String issuingAuthority;

    /**
     * 证书等级
     */
    private String certificateLevel;

    /**
     * 资质对应岗位
     */
    private String correspondingPosition;

    /**
     * 有效期起始
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date validFrom;

    /**
     * 有效期结束
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date validTo;

    /**
     * 证件照片路径
     */
    private Long uploadPhoto;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;
}

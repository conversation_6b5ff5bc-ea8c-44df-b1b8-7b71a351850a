package org.dromara.facility.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.bo.MonitorFacilityBoToMonitorFacilityMapper;
import org.dromara.facility.domain.vo.MonitorFacilityVo;
import org.dromara.facility.domain.vo.MonitorFacilityVoToMonitorFacilityMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {MonitorFacilityVoToMonitorFacilityMapper.class,MonitorFacilityBoToMonitorFacilityMapper.class},
    imports = {}
)
public interface MonitorFacilityToMonitorFacilityVoMapper extends BaseMapper<MonitorFacility, MonitorFacilityVo> {
}

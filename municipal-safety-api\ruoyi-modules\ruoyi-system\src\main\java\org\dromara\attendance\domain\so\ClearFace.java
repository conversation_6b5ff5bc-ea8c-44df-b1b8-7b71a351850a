package org.dromara.attendance.domain.so;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 访客系统人员名单对象 base_visitor
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
public class ClearFace {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "设备号不能为空")
    private String sns;

    @NotNull(message = "项目id不能为空")
    private String projectId;

    @NotNull(message = "身份证号不能为空")
    private String idCard;

    private Long[] personSnIds;
}

package org.dromara.flow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.ai.domain.dto.AiHazAnalysisTasksDto;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksVo;
import org.dromara.ai.mapper.AiHazAnalysisTasksMapper;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.flow.domain.PrjHazardousItemsSpecialWarning;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialWarningBo;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialWarningVo;
import org.dromara.flow.mapper.PrjHazardousItemsSpecialWarningMapper;
import org.dromara.flow.service.IPrjHazardousItemsSpecialWarningService;
import org.dromara.projects.domain.PrjHazardousItems;
import org.dromara.projects.domain.PrjProjects;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.mapper.PrjHazardousItemsMapper;
import org.dromara.projects.mapper.PrjProjectsMapper;
import org.dromara.warm.flow.orm.entity.FlowInstance;
import org.dromara.warm.flow.orm.mapper.FlowInstanceMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 特殊预警Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-06-19
 */
@RequiredArgsConstructor
@Service
public class PrjHazardousItemsSpecialWarningServiceImpl implements IPrjHazardousItemsSpecialWarningService {

    private final PrjHazardousItemsSpecialWarningMapper baseMapper;
    private final AiHazAnalysisTasksMapper aiHazAnalysisTasksMapper;
    private final FlowInstanceMapper flowInstanceMapper;
    private final PrjProjectsMapper prjProjectsMapper;
    private final PrjHazardousItemsMapper prjHazardousItemsMapper;

    /**
     * 查询特殊预警
     *
     * @param warningId 主键
     * @return 特殊预警
     */
    @Override
    public PrjHazardousItemsSpecialWarningVo queryById(Long warningId) {
        return baseMapper.selectVoById(warningId);
    }

    /**
     * 分页查询特殊预警列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 特殊预警分页列表
     */
    @Override
    public TableDataInfo<PrjHazardousItemsSpecialWarningVo> queryPageList(PrjHazardousItemsSpecialWarningBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PrjHazardousItemsSpecialWarning> lqw = buildQueryWrapper(bo);
        Page<PrjHazardousItemsSpecialWarningVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        TableDataInfo<PrjHazardousItemsSpecialWarningVo> dataInfo = TableDataInfo.build(result);

        List<PrjHazardousItemsSpecialWarningVo> rows = dataInfo.getRows();
        if (CollectionUtil.isNotEmpty(rows)) {
            //查询流程
            List<String> taskIds = rows.stream().map(PrjHazardousItemsSpecialWarningVo::getTaskId).toList();

            List<FlowInstance> flowInstances = flowInstanceMapper.selectByIds(taskIds);

            Map<Long, FlowInstance> flowInstanceMap = flowInstances.stream().collect(Collectors.toMap(FlowInstance::getId, s -> s));

            String constAiTaskId = "ai_task_id";

            List<String> aiTaskIds = flowInstances.stream().map(s -> {
                JSONObject jsonObject = JSON.parseObject(s.getVariable());
                return jsonObject.containsKey(constAiTaskId) ? jsonObject.getString(constAiTaskId) : null;
            }).collect(Collectors.toList());

            //查询隐患
            List<AiHazAnalysisTasksVo> aiHazAnalysisTasksVos = aiHazAnalysisTasksMapper.selectVoByIds(aiTaskIds);

            Map<Long, AiHazAnalysisTasksVo> analysisTasksVoMap = aiHazAnalysisTasksVos.stream().collect(Collectors.toMap(AiHazAnalysisTasksVo::getTaskId, s -> s));

            List<Long> projectIds = aiHazAnalysisTasksVos.stream().map(AiHazAnalysisTasksVo::getProjectId).toList();
            List<Long> itemIds = aiHazAnalysisTasksVos.stream().map(AiHazAnalysisTasksVo::getItemId).toList();

            //项目信息
            Map<Long, String> projectNameMap = getProjectNameByIds(projectIds);
            Map<Long, String> itemNameMap = getItemNameByIds(itemIds);

            for (PrjHazardousItemsSpecialWarningVo row : rows) {
                FlowInstance instance = flowInstanceMap.get(Long.valueOf(row.getTaskId()));

                JSONObject jsonObject = JSON.parseObject(instance.getVariable());
                //获取隐患id
                if (jsonObject.containsKey(constAiTaskId)) {
                    String aiTaskId = jsonObject.getString(constAiTaskId);
                    AiHazAnalysisTasksVo tasksVo = analysisTasksVoMap.get(Long.valueOf(aiTaskId));

                    row.setProjectId(tasksVo.getProjectId());
                    row.setProjectName(projectNameMap.get(tasksVo.getProjectId()));
                    row.setItemId(tasksVo.getItemId());
                    row.setItemName(itemNameMap.get(tasksVo.getItemId()));
                    row.setHazAnalysisId(Long.valueOf(aiTaskId));
                    row.setBusinessId(instance.getBusinessId());
                }
            }
            return dataInfo;
        }
        return null;
    }

    /**
     * 通过项目id集合获取项目名称
     *
     * @param projectIds
     * @return
     */
    private Map<Long, String> getProjectNameByIds(List<Long> projectIds) {
        LambdaQueryWrapper<PrjProjects> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PrjProjects::getProjectId, projectIds)
            .select(PrjProjects::getProjectId, PrjProjects::getProjectName);

        List<PrjProjects> projects = prjProjectsMapper.selectList(wrapper);

        return projects.stream().collect(Collectors.toMap(PrjProjects::getProjectId, PrjProjects::getProjectName));
    }

    /**
     * 通过工程项目id获取工程名称
     *
     * @param itemIds
     * @return
     */
    private Map<Long, String> getItemNameByIds(List<Long> itemIds) {
        LambdaQueryWrapper<PrjHazardousItems> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PrjHazardousItems::getItemId, itemIds)
            .select(PrjHazardousItems::getItemId, PrjHazardousItems::getItemName);

        List<PrjHazardousItems> prjHazardousItems = prjHazardousItemsMapper.selectList(wrapper);

        return prjHazardousItems.stream().collect(Collectors.toMap(PrjHazardousItems::getItemId, PrjHazardousItems::getItemName));
    }

    /**
     * 查询符合条件的特殊预警列表
     *
     * @param bo 查询条件
     * @return 特殊预警列表
     */
    @Override
    public List<PrjHazardousItemsSpecialWarningVo> queryList(PrjHazardousItemsSpecialWarningBo bo) {
        LambdaQueryWrapper<PrjHazardousItemsSpecialWarning> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PrjHazardousItemsSpecialWarning> buildQueryWrapper(PrjHazardousItemsSpecialWarningBo
                                                                                      bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PrjHazardousItemsSpecialWarning> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PrjHazardousItemsSpecialWarning::getWarningId);
        lqw.eq(StringUtils.isNotBlank(bo.getTaskId()), PrjHazardousItemsSpecialWarning::getTaskId, bo.getTaskId());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), PrjHazardousItemsSpecialWarning::getReason, bo.getReason());
        lqw.eq(StringUtils.isNotBlank(bo.getReasonType()), PrjHazardousItemsSpecialWarning::getReasonType, bo.getReasonType());
        return lqw;
    }

    /**
     * 新增特殊预警
     *
     * @param bo 特殊预警
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PrjHazardousItemsSpecialWarningBo bo) {
        PrjHazardousItemsSpecialWarning add = MapstructUtils.convert(bo, PrjHazardousItemsSpecialWarning.class);
        validEntityBeforeSave(add);

        LambdaQueryWrapper<FlowInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FlowInstance::getBusinessId, bo.getTaskId())
            .select(FlowInstance::getId, FlowInstance::getBusinessId);
        FlowInstance instance = flowInstanceMapper.selectOne(wrapper);

        if (instance != null) {
            add.setTaskId(instance.getId().toString());
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setWarningId(add.getWarningId());
        }
        return flag;
    }

    /**
     * 修改特殊预警
     *
     * @param bo 特殊预警
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PrjHazardousItemsSpecialWarningBo bo) {
        PrjHazardousItemsSpecialWarning update = MapstructUtils.convert(bo, PrjHazardousItemsSpecialWarning.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PrjHazardousItemsSpecialWarning entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除特殊预警信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}

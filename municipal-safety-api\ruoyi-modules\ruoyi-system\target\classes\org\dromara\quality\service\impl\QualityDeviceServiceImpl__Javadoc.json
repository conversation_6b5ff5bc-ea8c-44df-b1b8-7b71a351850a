{"doc": " 设备管理Service业务层处理\n\n <AUTHOR>\n @date 2024-01-01\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询设备管理\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.quality.domain.bo.QualityDeviceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询设备管理列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.quality.domain.bo.QualityDeviceBo"], "doc": " 查询设备管理列表\n"}, {"name": "fillOssInfo", "paramTypes": ["org.dromara.quality.domain.vo.QualityDeviceVo"], "doc": " 填充OSS文件信息\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.quality.domain.bo.QualityDeviceBo"], "doc": " 新增设备管理\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.quality.domain.bo.QualityDeviceBo"], "doc": " 修改设备管理\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.quality.domain.QualityDevice"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 批量删除设备管理\n"}, {"name": "generateDeviceCode", "paramTypes": ["java.lang.String"], "doc": " 生成设备编号\n"}, {"name": "checkDeviceCodeUnique", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 校验设备编号是否唯一\n"}, {"name": "uploadDeviceImage", "paramTypes": ["java.lang.Long", "org.springframework.web.multipart.MultipartFile"], "doc": " 上传设备图片\n"}, {"name": "uploadManualFile", "paramTypes": ["java.lang.Long", "org.springframework.web.multipart.MultipartFile"], "doc": " 上传使用说明书\n"}], "constructors": []}
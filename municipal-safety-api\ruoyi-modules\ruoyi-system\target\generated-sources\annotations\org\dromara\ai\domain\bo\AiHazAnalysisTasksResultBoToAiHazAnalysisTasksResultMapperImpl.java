package org.dromara.ai.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.ai.domain.AiHazAnalysisTasksResult;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class AiHazAnalysisTasksResultBoToAiHazAnalysisTasksResultMapperImpl implements AiHazAnalysisTasksResultBoToAiHazAnalysisTasksResultMapper {

    @Override
    public AiHazAnalysisTasksResult convert(AiHazAnalysisTasksResultBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AiHazAnalysisTasksResult aiHazAnalysisTasksResult = new AiHazAnalysisTasksResult();

        aiHazAnalysisTasksResult.setSearchValue( arg0.getSearchValue() );
        aiHazAnalysisTasksResult.setCreateDept( arg0.getCreateDept() );
        aiHazAnalysisTasksResult.setCreateBy( arg0.getCreateBy() );
        aiHazAnalysisTasksResult.setCreateTime( arg0.getCreateTime() );
        aiHazAnalysisTasksResult.setUpdateBy( arg0.getUpdateBy() );
        aiHazAnalysisTasksResult.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            aiHazAnalysisTasksResult.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        aiHazAnalysisTasksResult.setResultId( arg0.getResultId() );
        aiHazAnalysisTasksResult.setTaskId( arg0.getTaskId() );
        aiHazAnalysisTasksResult.setViolation( arg0.getViolation() );
        aiHazAnalysisTasksResult.setRegulation( arg0.getRegulation() );
        aiHazAnalysisTasksResult.setCoordinate( arg0.getCoordinate() );
        aiHazAnalysisTasksResult.setLevel( arg0.getLevel() );
        aiHazAnalysisTasksResult.setMeasure( arg0.getMeasure() );

        return aiHazAnalysisTasksResult;
    }

    @Override
    public AiHazAnalysisTasksResult convert(AiHazAnalysisTasksResultBo arg0, AiHazAnalysisTasksResult arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setResultId( arg0.getResultId() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setViolation( arg0.getViolation() );
        arg1.setRegulation( arg0.getRegulation() );
        arg1.setCoordinate( arg0.getCoordinate() );
        arg1.setLevel( arg0.getLevel() );
        arg1.setMeasure( arg0.getMeasure() );

        return arg1;
    }
}

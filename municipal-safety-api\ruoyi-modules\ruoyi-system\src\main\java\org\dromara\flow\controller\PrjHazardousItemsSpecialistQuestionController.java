package org.dromara.flow.controller;

import java.util.List;
import java.util.Map;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistQuestionBo;
import org.dromara.flow.domain.bo.PrjSpecialistQuestionDTO;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistQuestionVo;
import org.dromara.flow.service.IPrjHazardousItemsSpecialistQuestionService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;

/**
 * 省厅自动工单专家建议
 *
 * <AUTHOR> Li
 * @date 2025-06-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/hazardousItemsSpecialistQuestion")
public class PrjHazardousItemsSpecialistQuestionController extends BaseController {

    private final IPrjHazardousItemsSpecialistQuestionService prjHazardousItemsSpecialistQuestionService;

    /**
     * 查询省厅自动工单专家建议列表
     */
    @SaCheckPermission("system:hazardousItemsSpecialistQuestion:list")
    @GetMapping("/list")
    public TableDataInfo<PrjHazardousItemsSpecialistQuestionVo> list(PrjHazardousItemsSpecialistQuestionBo bo, PageQuery pageQuery) {
        return prjHazardousItemsSpecialistQuestionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出省厅自动工单专家建议列表
     */
    @SaCheckPermission("system:hazardousItemsSpecialistQuestion:export")
    @Log(title = "省厅自动工单专家建议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PrjHazardousItemsSpecialistQuestionBo bo, HttpServletResponse response) {
        List<PrjHazardousItemsSpecialistQuestionVo> list = prjHazardousItemsSpecialistQuestionService.queryList(bo);
        ExcelUtil.exportExcel(list, "省厅自动工单专家建议", PrjHazardousItemsSpecialistQuestionVo.class, response);
    }

    /**
     * 获取省厅自动工单专家建议详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:hazardousItemsSpecialistQuestion:query")
    @GetMapping("/{id}")
    public R<PrjHazardousItemsSpecialistQuestionVo> getInfo(@NotNull(message = "主键不能为空")
                                                            @PathVariable Long id) {
        return R.ok(prjHazardousItemsSpecialistQuestionService.queryById(id));
    }

    /**
     * 新增省厅自动工单专家建议
     */
    @SaCheckPermission("system:hazardousItemsSpecialistQuestion:add")
    @Log(title = "省厅自动工单专家建议", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PrjHazardousItemsSpecialistQuestionBo bo) {
        return toAjax(prjHazardousItemsSpecialistQuestionService.insertByBo(bo));
    }

    /**
     * 修改省厅自动工单专家建议
     */
    @SaCheckPermission("system:hazardousItemsSpecialistQuestion:edit")
    @Log(title = "省厅自动工单专家建议", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PrjHazardousItemsSpecialistQuestionBo bo) {
        return toAjax(prjHazardousItemsSpecialistQuestionService.updateByBo(bo));
    }

    /**
     * 删除省厅自动工单专家建议
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:hazardousItemsSpecialistQuestion:remove")
    @Log(title = "省厅自动工单专家建议", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(prjHazardousItemsSpecialistQuestionService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 批量新增省厅自动工单专家建议
     */
    @Log(title = "省厅自动工单专家建议", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/question")
    public R<Void> addBatch(@RequestBody List<PrjSpecialistQuestionDTO> questionDTOS) {
        return toAjax(prjHazardousItemsSpecialistQuestionService.addQuestion(questionDTOS));
    }

    /**
     * 查询专家意见
     */
    @GetMapping("/detail/{taskId}")
    public R<Map<String, Object>> detail(@PathVariable String taskId) {
        return R.ok(prjHazardousItemsSpecialistQuestionService.getDetail(taskId));
    }

    /**
     * 查询所有专家意见
     */
    @GetMapping("/detailAll/{taskId}")
    public R<Map<Long, List<PrjHazardousItemsSpecialistQuestionVo>>> detailAll(@PathVariable String taskId) {
        return R.ok(prjHazardousItemsSpecialistQuestionService.getDetailAll(taskId));
    }
}

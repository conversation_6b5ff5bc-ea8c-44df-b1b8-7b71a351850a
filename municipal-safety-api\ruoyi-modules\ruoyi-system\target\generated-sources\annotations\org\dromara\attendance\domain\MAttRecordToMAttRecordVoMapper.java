package org.dromara.attendance.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.attendance.domain.bo.MAttRecordBoToMAttRecordMapper;
import org.dromara.attendance.domain.vo.MAttRecordVo;
import org.dromara.attendance.domain.vo.MAttRecordVoToMAttRecordMapper;
import org.dromara.attendance.domain.vo.MAttRuleVoToMAttRuleMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {MAttRuleVoToMAttRuleMapper.class,MAttRuleToMAttRuleVoMapper.class,MAttRecordVoToMAttRecordMapper.class,MAttRecordBoToMAttRecordMapper.class},
    imports = {}
)
public interface MAttRecordToMAttRecordVoMapper extends BaseMapper<MAttRecord, MAttRecordVo> {
}

{"doc": " Excel 导入监听\n\n <AUTHOR>\n <AUTHOR> Li\n", "fields": [{"name": "isValidate", "doc": " 是否Validator检验，默认为是\n"}, {"name": "headMap", "doc": " excel 表头数据\n"}, {"name": "excelResult", "doc": " 导入回执\n"}], "enumConstants": [], "methods": [{"name": "onException", "paramTypes": ["java.lang.Exception", "com.alibaba.excel.context.AnalysisContext"], "doc": " 处理异常\n\n @param exception ExcelDataConvertException\n @param context   Excel 上下文\n"}], "constructors": []}
package org.dromara.attendance.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.attendance.domain.MAttSn;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 考勤设备业务对象 m_att_sn
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MAttSn.class, reverseConvertGenerate = false)
public class MAttSnBo extends BaseEntity {

    /**
     * 设备id
     */
    @NotNull(message = "设备id不能为空", groups = {EditGroup.class})
    private Long snId;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 设备sn号
     */
    private String sn;

    /**
     * 设备名称
     */
    private String snName;

    /**
     * 方向。0-进，1-出
     */
    private Long direction;

    /**
     * 设备状态。0-在线，1-掉线
     */
    private Long status;
}

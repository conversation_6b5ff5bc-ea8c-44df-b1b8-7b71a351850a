2025-05-06 09:02:50 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-06 09:02:50 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.15 with PID 3832 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-snailjob-server\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-06 09:02:50 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-05-06 09:02:52 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-05-06 09:02:52 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-05-06 09:02:52 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-06 09:02:52 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-05-06 09:02:52 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-05-06 09:02:52 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2117 ms
2025-05-06 09:02:54 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-05-06 09:02:55 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-06 09:02:55 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-8] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-06 09:02:55 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-06 09:02:55 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-06 09:02:56 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-05-06 09:02:56 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-05-06 09:02:56 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-05-06 09:02:56 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.4.0
2025-05-06 09:02:56 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-05-06 09:02:56 [main] INFO  c.a.s.s.common.rpc.server.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.GrpcServer, port = 17888
2025-05-06 09:02:56 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-05-06 09:02:56 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-05-06 09:02:56 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-05-06 09:02:56 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-05-06 09:02:56 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-05-06 09:02:56 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-05-06 09:02:56 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-05-06 09:02:56 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-05-06 09:02:56 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-05-06 09:02:56 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-05-06 09:02:56 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-05-06 09:02:56 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-05-06 09:02:56 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.4.0
2025-05-06 09:02:56 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 7.106 seconds (process running for 7.794)
2025-05-06 09:02:56 [snail-job-scheduled-thread-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-06 09:02:56 [RMI TCP Connection(3)-***************] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-06 09:02:56 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-06 09:02:56 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-05-06 09:02:57 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as cc20f5b8d840
2025-05-06 09:02:57 [snail-job-scheduled-thread-1] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3099667a
2025-05-06 09:02:57 [snail-job-scheduled-thread-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-06 09:02:57 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1919558453614714880]
2025-05-06 09:03:06 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[1] remoteNodeSize:[4]
2025-05-06 09:03:06 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-05-06 09:03:06 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-05-06 09:03:30 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-19] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1919558526171881472]
2025-05-06 09:17:45 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-284] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1918504200733401088]
2025-05-06 09:28:36 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-485] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919276597150371840]
2025-05-06 10:41:56 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-1843] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919562146875580416]
2025-05-06 10:41:56 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919562146875580416]
2025-05-06 14:38:06 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-6218] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919583313988640768]
2025-05-06 14:50:25 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-6447] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919642714208423936]
2025-05-06 14:57:56 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919645814931578880]
2025-05-06 15:39:25 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-7355] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919647697867837440]
2025-05-06 15:41:56 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-7401] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919658132692205568]
2025-05-06 15:50:15 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-7555] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919658745844973568]
2025-05-06 16:07:29 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-7875] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919662013308350464]
2025-05-06 16:14:16 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919665207438680064]
2025-05-06 16:17:16 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-8056] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919666926172233728]
2025-05-06 16:19:56 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919667715120701440]
2025-05-06 16:26:37 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-8230] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919668230978154496]
2025-05-06 16:32:15 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-8334] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919670078577512448]
2025-05-06 16:41:26 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-8505] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919671475008159744]
2025-05-06 16:45:56 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-8588] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919673755358978048]
2025-05-06 16:59:27 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-8839] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919674896381542400]
2025-05-06 17:12:16 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-9076] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919678304207466496]
2025-05-06 17:27:01 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-9344] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919558526171881472]
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.4.0
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-05-06 17:30:39 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1919558453614714880]
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.4.0
2025-05-06 17:30:39 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-05-06 17:30:39 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-06 17:30:39 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-06 17:35:05 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-06 17:35:05 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.15 with PID 2840 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-snailjob-server\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-06 17:35:05 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-05-06 17:35:06 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-05-06 17:35:06 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-05-06 17:35:06 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-06 17:35:06 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-05-06 17:35:06 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-05-06 17:35:06 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1655 ms
2025-05-06 17:35:08 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-05-06 17:35:09 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-06 17:35:09 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-06 17:35:09 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-06 17:35:09 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-06 17:35:10 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-05-06 17:35:10 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-05-06 17:35:10 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-05-06 17:35:10 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.4.0
2025-05-06 17:35:10 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-05-06 17:35:11 [main] INFO  c.a.s.s.common.rpc.server.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.GrpcServer, port = 17888
2025-05-06 17:35:11 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-05-06 17:35:11 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-05-06 17:35:11 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-05-06 17:35:11 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-05-06 17:35:11 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-05-06 17:35:11 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-05-06 17:35:11 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-05-06 17:35:11 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-05-06 17:35:11 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-05-06 17:35:11 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-05-06 17:35:11 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-05-06 17:35:11 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-05-06 17:35:11 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.4.0
2025-05-06 17:35:11 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 6.311 seconds (process running for 6.829)
2025-05-06 17:35:11 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-06 17:35:11 [RMI TCP Connection(3)-**********] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-06 17:35:11 [RMI TCP Connection(3)-**********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-06 17:35:11 [RMI TCP Connection(3)-**********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-05-06 17:35:11 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 67e6dd8f0c05
2025-05-06 17:35:12 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5bb07e44
2025-05-06 17:35:12 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-06 17:35:13 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1919687362569515008]
2025-05-06 17:35:21 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[1] remoteNodeSize:[4]
2025-05-06 17:35:21 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-05-06 17:35:21 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31]]
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.4.0
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-05-06 20:58:34 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1919687362569515008]
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.4.0
2025-05-06 20:58:34 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-05-06 20:58:34 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-06 20:58:34 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.

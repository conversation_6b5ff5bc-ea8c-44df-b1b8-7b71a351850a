package org.dromara.expert.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 *  专家主对象 expert
  * @date 2025-05-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("z_expert")
public class Expert extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 专家主键
     */
    @TableId(value = "expert_id")
    private Long expertId;

    /**
     * 名称
     */
    private String name;

    /**
     * 身份证件号
     */
    private String idCard;

    /**
     * 性别
     */
    private String sex;

    /**
     * 工作单位
     */
    private String workUnit;

    /**
     * 电话
     */
    private String phone;

    /**
     * 简介
     */
    private String introduce;

    /**
     * 职称
     */
    private String title;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在市
     */
    private String city;

    /**
     * 所在区
     */
    private String area;

    /**
     * 专业
     */
    private String major;

    /**
     * 行业
     */
    private String industry;

    /**
     * 0 正常 1 删除
     */
    @TableLogic
    private String delFlag;


    /** 专家类型 */
    private String type;

    /** 头像 */
    private String avatar;
}

package org.dromara.facility.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.facility.domain.vo.JlTowerRealVo;
import org.dromara.facility.domain.bo.JlTowerRealBo;
import org.dromara.facility.service.IJlTowerRealService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 塔机实时数据
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/towerReal")
public class JlTowerRealController extends BaseController {

    private final IJlTowerRealService jlTowerRealService;

    /**
     * 查询塔机实时数据列表
     */
    @GetMapping("/list")
    public TableDataInfo<JlTowerRealVo> list(JlTowerRealBo bo, PageQuery pageQuery) {
        return jlTowerRealService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取塔机实时数据详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<JlTowerRealVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(jlTowerRealService.queryById(id));
    }
}

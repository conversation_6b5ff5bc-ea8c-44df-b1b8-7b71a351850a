<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <!--            <el-form-item label="计划开始时间" prop="beginTime">-->
            <!--              <el-date-picker clearable-->
            <!--                v-model="queryParams.beginTime"-->
            <!--                type="date"-->
            <!--                value-format="YYYY-MM-DD"-->
            <!--                placeholder="请选择计划开始时间"-->
            <!--              />-->
            <!--            </el-form-item>-->
            <!--            <el-form-item label="计划结束时间" prop="endTime">-->
            <!--              <el-date-picker clearable-->
            <!--                v-model="queryParams.endTime"-->
            <!--                type="date"-->
            <!--                value-format="YYYY-MM-DD"-->
            <!--                placeholder="请选择计划结束时间"-->
            <!--              />-->
            <!--            </el-form-item>-->

            <el-form-item label="项目名称" prop="projectIds">
              <!-- <el-input v-model="queryParams.projectIds" placeholder="请输入项目名称" clearable @keyup.enter="handleQuery" /> -->
              <el-select v-model="queryParams.projectIds" filterable placeholder="请选择项目" style="width: 240px" clearable
                @change="changePro">
                <el-option v-for="item in projectSelectData" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="参与部门  " prop="deptIds">
              <!-- <el-input v-model="queryParams.deptIds" placeholder="请输入监督机构名称" clearable @keyup.enter="handleQuery" /> -->

              <el-select clearable v-model="queryParams.deptIds" placeholder="协助部门" filterable allow-create size="large"
                style="width: 240px">
                <el-option v-for="item in cydeptList" :key="item.deptId" :label="item.deptName" :value="item.deptId" />
              </el-select>
            </el-form-item>
            <el-form-item label="专家名称" prop="expertIds">
              <!-- <el-input v-model="queryParams.expertIds" placeholder="请输入专家名称" clearable @keyup.enter="handleQuery" /> -->
              <el-select clearable v-model="queryParams.expertIds" placeholder="专家名称" filterable allow-create
                size="large" style="width: 240px">
                <el-option v-for="item in cygetexpertList" :key="item.deptId" :label="item.name"
                  :value="item.expertId" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['plan:patrolPlan:add']">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['plan:patrolPlan:edit']"
              >修改</el-button
            >
          </el-col> -->
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['plan:patrolPlan:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['plan:patrolPlan:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table show-overflow-tooltip v-loading="loading" :data="patrolPlanList"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="计划名称" align="center" prop="planName" />
        <el-table-column label="计划描述" align="center" prop="remarks" />
        <el-table-column label="计划开始时间" align="center" prop="beginTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.beginTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="计划结束时间" align="center" prop="endTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="参与部门  " align="center" prop="deptIds">
          <template #default="scope">
            {{Array.isArray(scope.row.deptList) ? scope.row.deptList.map((e) => e.deptName).join(',') : ''}}
          </template>
        </el-table-column>
        <!-- <el-table-column label="项目名称" align="center" prop="projectIds" /> -->
        <el-table-column label="项目名称" align="center" prop="projectList">
          <template #default="scope">
            {{Array.isArray(scope.row.projectList) ? scope.row.projectList.map((e) => e.projectName).join(',') : ''}}
          </template>
        </el-table-column>
        <el-table-column label="专家名称" align="center" prop="expertList">
          <template #default="scope">
            {{Array.isArray(scope.row.expertList) ? scope.row.expertList.map((e) => e.name).join(',') : ''}}
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row, 1)"
                v-hasPermi="['plan:patrolPlan:edit']"></el-button>
            </el-tooltip>

            <el-tooltip content="隐患列表" placement="top">
              <el-button link type="primary" icon="Document" @click="handleDetail(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="List" @click="handleUpdate(scope.row, 2)"
                v-hasPermi="['plan:patrolPlan:ch']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['plan:patrolPlan:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改巡检计划对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="50%" append-to-body>
      <el-form ref="patrolPlanFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="计划名称" prop="planName">
          <el-input :disabled="typevalue == 2" v-model="form.planName" type="text" style="width: 400px"
            placeholder="请输入内容" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划开始时间" prop="beginTime">
              <el-date-picker :disabled="typevalue == 2" style="width: 400px" clearable v-model="form.beginTime"
                type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择计划开始时间" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划结束时间" prop="endTime">
              <el-date-picker :disabled="typevalue == 2" style="width: 400px" clearable v-model="form.endTime"
                type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择计划结束时间" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="计划描述" prop="remarks">
          <el-input :disabled="typevalue == 2" v-model="form.remarks" type="textarea" placeholder="" />
        </el-form-item>
        <el-form-item label="参与部门" prop="deptIds">
          <!-- <el-select v-model="form.deptIds" placeholder="协助部门可多选" multiple filterable allow-create size="large" style="width: 240px">
            <el-option v-for="item in areadeptList" :key="item.deptId" :label="item.deptName" :value="item.deptId" />
          </el-select> -->
          <div class="flex items-center" style="width: 100%">
            <div class="flex-1 min-h-[32px]">
              <el-table border :data="areadeptList">
                <el-table-column label="部门名称" align="center" prop="deptName" />
                <el-table-column v-if="typevalue != 2" label="操作" align="center" prop="status">
                  <template #default="scope">
                    <el-button text type="danger" @click="handleRemovedept(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <el-button-group v-if="typevalue != 2" class="ml-2">
              <el-button @click="selectDeptChange">选择</el-button>
            </el-button-group>
          </div>
        </el-form-item>

        <el-form-item label="项目名称" prop="projectIds">
          <div class="flex items-center" style="width: 100%">
            <div class="flex-1 min-h-[32px]">
              <el-table border :data="selectedProjects">
                <el-table-column label="项目名称" align="center" prop="projectName" />
                <el-table-column label="项目编码/标识" align="center" prop="projectCode" />
                <!--        <el-table-column label="工程概况" align="center" prop="projectOverview" show-overflow-tooltip />-->
                <el-table-column label="施工许可证编号" align="center" prop="constructionPermitNo" />
                <el-table-column label="所在地区" align="center">
                  <template #default="scope">
                    {{ [scope.row.provinceName, scope.row.cityName, scope.row.districtName].filter(Boolean).join('/') ||
                      '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="项目位置" align="center" prop="locationDetail"></el-table-column>>
                <el-table-column v-if="typevalue != 2" label="操作" align="center" prop="status">
                  <template #default="scope">
                    <el-button text type="danger" @click="handleRemoveProject(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <el-button-group v-if="typevalue != 2" class="ml-2">
              <el-button @click="selectProjectChange">选择</el-button>
            </el-button-group>
          </div>
        </el-form-item>
        <el-form-item label="专家名称" prop="expertIds">
          <div class="flex items-center" style="width: 100%">
            <div class="flex-1 min-h-[32px]">
              <div class="flex items-center flex-wrap gap-x-2 gap-y-1">
                <!-- <el-tag type="success" v-for="item in selectedexpertIds" :key="item.expertId" closable @close="handleRemoveexpertIds(item)">
                  {{ item.name }}
                </el-tag> -->
                <el-table border :data="selectedexpertIds">
                  <el-table-column label="名称" align="center" prop="name" />
                  <el-table-column label="身份证号" align="center" prop="idCard" />
                  <el-table-column label="性别" align="center" prop="sex">
                    <template #default="scope">
                      <dict-tag :options="sys_user_sex" :value="scope.row.sex" />
                    </template>
                  </el-table-column>
                  <el-table-column label="工作单位" align="center" prop="workUnit" />
                  <el-table-column label="电话" align="center" prop="phone" />
                  <el-table-column label="职称" align="center" prop="title" />
                  <el-table-column v-if="typevalue != 2" label="操作" align="center" prop="status">
                    <template #default="scope">
                      <el-button text type="danger" @click="handleRemoveexpertIds(scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <el-button-group v-if="typevalue != 2" class="ml-2">
              <el-button @click="selectxpertChange">选择</el-button>
            </el-button-group>
          </div>
        </el-form-item>
        <!-- <el-form-item label="专家名称" prop="expertIds">
          <el-input v-model="form.expertIds" type="textarea" placeholder="请输入内容" />
        </el-form-item> -->
      </el-form>
      <template #footer v-if="typevalue != 2">
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入监控管理对话框 -->
    <Project :isShowModel="isShowModel" @update:isShowModel="isShowModelChange"
      @selectionProjectData="handleSelectionProject" />
    <!-- 专家管理 -->
    <Expertmanagement :isShowModel="showExpertModel" @update:showExpertModel="showExpertModelChange"
      @selectionProjectData="handleSelectionexpert" />
    <!-- 部门管理 -->
    <DepartmentManagement :isShowModel="showExpertModel2" @update:showExpertModel2="showExpertModelChange2"
      @selectionProjectData="handleSelectionexpert2" />
    <el-dialog style="width: 70%" v-model="dialogdeil.visible" :title="dialogdeil.title">
      <Aitasks v-if="dialogdeil.visible" :deilplanId="deilplanId" />
    </el-dialog>
  </div>
</template>

<script setup name="PatrolPlan" lang="ts">
import { listPatrolPlan, getPatrolPlan, delPatrolPlan, addPatrolPlan, updatePatrolPlan, deptList, expertList } from '@/api/plan/patrolPlan';
import { PatrolPlanVO, PatrolPlanQuery, PatrolPlanForm } from '@/api/plan/patrolPlan/types';
import Project from '@/components/Project/indexcheck.vue';
import Expertmanagement from '@/components/Expertmanagement/index.vue';
import DepartmentManagement from '@/components/DepartmentManagement/index.vue';
import Aitasks from '@/views/ai/ai_haz_analysis_tasks/indexsub.vue';
import { watch } from 'vue';
import { get_prj_search_data, getItemList } from '@/api/projects/prj_hazardous_items';
import { listAi_haz_analysis_tasks, getAi_haz_analysis_tasks, AIProblem } from '@/api/ai/ai_haz_analysis_tasks';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_user_sex } = toRefs<any>(proxy?.useDict('sys_user_sex'));
const selectedExperts = ref([]);
const patrolPlanList = ref<PatrolPlanVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const isShowModel = ref(false);
const showExpertModel = ref(false);
const showExpertModel2 = ref(false);
const selectedProjects = ref<{ projectId: string | number; projectName: string }[]>([]);
const getItemData = ref([]);
const cydeptList = ref([]);
const cygetexpertList = ref([]);
const typevalue = ref();

const projectSelectData = ref([]);
const queryFormRef = ref<ElFormInstance>();
const patrolPlanFormRef = ref<ElFormInstance>();
const selectedexpertIds = ref([]); // 建议改名为 selectedExperts
const selectedProjectsid = ref('');
const deilplanId = ref('');
const selectedexpertid = ref('');
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const areadeptList = ref([]);
const initFormData = {
  planId: undefined,
  planName: undefined,
  beginTime: undefined,
  endTime: undefined,
  mainDeptId: undefined,
  executeDeptId: undefined,
  deptIds: undefined,
  projectIds: undefined,
  expertIds: undefined,
  remarks: undefined
};
const data = reactive<PageData<PatrolPlanForm, PatrolPlanQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    beginTime: undefined,
    endTime: undefined,
    deptIds: undefined,
    projectIds: undefined,
    expertIds: undefined,
    remarks: undefined,
    params: {}
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);
const dialogdeil = reactive({
  visible: false,
  title: ''
});
/** 查询巡检计划列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPatrolPlan(queryParams.value);
  patrolPlanList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};
//查询项目选择框数据
const getProjectSelectData = async () => {
  const { data } = await get_prj_search_data();
  projectSelectData.value = data;
};
const changePro = async (val) => {
  getItemList(val).then((res) => {
    getItemData.value = res.data;
  });
};
/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
// 专家选择
const selectxpertChange = () => {
  showExpertModel.value = true;
};
// 部门选择
const selectDeptChange = () => {
  showExpertModel2.value = true;
};

// 项目选择
const selectProjectChange = () => {
  isShowModel.value = true;
};
const isShowModelChange = (val: boolean) => {
  isShowModel.value = val;
};
const showExpertModelChange = (val: boolean) => {
  showExpertModel.value = val;
};
const showExpertModelChange2 = (val: boolean) => {
  showExpertModel2.value = val;
};

// const handleSelectionProject = (data) => {
//   console.log('data', data);
//   // form.value.projectId = data.projectId;
//   form.value.projectIds = data.map((item) => item.projectName).join(',');
//   // form.value.projectName = data.projectName;
//   // itemId.value = data.projectId;
//   // console.log('data.projectId', data.projectId, 'data.projectName', data.projectName, 'data.projectId', data.projectId);

//   isShowModel.value = false;
// };
const handleSelectionProject = (data) => {
  let hasDuplicate = false;

  console.log('data', data, selectedProjectsid);
  selectedProjectsid.value = data.map((item) => item.projectId).join(',');
  const filteredData = data.filter((item) => {
    const exists = selectedProjects.value.some((selected) => selected.projectId === item.projectId);
    if (exists) {
      hasDuplicate = true;
      proxy?.$modal.msgWarning(`【${item.projectName}】已添加`);
    }
    return !exists;
  });

  if (filteredData.length > 0) {
    selectedProjects.value = [...selectedProjects.value, ...filteredData];
    form.value.projectIds = selectedProjects.value.map((item) => item.projectName).join(',');
  }
  isShowModel.value = false;
};
const handleSelectionexpert = (data) => {
  console.log('handleSelectionexpert', data);
  selectedexpertid.value = data.map((item) => item.expertId).join(',');

  let hasDuplicate = false;
  const filteredData = data.filter((item) => {
    const exists = selectedexpertIds.value.some((selected) => selected.expertId === item.expertId);
    if (exists) {
      hasDuplicate = true;
      proxy?.$modal.msgWarning(`【${item.name}】已添加`);
    }
    return !exists;
  });

  if (filteredData.length > 0) {
    selectedexpertIds.value = [...selectedexpertIds.value, ...filteredData];
    form.value.expertIds = selectedexpertIds.value.map((item) => item.name).join(',');
  }

  showExpertModel.value = false;
};
const handleSelectionexpert2 = (data) => {
  console.log('handleSelectionexpert', data);

  let hasDuplicate = false;
  const filteredData = data.filter((item) => {
    const exists = areadeptList.value.some((selected) => selected.deptId === item.deptId);
    if (exists) {
      hasDuplicate = true;
      proxy?.$modal.msgWarning(`【${item.deptName}】已添加`);
    }
    return !exists;
  });

  if (filteredData.length > 0) {
    areadeptList.value = [...areadeptList.value, ...filteredData];
    form.value.deptIds = areadeptList.value.map((item) => item.deptName).join(',');
  }

  showExpertModel2.value = false;
};

const handleRemoveexpertIds = (itemToRemove) => {
  selectedexpertIds.value = selectedexpertIds.value.filter((item) => item.expertId !== itemToRemove.expertId);
  form.value.expertIds = selectedexpertIds.value.map((item) => item.name).join(',');
};
const handleRemoveProject = (itemToRemove) => {
  selectedProjects.value = selectedProjects.value.filter((item) => item.projectId !== itemToRemove.projectId);
  form.value.projectIds = selectedProjects.value.map((item) => item.projectName).join(',');
};
const handleRemovedept = (itemToRemove) => {
  areadeptList.value = areadeptList.value.filter((item) => item.deptId !== itemToRemove.deptId);
  form.value.deptIds = areadeptList.value.map((item) => item.deptId).join(',');
};
/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  patrolPlanFormRef.value?.resetFields();
  areadeptList.value = [];
  selectedProjects.value = [];
  selectedexpertIds.value = [];
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: PatrolPlanVO[]) => {
  ids.value = selection.map((item) => item.planId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加巡检计划';
  typevalue.value = 3;
};

// /** 修改按钮操作 */
// const handleUpdate = async (row?: PatrolPlanVO) => {
//   reset();
//   const _planId = row?.planId || ids.value[0];
//   const res = await getPatrolPlan(_planId);
//   Object.assign(form.value, res.data);
//   dialog.visible = true;
//   dialog.title = '修改巡检计划';
// };
const handleUpdate = async (row, type) => {
  typevalue.value = type;
  reset();
  const _planId = row?.planId || ids.value[0];
  const res = await getPatrolPlan(_planId);
  console.log('res', res);

  // 回显表单数据
  Object.assign(form.value, res.data);

  // 回显项目信息（假设 res.data.projects 是从接口返回的项目列表）
  if (res.data.projectIds && res.data.projectIds.length > 0) {
    selectedProjects.value = res.data.projectList;
    form.value.projectIds = selectedProjects.value.map((p) => p.projectId);
  }

  // 回显专家信息（假设 res.data.experts 是从接口返回的专家列表）
  if (res.data.expertList && res.data.expertList.length > 0) {
    selectedexpertIds.value = res.data.expertList;
    form.value.expertIds = selectedexpertIds.value.map((e) => e.expertId);
  }
  // 回显部门信息（假设 res.data.experts 是从接口返回的专家列表）
  if (res.data.deptList && res.data.deptList.length > 0) {
    areadeptList.value = res.data.deptList;
    form.value.deptIds = areadeptList.value.map((e) => e.deptId);
  }

  dialog.visible = true;
  if (type == 1) {
    dialog.title = '修改巡检计划';
  } else {
    dialog.title = '巡检计划详情';
  }
};
// /** 提交按钮 */
// const submitForm = () => {
//   ElMessageBox.confirm(`是否${typevalue.value == 3 ? '新增' : '修改'}巡检计划？`, { type: 'warning' })
//     .then(() => {
//       patrolPlanFormRef.value?.validate(async (valid: boolean) => {
//         if (valid) {
//           // buttonLoading.value = true;

//           // 替换 projectIds 为 projectId 列表
//           form.value.projectIds = selectedProjects.value.map((item) => item.projectId).join(',');

//           // 替换 expertIds 为 expertId 列表
//           form.value.expertIds = selectedexpertIds.value.map((item) => item.expertId).join(',');
//           // 替换 expertIds 为 expertId 列表
//           form.value.deptIds = areadeptList.value.map((item) => item.deptId).join(',');
//           console.log('form.value', form.value);
//           try {
//             if (form.value.planId) {
//               await updatePatrolPlan(form.value);
//             } else {
//               await addPatrolPlan(form.value);
//             }
//             proxy?.$modal.msgSuccess('操作成功');
//             dialog.visible = false;
//             await getList();
//           } finally {
//             buttonLoading.value = false;
//           }
//         }
//       });
//     })
//     .catch(() => {});
// };
/** 提交按钮 */
const submitForm = () => {
  ElMessageBox.confirm(`是否${typevalue.value == 3 ? '新增' : '修改'}巡检计划？`, { type: 'warning' })
    .then(() => {
      patrolPlanFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
          // 校验必填字段
          const requiredFields = [
            { field: form.value.planName, message: '计划名称不能为空' },
            { field: form.value.beginTime, message: '计划开始时间不能为空' },
            { field: form.value.endTime, message: '计划结束时间不能为空' },
            { field: form.value.remarks, message: '计划描述不能为空' },
            { field: areadeptList.value.length > 0, message: '请选择参与部门' },
            { field: selectedProjects.value.length > 0, message: '请选择项目名称' },
            { field: selectedexpertIds.value.length > 0, message: '请选择专家名称' }
          ];

          for (const { field, message } of requiredFields) {
            if (!field) {
              proxy?.$modal.msgWarning(message);
              return;
            }
          }

          // 替换 projectIds 为 projectId 列表
          form.value.projectIds = selectedProjects.value.map((item) => item.projectId).join(',');

          // 替换 expertIds 为 expertId 列表
          form.value.expertIds = selectedexpertIds.value.map((item) => item.expertId).join(',');
          // 替换 deptIds 为 deptId 列表
          form.value.deptIds = areadeptList.value.map((item) => item.deptId).join(',');

          try {
            if (form.value.planId) {
              await updatePatrolPlan(form.value);
            } else {
              await addPatrolPlan(form.value);
            }
            proxy?.$modal.msgSuccess('操作成功');
            dialog.visible = false;
            await getList();
          } finally {
            buttonLoading.value = false;
          }
        }
      });
    })
    .catch(() => { });
};
/** 删除按钮操作 */
const handleDelete = async (row?: PatrolPlanVO) => {
  const _planIds = row?.planId || ids.value;
  await proxy?.$modal.confirm('是否确认删除巡检计划编号为"' + _planIds + '"的数据项？').finally(() => (loading.value = false));
  await delPatrolPlan(_planIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'plan/patrolPlan/export',
    {
      ...queryParams.value
    },
    `patrolPlan_${new Date().getTime()}.xlsx`
  );
};
/** 参与部门筛选数据 */
const getdeptList = async () => {
  const res = await deptList();
  if (res.code == 200) {
    cydeptList.value = res.data;
  }
};
/** 参与部门筛选数据 */
const getexpertList = async () => {
  const res = await expertList();
  if (res.code == 200) {
    cygetexpertList.value = res.data;
  }
};

/** 查看详情按钮操作 */
const handleDetail = async (row) => {
  deilplanId.value = row.planId;
  dialogdeil.visible = true;
};
onMounted(() => {
  getList();
  getProjectSelectData();
  getdeptList();
  getexpertList();
});
</script>

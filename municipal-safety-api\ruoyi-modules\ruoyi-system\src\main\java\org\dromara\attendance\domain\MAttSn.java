package org.dromara.attendance.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 考勤设备对象 m_att_sn
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("m_att_sn")
public class MAttSn extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    @TableId(value = "sn_id")
    private Long snId;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 设备sn号
     */
    private String sn;

    /**
     * 设备名称
     */
    private String snName;

    /**
     * 方向。0-进，1-出
     */
    private Long direction;

    /**
     * 设备状态。0-在线，1-掉线
     */
    private Long status;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;


}

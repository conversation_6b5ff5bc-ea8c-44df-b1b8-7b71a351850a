import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { Ai_haz_analysis_tasksVO, Ai_haz_analysis_tasksForm, Ai_haz_analysis_tasksQuery } from '@/api/ai/ai_haz_analysis_tasks/types';

/**
 * 查询智能隐患分析任务列表
 * @param query
 * @returns {*}
 */

export const listAi_haz_analysis_tasks = (query?: Ai_haz_analysis_tasksQuery): AxiosPromise<Ai_haz_analysis_tasksVO[]> => {
  return request({
    url: '/ai/ai_haz_analysis_tasks/list',
    method: 'get',
    params: query
  });
};
export const AIProblem = (query?: Ai_haz_analysis_tasksQuery): AxiosPromise<Ai_haz_analysis_tasksVO[]> => {
  return request({
    url: '/plan/patrolPlan/AIProblem',
    method: 'get',
    params: query
  });
};

/**
 * 查询智能隐患分析任务详细
 * @param taskId
 */
export const getAi_haz_analysis_tasks = (taskId: string | number): AxiosPromise<Ai_haz_analysis_tasksVO> => {
  return request({
    url: '/ai/ai_haz_analysis_tasks/' + taskId,
    method: 'get'
  });
};

/**
 * 新增智能隐患分析任务
 * @param data
 */
export const addAi_haz_analysis_tasks = (data: Ai_haz_analysis_tasksForm) => {
  return request({
    url: '/ai/ai_haz_analysis_tasks',
    method: 'post',
    data: data
  });
};

/**
 * 修改智能隐患分析任务
 * @param data
 */
export const updateAi_haz_analysis_tasks = (data: Ai_haz_analysis_tasksForm) => {
  return request({
    url: '/ai/ai_haz_analysis_tasks',
    method: 'put',
    data: data
  });
};

/**
 * 删除智能隐患分析任务
 * @param taskId
 */
export const delAi_haz_analysis_tasks = (taskId: string | number | Array<string | number>) => {
  return request({
    url: '/ai/ai_haz_analysis_tasks/' + taskId,
    method: 'delete'
  });
};


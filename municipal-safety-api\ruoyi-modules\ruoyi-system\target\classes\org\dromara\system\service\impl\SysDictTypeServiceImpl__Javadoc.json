{"doc": " 字典 业务层处理\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectDictTypeList", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": " 根据条件分页查询字典类型\n\n @param dictType 字典类型信息\n @return 字典类型集合信息\n"}, {"name": "selectDictTypeAll", "paramTypes": [], "doc": " 根据所有字典类型\n\n @return 字典类型集合信息\n"}, {"name": "selectDictDataByType", "paramTypes": ["java.lang.String"], "doc": " 根据字典类型查询字典数据\n\n @param dictType 字典类型\n @return 字典数据集合信息\n"}, {"name": "selectDictTypeById", "paramTypes": ["java.lang.Long"], "doc": " 根据字典类型ID查询信息\n\n @param dictId 字典类型ID\n @return 字典类型\n"}, {"name": "selectDictTypeByType", "paramTypes": ["java.lang.String"], "doc": " 根据字典类型查询信息\n\n @param dictType 字典类型\n @return 字典类型\n"}, {"name": "deleteDictTypeByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除字典类型信息\n\n @param dictIds 需要删除的字典ID\n"}, {"name": "resetDictCache", "paramTypes": [], "doc": " 重置字典缓存数据\n"}, {"name": "insertDictType", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": " 新增保存字典类型信息\n\n @param bo 字典类型信息\n @return 结果\n"}, {"name": "updateDictType", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": " 修改保存字典类型信息\n\n @param bo 字典类型信息\n @return 结果\n"}, {"name": "checkDictTypeUnique", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": " 校验字典类型称是否唯一\n\n @param dictType 字典类型\n @return 结果\n"}, {"name": "getDictLabel", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 根据字典类型和字典值获取字典标签\n\n @param dictType  字典类型\n @param dictValue 字典值\n @param separator 分隔符\n @return 字典标签\n"}, {"name": "getDictValue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 根据字典类型和字典标签获取字典值\n\n @param dictType  字典类型\n @param dictLabel 字典标签\n @param separator 分隔符\n @return 字典值\n"}, {"name": "getAllDictByDictType", "paramTypes": ["java.lang.String"], "doc": " 获取字典下所有的字典值与标签\n\n @param dictType 字典类型\n @return dictValue为key，dictLabel为值组成的Map\n"}, {"name": "getDictType", "paramTypes": ["java.lang.String"], "doc": " 根据字典类型查询详细信息\n\n @param dictType 字典类型\n @return 字典类型详细信息\n"}, {"name": "getDictData", "paramTypes": ["java.lang.String"], "doc": " 根据字典类型查询字典数据列表\n\n @param dictType 字典类型\n @return 字典数据列表\n"}], "constructors": []}
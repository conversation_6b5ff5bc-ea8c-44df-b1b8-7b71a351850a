<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.XGRYMapper">

    <resultMap id="XgryResultMap" type="org.dromara.system.domain.XGRY">
        <id property="id" column="ID"/>
        <result property="recordNum" column="RECORDNUM"/>
        <result property="constructionPermitNum" column="CONSTRUCTIONPERMITNUM"/>
        <result property="name" column="NAME"/>
        <result property="identityCard" column="IDENTITYCARD"/>
        <result property="corpName" column="CORPNAME"/>
        <result property="corpCode" column="CORPCODE"/>
        <result property="lxdh" column="LXDH"/>
        <result property="lbfl" column="LBFL"/>
        <result property="personType" column="PERSONTYPE"/>
        <result property="zsbh" column="ZSBH"/>
        <result property="delFlag" column="DEL_FLAG"/>
    </resultMap>

    <!-- Insert a new XGRY record -->
    <insert id="insertXgry" parameterType="org.dromara.system.domain.XGRY" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO XGRY (RECORDNUM,
                          CONSTRUCTIONPERMITNUM,
                          NAME,
                          IDENTITYCARD,
                          CORPNAME,
                          CORPCODE,
                          LXDH,
                          LBFL,
                          PERSONTYPE,
                          ZSBH,
                          DEL_FLAG)
        VALUES (#{recordNum},
                #{constructionPermitNum},
                #{name},
                #{identityCard},
                #{corpName},
                #{corpCode},
                #{lxdh},
                #{lbfl},
                #{personType},
                #{zsbh},
                '0')
    </insert>

    <!-- 批量插入XGRY操作 -->
    <insert id="batchInsertXgry" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO XGRY (
        RECORDNUM,
        CONSTRUCTIONPERMITNUM,
        NAME,
        IDENTITYCARD,
        CORPNAME,
        CORPCODE,
        LXDH,
        LBFL,
        PERSONTYPE,
        ZSBH,
        DEL_FLAG
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.recordNum},
            #{item.constructionPermitNum},
            #{item.name},
            #{item.identityCard},
            #{item.corpName},
            #{item.corpCode},
            #{item.lxdh},
            #{item.lbfl},
            #{item.personType},
            #{item.zsbh},
            '0'
            )
        </foreach>
    </insert>

    <!-- Update an existing XGRY record -->
    <update id="updateXgry" parameterType="org.dromara.system.domain.XGRY">
        UPDATE XGRY
        SET RECORDNUM             = #{recordNum},
            CONSTRUCTIONPERMITNUM = #{constructionPermitNum},
            NAME                  = #{name},
            IDENTITYCARD          = #{identityCard},
            CORPNAME              = #{corpName},
            CORPCODE              = #{corpCode},
            LXDH                  = #{lxdh},
            LBFL                  = #{lbfl},
            PERSONTYPE            = #{personType},
            ZSBH                  = #{zsbh}
        WHERE ID = #{id}
          AND DEL_FLAG = '0'
    </update>

    <!-- Soft delete a XGRY record (set del_flag to '1') -->
    <update id="deleteXgryById" parameterType="java.lang.Integer">
        UPDATE XGRY
        SET DEL_FLAG = '1'
        WHERE ID = #{id}
          AND DEL_FLAG = '0'
    </update>

    <!-- Select XGRY by ID -->
    <select id="selectXgryById" parameterType="java.lang.Integer" resultMap="XgryResultMap">
        SELECT *
        FROM XGRY
        WHERE ID = #{id}
          AND DEL_FLAG = '0'
    </select>

    <!-- Select all XGRY records (not deleted) -->
    <select id="selectAllXgry" resultMap="XgryResultMap">
        SELECT *
        FROM XGRY
        WHERE DEL_FLAG = '0'
    </select>

    <!-- Select XGRY by record number -->
    <select id="selectXgryByRecordNum" parameterType="java.lang.String" resultMap="XgryResultMap">
        SELECT *
        FROM XGRY
        WHERE RECORDNUM = #{recordNum}
          AND DEL_FLAG = '0'
    </select>

    <!-- Select XGRY by identity card -->
    <select id="selectXgryByIdentityCard" parameterType="java.lang.String" resultMap="XgryResultMap">
        SELECT *
        FROM XGRY
        WHERE IDENTITYCARD = #{identityCard}
          AND DEL_FLAG = '0'
    </select>

    <!-- Select XGRY by construction permit number -->
    <select id="selectXgryByConstructionPermitNum" parameterType="java.lang.String" resultMap="XgryResultMap">
        SELECT *
        FROM XGRY
        WHERE CONSTRUCTIONPERMITNUM = #{constructionPermitNum}
          AND DEL_FLAG = '0'
    </select>

    <!-- Check if a record number exists -->
    <select id="checkRecordNumExists" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM XGRY
        WHERE RECORDNUM = #{recordNum}
          AND DEL_FLAG = '0'
    </select>
    <delete id="deleteAllPhysically">
        DELETE FROM XGRY
    </delete>
</mapper>

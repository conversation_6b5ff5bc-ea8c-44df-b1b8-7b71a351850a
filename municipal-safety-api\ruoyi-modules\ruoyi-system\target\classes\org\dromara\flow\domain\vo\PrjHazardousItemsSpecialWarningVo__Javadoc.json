{"doc": " 特殊预警视图对象 prj_hazardous_items_special_warning\n\n <AUTHOR>\n @date 2025-06-19\n", "fields": [{"name": "warningId", "doc": " 特殊预警id\n"}, {"name": "taskId", "doc": " 流程业务id\n"}, {"name": "reason", "doc": " 预警原因（质监站可补充）\n"}, {"name": "reasonType", "doc": " 预警类型(字典special_warning_type)\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "projectId", "doc": " 项目id\n"}, {"name": "projectName", "doc": " 项目名称\n"}, {"name": "itemId", "doc": " 工程id\n"}, {"name": "itemName", "doc": " 工程名称\n"}, {"name": "hazAnalysisId", "doc": " ai识别id\n"}, {"name": "businessId", "doc": " 业务流程id\n"}], "enumConstants": [], "methods": [], "constructors": []}
package org.dromara.facility.service.impl;

import com.alibaba.fastjson2.JSON;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.facility.domain.bo.LnNutBo;
import org.dromara.facility.domain.vo.LnNutVo;
import org.dromara.facility.domain.LnNut;
import org.dromara.facility.mapper.LnNutMapper;
import org.dromara.facility.service.ILnNutService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 绿能螺母Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@RequiredArgsConstructor
@Service
public class LnNutServiceImpl implements ILnNutService {

    private final LnNutMapper baseMapper;

    /**
     * 查询绿能螺母
     *
     * @param id 主键
     * @return 绿能螺母
     */
    @Override
    public LnNutVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询绿能螺母列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能螺母分页列表
     */
    @Override
    public TableDataInfo<LnNutVo> queryPageList(LnNutBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LnNut> lqw = buildQueryWrapper(bo);
        Page<LnNutVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的绿能螺母列表
     *
     * @param bo 查询条件
     * @return 绿能螺母列表
     */
    @Override
    public List<LnNutVo> queryList(LnNutBo bo) {
        LambdaQueryWrapper<LnNut> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LnNut> buildQueryWrapper(LnNutBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LnNut> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LnNut::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getPara()), LnNut::getPara, bo.getPara());
        lqw.eq(StringUtils.isNotBlank(bo.getVt()), LnNut::getVt, bo.getVt());
        lqw.eq(StringUtils.isNotBlank(bo.getQds()), LnNut::getQds, bo.getQds());
        lqw.eq(bo.getTime() != null, LnNut::getTime, bo.getTime());
        lqw.eq(StringUtils.isNotBlank(bo.getValue()), LnNut::getValue, bo.getValue());
        lqw.eq(StringUtils.isNotBlank(bo.getDevNo()), LnNut::getDevNo, bo.getDevNo());
        return lqw;
    }

    /**
     * 新增绿能螺母
     *
     * @param bo 绿能螺母
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LnNutBo bo) {
        LnNut add = MapstructUtils.convert(bo, LnNut.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public void insertByJson(String jsonString) {
        LnNut add = MapstructUtils.convert(JSON.parseObject(jsonString, LnNutBo.class), LnNut.class);
        assert add != null;
        add.setCreateTime(new Date());
        baseMapper.insert(add);
    }

    /**
     * 修改绿能螺母
     *
     * @param bo 绿能螺母
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LnNutBo bo) {
        LnNut update = MapstructUtils.convert(bo, LnNut.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LnNut entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除绿能螺母信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


}

package org.dromara.flow.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.flow.domain.PrjHazardousItemsFile;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsFileBoToPrjHazardousItemsFileMapperImpl implements PrjHazardousItemsFileBoToPrjHazardousItemsFileMapper {

    @Override
    public PrjHazardousItemsFile convert(PrjHazardousItemsFileBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsFile prjHazardousItemsFile = new PrjHazardousItemsFile();

        prjHazardousItemsFile.setSearchValue( arg0.getSearchValue() );
        prjHazardousItemsFile.setCreateDept( arg0.getCreateDept() );
        prjHazardousItemsFile.setCreateBy( arg0.getCreateBy() );
        prjHazardousItemsFile.setCreateTime( arg0.getCreateTime() );
        prjHazardousItemsFile.setUpdateBy( arg0.getUpdateBy() );
        prjHazardousItemsFile.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            prjHazardousItemsFile.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        prjHazardousItemsFile.setItemFileId( arg0.getItemFileId() );
        prjHazardousItemsFile.setName( arg0.getName() );
        prjHazardousItemsFile.setFileId( arg0.getFileId() );
        prjHazardousItemsFile.setTaskId( arg0.getTaskId() );
        prjHazardousItemsFile.setServiceType( arg0.getServiceType() );
        prjHazardousItemsFile.setCallFileId( arg0.getCallFileId() );

        return prjHazardousItemsFile;
    }

    @Override
    public PrjHazardousItemsFile convert(PrjHazardousItemsFileBo arg0, PrjHazardousItemsFile arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setItemFileId( arg0.getItemFileId() );
        arg1.setName( arg0.getName() );
        arg1.setFileId( arg0.getFileId() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setServiceType( arg0.getServiceType() );
        arg1.setCallFileId( arg0.getCallFileId() );

        return arg1;
    }
}

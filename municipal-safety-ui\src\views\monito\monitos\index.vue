<template>
  <div class="p-2">
    <el-row :gutter="10">
      <el-col :span="4">
        <left @getMonitorChangeUrl="getMonitorUrl" />
      </el-col>
      <el-col :span="20">
        <div class="monitorMiddle">
          <div v-if="monitorVal == 1" style="width: 100%;display: flex;">
            <div class="moddleChildren" v-for="(item1, index1) in monitorVal" :key="index1" style="width: 100%;">
              <div :class="['children', monitor_index == index1 && monitorVal == 1 ? 'active' : 'active1']"
                @click="monitorClick(index1, 1)">
                <MonitorPlayer :accessToken="accessToken" :url="playUrl" @capturePicture="handleCapturePicture"
                  :monitorVal="monitorVal" :index0="index1" :monitorIndex="monitor_index"
                  style="position: absolute;top: 50%;left: 0;transform: translateY(-50%);" />
              </div>
            </div>
          </div>
          <div v-else-if="monitorVal == 4" style="width: 100%;display: flex;flex-wrap: wrap;">
            <div class="moddleChildren" v-for="(item1, index1) in monitorVal" :key="index1" style="width: 50%;">
              <div :class="['children', monitor_index == index1 && monitorVal == 4 ? 'active' : 'active1']"
                @click="monitorClick(index1, 4)">
                <MonitorPlayer :accessToken="accessToken" :url="playUrl" @capturePicture="handleCapturePicture"
                  :monitorVal="monitorVal" :index0="index1" :monitorIndex="monitor_index"
                  style="position: absolute;top: 50%;left: 0;transform: translateY(-50%);" />
              </div>
            </div>
          </div>
          <div v-else-if="monitorVal == 9" style="width: 100%;display: flex;flex-wrap: wrap;">
            <div class="moddleChildren" v-for="(item1, index1) in monitorVal" :key="index1" style="width: 33.33%;">
              <div :class="['children', monitor_index == index1 && monitorVal == 9 ? 'active' : 'active1']"
                @click="monitorClick(index1, 9)">
                <MonitorPlayer :accessToken="accessToken" :url="playUrl" @capturePicture="handleCapturePicture"
                  :monitorVal="monitorVal" :index0="index1" :monitorIndex="monitor_index"
                  style="position: absolute;top: 50%;left: 0;transform: translateY(-50%);" />
              </div>
            </div>
          </div>
          <div class="footerIcon">
            <img src="@/assets/img_slices/split_screen_click_icon.png" alt="" @click="selectMonitorIcon(1)">
            <img src="@/assets/img_slices/quarter_click_icon.png" alt="" @click="selectMonitorIcon(4)">
            <img src="@/assets/img_slices/nine_click_icon.png" alt="" @click="selectMonitorIcon(9)">
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { getAccessToken, getPlayMonitorUrl, capturePictureAnalysis } from '@/api/monito/monito';
import left from '@/components/Monitor/left.vue';
import MonitorPlayer from '@/components/MonitorPlayer/index1.vue'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const monitorVal = ref(1)
const accessToken = ref<string>()
const monitoId = ref()
const playUrl = ref<string[]>(['', '', '', '', '', '', '', '', ''])
const monitor_index = ref<number>(0)

const selectMonitorIcon = (val: number) => {
  playUrl.value.forEach((item, index) => {
    if (index != 0) {
      playUrl.value[index] = '';
    }
  })
  switch (val) {
    case 1:
      monitor_index.value = 0;
      monitorVal.value = 1;
      break;
    case 4:
      monitor_index.value = 0;
      monitorVal.value = 4;
      break;
    case 9:
      monitor_index.value = 0;
      monitorVal.value = 9;
      break;
    default:
      break;
  }
}
// 获取监控视频的accessToken
const getMonitorAccessToken = async () => {
  const res = await getAccessToken();
  accessToken.value = res.msg;
}
// 获取监控视频的播放地址
const getMonitorUrl = async (value: { deviceCode: string | number, channelNo: string | number, monitoId: string | number }) => {
  if (value.monitoId) {
    monitoId.value = value.monitoId;
  }
  const res = await getPlayMonitorUrl(value.deviceCode, value.channelNo ? value.channelNo : 1);
  if (res.code == 200) {
    nextTick(() => {
      playUrl.value[monitor_index.value] = res.msg ? res.msg : '';
    })
  }
}
const handleCapturePicture = async (val: any) => {
  const res = await capturePictureAnalysis({ base64: val, monitoId: monitoId.value });
  if (res.code == 200) {
    proxy?.$modal.msgSuccess('AI分析图片成功');
  }
}
const monitorClick = (index: number, val: number) => {
  switch (val) {
    case 1:
      monitorVal.value = 1;
      monitor_index.value = index;
      break;
    case 4:
      monitorVal.value = 4;
      monitor_index.value = index;
      break;
    case 9:
      monitorVal.value = 9;
      monitor_index.value = index;
      break;
    default:
      break;
  }
}
onMounted(() => {
  getMonitorAccessToken();
})
</script>

<style scoped lang="scss">
.monitorMiddle {
  width: 100%;
  height: calc(100vh - 160px);
  border: 1px solid #ccc;
  display: flex;
  flex-wrap: wrap;
  position: relative;
  padding-bottom: 45px;

  .moddleChildren {
    width: 100%;
    padding: 10px;

    .children {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      overflow: hidden;
      position: relative;
      cursor: pointer;

      &.active {
        border: 2px solid #409eff;
      }

      &.active1 {
        border: 1px solid #ccc;
      }
    }
  }

  .footerIcon {
    display: flex;
    width: 30px;
    height: 30px;
    position: absolute;
    bottom: 10px;
    left: 10px;

    img {
      width: 100%;
      height: 100%;
      margin-right: 10px;
      cursor: pointer;
    }
  }
}
</style>
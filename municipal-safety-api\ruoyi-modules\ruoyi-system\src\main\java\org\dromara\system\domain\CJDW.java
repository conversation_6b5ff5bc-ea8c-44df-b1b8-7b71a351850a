package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cjdw")
public class CJDW extends BaseEntity {
    @TableId(value = "id")
    private Integer id;
    private String builderLicenceNum;           // 施工许可证编号
    private String corpType;                    // 企业类型
    private String corpName;                    // 企业名称
    private String socialCreditCode;            // 社会信用代码
    private String legalMan;                    // 法人名称
    private String corpQualification;           // 企业资质类型
    private String corpQualificationCode;       // 企业资质类型证号
    @TableLogic
    private String delFlag;                     //删除标志（0代表存在 1代表删除）
}

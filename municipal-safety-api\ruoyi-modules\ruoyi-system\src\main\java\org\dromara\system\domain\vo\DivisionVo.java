package org.dromara.system.domain.vo;

import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.system.domain.Division;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 行政区划视图对象 z_division
 * @date 2025-04-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Division.class)
public class DivisionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 区划代码
     */
    @ExcelProperty(value = "区划代码")
    private String divisionCode;

    /**
     * 区划名称
     */
    @ExcelProperty(value = "区划名称")
    private String divisionName;

    /**
     * 区划级别
     */
    @ExcelProperty(value = "区划级别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "level")
    private String level;

    /**
     * 父级区划代码
     */
    @ExcelProperty(value = "父级区划代码")
    private String parentCode;

    private List<DivisionVo> children;

    private List<PrjProjectsVo> projectsList;

}

package org.dromara.quality.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 测量统计详细数据VO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MeasurementStatisticsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 测量记录总数
     */
    private Integer total;

    /**
     * 正常测量记录数
     */
    private Integer normalCount;

    /**
     * 异常测量记录数
     */
    private Integer abnormalCount;

    /**
     * 隐患记录数
     */
    private Integer hazardCount;

    /**
     * 待复测项目数量
     */
    private Integer retestCount;

    /**
     * 测量记录较上月增长百分比
     */
    private Integer increase;

    /**
     * 测量记录较上月增长率
     */
    private Double increaseRate;

    /**
     * 正常率百分比
     */
    private Integer normalRate;

    /**
     * 隐患率百分比
     */
    private Integer hazardRate;
} 
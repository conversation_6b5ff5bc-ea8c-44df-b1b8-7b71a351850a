package org.dromara.projects.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.projects.domain.PrjHazardousItems;
import org.dromara.projects.domain.vo.PrjHazardousItemsVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/14 19:00
 * @Description TODO
 * @Version 1.0
 */
@Mapper
public interface PrjHazardousItemsConvert {

    PrjHazardousItemsConvert INSTANCE = Mappers.getMapper(PrjHazardousItemsConvert.class);

    /**
     * 分页转换
     *
     * @param itemsPage
     * @return
     */
    Page<PrjHazardousItemsVo> page2voPage(Page<PrjHazardousItems> itemsPage);

    /**
     * 单个转换
     *
     * @param items
     * @return
     */
    PrjHazardousItemsVo items2vo(PrjHazardousItems items);

    /**
     * 列表转换
     *
     * @param itemsList
     * @return
     */
    List<PrjHazardousItemsVo> listItems2voList(List<PrjHazardousItems> itemsList);
}

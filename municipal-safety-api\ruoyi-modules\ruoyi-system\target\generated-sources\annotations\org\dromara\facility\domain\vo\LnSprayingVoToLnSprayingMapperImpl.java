package org.dromara.facility.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.LnSpraying;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnSprayingVoToLnSprayingMapperImpl implements LnSprayingVoToLnSprayingMapper {

    @Override
    public LnSpraying convert(LnSprayingVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnSpraying lnSpraying = new LnSpraying();

        lnSpraying.setId( arg0.getId() );
        lnSpraying.setRelayStatus( arg0.getRelayStatus() );
        lnSpraying.setWorkMode( arg0.getWorkMode() );
        lnSpraying.setWaterSensor( arg0.getWaterSensor() );
        lnSpraying.setStatus( arg0.getStatus() );
        lnSpraying.setDevNo( arg0.getDevNo() );
        lnSpraying.setCreateTime( arg0.getCreateTime() );

        return lnSpraying;
    }

    @Override
    public LnSpraying convert(LnSprayingVo arg0, LnSpraying arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setRelayStatus( arg0.getRelayStatus() );
        arg1.setWorkMode( arg0.getWorkMode() );
        arg1.setWaterSensor( arg0.getWaterSensor() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}

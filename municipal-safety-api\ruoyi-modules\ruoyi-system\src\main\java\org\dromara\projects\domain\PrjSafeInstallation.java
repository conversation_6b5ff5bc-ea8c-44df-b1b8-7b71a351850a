package org.dromara.projects.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 【安拆任务】安拆人员对象 prj_safe_installation
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prj_safe_installation")
public class PrjSafeInstallation extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "installation_id")
    private Long installationId;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 自定义岗位名称
     */
    private String userPositionName;

    /**
     * 人脸照片地址
     */
    private String face;

    /**
     * 资质证书地址
     */
    private String certificate;

    /**
     * 关联安拆prj_safe_task.open_task_id
     */
    private Long openTaskId;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private Long delFlag;
}

2025-05-07 08:39:29 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.15 with PID 16848 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-monitor-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-07 08:39:29 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-05-07 08:39:30 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-05-07 08:39:31 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-07 08:39:31 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1173 ms
2025-05-07 08:39:31 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-05-07 08:39:31 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-05-07 08:39:31 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-05-07 08:39:32 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-07 08:39:32 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-07 08:39:32 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-07 08:39:32 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-07 08:39:32 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-05-07 08:39:32 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 3.382 seconds (process running for 3.906)
2025-05-07 08:39:32 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-07 08:39:32 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-07 08:39:32 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-05-07 08:39:33 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as cfaedb4e44dd
2025-05-07 08:39:33 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【cfaedb4e44dd】, 状态【UP】, 服务URL【http://***************:9090/】
2025-05-07 08:39:37 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【cc20f5b8d840】, 状态【UP】, 服务URL【http://***************:8800/snail-job】
2025-05-07 08:39:57 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【UP】, 服务URL【http://***************:8080/】
2025-05-07 09:56:23 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-05-07 09:56:23 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-05-07 09:56:23 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-07 09:56:23 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-07 10:19:37 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.15 with PID 1384 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-monitor-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-07 10:19:37 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-05-07 10:19:38 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-05-07 10:19:38 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-07 10:19:38 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 940 ms
2025-05-07 10:19:38 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-05-07 10:19:38 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-05-07 10:19:39 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-05-07 10:19:39 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-07 10:19:39 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-07 10:19:39 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-07 10:19:39 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-07 10:19:39 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-05-07 10:19:39 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.961 seconds (process running for 3.383)
2025-05-07 10:19:39 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-07 10:19:39 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-07 10:19:39 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-07 10:19:40 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as cfaedb4e44dd
2025-05-07 10:19:40 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【cfaedb4e44dd】, 状态【UP】, 服务URL【http://***************:9090/】
2025-05-07 10:19:41 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【cc20f5b8d840】, 状态【UP】, 服务URL【http://***************:8800/snail-job】
2025-05-07 10:20:07 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【UP】, 服务URL【http://***************:8080/】
2025-05-07 10:53:59 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务下线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【DOWN】, 服务URL【http://***************:8080/】
2025-05-07 10:53:59 [reactor-http-nio-4] INFO  d.c.b.a.server.services.InfoUpdater - Couldn't retrieve info for Instance(id=3f35cdd037b2, version=3, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T02:53:59.417502200Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://***************:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://***************:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://***************:8080/actuator/logfile), health=Endpoint(id=health, url=http://***************:8080/actuator/health), env=Endpoint(id=env, url=http://***************:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://***************:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://***************:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://***************:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://***************:8080/actuator/startup), beans=Endpoint(id=beans, url=http://***************:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://***************:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://***************:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://***************:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://***************:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://***************:8080/actuator/conditions), info=Endpoint(id=info, url=http://***************:8080/actuator/info)}), buildVersion=null, tags=Tags(values={})): 503 SERVICE_UNAVAILABLE
2025-05-07 10:54:31 [reactor-http-nio-5] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=3f35cdd037b2, version=4, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T02:53:59.417502200Z, info=Info(values={}), endpoints=Endpoints(endpoints={health=Endpoint(id=health, url=http://***************:8080/actuator/health)}), buildVersion=null, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: Connection refused: getsockopt: /***************:8080
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET health [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1743)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:323)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.tryFailure(TransportConnector.java:576)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.setFailure(TransportConnector.java:522)
		at reactor.netty.transport.TransportConnector.lambda$doConnect$7(TransportConnector.java:261)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:326)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:342)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: getsockopt: /***************:8080
Caused by: java.net.ConnectException: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:336)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:339)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-07 10:54:31 [reactor-http-nio-5] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【OFFLINE】, 服务URL【http://***************:8080/】
2025-05-07 10:54:53 [reactor-http-nio-7] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【UP】, 服务URL【http://***************:8080/】
2025-05-07 10:59:09 [reactor-http-nio-7] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务下线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【DOWN】, 服务URL【http://***************:8080/】
2025-05-07 10:59:09 [reactor-http-nio-7] INFO  d.c.b.a.server.services.InfoUpdater - Couldn't retrieve info for Instance(id=3f35cdd037b2, version=9, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T02:59:09.414109500Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://***************:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://***************:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://***************:8080/actuator/logfile), health=Endpoint(id=health, url=http://***************:8080/actuator/health), env=Endpoint(id=env, url=http://***************:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://***************:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://***************:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://***************:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://***************:8080/actuator/startup), beans=Endpoint(id=beans, url=http://***************:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://***************:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://***************:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://***************:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://***************:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://***************:8080/actuator/conditions), info=Endpoint(id=info, url=http://***************:8080/actuator/info)}), buildVersion=null, tags=Tags(values={})): 503 SERVICE_UNAVAILABLE
2025-05-07 10:59:41 [reactor-http-nio-8] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=3f35cdd037b2, version=10, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T02:59:09.414109500Z, info=Info(values={}), endpoints=Endpoints(endpoints={health=Endpoint(id=health, url=http://***************:8080/actuator/health)}), buildVersion=null, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: Connection refused: getsockopt: /***************:8080
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET health [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1743)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:323)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.tryFailure(TransportConnector.java:576)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.setFailure(TransportConnector.java:522)
		at reactor.netty.transport.TransportConnector.lambda$doConnect$7(TransportConnector.java:261)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:326)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:342)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: getsockopt: /***************:8080
Caused by: java.net.ConnectException: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:336)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:339)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-07 10:59:41 [reactor-http-nio-8] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【OFFLINE】, 服务URL【http://***************:8080/】
2025-05-07 10:59:49 [reactor-http-nio-9] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【UP】, 服务URL【http://***************:8080/】
2025-05-07 11:06:09 [reactor-http-nio-9] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务下线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【DOWN】, 服务URL【http://***************:8080/】
2025-05-07 11:06:09 [reactor-http-nio-9] INFO  d.c.b.a.server.services.InfoUpdater - Couldn't retrieve info for Instance(id=3f35cdd037b2, version=15, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T03:06:09.424894700Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://***************:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://***************:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://***************:8080/actuator/logfile), health=Endpoint(id=health, url=http://***************:8080/actuator/health), env=Endpoint(id=env, url=http://***************:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://***************:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://***************:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://***************:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://***************:8080/actuator/startup), beans=Endpoint(id=beans, url=http://***************:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://***************:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://***************:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://***************:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://***************:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://***************:8080/actuator/conditions), info=Endpoint(id=info, url=http://***************:8080/actuator/info)}), buildVersion=null, tags=Tags(values={})): 503 SERVICE_UNAVAILABLE
2025-05-07 11:06:51 [reactor-http-nio-10] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=3f35cdd037b2, version=16, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T03:06:09.424894700Z, info=Info(values={}), endpoints=Endpoints(endpoints={health=Endpoint(id=health, url=http://***************:8080/actuator/health)}), buildVersion=null, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: Connection refused: getsockopt: /***************:8080
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET health [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1743)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:323)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.tryFailure(TransportConnector.java:576)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.setFailure(TransportConnector.java:522)
		at reactor.netty.transport.TransportConnector.lambda$doConnect$7(TransportConnector.java:261)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:326)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:342)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: getsockopt: /***************:8080
Caused by: java.net.ConnectException: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:336)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:339)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-07 11:06:51 [reactor-http-nio-10] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【OFFLINE】, 服务URL【http://***************:8080/】
2025-05-07 11:07:01 [reactor-http-nio-11] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【UP】, 服务URL【http://***************:8080/】
2025-05-07 11:07:39 [reactor-http-nio-11] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务下线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【DOWN】, 服务URL【http://***************:8080/】
2025-05-07 11:07:39 [reactor-http-nio-11] INFO  d.c.b.a.server.services.InfoUpdater - Couldn't retrieve info for Instance(id=3f35cdd037b2, version=21, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T03:07:39.417279200Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://***************:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://***************:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://***************:8080/actuator/logfile), health=Endpoint(id=health, url=http://***************:8080/actuator/health), env=Endpoint(id=env, url=http://***************:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://***************:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://***************:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://***************:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://***************:8080/actuator/startup), beans=Endpoint(id=beans, url=http://***************:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://***************:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://***************:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://***************:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://***************:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://***************:8080/actuator/conditions), info=Endpoint(id=info, url=http://***************:8080/actuator/info)}), buildVersion=null, tags=Tags(values={})): 503 SERVICE_UNAVAILABLE
2025-05-07 11:08:01 [reactor-http-nio-12] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=3f35cdd037b2, version=22, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T03:07:39.417279200Z, info=Info(values={}), endpoints=Endpoints(endpoints={health=Endpoint(id=health, url=http://***************:8080/actuator/health)}), buildVersion=null, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: Connection refused: getsockopt: /***************:8080
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET health [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1743)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:323)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.tryFailure(TransportConnector.java:576)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.setFailure(TransportConnector.java:522)
		at reactor.netty.transport.TransportConnector.lambda$doConnect$7(TransportConnector.java:261)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:326)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:342)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: getsockopt: /***************:8080
Caused by: java.net.ConnectException: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:336)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:339)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-07 11:08:01 [reactor-http-nio-12] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【OFFLINE】, 服务URL【http://***************:8080/】
2025-05-07 11:08:18 [reactor-http-nio-1] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【UP】, 服务URL【http://***************:8080/】
2025-05-07 15:56:19 [reactor-http-nio-1] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务下线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【DOWN】, 服务URL【http://***************:8080/】
2025-05-07 15:56:19 [reactor-http-nio-1] INFO  d.c.b.a.server.services.InfoUpdater - Couldn't retrieve info for Instance(id=3f35cdd037b2, version=27, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T07:56:19.427847100Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://***************:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://***************:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://***************:8080/actuator/logfile), health=Endpoint(id=health, url=http://***************:8080/actuator/health), env=Endpoint(id=env, url=http://***************:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://***************:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://***************:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://***************:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://***************:8080/actuator/startup), beans=Endpoint(id=beans, url=http://***************:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://***************:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://***************:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://***************:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://***************:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://***************:8080/actuator/conditions), info=Endpoint(id=info, url=http://***************:8080/actuator/info)}), buildVersion=null, tags=Tags(values={})): 503 SERVICE_UNAVAILABLE
2025-05-07 15:57:01 [reactor-http-nio-1] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=3f35cdd037b2, version=28, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T07:56:19.427847100Z, info=Info(values={}), endpoints=Endpoints(endpoints={health=Endpoint(id=health, url=http://***************:8080/actuator/health)}), buildVersion=null, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: Connection refused: getsockopt: /***************:8080
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET health [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1743)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:323)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.tryFailure(TransportConnector.java:576)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.setFailure(TransportConnector.java:522)
		at reactor.netty.transport.TransportConnector.lambda$doConnect$7(TransportConnector.java:261)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:326)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:342)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: getsockopt: /***************:8080
Caused by: java.net.ConnectException: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:336)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:339)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-07 15:57:01 [reactor-http-nio-1] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【OFFLINE】, 服务URL【http://***************:8080/】
2025-05-07 15:58:36 [reactor-http-nio-6] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【UP】, 服务URL【http://***************:8080/】
2025-05-07 17:35:09 [reactor-http-nio-6] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务下线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【DOWN】, 服务URL【http://***************:8080/】
2025-05-07 17:35:09 [reactor-http-nio-6] INFO  d.c.b.a.server.services.InfoUpdater - Couldn't retrieve info for Instance(id=3f35cdd037b2, version=33, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T09:35:09.420273100Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://***************:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://***************:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://***************:8080/actuator/logfile), health=Endpoint(id=health, url=http://***************:8080/actuator/health), env=Endpoint(id=env, url=http://***************:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://***************:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://***************:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://***************:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://***************:8080/actuator/startup), beans=Endpoint(id=beans, url=http://***************:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://***************:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://***************:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://***************:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://***************:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://***************:8080/actuator/conditions), info=Endpoint(id=info, url=http://***************:8080/actuator/info)}), buildVersion=null, tags=Tags(values={})): 503 SERVICE_UNAVAILABLE
2025-05-07 17:35:51 [reactor-http-nio-10] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=3f35cdd037b2, version=34, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T09:35:09.420273100Z, info=Info(values={}), endpoints=Endpoints(endpoints={health=Endpoint(id=health, url=http://***************:8080/actuator/health)}), buildVersion=null, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: Connection refused: getsockopt: /***************:8080
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET health [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1743)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:323)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.tryFailure(TransportConnector.java:576)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.setFailure(TransportConnector.java:522)
		at reactor.netty.transport.TransportConnector.lambda$doConnect$7(TransportConnector.java:261)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:326)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:342)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: getsockopt: /***************:8080
Caused by: java.net.ConnectException: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:336)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:339)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-07 17:35:51 [reactor-http-nio-10] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【OFFLINE】, 服务URL【http://***************:8080/】
2025-05-07 17:36:07 [reactor-http-nio-11] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【UP】, 服务URL【http://***************:8080/】
2025-05-07 19:53:58 [parallel-8] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=3f35cdd037b2, version=38, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, db={status=UP, details={database=MySQL, validationQuery=isValid()}}, diskSpace={status=UP, details={total=355626323968, free=308931211264, threshold=10485760, path=D:\Work\job\municipal-safety\municipal-safety-api\., exists=true}}, ping={status=UP}, redis={status=UP, details={version=********}}}), statusTimestamp=2025-05-07T09:36:07.544384100Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://***************:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://***************:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://***************:8080/actuator/logfile), health=Endpoint(id=health, url=http://***************:8080/actuator/health), env=Endpoint(id=env, url=http://***************:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://***************:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://***************:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://***************:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://***************:8080/actuator/startup), beans=Endpoint(id=beans, url=http://***************:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://***************:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://***************:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://***************:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://***************:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://***************:8080/actuator/conditions), info=Endpoint(id=info, url=http://***************:8080/actuator/info)}), buildVersion=null, tags=Tags(values={}))
java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 9000ms in 'log' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281)
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270)
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-07 19:53:58 [parallel-8] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【OFFLINE】, 服务URL【http://***************:8080/】
2025-05-07 19:54:29 [reactor-http-nio-5] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【UP】, 服务URL【http://***************:8080/】
2025-05-07 20:00:49 [parallel-12] WARN  d.c.b.a.server.services.InfoUpdater - Couldn't retrieve info for Instance(id=3f35cdd037b2, version=40, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, db={status=UP, details={database=MySQL, validationQuery=isValid()}}, diskSpace={status=UP, details={total=355626323968, free=308930338816, threshold=10485760, path=D:\Work\job\municipal-safety\municipal-safety-api\., exists=true}}, ping={status=UP}, redis={status=UP, details={version=********}}}), statusTimestamp=2025-05-07T11:54:29.474485400Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://***************:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://***************:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://***************:8080/actuator/logfile), health=Endpoint(id=health, url=http://***************:8080/actuator/health), env=Endpoint(id=env, url=http://***************:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://***************:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://***************:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://***************:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://***************:8080/actuator/startup), beans=Endpoint(id=beans, url=http://***************:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://***************:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://***************:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://***************:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://***************:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://***************:8080/actuator/conditions), info=Endpoint(id=info, url=http://***************:8080/actuator/info)}), buildVersion=null, tags=Tags(values={}))
java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 10000ms in 'peek' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET info [DefaultWebClient]
Original Stack Trace:
		at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296)
		at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281)
		at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270)
		at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285)
		at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
		at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
		at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
		at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-07 20:00:58 [parallel-3] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=3f35cdd037b2, version=40, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, db={status=UP, details={database=MySQL, validationQuery=isValid()}}, diskSpace={status=UP, details={total=355626323968, free=308930338816, threshold=10485760, path=D:\Work\job\municipal-safety\municipal-safety-api\., exists=true}}, ping={status=UP}, redis={status=UP, details={version=********}}}), statusTimestamp=2025-05-07T11:54:29.474485400Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://***************:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://***************:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://***************:8080/actuator/logfile), health=Endpoint(id=health, url=http://***************:8080/actuator/health), env=Endpoint(id=env, url=http://***************:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://***************:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://***************:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://***************:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://***************:8080/actuator/startup), beans=Endpoint(id=beans, url=http://***************:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://***************:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://***************:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://***************:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://***************:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://***************:8080/actuator/conditions), info=Endpoint(id=info, url=http://***************:8080/actuator/info)}), buildVersion=null, tags=Tags(values={}))
java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 9000ms in 'log' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281)
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270)
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-07 20:00:58 [parallel-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【OFFLINE】, 服务URL【http://***************:8080/】
2025-05-07 20:01:09 [reactor-http-nio-5] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【UP】, 服务URL【http://***************:8080/】
2025-05-07 20:25:27 [reactor-http-nio-7] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3056ce13af63】, 状态【UP】, 服务URL【http://198.18.0.1:8080/】
2025-05-07 20:25:29 [reactor-http-nio-8] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【4e79a5e05e86】, 状态【UP】, 服务URL【http://198.18.0.1:9090/】
2025-05-07 20:25:30 [reactor-http-nio-9] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【67e6dd8f0c05】, 状态【UP】, 服务URL【http://198.18.0.1:8800/snail-job】
2025-05-07 20:50:19 [reactor-http-nio-7] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务下线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3056ce13af63】, 状态【DOWN】, 服务URL【http://198.18.0.1:8080/】
2025-05-07 20:50:19 [reactor-http-nio-7] INFO  d.c.b.a.server.services.InfoUpdater - Couldn't retrieve info for Instance(id=3056ce13af63, version=3, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://198.18.0.1:8080/actuator, healthUrl=http://198.18.0.1:8080/actuator/health, serviceUrl=http://198.18.0.1:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T12:50:19.418614100Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://198.18.0.1:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://198.18.0.1:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://198.18.0.1:8080/actuator/logfile), health=Endpoint(id=health, url=http://198.18.0.1:8080/actuator/health), env=Endpoint(id=env, url=http://198.18.0.1:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://198.18.0.1:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://198.18.0.1:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://198.18.0.1:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://198.18.0.1:8080/actuator/startup), beans=Endpoint(id=beans, url=http://198.18.0.1:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://198.18.0.1:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://198.18.0.1:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://198.18.0.1:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://198.18.0.1:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://198.18.0.1:8080/actuator/conditions), info=Endpoint(id=info, url=http://198.18.0.1:8080/actuator/info)}), buildVersion=null, tags=Tags(values={})): 503 SERVICE_UNAVAILABLE
2025-05-07 20:50:29 [reactor-http-nio-5] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务下线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【DOWN】, 服务URL【http://***************:8080/】
2025-05-07 20:50:29 [reactor-http-nio-5] INFO  d.c.b.a.server.services.InfoUpdater - Couldn't retrieve info for Instance(id=3f35cdd037b2, version=43, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T12:50:29.416870800Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://***************:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://***************:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://***************:8080/actuator/logfile), health=Endpoint(id=health, url=http://***************:8080/actuator/health), env=Endpoint(id=env, url=http://***************:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://***************:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://***************:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://***************:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://***************:8080/actuator/startup), beans=Endpoint(id=beans, url=http://***************:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://***************:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://***************:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://***************:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://***************:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://***************:8080/actuator/conditions), info=Endpoint(id=info, url=http://***************:8080/actuator/info)}), buildVersion=null, tags=Tags(values={})): 503 SERVICE_UNAVAILABLE
2025-05-07 20:51:01 [reactor-http-nio-12] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=3f35cdd037b2, version=44, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://***************:8080/actuator, healthUrl=http://***************:8080/actuator/health, serviceUrl=http://***************:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T12:50:29.416870800Z, info=Info(values={}), endpoints=Endpoints(endpoints={health=Endpoint(id=health, url=http://***************:8080/actuator/health)}), buildVersion=null, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: Connection refused: getsockopt: /***************:8080
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET health [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1743)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:323)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.tryFailure(TransportConnector.java:576)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.setFailure(TransportConnector.java:522)
		at reactor.netty.transport.TransportConnector.lambda$doConnect$7(TransportConnector.java:261)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:326)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:342)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: getsockopt: /***************:8080
Caused by: java.net.ConnectException: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:336)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:339)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-07 20:51:01 [reactor-http-nio-11] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=3056ce13af63, version=4, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://198.18.0.1:8080/actuator, healthUrl=http://198.18.0.1:8080/actuator/health, serviceUrl=http://198.18.0.1:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=DOWN, details={error=Service Unavailable, status=503}), statusTimestamp=2025-05-07T12:50:19.418614100Z, info=Info(values={}), endpoints=Endpoints(endpoints={health=Endpoint(id=health, url=http://198.18.0.1:8080/actuator/health)}), buildVersion=null, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: Connection refused: getsockopt: /198.18.0.1:8080
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET health [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1743)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:323)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.tryFailure(TransportConnector.java:576)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.setFailure(TransportConnector.java:522)
		at reactor.netty.transport.TransportConnector.lambda$doConnect$7(TransportConnector.java:261)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:326)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:342)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: getsockopt: /198.18.0.1:8080
Caused by: java.net.ConnectException: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:336)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:339)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:784)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-07 20:51:01 [reactor-http-nio-12] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【OFFLINE】, 服务URL【http://***************:8080/】
2025-05-07 20:51:01 [reactor-http-nio-11] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3056ce13af63】, 状态【OFFLINE】, 服务URL【http://198.18.0.1:8080/】
2025-05-07 20:51:27 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3056ce13af63】, 状态【UP】, 服务URL【http://198.18.0.1:8080/】
2025-05-07 20:51:39 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【UP】, 服务URL【http://***************:8080/】

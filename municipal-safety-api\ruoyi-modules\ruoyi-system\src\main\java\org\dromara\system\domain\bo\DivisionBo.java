package org.dromara.system.domain.bo;

import org.dromara.system.domain.Division;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 行政区划业务对象 z_division
 * @date 2025-04-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Division.class, reverseConvertGenerate = false)
public class DivisionBo extends BaseEntity {

    /**
     * 区划代码
     */
    @NotBlank(message = "区划代码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String divisionCode;

    /**
     * 区划名称
     */
    @NotBlank(message = "区划名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String divisionName;

    /**
     * 区划级别
     */
    @NotBlank(message = "区划级别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String level;

    /**
     * 父级区划代码
     */
    private String parentCode;

    private Long divisionId;

    private String delFlag;

    private DivisionBo children;
}

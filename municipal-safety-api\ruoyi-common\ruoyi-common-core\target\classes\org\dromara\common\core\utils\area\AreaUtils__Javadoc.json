{"doc": " 区域工具类\n\n <AUTHOR>\n", "fields": [{"name": "INSTANCE", "doc": " 初始化 SEARCHER\n"}, {"name": "areas", "doc": " Area 内存缓存，提升访问速度\n"}], "enumConstants": [], "methods": [{"name": "getArea", "paramTypes": ["java.lang.Integer"], "doc": " 获得指定编号对应的区域\n\n @param id 区域编号\n @return 区域\n"}, {"name": "parseArea", "paramTypes": ["java.lang.String"], "doc": " 获得指定区域对应的编号\n\n @param pathStr 区域路径，例如说：河南省/石家庄市/新华区\n @return 区域\n"}, {"name": "getAreaNodePathList", "paramTypes": ["java.util.List"], "doc": " 获取所有节点的全路径名称如：河南省/石家庄市/新华区\n\n @param areas 地区树\n @return 所有节点的全路径名称\n"}, {"name": "getAreaNodePathList", "paramTypes": ["org.dromara.common.core.utils.area.Area", "java.lang.String", "java.util.List"], "doc": " 构建一棵树的所有节点的全路径名称，并将其存储为 \"祖先/父级/子级\" 的形式\n\n @param node  父节点\n @param path  全路径名称\n @param paths 全路径名称列表，省份/城市/地区\n"}, {"name": "format", "paramTypes": ["java.lang.Integer"], "doc": " 格式化区域\n\n @param id 区域编号\n @return 格式化后的区域\n"}, {"name": "format", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 格式化区域\n\n 例如说：\n 1. id = “静安区”时：上海 上海市 静安区\n 2. id = “上海市”时：上海 上海市\n 3. id = “上海”时：上海\n 4. id = “美国”时：美国\n 当区域在中国时，默认不显示中国\n\n @param id        区域编号\n @param separator 分隔符\n @return 格式化后的区域\n"}, {"name": "getByType", "paramTypes": ["org.dromara.common.core.enums.AreaTypeEnum", "java.util.function.Function"], "doc": " 获取指定类型的区域列表\n\n @param type 区域类型\n @param func 转换函数\n @param <T>  结果类型\n @return 区域列表\n"}, {"name": "getParentIdByType", "paramTypes": ["java.lang.Integer", "org.dromara.common.core.enums.AreaTypeEnum"], "doc": " 根据区域编号、上级区域类型，获取上级区域编号\n\n @param id   区域编号\n @param type 区域类型\n @return 上级区域编号\n"}], "constructors": []}
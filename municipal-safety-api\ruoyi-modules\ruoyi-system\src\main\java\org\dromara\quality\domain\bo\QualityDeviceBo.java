package org.dromara.quality.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.quality.domain.QualityDevice;

/**
 * 设备管理业务对象 quality_device
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = QualityDevice.class, reverseConvertGenerate = false)
public class QualityDeviceBo extends BaseEntity {

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空", groups = {EditGroup.class})
    private Long deviceId;

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String deviceName;

    /**
     * 规格型号
     */
    private String specification;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 设备图片OSS ID
     */
    private Long deviceImageOssId;

    /**
     * 设备简介
     */
    private String deviceDescription;

    /**
     * 设备用途
     */
    private String devicePurpose;

    /**
     * 使用说明
     */
    private String usageInstructions;

    /**
     * 使用说明书OSS ID
     */
    private Long manualFileOssId;

    /**
     * 设备状态（0正常 1停用）
     */
    private String status;

}

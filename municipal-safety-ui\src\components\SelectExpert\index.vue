<template>
  <div class="selectExpert">
    <el-dialog :title="expertDialog.title" v-model="expertDialog.visible" append-to-body @close="handleClose"
      width="70%">
      <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
        :leave-active-class="proxy?.animate.searchAnimate.leave">
        <div v-show="showSearch" class="mb-[10px]">
          <el-card shadow="never">
            <el-form ref="queryFormRef" :model="queryParams" :inline="true">
              <el-form-item label="职称" prop="title">
                <el-input v-model="queryParams.title" placeholder="请输入职称" clearable />
              </el-form-item>
              <el-form-item label="专业" prop="major">
                <el-select v-model="queryParams.major" placeholder="请选择专业" clearable>
                  <el-option v-for="dict in expert_major" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="行业" prop="industry">
                <el-select v-model="queryParams.industry" placeholder="请选择行业" clearable>
                  <el-option v-for="dict in expert_industry" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>

      <el-card shadow="never">
        <template #header>
          <el-row :gutter="10" class="mb8">
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>
        </template>

        <el-table v-loading="loading" :data="expertList" @selection-change="handleSelectionChange"
          show-overflow-tooltip>
          <el-table-column type="selection" width="55" />
          <el-table-column label="专家主键" align="center" prop="expertId" v-if="false" />
          <el-table-column label="名称" align="center" prop="name" />
          <el-table-column label="性别" align="center" prop="sex">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.sex" />
            </template>
          </el-table-column>
          <el-table-column label="电话" align="center" prop="phone" />
          <el-table-column label="专家类型" align="center" prop="type">
            <template #default="scope">
              <dict-tag :options="expert_type" :value="scope.row.type" />
            </template>
          </el-table-column>
          <el-table-column label="职称" align="center" prop="title" />
          <el-table-column label="专业" align="center" prop="major">
            <template #default="scope">
              <dict-tag :options="expert_major" :value="scope.row.major" />
            </template>
          </el-table-column>
          <el-table-column label="行业" align="center" prop="industry">
            <template #default="scope">
              <dict-tag :options="expert_industry" :value="scope.row.industry" />
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-card>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmChange">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { listExpert } from '@/api/expert/expert';
import { ExpertQuery, ExpertVO } from '@/api/expert/expert/types';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { expert_industry, sys_user_sex, expert_major, expert_type } = toRefs<any>(
  proxy?.useDict('expert_industry', 'sys_user_sex', 'expert_major', 'expert_type')
);

const props = defineProps({
  isShowModel: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:isShowModel', 'selectEmit'])
const showSearch = ref(true);
const loading = ref(false);
const expertDialog = reactive({
  visible: false,
  title: '选择专家'
})
const queryFormRef = ref<ElFormInstance>();
const queryParams = reactive<ExpertQuery>({
  pageNum: 1,
  pageSize: 10,
  title: undefined,
  major: undefined,
  industry: undefined,
  params: {}
})
const expertList = ref<ExpertVO[]>();
const total = ref(0);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const selecExpertData = ref<ExpertVO[]>([]);
watch(() => props.isShowModel, (val) => {
  expertDialog.visible = val
})
/** 查询 专家主列表 */
const getList = async () => {
  loading.value = true;
  const res = await listExpert(queryParams);
  expertList.value = res.rows;
  expertList.value.forEach((item) => {
    item.expertArea = item.provinceName + ' ' + item.cityName + ' ' + ' ' + item.areaName;
  });
  total.value = res.total;
  loading.value = false;
};
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};
/** 多选框选中数据 */
const handleSelectionChange = (selection: ExpertVO[]) => {
  ids.value = selection.map((item) => item.expertId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
  selecExpertData.value = selection;
};
/** 确定按钮操作 */
const confirmChange = () => {
  emit('update:isShowModel', false)
  emit('selectEmit', selecExpertData.value)
};
/** 取消按钮操作 */
const cancel = () => {
  emit('update:isShowModel', false)
};
const handleClose = () => {
  emit('update:isShowModel', false)
}
onMounted(() => {
  getList();
})
</script>

<style scoped></style>
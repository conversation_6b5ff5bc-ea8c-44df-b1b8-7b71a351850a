package org.dromara.facility.service;

import org.dromara.facility.domain.vo.LnSprayingVo;
import org.dromara.facility.domain.bo.LnSprayingBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 绿能喷淋设备Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
public interface ILnSprayingService extends BaseFacilityHandle{

    /**
     * 查询绿能喷淋设备
     *
     * @param id 主键
     * @return 绿能喷淋设备
     */
    LnSprayingVo queryById(Long id);

    /**
     * 分页查询绿能喷淋设备列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能喷淋设备分页列表
     */
    TableDataInfo<LnSprayingVo> queryPageList(LnSprayingBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的绿能喷淋设备列表
     *
     * @param bo 查询条件
     * @return 绿能喷淋设备列表
     */
    List<LnSprayingVo> queryList(LnSprayingBo bo);

    /**
     * 新增绿能喷淋设备
     *
     * @param bo 绿能喷淋设备
     * @return 是否新增成功
     */
    Boolean insertByBo(LnSprayingBo bo);

    /**
     * 修改绿能喷淋设备
     *
     * @param bo 绿能喷淋设备
     * @return 是否修改成功
     */
    Boolean updateByBo(LnSprayingBo bo);

    /**
     * 校验并批量删除绿能喷淋设备信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

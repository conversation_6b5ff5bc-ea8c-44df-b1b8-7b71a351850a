package org.dromara.quality.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.quality.domain.QualityDevice;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 设备管理视图对象 quality_device
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = QualityDevice.class)
public class QualityDeviceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 规格型号
     */
    @ExcelProperty(value = "规格型号")
    private String specification;

    /**
     * 设备类型
     */
    @ExcelProperty(value = "设备类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "device_type")
    private String deviceType;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String deviceCode;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private Integer quantity;

    /**
     * 设备图片OSS ID
     */
    private Long deviceImageOssId;

    /**
     * 设备图片URL
     */
    @ExcelProperty(value = "设备图片")
    @Translation(type = TransConstant.OSS_ID_TO_URL, mapper = "deviceImageOssId")
    private String deviceImageUrl;

    /**
     * 设备简介
     */
    @ExcelProperty(value = "设备简介")
    private String deviceDescription;

    /**
     * 设备用途
     */
    @ExcelProperty(value = "设备用途")
    private String devicePurpose;

    /**
     * 使用说明
     */
    @ExcelProperty(value = "使用说明")
    private String usageInstructions;

    /**
     * 使用说明书OSS ID
     */
    private Long manualFileOssId;

    /**
     * 使用说明书URL
     */
    @ExcelProperty(value = "使用说明书")
    @Translation(type = TransConstant.OSS_ID_TO_URL, mapper = "manualFileOssId")
    private String manualFileUrl;

    /**
     * 使用说明书文件名
     */
    private String manualFileName;

    /**
     * 设备状态
     */
    @ExcelProperty(value = "设备状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private Long createBy;

    /**
     * 创建人账号
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    @ExcelProperty(value = "创建人账号")
    private String createByName;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private Long updateBy;

    /**
     * 更新人账号
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "updateBy")
    @ExcelProperty(value = "更新人账号")
    private String updateByName;

}

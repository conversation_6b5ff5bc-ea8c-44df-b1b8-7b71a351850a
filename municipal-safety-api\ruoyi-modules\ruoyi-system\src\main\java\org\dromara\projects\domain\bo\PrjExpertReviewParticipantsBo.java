package org.dromara.projects.domain.bo;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.projects.domain.PrjExpertReviewParticipants;

/**
 * [项目管理] 列出专家论证会议的参会人员业务对象 prj_expert_review_participants
 *
 * <AUTHOR> brother
 * @date 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjExpertReviewParticipants.class, reverseConvertGenerate = false)
public class PrjExpertReviewParticipantsBo extends BaseEntity {

    /**
     * 专家论证会议ID (逻辑外键至 prj_expert_reviews.review_id)
     */
    @NotNull(message = "专家论证会议ID (逻辑外键至 prj_expert_reviews.review_id)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long reviewId;

    /**
     * 参与者用户ID (逻辑外键至 sys_users.user_id)
     */
    @NotNull(message = "参与者用户ID (逻辑外键至 sys_users.user_id)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 会议中的角色
     */
    @NotBlank(message = "会议中的角色不能为空", groups = {AddGroup.class, EditGroup.class})
    private String roleInMeeting;

    /**
     * 是否评审专家 (0:否, 1:是)
     */
    private Long isAttendingExpert;
}

<template>
  <div class="p-2" style="height: calc(100vh - 155px);">
    <el-card shadow="never">
      <div style="display: flex; justify-content: space-between">
        <div>
          <el-button v-if="submitButtonShow" :loading="buttonLoading" type="info"
            @click="submitForm('draft')">暂存</el-button>
          <el-button v-if="submitButtonShow" :loading="buttonLoading" type="primary" @click="submitForm('submit')">提
            交</el-button>
          <el-button v-if="approvalButtonShow" :loading="buttonLoading" type="primary"
            @click="approvalVerifyOpen">审批</el-button>
          <el-button v-if="form && form.id" type="primary" @click="handleApprovalRecord">流程进度</el-button>
        </div>
        <div>
          <el-button style="float: right" @click="goBack()">返回</el-button>
        </div>
      </div>
    </el-card>
    <!-- 隐患清单 -->
    <el-row :gutter="10" style="width: 100%;height: 100%;padding: 10px;">
      <el-col :span="5" style="display: flex;flex-direction: column;align-items: flex-start;">
        <div style="margin-bottom: 10px;">
          <p style="text-align: start;color: #409EFF;margin: 0 0 16px;">分析前</p>
          <HeaderPrewiew :src="aiDetailData?.photoDocumentUrl" width="17vw" height="17vw"
            :preview-src-list="[aiDetailData?.photoDocumentUrl]">
          </HeaderPrewiew>
        </div>
        <div>
          <p style="text-align: start;color: #67C23A;margin: 10px 0 16px;">分析后</p>
          <HeaderPrewiew :src="aiDetailData?.aiPhotoDocumentUrl" width="17vw" height="17vw"
            :preview-src-list="[aiDetailData?.aiPhotoDocumentUrl]">
          </HeaderPrewiew>
        </div>
      </el-col>
      <el-col :span="10" style="display: flex;flex-direction: column;height: 100%;padding-top: 5px;">
        <div style="width: 100%;height: calc(100vh - 227px);overflow-y: auto;">
          <el-card style="width: 100%;margin-bottom: 20px;" v-for="(item, index) in aiDetailData?.violations"
            :key="index">
            <template #header>
              <div class="card-header">
                <div style="display: flex;align-items: center;">
                  <el-checkbox v-if="isPreview != 'view'" v-model="rgfjCheck[index]" />
                  <span style="display: block;padding-bottom: 3px;margin-left: 8px;">问题 {{ index + 1 }}</span>
                </div>
                <div style="display: flex;align-items: center;">
                  <span style="color: #409EFF;">危险级别：</span>
                  <dict-tag :options="hidden_danger_type" :value="item.level" />
                </div>
              </div>
            </template>
            <div style="display: flex;">
              <span style="color: #666;display: block;width: 80px;">隐患描述：</span>
              <span style="display: block;flex: 1;">{{ item.violation }}</span>
            </div>
            <div style="display: flex;margin: 15px 0;">
              <span style="color: #666;display: block;width: 80px;">违反条款：</span>
              <span style="display: block;flex: 1;">{{ item.regulation }}</span>
            </div>
            <div style="display: flex;">
              <span style="color: #666;display: block;width: 80px;">整改意见：</span>
              <span style="display: block;flex: 1;">{{ item.measure
              }}</span>
            </div>
          </el-card>
        </div>
      </el-col>
      <el-col :span="9" style="padding-left: 20px;height: 100%;padding-top: 5px;">
        <div style="width: 100%;height: calc(100vh - 227px);overflow-y: auto;padding-right: 20px;">
          <div>
            <label style="font-size: 15px;">派遣专家</label>
            <div style="margin-top: 25px;">
              <template v-for="(item, index) in dispatchRightData.specialistNames" :key="index">
                <el-tag type="primary" style="margin-right: 12px;">{{ item
                }}</el-tag>
              </template>
            </div>
            <div style="margin-top: 40px;">
              <label style="font-size: 15px;">补充说明</label>
              <el-input class="bcsmInput" v-model="dispatchRightData.instruction" :rows="15" type="textarea"
                style="margin-top: 15px;" :disabled="true" />
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 提交组件 -->
    <submitVerify ref="submitVerifyRef" :task-variables="taskVariables" :isReadyFile="false" :isSelectBtnDisabled="true"
      @submit-callback="submitCallback" @beforeSubmit="beforeSubmit" />
    <!-- 审批记录 -->
    <approvalRecord ref="approvalRecordRef" />
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" :before-close="handleClose" width="500">
      <el-select v-model="flowCode" placeholder="Select" style="width: 240px">
        <el-option v-for="item in flowCodeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="submitFlow()"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Leave" lang="ts">
import { addLeave, updateLeave } from '@/api/workflow/leave';
import { LeaveForm, LeaveQuery, LeaveVO } from '@/api/workflow/leave/types';
import { startWorkFlow, getTaskVariables } from '@/api/workflow/task';
import { getPrj_hazardous_items_ai_detail } from '@/api/customFlow/api'
import { addHazardousItemsSpecialWarningFirstEcho } from '@/api/second/index'
import { listByIds } from '@/api/system/oss/index'
import SubmitVerify from '@/components/Process/submitVerify.vue';
import ApprovalRecord from '@/components/Process/approvalRecord.vue';
import { AxiosResponse } from 'axios';
import { StartProcessBo } from '@/api/workflow/workflowCommon/types';
import HeaderPrewiew from '@/components/ImagePreview/index.vue';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { hidden_danger_type } = toRefs<any>(proxy?.useDict('hidden_danger_type'));

const buttonLoading = ref(false);
const loading = ref(true);
const leaveTime = ref<Array<string>>([]);
// 存放隐患清单详情的数据
const aiDetailData = ref();
// 人工复检多选值
const rgfjCheck = ref([])
// 派遣审查右侧的所有数据
const dispatchRightData = ref({
  specialistNames: '', // 专家名字
  instruction: '', // 补充说明
});
// 判断是预览还是编辑状态
const isPreview = ref('')
// 单选框选中的值
const isSelected = ref([]);
const personComments = ref([]);
//路由参数
const routeParams = ref<Record<string, any>>({});
const flowCodeOptions = [
  {
    value: 'leave1',
    label: '请假申请-普通'
  },
  {
    value: 'leave2',
    label: '请假申请-排他网关'
  },
  {
    value: 'leave3',
    label: '请假申请-并行网关'
  },
  {
    value: 'leave4',
    label: '请假申请-会签'
  },
  {
    value: 'leave5',
    label: '请假申请-并行会签网关'
  },
  {
    value: 'leave6',
    label: '请假申请-排他并行会签'
  }
];

const flowCode = ref<string>('');

const dialogVisible = reactive<DialogOption>({
  visible: false,
  title: '流程定义'
});
//提交组件
const submitVerifyRef = ref<InstanceType<typeof SubmitVerify>>();
//审批记录组件
const approvalRecordRef = ref<InstanceType<typeof ApprovalRecord>>();

const leaveFormRef = ref<ElFormInstance>();

const submitFormData = ref<StartProcessBo>({
  businessId: '',
  flowCode: '',
  variables: {}
});
const taskVariables = ref<Record<string, any>>({});

const initFormData: LeaveForm = {
  id: undefined,
  leaveType: undefined,
  startDate: undefined,
  endDate: undefined,
  leaveDays: undefined,
  remark: undefined,
  status: 'waiting'
};
const data = reactive<PageData<LeaveForm, LeaveQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    startLeaveDays: undefined,
    endLeaveDays: undefined
  },
  rules: {
    id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
    leaveType: [{ required: true, message: '请假类型不能为空', trigger: 'blur' }],
    leaveTime: [{ required: true, message: '请假时间不能为空', trigger: 'blur' }],
    leaveDays: [{ required: true, message: '请假天数不能为空', trigger: 'blur' }]
  }
});

const handleClose = () => {
  dialogVisible.visible = false;
  flowCode.value = '';
  buttonLoading.value = false;
};
const { form, rules } = toRefs(data);

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  leaveTime.value = [];
  leaveFormRef.value?.resetFields();
};
// 使用ossId查询图片的url地址
const getImageUrl = async (ossId: string | number) => {
  const { data } = await listByIds(ossId);
  return data[0]?.url;
}
// 获取隐患详情数据
const getAiDetail = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(255, 255, 255, 0.8)',
  })
  isSelected.value = [];
  personComments.value = [];
  const res = await getPrj_hazardous_items_ai_detail(taskVariables.value.ai_task_id);
  if (res.code === 200) {
    aiDetailData.value = res.data;
    aiDetailData.value.photoDocumentUrl = await getImageUrl(aiDetailData.value.photoDocumentId);
    aiDetailData.value.aiPhotoDocumentUrl = await getImageUrl(aiDetailData.value.aiPhotoDocumentId);
    aiDetailData.value.violations.forEach(item => {
      isSelected.value.push(item.commentStatus == null ? 0 : item.commentStatus);
      personComments.value.push(item.personComments);
      if (routeParams.value.type == 'view') {
        item.disable = true;
      } else {
        item.disable = false;
      }
    });
  }
  loading.close();
}
// 获取右侧派遣专家和补充说明的相关详情
const getDispatchRightData = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(255, 255, 255, 0.8)',
  })
  const res = await addHazardousItemsSpecialWarningFirstEcho(routeParams.value.id);
  if (res.code === 200) {
    dispatchRightData.value.specialistNames = res.data.specialistNames;
    dispatchRightData.value.instruction = res.data.instruction;
  }
  loading.close();
}
// 获取任务变量数据
const getTaskVariablesData = async () => {
  const res = await getTaskVariables(routeParams.value.id);
  if (res.code === 200) {
    taskVariables.value = res.data;
  }
}
/** 暂存提交按钮 */
const submitForm = (status: string) => {
  if (leaveTime.value.length === 0) {
    proxy?.$modal.msgError('请假时间不能为空');
    return;
  }
  try {
    leaveFormRef.value?.validate(async (valid: boolean) => {
      form.value.startDate = leaveTime.value[0];
      form.value.endDate = leaveTime.value[1];
      if (valid) {
        buttonLoading.value = true;
        let res: AxiosResponse<LeaveVO>;
        if (form.value.id) {
          res = await updateLeave(form.value);
        } else {
          res = await addLeave(form.value);
        }
        form.value = res.data;
        if (status === 'draft') {
          buttonLoading.value = false;
          proxy?.$modal.msgSuccess('暂存成功');
          proxy.$tab.closePage(proxy.$route);
          proxy.$router.go(-1);
        } else {
          if ((form.value.status === 'draft' && (flowCode.value === '' || flowCode.value === null)) || routeParams.value.type === 'add') {
            flowCode.value = flowCodeOptions[0].value;
            dialogVisible.visible = true;
            return;
          }
          //说明启动过先随意穿个参数
          if (flowCode.value === '' || flowCode.value === null) {
            flowCode.value = 'xx';
          }
          await handleStartWorkFlow(res.data);
        }
      }
    });
  } finally {
    buttonLoading.value = false;
  }
};

const submitFlow = async () => {
  handleStartWorkFlow(form.value);
  dialogVisible.visible = false;
};
//提交申请
const handleStartWorkFlow = async (data: LeaveForm) => {
  try {
    submitFormData.value.flowCode = flowCode.value;
    submitFormData.value.businessId = data.id;
    //流程变量
    taskVariables.value = {
      leaveDays: data.leaveDays,
      userList: ['1', '3', '4']
    };
    submitFormData.value.variables = taskVariables.value;
    const resp = await startWorkFlow(submitFormData.value);
    if (submitVerifyRef.value) {
      buttonLoading.value = false;
      submitVerifyRef.value.openDialog(resp.data.taskId);
    }
  } finally {
    buttonLoading.value = false;
  }
};
//头部流程进度
const handleApprovalRecord = () => {
  approvalRecordRef.value.init(form.value.id);
};

//提交组件回调
const submitCallback = async () => {
  await proxy.$tab.closePage(proxy.$route);
  proxy.$router.go(-1);
};
// 提交前的通用回调函数
const beforeSubmit = async (fun) => {
  // 提交前的逻辑处理
  fun(true)
};
//头部返回
const goBack = () => {
  proxy.$tab.closePage(proxy.$route);
  proxy.$router.go(-1);
};
//头部审批
const approvalVerifyOpen = async () => {
  const isAllSelect = isSelected.value.every(item => item == 1);
  if (!isAllSelect) {
    submitVerifyRef.value.openDialog(routeParams.value.taskId);
  } else {
    proxy?.$modal.msgError('该项目清单已经提交审批了，无法再次提交');
  }
};
//校验提交按钮是否显示
const submitButtonShow = computed(() => {
  return (
    routeParams.value.type === 'add' ||
    (routeParams.value.type === 'update' &&
      form.value.status &&
      (form.value.status === 'draft' || form.value.status === 'cancel' || form.value.status === 'back'))
  );
});

//校验审批按钮是否显示
const approvalButtonShow = computed(() => {
  return routeParams.value.type === 'approval' && form.value.status && form.value.status === 'waiting';
});
onMounted(() => {
  nextTick(async () => {
    routeParams.value = proxy.$route.query;
    reset();
    await getTaskVariablesData();
    loading.value = false;
    isPreview.value = routeParams.value.type == 'approval' ? 'view' : routeParams.value.type;
    form.value.id = routeParams.value.id;
    getAiDetail();
    getDispatchRightData();
  });
});
</script>
<style lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bcsmInput {
  .el-textarea__inner {
    background-color: #fff !important;
  }
}
</style>

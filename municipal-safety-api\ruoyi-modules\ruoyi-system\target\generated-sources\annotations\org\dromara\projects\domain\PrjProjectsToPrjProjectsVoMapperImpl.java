package org.dromara.projects.domain;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:15+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjProjectsToPrjProjectsVoMapperImpl implements PrjProjectsToPrjProjectsVoMapper {

    @Override
    public PrjProjectsVo convert(PrjProjects arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjProjectsVo prjProjectsVo = new PrjProjectsVo();

        prjProjectsVo.setProjectId( arg0.getProjectId() );
        prjProjectsVo.setProjectName( arg0.getProjectName() );
        prjProjectsVo.setProjectCode( arg0.getProjectCode() );
        prjProjectsVo.setProjectOverview( arg0.getProjectOverview() );
        prjProjectsVo.setConstructionPermitNo( arg0.getConstructionPermitNo() );
        prjProjectsVo.setConstructionPermitDocId( arg0.getConstructionPermitDocId() );
        prjProjectsVo.setLola( arg0.getLola() );
        prjProjectsVo.setProvinceCode( arg0.getProvinceCode() );
        prjProjectsVo.setProvinceName( arg0.getProvinceName() );
        prjProjectsVo.setCityCode( arg0.getCityCode() );
        prjProjectsVo.setCityName( arg0.getCityName() );
        prjProjectsVo.setDistrictCode( arg0.getDistrictCode() );
        prjProjectsVo.setDistrictName( arg0.getDistrictName() );
        prjProjectsVo.setCountyCode( arg0.getCountyCode() );
        prjProjectsVo.setCountyName( arg0.getCountyName() );
        prjProjectsVo.setLocationDetail( arg0.getLocationDetail() );
        prjProjectsVo.setSiteArea( arg0.getSiteArea() );
        prjProjectsVo.setBudgetTotal( arg0.getBudgetTotal() );
        prjProjectsVo.setSupervisingQsOrgId( arg0.getSupervisingQsOrgId() );
        prjProjectsVo.setStatus( arg0.getStatus() );
        prjProjectsVo.setStartDate( arg0.getStartDate() );
        prjProjectsVo.setPlannedEndDate( arg0.getPlannedEndDate() );
        prjProjectsVo.setActualStartDate( arg0.getActualStartDate() );
        prjProjectsVo.setActualEndDate( arg0.getActualEndDate() );
        prjProjectsVo.setClientOrgId( arg0.getClientOrgId() );
        prjProjectsVo.setConstructionOrgId( arg0.getConstructionOrgId() );
        prjProjectsVo.setSupervisionOrgId( arg0.getSupervisionOrgId() );
        prjProjectsVo.setDesignOrgId( arg0.getDesignOrgId() );
        prjProjectsVo.setSurveyOrgId( arg0.getSurveyOrgId() );
        prjProjectsVo.setInstallationDismantlingOrgId( arg0.getInstallationDismantlingOrgId() );
        prjProjectsVo.setMaintenanceOrgId( arg0.getMaintenanceOrgId() );
        prjProjectsVo.setSubcontractorOrgIds( arg0.getSubcontractorOrgIds() );
        prjProjectsVo.setProjectManagerUserId( arg0.getProjectManagerUserId() );
        prjProjectsVo.setSupervisionChiefEngUserId( arg0.getSupervisionChiefEngUserId() );
        prjProjectsVo.setSafetyMeasuresFeeDocId( arg0.getSafetyMeasuresFeeDocId() );

        return prjProjectsVo;
    }

    @Override
    public PrjProjectsVo convert(PrjProjects arg0, PrjProjectsVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setProjectId( arg0.getProjectId() );
        arg1.setProjectName( arg0.getProjectName() );
        arg1.setProjectCode( arg0.getProjectCode() );
        arg1.setProjectOverview( arg0.getProjectOverview() );
        arg1.setConstructionPermitNo( arg0.getConstructionPermitNo() );
        arg1.setConstructionPermitDocId( arg0.getConstructionPermitDocId() );
        arg1.setLola( arg0.getLola() );
        arg1.setProvinceCode( arg0.getProvinceCode() );
        arg1.setProvinceName( arg0.getProvinceName() );
        arg1.setCityCode( arg0.getCityCode() );
        arg1.setCityName( arg0.getCityName() );
        arg1.setDistrictCode( arg0.getDistrictCode() );
        arg1.setDistrictName( arg0.getDistrictName() );
        arg1.setCountyCode( arg0.getCountyCode() );
        arg1.setCountyName( arg0.getCountyName() );
        arg1.setLocationDetail( arg0.getLocationDetail() );
        arg1.setSiteArea( arg0.getSiteArea() );
        arg1.setBudgetTotal( arg0.getBudgetTotal() );
        arg1.setSupervisingQsOrgId( arg0.getSupervisingQsOrgId() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setStartDate( arg0.getStartDate() );
        arg1.setPlannedEndDate( arg0.getPlannedEndDate() );
        arg1.setActualStartDate( arg0.getActualStartDate() );
        arg1.setActualEndDate( arg0.getActualEndDate() );
        arg1.setClientOrgId( arg0.getClientOrgId() );
        arg1.setConstructionOrgId( arg0.getConstructionOrgId() );
        arg1.setSupervisionOrgId( arg0.getSupervisionOrgId() );
        arg1.setDesignOrgId( arg0.getDesignOrgId() );
        arg1.setSurveyOrgId( arg0.getSurveyOrgId() );
        arg1.setInstallationDismantlingOrgId( arg0.getInstallationDismantlingOrgId() );
        arg1.setMaintenanceOrgId( arg0.getMaintenanceOrgId() );
        arg1.setSubcontractorOrgIds( arg0.getSubcontractorOrgIds() );
        arg1.setProjectManagerUserId( arg0.getProjectManagerUserId() );
        arg1.setSupervisionChiefEngUserId( arg0.getSupervisionChiefEngUserId() );
        arg1.setSafetyMeasuresFeeDocId( arg0.getSafetyMeasuresFeeDocId() );

        return arg1;
    }
}

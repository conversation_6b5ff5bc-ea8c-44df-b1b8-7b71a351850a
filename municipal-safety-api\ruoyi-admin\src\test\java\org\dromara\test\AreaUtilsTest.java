package org.dromara.test;




import cn.hutool.core.lang.Assert;
import org.dromara.common.core.utils.area.Area;
import org.dromara.common.core.enums.AreaTypeEnum;
import org.dromara.common.core.utils.area.AreaUtils;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * {@link AreaUtils} 的单元测试
 *
 * <AUTHOR>
 */
public class AreaUtilsTest {

    @Test
    public void testGetArea() {
        // 调用：北京
        Area city = AreaUtils.parseArea("兰州市/七里河区");
        Area area = AreaUtils.getArea(620103);
        String format = AreaUtils.formatId(620103,"/");
        // 断言
        assertEquals(area.getId(), 110100);
        assertEquals(area.getName(), "北京市");
        assertEquals(area.getType(), AreaTypeEnum.CITY.getType());
        assertEquals(area.getParent().getId(), 110000);
        assertEquals(area.getChildren().size(), 16);
    }

    @Test
    public void testFormat() {
        assertEquals(AreaUtils.format(110105), "北京市 北京市 朝阳区");
        assertEquals(AreaUtils.format(1), "中国");
        assertEquals(AreaUtils.format(2), "蒙古");
    }

    /**
     * 获得地区树
     */
    @Test
    public void testGetAreaTree() {
        Area area = AreaUtils.getArea(Area.ID_CHINA);
        Assert.notNull(area, "获取不到中国");
        System.out.println(area.getChildren());
    }

}

package org.dromara.facility.domain.bo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.facility.domain.JlLifterReal;

import java.util.Date;

/**
 * 升降机实时数据业务对象 jl_lifter_real
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = JlLifterReal.class, reverseConvertGenerate = false)
public class JlLifterRealBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 吊笼编号
     */
    private String tcNo;

    /**
     * 0 是左   1是右
     */
    private Long lifterRight;

    /**
     * 吊笼编号和左右笼
     */
    private String tcRightStr;

    /**
     * 时间
     */
    private Date date;

    /**
     * 实时起重量
     */
    private Long weight;

    /**
     * 重量百分比
     */
    private Long weightPercent;

    /**
     * 实时人数
     */
    private Long person;

    /**
     * 实时高度
     */
    private Long height;

    /**
     * 高度百分比
     */
    private Long heightPercent;

    /**
     * 实时速度
     */
    private Long speed;

    /**
     * 速度方向 0停止，1上，2下
     */
    private Long speedDir;

    /**
     * 实时倾斜度
     */
    private Long slant1;

    /**
     * 倾斜百分比
     */
    private Long slant1Percent;

    /**
     * 实时倾斜度
     */
    private Long slant2;

    /**
     * 倾斜百分比
     */
    private Long slant2Percent;

    /**
     * 驾驶员身份认证结果 00为未认证 01为已认证
     */
    private String driverAuth;

    /**
     * 前门状态 数值1代表开启，0带便关闭
     */
    private Long frontDoor;

    /**
     * 后门状态 数值1代表开启，0带便关闭
     */
    private Long backDoor;

    /**
     * 门锁异常指示 0无异常1有异常
     */
    private Long doorLock;

    /**
     * 重量
     */
    private Long systemStatusWeight;

    /**
     * 高度限位
     */
    private Long systemStatusHeight;

    /**
     * 超速
     */
    private Long systemStatusSpeed;

    /**
     * 人数
     */
    private Long systemStatusPerson;

    /**
     * 倾斜
     */
    private Long systemStatusSlant;

    /**
     * 前门锁状态：数字0正常,数值1异常
     */
    private Long systemStatusFrontDoor;

    /**
     * 后门锁状态：数字0正常,数值1异常
     */
    private Long systemStatusBackDoor;

    /**
     * 风速，0表示正常，1表示预警，2表示报警
     */
    private Long systemStatusWindSpeed;

    /**
     * 上限位，0表示正常，1表示报警
     */
    private Long systemStatusUpperLimit;

    /**
     * 防坠器，0表示正常，1表示报警
     */
    private Long systemStatusFallingProtector;

    /**
     * 实时风速
     */
    private Long windSpeed;

    /**
     * 当前楼层
     */
    private Long currentFloor;

    /**
     * 未戴安全帽的人数
     */
    private Long uncovered;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

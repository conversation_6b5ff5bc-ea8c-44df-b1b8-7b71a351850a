package org.dromara.projects.domain.bo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.PrjExpertReviewParticipants;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {},
    imports = {}
)
public interface PrjExpertReviewParticipantsBoToPrjExpertReviewParticipantsMapper extends BaseMapper<PrjExpertReviewParticipantsBo, PrjExpertReviewParticipants> {
}

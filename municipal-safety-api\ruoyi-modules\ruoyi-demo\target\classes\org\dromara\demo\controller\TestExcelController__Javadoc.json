{"doc": " 测试Excel功能\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "exportTemplateOne", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 单列表多数据\n"}, {"name": "exportTemplateMuliti", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 多列表多数据\n"}, {"name": "exportWithOptions", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 导出下拉框\n\n @param response /\n"}, {"name": "exportTemplateMultiSheet", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 多个sheet导出\n"}, {"name": "importWithOptions", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 导入表格\n"}], "constructors": []}
package org.dromara.facility.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.LnDumpPlat;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnDumpPlatVoToLnDumpPlatMapperImpl implements LnDumpPlatVoToLnDumpPlatMapper {

    @Override
    public LnDumpPlat convert(LnDumpPlatVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnDumpPlat lnDumpPlat = new LnDumpPlat();

        lnDumpPlat.setId( arg0.getId() );
        lnDumpPlat.setDumpnumber( arg0.getDumpnumber() );
        lnDumpPlat.setWeightMax( arg0.getWeightMax() );
        lnDumpPlat.setWeight( arg0.getWeight() );
        lnDumpPlat.setTilt( arg0.getTilt() );
        lnDumpPlat.setBatvolt( arg0.getBatvolt() );
        lnDumpPlat.setWightPercent( arg0.getWightPercent() );
        lnDumpPlat.setTiltPercentX( arg0.getTiltPercentX() );
        lnDumpPlat.setTiltPercentY( arg0.getTiltPercentY() );
        lnDumpPlat.setAlarmInfo( arg0.getAlarmInfo() );
        lnDumpPlat.setStatus( arg0.getStatus() );
        lnDumpPlat.setIdleWeightReal( arg0.getIdleWeightReal() );
        lnDumpPlat.setLoadWeightReal( arg0.getLoadWeightReal() );
        lnDumpPlat.setWeightWarning( arg0.getWeightWarning() );
        lnDumpPlat.setWeightAlarm( arg0.getWeightAlarm() );
        lnDumpPlat.setTiltWarning( arg0.getTiltWarning() );
        lnDumpPlat.setTiltAlarm( arg0.getTiltAlarm() );
        lnDumpPlat.setDeviceIp( arg0.getDeviceIp() );
        lnDumpPlat.setRealTiltX( arg0.getRealTiltX() );
        lnDumpPlat.setRealTiltY( arg0.getRealTiltY() );
        lnDumpPlat.setDevNo( arg0.getDevNo() );
        lnDumpPlat.setCreateTime( arg0.getCreateTime() );

        return lnDumpPlat;
    }

    @Override
    public LnDumpPlat convert(LnDumpPlatVo arg0, LnDumpPlat arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setDumpnumber( arg0.getDumpnumber() );
        arg1.setWeightMax( arg0.getWeightMax() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setTilt( arg0.getTilt() );
        arg1.setBatvolt( arg0.getBatvolt() );
        arg1.setWightPercent( arg0.getWightPercent() );
        arg1.setTiltPercentX( arg0.getTiltPercentX() );
        arg1.setTiltPercentY( arg0.getTiltPercentY() );
        arg1.setAlarmInfo( arg0.getAlarmInfo() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setIdleWeightReal( arg0.getIdleWeightReal() );
        arg1.setLoadWeightReal( arg0.getLoadWeightReal() );
        arg1.setWeightWarning( arg0.getWeightWarning() );
        arg1.setWeightAlarm( arg0.getWeightAlarm() );
        arg1.setTiltWarning( arg0.getTiltWarning() );
        arg1.setTiltAlarm( arg0.getTiltAlarm() );
        arg1.setDeviceIp( arg0.getDeviceIp() );
        arg1.setRealTiltX( arg0.getRealTiltX() );
        arg1.setRealTiltY( arg0.getRealTiltY() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}

package org.dromara.flow.domain.vo;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.dromara.flow.domain.PrjHazardousItemsComments;
import org.dromara.flow.domain.PrjHazardousItemsFile;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsCommentsVoToPrjHazardousItemsCommentsMapperImpl implements PrjHazardousItemsCommentsVoToPrjHazardousItemsCommentsMapper {

    @Override
    public PrjHazardousItemsComments convert(PrjHazardousItemsCommentsVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsComments prjHazardousItemsComments = new PrjHazardousItemsComments();

        prjHazardousItemsComments.setCreateTime( arg0.getCreateTime() );
        prjHazardousItemsComments.setId( arg0.getId() );
        prjHazardousItemsComments.setQuestion( arg0.getQuestion() );
        prjHazardousItemsComments.setTimeLimit( arg0.getTimeLimit() );
        prjHazardousItemsComments.setTimeType( arg0.getTimeType() );
        prjHazardousItemsComments.setCorrectionsFile( arg0.getCorrectionsFile() );
        prjHazardousItemsComments.setCorrectionsContent( arg0.getCorrectionsContent() );
        prjHazardousItemsComments.setCorrectionsBackFile( arg0.getCorrectionsBackFile() );
        prjHazardousItemsComments.setSuspensionFile( arg0.getSuspensionFile() );
        prjHazardousItemsComments.setSuspensionContent( arg0.getSuspensionContent() );
        prjHazardousItemsComments.setSuspensionBackFile( arg0.getSuspensionBackFile() );
        prjHazardousItemsComments.setPenaltyFile( arg0.getPenaltyFile() );
        prjHazardousItemsComments.setPenaltyContent( arg0.getPenaltyContent() );
        prjHazardousItemsComments.setPenaltyBackFile( arg0.getPenaltyBackFile() );
        prjHazardousItemsComments.setTaskId( arg0.getTaskId() );
        List<PrjHazardousItemsFile> list = arg0.getElseFile();
        if ( list != null ) {
            prjHazardousItemsComments.setElseFile( new ArrayList<PrjHazardousItemsFile>( list ) );
        }

        return prjHazardousItemsComments;
    }

    @Override
    public PrjHazardousItemsComments convert(PrjHazardousItemsCommentsVo arg0, PrjHazardousItemsComments arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setId( arg0.getId() );
        arg1.setQuestion( arg0.getQuestion() );
        arg1.setTimeLimit( arg0.getTimeLimit() );
        arg1.setTimeType( arg0.getTimeType() );
        arg1.setCorrectionsFile( arg0.getCorrectionsFile() );
        arg1.setCorrectionsContent( arg0.getCorrectionsContent() );
        arg1.setCorrectionsBackFile( arg0.getCorrectionsBackFile() );
        arg1.setSuspensionFile( arg0.getSuspensionFile() );
        arg1.setSuspensionContent( arg0.getSuspensionContent() );
        arg1.setSuspensionBackFile( arg0.getSuspensionBackFile() );
        arg1.setPenaltyFile( arg0.getPenaltyFile() );
        arg1.setPenaltyContent( arg0.getPenaltyContent() );
        arg1.setPenaltyBackFile( arg0.getPenaltyBackFile() );
        arg1.setTaskId( arg0.getTaskId() );
        if ( arg1.getElseFile() != null ) {
            List<PrjHazardousItemsFile> list = arg0.getElseFile();
            if ( list != null ) {
                arg1.getElseFile().clear();
                arg1.getElseFile().addAll( list );
            }
            else {
                arg1.setElseFile( null );
            }
        }
        else {
            List<PrjHazardousItemsFile> list = arg0.getElseFile();
            if ( list != null ) {
                arg1.setElseFile( new ArrayList<PrjHazardousItemsFile>( list ) );
            }
        }

        return arg1;
    }
}

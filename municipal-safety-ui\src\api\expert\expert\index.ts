import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ExpertVO, ExpertForm, ExpertQuery } from '@/api/expert/expert/types';
import { param } from '@/utils';

/**
 * 查询 专家主列表
 * @param query
 * @returns {*}
 */

export const listExpert = (query?: ExpertQuery): AxiosPromise<ExpertVO[]> => {
  return request({
    url: '/expert/expert/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询 专家主详细
 * @param expertId
 */
export const getExpert = (expertId: string | number): AxiosPromise<ExpertVO> => {
  return request({
    url: '/expert/expert/' + expertId,
    method: 'get'
  });
};

/**
 * 新增 专家主
 * @param data
 */
export const addExpert = (data: ExpertForm) => {
  return request({
    url: '/expert/expert',
    method: 'post',
    data: data
  });
};

/**
 * 修改 专家主
 * @param data
 */
export const updateExpert = (data: ExpertForm) => {
  return request({
    url: '/expert/expert',
    method: 'put',
    data: data
  });
};

/**
 * 删除 专家主
 * @param expertId
 */
export const delExpert = (expertId: string | number | Array<string | number>) => {
  return request({
    url: '/expert/expert/' + expertId,
    method: 'delete'
  });
};
// 获取省份的下拉列表
export const getProvinceList = (): AxiosPromise<ExpertVO[]> => {
  return request({
    url: '/system/division/getProvinceList',
    method: 'get'
  });
}
// 获取城市的下拉列表
export const getCityList = (divisionCode: string): AxiosPromise<ExpertVO[]> => {
  return request({
    url: `/system/division/getCityList/${divisionCode}`,
    method: 'get',
  });
}
// 获取区县的下拉列表
export const getAreaList = (divisionCode: string): AxiosPromise<ExpertVO[]> => {
  return request({
    url: `/system/division/getAreaList/${divisionCode}`,
    method: 'get',
  });
}
// 获取省市区所有地级数据
export const getAllCityList = (): AxiosPromise<ExpertVO[]> => {
  return request({
    url: '/system/division/list',
    method: 'get',
  });
}
// 删除项目信息
export const delProject = (projectId: string) => {
  return request({
    url: `/expert/expert/project/${projectId}`,
    method: 'delete'
  });
}
// 删除领域信息
export const delField = (expertId: string) => {
  return request({
    url: `/expert/expert/field/${expertId}`,
    method: 'delete'
  });
}
// 重置密码
export const resetpassworld = (idCard ) => {
  return request({
    url: `/expert/expert/resetPwd`,
    method: 'get' ,
    params: { idCard }
  });
}

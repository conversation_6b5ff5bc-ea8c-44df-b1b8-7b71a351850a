export interface EquipmentVO {
  /**
   * 特种设备ID
   */
  equipmentId: string | number;

  /**
   * 使用登记证书编号
   */
  certificateNumber: string;

  /**
   * 使用登记证书发证机关
   */
  issuer: string;

  /**
   * 使用登记证书发证日期
   */
  issueDate: string;

  /**
   * 使用登记证书id
   */
  useRegistrationCertificate: number;

  /**
   * 设备类别
   */
  equipmentCategory: string;

  /**
   * 规格型号
   */
  modelSpec: string;

  /**
   * 出厂编号
   */
  factoryNumber: string;

  /**
   * 备案编号
   */
  recordNumber: string;

  /**
   * 制造单位
   */
  manufacturer: string;

  /**
   * 产权单位
   */
  propertyOwner: string;

  /**
   * 工程名称
   */
  projectName: string;

  /**
   * 工程项目地址
   */
  projectAddress: string;

  /**
   * 项目ID
   */
  projectId: string | number;

  /**
   * 项目工程ID
   */
  itemId: string | number;

  /**
   * 使用单位
   */
  usageUnit: string;

  /**
   * 维保单位
   */
  maintenanceUnit: string;

  /**
   * 安装单位
   */
  installationUnit: string;

  /**
   * 检测单位
   */
  inspectionUnit: string;

  /**
   * 项目负责人
   */
  projectManager: string;

  /**
   * 安装日期
   */
  installationDate: string;

  /**
   * 检测日期
   */
  inspectionDate: string;

  /**
   * 进场日期
   */
  enterDate: string;

  /**
   * 退场日期
   */
  exitDate: string;

  /**
   * 存放地点（使用部位）
   */
  location: string;

  /**
   * 特种作业人员ID(操作人员)
   */
  sopId: string | number;

  /**
   * 备注
   */
  remarks: string;

}

export interface EquipmentForm extends BaseEntity {
  /**
   * 特种设备ID
   */
  equipmentId?: string | number;

  /**
   * 使用登记证书编号
   */
  certificateNumber?: string;

  /**
   * 使用登记证书发证机关
   */
  issuer?: string;

  /**
   * 使用登记证书发证日期
   */
  issueDate?: string;

  /**
   * 使用登记证书id
   */
  useRegistrationCertificate?: number;

  /**
   * 设备类别
   */
  equipmentCategory?: string;
  /**
   * 设备编号
   */
  devNo?: string;

  /**
   * 规格型号
   */
  modelSpec?: string;

  /**
   * 出厂编号
   */
  factoryNumber?: string;

  /**
   * 备案编号
   */
  recordNumber?: string;

  /**
   * 制造单位
   */
  manufacturer?: string;

  /**
   * 产权单位
   */
  propertyOwner?: string;

  /**
   * 工程名称
   */
  projectName?: string;

  /**
   * 工程项目地址
   */
  projectAddress?: string;

  /**
   * 项目ID
   */
  projectId?: string | number;

  /**
   * 项目工程ID
   */
  itemId?: string | number;

  /**
   * 使用单位
   */
  usageUnit?: string;

  /**
   * 维保单位
   */
  maintenanceUnit?: string;

  /**
   * 安装单位
   */
  installationUnit?: string;

  /**
   * 检测单位
   */
  inspectionUnit?: string;

  /**
   * 项目负责人
   */
  projectManager?: string;

  /**
   * 安装日期
   */
  installationDate?: string;

  /**
   * 检测日期
   */
  inspectionDate?: string;

  /**
   * 进场日期
   */
  enterDate?: string;

  /**
   * 退场日期
   */
  exitDate?: string;

  /**
   * 存放地点（使用部位）
   */
  location?: string;

  /**
   * 特种作业人员ID(操作人员)
   */
  sopId?: string | number;

  /**
   * 备注
   */
  remarks?: string;

}

export interface EquipmentQuery extends PageQuery {

  /**
   * 使用登记证书编号
   */
  certificateNumber?: string;

  /**
   * 使用登记证书发证机关
   */
  issuer?: string;

  /**
   * 使用登记证书发证日期
   */
  issueDate?: string;

  /**
   * 使用登记证书id
   */
  useRegistrationCertificate?: number;

  /**
   * 设备类别
   */
  equipmentCategory?: string;

  /**
   * 规格型号
   */
  modelSpec?: string;

  /**
   * 出厂编号
   */
  factoryNumber?: string;

  /**
   * 备案编号
   */
  recordNumber?: string;

  /**
   * 制造单位
   */
  manufacturer?: string;

  /**
   * 产权单位
   */
  propertyOwner?: string;

  /**
   * 工程名称
   */
  projectName?: string;

  /**
   * 工程项目地址
   */
  projectAddress?: string;

  /**
   * 项目ID
   */
  projectId?: string | number;

  /**
   * 项目工程ID
   */
  itemId?: string | number;

  /**
   * 使用单位
   */
  usageUnit?: string;

  /**
   * 维保单位
   */
  maintenanceUnit?: string;

  /**
   * 安装单位
   */
  installationUnit?: string;

  /**
   * 检测单位
   */
  inspectionUnit?: string;

  /**
   * 项目负责人
   */
  projectManager?: string;

  /**
   * 安装日期
   */
  installationDate?: string;

  /**
   * 检测日期
   */
  inspectionDate?: string;

  /**
   * 进场日期
   */
  enterDate?: string;

  /**
   * 退场日期
   */
  exitDate?: string;

  /**
   * 存放地点（使用部位）
   */
  location?: string;

  /**
   * 特种作业人员ID(操作人员)
   */
  sopId?: string | number;

  /**
   * 备注
   */
  remarks?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}




{"doc": " 表格分页数据对象\n\n <AUTHOR> Li\n", "fields": [{"name": "total", "doc": " 总记录数\n"}, {"name": "rows", "doc": " 列表数据\n"}, {"name": "code", "doc": " 消息状态码\n"}, {"name": "msg", "doc": " 消息内容\n"}], "enumConstants": [], "methods": [{"name": "build", "paramTypes": ["com.baomidou.mybatisplus.core.metadata.IPage"], "doc": " 根据分页对象构建表格分页数据对象\n"}, {"name": "build", "paramTypes": ["java.util.List"], "doc": " 根据数据列表构建表格分页数据对象\n"}, {"name": "build", "paramTypes": [], "doc": " 构建表格分页数据对象\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.util.List", "long"], "doc": " 分页\n\n @param list  列表数据\n @param total 总记录数\n"}]}
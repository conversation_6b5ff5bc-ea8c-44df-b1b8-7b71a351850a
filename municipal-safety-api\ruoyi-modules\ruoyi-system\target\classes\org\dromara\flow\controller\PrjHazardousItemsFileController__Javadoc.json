{"doc": " 通用流程附件\n\n <AUTHOR>\n @date 2025-06-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsFileBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询通用流程附件列表\n"}, {"name": "export", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsFileBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出通用流程附件列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取通用流程附件详细信息\n\n @param itemFileId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsFileBo"], "doc": " 新增通用流程附件\n"}, {"name": "edit", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsFileBo"], "doc": " 修改通用流程附件\n"}, {"name": "edit", "paramTypes": ["java.util.List"], "doc": " 修改通用流程附件\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除通用流程附件\n\n @param itemFileIds 主键串\n"}], "constructors": []}
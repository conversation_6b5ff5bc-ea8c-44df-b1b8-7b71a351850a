package org.dromara.flow.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.flow.domain.PrjHazardousItemsComments;
import org.dromara.flow.domain.PrjHazardousItemsFile;
import org.dromara.flow.domain.PrjHazardousItemsSpecialWarning;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 质监站隐患清单整改视图对象 prj_hazardous_items_comments
 *
 * <AUTHOR> zu da
 * @date 2025-06-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjHazardousItemsComments.class)
public class PrjHazardousItemsCommentsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 问题点id
     */
    @ExcelProperty(value = "问题点id")
    private String question;

    /**
     * 整改时限
     */
    @ExcelProperty(value = "整改时限")
    private Long timeLimit;

    /**
     * 整改时限类型 1小时 2天
     */
    @ExcelProperty(value = "整改时限类型 1小时 2天")
    private Long timeType;

    /**
     * 限期整改文件
     */
    @ExcelProperty(value = "限期整改文件")
    private Long correctionsFile;

    /**
     * 限期整改内容
     */
    @ExcelProperty(value = "限期整改内容")
    private String correctionsContent;

    /**
     * 限期整改报告
     */
    private Long correctionsBackFile;

    /**
     * 停工通知文件
     */
    @ExcelProperty(value = "停工通知文件")
    private Long suspensionFile;

    /**
     * 停工通知内容
     */
    @ExcelProperty(value = "停工通知内容")
    private String suspensionContent;

    /**
     * 停工整改报告
     */
    private Long suspensionBackFile;

    /**
     * 行政处罚文件
     */
    @ExcelProperty(value = "行政处罚文件")
    private Long penaltyFile;

    /**
     * 行政处罚内容
     */
    @ExcelProperty(value = "行政处罚内容")
    private String penaltyContent;

    /**
     * 行政处罚报告
     */
    private Long penaltyBackFile;

    /**
     * 业务id
     */
    private String taskId;

    /**
     * 未来时间
     */
    private Long futureTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 附件列表
     */
    @TableField(exist = false)
    private List<PrjHazardousItemsFile> elseFile;

    @TableField(exist = false)
    private PrjHazardousItemsSpecialWarning warning;
}

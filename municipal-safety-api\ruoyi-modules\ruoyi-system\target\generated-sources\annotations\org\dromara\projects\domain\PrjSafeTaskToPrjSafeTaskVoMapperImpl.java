package org.dromara.projects.domain;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.vo.PrjSafeTaskVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjSafeTaskToPrjSafeTaskVoMapperImpl implements PrjSafeTaskToPrjSafeTaskVoMapper {

    @Override
    public PrjSafeTaskVo convert(PrjSafeTask arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjSafeTaskVo prjSafeTaskVo = new PrjSafeTaskVo();

        prjSafeTaskVo.setOpenTaskId( arg0.getOpenTaskId() );
        prjSafeTaskVo.setProjectName( arg0.getProjectName() );
        prjSafeTaskVo.setProjectAddress( arg0.getProjectAddress() );
        prjSafeTaskVo.setProjectLongitude( arg0.getProjectLongitude() );
        prjSafeTaskVo.setProjectLatitude( arg0.getProjectLatitude() );
        prjSafeTaskVo.setProjectCraneNum( arg0.getProjectCraneNum() );
        prjSafeTaskVo.setCraneType( arg0.getCraneType() );
        prjSafeTaskVo.setCraneModel( arg0.getCraneModel() );
        prjSafeTaskVo.setCraneSn( arg0.getCraneSn() );
        prjSafeTaskVo.setCraneProductionDate( arg0.getCraneProductionDate() );
        prjSafeTaskVo.setPropertyCompanyName( arg0.getPropertyCompanyName() );
        prjSafeTaskVo.setFactoryName( arg0.getFactoryName() );
        prjSafeTaskVo.setJackingType( arg0.getJackingType() );
        prjSafeTaskVo.setExecutioinDate( arg0.getExecutioinDate() );
        prjSafeTaskVo.setSectionNum( arg0.getSectionNum() );
        if ( arg0.getModifiedCraneHeight() != null ) {
            prjSafeTaskVo.setModifiedCraneHeight( arg0.getModifiedCraneHeight().doubleValue() );
        }
        if ( arg0.getInitialCraneHeight() != null ) {
            prjSafeTaskVo.setInitialCraneHeight( arg0.getInitialCraneHeight().doubleValue() );
        }
        prjSafeTaskVo.setInstallationUnitName( arg0.getInstallationUnitName() );
        prjSafeTaskVo.setInstallationUnitQualification( arg0.getInstallationUnitQualification() );
        prjSafeTaskVo.setSafetyProductionPermit( arg0.getSafetyProductionPermit() );
        prjSafeTaskVo.setStatus( arg0.getStatus() );

        return prjSafeTaskVo;
    }

    @Override
    public PrjSafeTaskVo convert(PrjSafeTask arg0, PrjSafeTaskVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setOpenTaskId( arg0.getOpenTaskId() );
        arg1.setProjectName( arg0.getProjectName() );
        arg1.setProjectAddress( arg0.getProjectAddress() );
        arg1.setProjectLongitude( arg0.getProjectLongitude() );
        arg1.setProjectLatitude( arg0.getProjectLatitude() );
        arg1.setProjectCraneNum( arg0.getProjectCraneNum() );
        arg1.setCraneType( arg0.getCraneType() );
        arg1.setCraneModel( arg0.getCraneModel() );
        arg1.setCraneSn( arg0.getCraneSn() );
        arg1.setCraneProductionDate( arg0.getCraneProductionDate() );
        arg1.setPropertyCompanyName( arg0.getPropertyCompanyName() );
        arg1.setFactoryName( arg0.getFactoryName() );
        arg1.setJackingType( arg0.getJackingType() );
        arg1.setExecutioinDate( arg0.getExecutioinDate() );
        arg1.setSectionNum( arg0.getSectionNum() );
        if ( arg0.getModifiedCraneHeight() != null ) {
            arg1.setModifiedCraneHeight( arg0.getModifiedCraneHeight().doubleValue() );
        }
        else {
            arg1.setModifiedCraneHeight( null );
        }
        if ( arg0.getInitialCraneHeight() != null ) {
            arg1.setInitialCraneHeight( arg0.getInitialCraneHeight().doubleValue() );
        }
        else {
            arg1.setInitialCraneHeight( null );
        }
        arg1.setInstallationUnitName( arg0.getInstallationUnitName() );
        arg1.setInstallationUnitQualification( arg0.getInstallationUnitQualification() );
        arg1.setSafetyProductionPermit( arg0.getSafetyProductionPermit() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}

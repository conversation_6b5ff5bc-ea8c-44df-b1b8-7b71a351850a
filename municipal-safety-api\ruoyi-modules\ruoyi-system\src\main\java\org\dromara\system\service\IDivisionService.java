package org.dromara.system.service;

import org.dromara.system.domain.vo.DivisionVo;
import org.dromara.system.domain.bo.DivisionBo;

import java.util.Collection;
import java.util.List;

/**
 * 行政区划Service接口
 *
 * @date 2025-04-30
 */
public interface IDivisionService {

    /**
     * 查询行政区划
     *
     * @param divisionId 主键
     * @return 行政区划
     */
    DivisionVo queryById(Long divisionId);


    /**
     * 查询符合条件的行政区划列表
     *
     * @param bo 查询条件
     * @return 行政区划列表
     */
    List<DivisionVo> queryList(DivisionBo bo);

    /**
     * 新增行政区划
     *
     * @param bo 行政区划
     * @return 是否新增成功
     */
    Boolean insertByBo(DivisionBo bo);

    /**
     * 修改行政区划
     *
     * @param bo 行政区划
     * @return 是否修改成功
     */
    Boolean updateByBo(DivisionBo bo);

    /**
     * 校验并批量删除行政区划信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    List<DivisionVo> getTreeList(DivisionBo bo);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.XMXXMapper">

    <resultMap id="XmxxResult" type="org.dromara.system.domain.XMXX">
        <id property="id" column="ID"/>
        <result property="xmszs" column="XMSZS"/>
        <result property="xmszq" column="XMSZQ"/>
        <result property="projectCode" column="PROJECTCODE"/>
        <result property="builderLicenceNum" column="BUILDERLICENCENUM"/>
        <result property="projectName" column="PROJECTNAME"/>
        <result property="address" column="ADDRESS"/>
        <result property="cost" column="COST"/>
        <result property="structureTypeNum" column="STRUCTURETYPENUM"/>
        <result property="prjFunctionNum" column="PRJFUNCTIONNUM"/>
        <result property="investType" column="INVESTTYPE"/>
        <result property="baseType" column="BASETYPE"/>
        <result property="area" column="AREA"/>
        <result property="beginDate" column="BEGINDATE"/>
        <result property="endDate" column="ENDDATE"/>
        <result property="superviseProgress" column="SUPERVISEPROGRESS"/>
        <result property="informDate" column="INFORMDATE"/>
        <result property="declareDescribe" column="DECLAREDESCRIBE"/>
        <result property="superviseOrgan" column="SUPERVISEORGAN"/>
        <result property="superviseOrganSocialCreditCode" column="SUPERVISEORGANSOCIALCREDITCODE"/>
        <result property="areaCode" column="AREACODE"/>
        <result property="superviseUser" column="SUPERVISEUSER"/>
        <result property="locationX" column="LOCATIONX"/>
        <result property="locationY" column="LOCATIONY"/>
        <result property="delFlag" column="DEL_FLAG"/>
    </resultMap>

    <sql id="selectXmxxVo">
        SELECT ID,
               XMSZS,
               XMSZQ,
               PROJECTCODE,
               BUILDERLICENCENUM,
               PROJECTNAME,
               ADDRESS,
               COST,
               STRUCTURETYPENUM,
               PRJFUNCTIONNUM,
               INVESTTYPE,
               BASETYPE,
               AREA,
               BEGINDATE,
               ENDDATE,
               SUPERVISEPROGRESS,
               INFORMDATE,
               DECLAREDESCRIBE,
               SUPERVISEORGAN,
               SUPERVISEORGANSOCIALCREDITCODE,
               AREACODE,
               SUPERVISEUSER,
               LOCATIONX,
               LOCATIONY,
               DEL_FLAG
        FROM XMXX
    </sql>

    <!-- Insert operation -->
    <insert id="insertXmxx" parameterType="org.dromara.system.domain.XMXX" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO XMXX (XMSZS, XMSZQ, PROJECTCODE, BUILDERLICENCENUM, PROJECTNAME, ADDRESS, COST,
                          STRUCTURETYPENUM, PRJFUNCTIONNUM, INVESTTYPE, BASETYPE, AREA, BEGINDATE, ENDDATE,
                          SUPERVISEPROGRESS, INFORMDATE, DECLAREDESCRIBE, SUPERVISEORGAN,
                          SUPERVISEORGANSOCIALCREDITCODE,
                          AREACODE, SUPERVISEUSER, LOCATIONX, LOCATIONY, DEL_FLAG)
        VALUES (#{xmszs}, #{xmszq}, #{projectCode}, #{builderLicenceNum}, #{projectName}, #{address}, #{cost},
                #{structureTypeNum}, #{prjFunctionNum}, #{investType}, #{baseType}, #{area}, #{beginDate}, #{endDate},
                #{superviseProgress}, #{informDate}, #{declareDescribe}, #{superviseOrgan},
                #{superviseOrganSocialCreditCode},
                #{areaCode}, #{superviseUser}, #{locationX}, #{locationY}, '0')
    </insert>

    <!-- 批量插入操作 -->
    <insert id="batchInsertXmxx" parameterType="java.util.List">
        INSERT INTO XMXX (XMSZS, XMSZQ, PROJECTCODE, BUILDERLICENCENUM, PROJECTNAME, ADDRESS, COST,
        STRUCTURETYPENUM, PRJFUNCTIONNUM, INVESTTYPE, BASETYPE, AREA, BEGINDATE, ENDDATE,
        SUPERVISEPROGRESS, INFORMDATE, DECLAREDESCRIBE, SUPERVISEORGAN,
        SUPERVISEORGANSOCIALCREDITCODE,
        AREACODE, SUPERVISEUSER, LOCATIONX, LOCATIONY, DEL_FLAG)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.xmszs}, #{item.xmszq}, #{item.projectCode}, #{item.builderLicenceNum}, #{item.projectName},
            #{item.address}, #{item.cost}, #{item.structureTypeNum}, #{item.prjFunctionNum}, #{item.investType},
            #{item.baseType}, #{item.area}, #{item.beginDate}, #{item.endDate}, #{item.superviseProgress},
            #{item.informDate}, #{item.declareDescribe}, #{item.superviseOrgan},
            #{item.superviseOrganSocialCreditCode}, #{item.areaCode}, #{item.superviseUser},
            #{item.locationX}, #{item.locationY}, '0')
        </foreach>
    </insert>

    <!-- Update operation -->
    <update id="update" parameterType="org.dromara.system.domain.XMXX">
        UPDATE XMXX
        <set>
            <if test="xmszs != null and xmszs != ''">XMSZS = #{xmszs},</if>
            <if test="xmszq != null and xmszq != ''">XMSZQ = #{xmszq},</if>
            <if test="projectCode != null and projectCode != ''">PROJECTCODE = #{projectCode},</if>
            <if test="builderLicenceNum != null and builderLicenceNum != ''">BUILDERLICENCENUM = #{builderLicenceNum},
            </if>
            <if test="projectName != null and projectName != ''">PROJECTNAME = #{projectName},</if>
            <if test="address != null and address != ''">ADDRESS = #{address},</if>
            <if test="cost != null">COST = #{cost},</if>
            <if test="structureTypeNum != null and structureTypeNum != ''">STRUCTURETYPENUM = #{structureTypeNum},</if>
            <if test="prjFunctionNum != null and prjFunctionNum != ''">PRJFUNCTIONNUM = #{prjFunctionNum},</if>
            <if test="investType != null and investType != ''">INVESTTYPE = #{investType},</if>
            <if test="baseType != null and baseType != ''">BASETYPE = #{baseType},</if>
            <if test="area != null">AREA = #{area},</if>
            <if test="beginDate != null">BEGINDATE = #{beginDate},</if>
            <if test="endDate != null">ENDDATE = #{endDate},</if>
            <if test="superviseProgress != null and superviseProgress != ''">SUPERVISEPROGRESS = #{superviseProgress},
            </if>
            <if test="informDate != null">INFORMDATE = #{informDate},</if>
            <if test="declareDescribe != null and declareDescribe != ''">DECLAREDESCRIBE = #{declareDescribe},</if>
            <if test="superviseOrgan != null and superviseOrgan != ''">SUPERVISEORGAN = #{superviseOrgan},</if>
            <if test="superviseOrganSocialCreditCode != null and superviseOrganSocialCreditCode != ''">
                SUPERVISEORGANSOCIALCREDITCODE = #{superviseOrganSocialCreditCode},
            </if>
            <if test="areaCode != null and areaCode != ''">AREACODE = #{areaCode},</if>
            <if test="superviseUser != null and superviseUser != ''">SUPERVISEUSER = #{superviseUser},</if>
            <if test="locationX != null and locationX != ''">LOCATIONX = #{locationX},</if>
            <if test="locationY != null and locationY != ''">LOCATIONY = #{locationY}</if>
        </set>
        WHERE ID = #{id} AND DEL_FLAG = '0'
    </update>

    <!-- Logical delete (set del_flag to '1') -->
    <update id="deleteById" parameterType="Integer">
        UPDATE XMXX
        SET DEL_FLAG = '1'
        WHERE ID = #{id}
          AND DEL_FLAG = '0'
    </update>

    <!-- Physical delete (remove from database) -->
    <delete id="deleteByIdPhysically" parameterType="Integer">
        DELETE
        FROM XMXX
        WHERE ID = #{id}
    </delete>

    <delete id="deleteAllPhysically">
        DELETE FROM XMXX
    </delete>

    <!-- Select by ID -->
    <select id="selectById" parameterType="Integer" resultMap="XmxxResult">
        <include refid="selectXmxxVo"/>
        WHERE ID = #{id} AND DEL_FLAG = '0'
    </select>

    <!-- Select by project code -->
    <select id="selectByProjectCode" parameterType="String" resultMap="XmxxResult">
        <include refid="selectXmxxVo"/>
        WHERE PROJECTCODE = #{projectCode} AND DEL_FLAG = '0'
    </select>

    <!-- Select all (not deleted) -->
    <select id="selectAll" resultMap="XmxxResult">
        <include refid="selectXmxxVo"/>
        WHERE DEL_FLAG = '0'
    </select>

    <!-- Select list by condition -->
    <select id="selectList" parameterType="org.dromara.system.domain.XMXX" resultMap="XmxxResult">
        <include refid="selectXmxxVo"/>
        <where>
            DEL_FLAG = '0'
            <if test="xmszs != null and xmszs != ''">AND XMSZS = #{xmszs}</if>
            <if test="xmszq != null and xmszq != ''">AND XMSZQ = #{xmszq}</if>
            <if test="projectName != null and projectName != ''">AND PROJECTNAME LIKE CONCAT('%', #{projectName}, '%')
            </if>
            <if test="builderLicenceNum != null and builderLicenceNum != ''">AND BUILDERLICENCENUM =
                #{builderLicenceNum}
            </if>
            <if test="superviseOrgan != null and superviseOrgan != ''">AND SUPERVISEORGAN = #{superviseOrgan}</if>
            <!-- Add more conditions as needed -->
        </where>
    </select>

    <select id="findBuilderlicencenumList"  resultMap="XmxxResult">
        select DISTINCT BUILDERLICENCENUM from xmxx where LENGTH(BUILDERLICENCENUM) = 18
    </select>

</mapper>

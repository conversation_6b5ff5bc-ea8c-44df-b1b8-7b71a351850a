package org.dromara.projects.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.ai.domain.dto.AiHazAnalysisResultResetDTO;
import org.dromara.ai.domain.dto.AiHazAnalysisTasksDto;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.projects.domain.bo.PrjHazardousItemsAppBo;
import org.dromara.projects.domain.bo.PrjHazardousItemsBo;
import org.dromara.projects.domain.vo.ItemsAiDetailVO;
import org.dromara.projects.domain.vo.PrjHazardousItemsVo;

import java.util.Collection;
import java.util.List;

/**
 * [项目管理] 列出项目内具体的危险性较大的分部分项工程Service接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
public interface IPrjHazardousItemsService {

    /**
     * 查询[项目管理] 列出项目内具体的危险性较大的分部分项工程
     *
     * @param itemId 主键
     * @return [项目管理] 列出项目内具体的危险性较大的分部分项工程
     */
    PrjHazardousItemsVo queryById(Long itemId);

    PrjHazardousItemsVo queryByIdApp(Long itemId);

    /**
     * 分页查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return [项目管理] 列出项目内具体的危险性较大的分部分项工程分页列表
     */
    TableDataInfo<PrjHazardousItemsVo> queryPageList(PrjHazardousItemsBo bo, PageQuery pageQuery);

    /**
     * 管理员分页查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return [项目管理] 列出项目内具体的危险性较大的分部分项工程分页列表
     */
    TableDataInfo<PrjHazardousItemsVo> queryAdminPageList(PrjHazardousItemsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的[项目管理] 列出项目内具体的危险性较大的分部分项工程列表
     *
     * @param bo 查询条件
     * @return [项目管理] 列出项目内具体的危险性较大的分部分项工程列表
     */
    List<PrjHazardousItemsVo> queryList(PrjHazardousItemsBo bo);

    /**
     * 新增[项目管理] 列出项目内具体的危险性较大的分部分项工程
     *
     * @param bo [项目管理] 列出项目内具体的危险性较大的分部分项工程
     * @return 是否新增成功
     */
    String insertByBo(PrjHazardousItemsBo bo);

    /**
     * 修改[项目管理] 列出项目内具体的危险性较大的分部分项工程
     *
     * @param bo [项目管理] 列出项目内具体的危险性较大的分部分项工程
     * @return 是否修改成功
     */
    Boolean updateByBo(PrjHazardousItemsBo bo);

    /**
     * 校验并批量删除[项目管理] 列出项目内具体的危险性较大的分部分项工程信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 分部分项获取详细信息
     * @param itemId
     * @return
     */
    PrjHazardousItemsVo queryDetailById(Long itemId);

    /**
     * ai隐患清单详情
     * @param taskId
     * @return
     */
    ItemsAiDetailVO getAiHazAnalysisTaskDetail(Long taskId);

    IPage<PrjHazardousItemsVo> queryAppList(PrjHazardousItemsAppBo bo);

    Boolean saveAiTaskAndResult(AiHazAnalysisResultResetDTO dto);
}

好的，这就为您整理一份基于前面所有讨论内容的、详细的、无外键关联的数据库设计字典。其中包含了所有表和字段，并对作为字典的字段提供了中文含义注解。

**重要说明：**

*   **无外键约束：** 此设计严格遵守“无外键关联”的要求。所有表之间的关联都是“逻辑外键”，我在注释中会指明。
*   **注释注解：** 我会为每个表和每个字段添加详细的中文注释。
*   **字典字段中文含义：** 对于那些作为“字典”使用的字段（例如 `org_type`, `status`, `warning_type` 等），我会在其注释中列出建议的枚举值及其对应的中文含义。
*   **模块前缀：** 表名继续使用之前确定的模块前缀 (`sys_`, `prj_`, `mon_`, `wo_`, `insp_`, `ai_haz_`, `att_rule_`)。

```sql
-- -----------------------------------------------------
-- 危大工程监管系统数据库 Schema 创建脚本 (无外键约束 - 带模块前缀 - 完整数据字典版)
-- 版本: 1.4
-- 创建日期: 2025-05-09
-- 注意: 此版本移除了所有外键约束，注释中包含字典含义
-- -----------------------------------------------------

-- 设置会话变量
SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='TRADITIONAL,ALLOW_INVALID_DATES';

-- 设置字符集
SET NAMES utf8mb4;

-- =============================================
-- 核心系统 (sys_)
-- =============================================

-- -----------------------------------------------------
-- 表 `sys_organizations` (组织机构表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `sys_organizations` ;

CREATE TABLE IF NOT EXISTS `sys_organizations` (
  `org_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '组织机构ID (主键)',
  `org_name` VARCHAR(255) NOT NULL COMMENT '组织机构名称',
  `org_type` VARCHAR(50) NOT NULL COMMENT '组织机构类型 (字典: CLIENT[建设单位], CONSTRUCTION_GENERAL[施工总承包单位], CONSTRUCTION_SUB[专业分包单位], SUPERVISION[监理单位], DESIGN[设计单位], SURVEY[勘察单位], GOV_MINISTRY[(国家)部级监管部门], GOV_PROVINCE[省级住建部门], GOV_CITY_DEPT[市级住建部门], GOV_CITY_QS[市级质监站/安监机构], GOV_DISTRICT_DEPT[区/县级住建部门], GOV_DISTRICT_QS[区/县级质监站/安监机构], EXPERT_ORG[专家库管理单位], THIRD_PARTY_MONITORING[第三方监测单位], OTHER[其他相关单位])',
  `org_code` VARCHAR(100) NULL DEFAULT NULL UNIQUE COMMENT '组织机构代码 (例如: 统一社会信用代码)',
  `address` TEXT NULL DEFAULT NULL COMMENT '地址',
  `contact_person` VARCHAR(100) NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` VARCHAR(50) NULL DEFAULT NULL COMMENT '联系电话',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`org_id`),
  UNIQUE INDEX `org_code_UNIQUE` (`org_code` ASC))
ENGINE = InnoDB
COMMENT = '[核心系统] 存储参与系统的各组织机构信息';


-- -----------------------------------------------------
-- 表 `sys_roles` (角色表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `sys_roles` ;

CREATE TABLE IF NOT EXISTS `sys_roles` (
  `role_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '角色ID (主键)',
  `role_name` VARCHAR(100) NOT NULL COMMENT '角色名称 (例如: "系统管理员", "建设单位用户", "专家")',
  `role_key` VARCHAR(100) NOT NULL COMMENT '角色权限关键字 (用于程序识别，例如: "admin", "client", "expert")',
  `description` TEXT NULL DEFAULT NULL COMMENT '角色描述',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`role_id`),
  UNIQUE INDEX `role_name_UNIQUE` (`role_name` ASC, `del_flag` ASC),
  UNIQUE INDEX `role_key_UNIQUE` (`role_key` ASC, `del_flag` ASC))
ENGINE = InnoDB
COMMENT = '[核心系统] 定义系统角色及其权限标识';


-- -----------------------------------------------------
-- 表 `sys_users` (用户表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `sys_users` ;

CREATE TABLE IF NOT EXISTS `sys_users` (
  `user_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID (主键)',
  `username` VARCHAR(100) NOT NULL COMMENT '登录用户名',
  `password_hash` VARCHAR(255) NOT NULL COMMENT '密码哈希值',
  `full_name` VARCHAR(100) NULL DEFAULT NULL COMMENT '用户真实姓名',
  `org_id` BIGINT NULL DEFAULT NULL COMMENT '所属组织机构ID (逻辑外键至 sys_organizations.org_id)',
  `role_id` BIGINT NULL DEFAULT NULL COMMENT '主要角色ID (逻辑外键至 sys_roles.role_id, 若需多角色可建中间表)',
  `phone_number` VARCHAR(50) NULL DEFAULT NULL UNIQUE COMMENT '手机号码',
  `email` VARCHAR(100) NULL DEFAULT NULL UNIQUE COMMENT '电子邮箱',
  `avatar_url` VARCHAR(255) NULL DEFAULT NULL COMMENT '用户头像URL',
  `status` CHAR(1) NOT NULL DEFAULT '0' COMMENT '用户状态 (字典: 0[正常/启用], 1[停用/禁用])',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`user_id`),
  UNIQUE INDEX `username_UNIQUE` (`username` ASC, `del_flag` ASC),
  UNIQUE INDEX `phone_number_UNIQUE` (`phone_number` ASC, `del_flag` ASC),
  UNIQUE INDEX `email_UNIQUE` (`email` ASC, `del_flag` ASC),
  INDEX `idx_sys_users_org_id` (`org_id` ASC),
  INDEX `idx_sys_users_role_id` (`role_id` ASC))
ENGINE = InnoDB
COMMENT = '[核心系统] 存储系统用户账号信息';


-- -----------------------------------------------------
-- 表 `sys_documents` (文档表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `sys_documents` ;

CREATE TABLE IF NOT EXISTS `sys_documents` (
  `document_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '文档ID (主键)',
  `project_id` BIGINT NULL DEFAULT NULL COMMENT '关联的项目ID (逻辑外键至 prj_projects.project_id, 可为NULL表示系统级文档)',
  `related_entity_type` VARCHAR(50) NULL DEFAULT NULL COMMENT '关联实体类型 (字典: PROJECT[项目], HAZARDOUS_ITEM[危大工程项], CONSTRUCTION_PLAN[施工方案], EXPERT_REVIEW[专家论证], RECTIFICATION[整改记录], EQUIPMENT[特种设备], USER_CV[用户简历], CERTIFICATE[证书], ORGANIZATION[组织机构], WORK_ORDER[工单], INSPECTION[巡检记录], SPOT_CHECK[项目抽检], AI_ANALYSIS_TASK[智能分析任务] 等)',
  `related_entity_id` BIGINT NULL DEFAULT NULL COMMENT '关联实体ID (配合related_entity_type使用，指向具体业务表记录的ID)',
  `document_category` VARCHAR(100) NULL DEFAULT NULL COMMENT '文档类别 (字典: PLAN[方案], REPORT[报告], CERTIFICATE_SCAN[证书扫描件], EVIDENCE[证据材料], PHOTO[照片], DRAWING[图纸], FORM[表格], CONTRACT[合同], MEETING_MINUTES[会议纪要], CV[简历], PERMIT[许可证], FINANCIAL_VOUCHER[财务凭证] 等)',
  `file_name` VARCHAR(255) NOT NULL COMMENT '原始文件名',
  `file_path` VARCHAR(512) NOT NULL COMMENT '文件存储相对路径或URL',
  `file_type` VARCHAR(50) NULL DEFAULT NULL COMMENT '文件MIME类型或扩展名 (例如: "application/pdf", "image/jpeg")',
  `file_size` BIGINT NULL DEFAULT NULL COMMENT '文件大小 (字节)',
  `upload_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间戳',
  `uploaded_by_user_id` BIGINT NULL DEFAULT NULL COMMENT '上传用户ID (逻辑外键至 sys_users.user_id)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`document_id`),
  INDEX `idx_sys_documents_project_id` (`project_id` ASC),
  INDEX `idx_sys_documents_uploaded_by_user_id` (`uploaded_by_user_id` ASC),
  INDEX `idx_related_entity` (`related_entity_type` ASC, `related_entity_id` ASC),
  INDEX `idx_document_category` (`document_category` ASC))
ENGINE = InnoDB
COMMENT = '[核心系统] 存储所有上传文档的元数据信息，实现灵活关联';


-- -----------------------------------------------------
-- 表 `sys_experts` (专家表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `sys_experts` ;

CREATE TABLE IF NOT EXISTS `sys_experts` (
  `expert_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '专家ID (主键)',
  `user_id` BIGINT NOT NULL UNIQUE COMMENT '关联的用户账号ID (逻辑外键至 sys_users.user_id)',
  `qualification_level` VARCHAR(100) NULL DEFAULT NULL COMMENT '职称/资格 (例如: "高级工程师", "教授级高级工程师")',
  `specialization` TEXT NULL DEFAULT NULL COMMENT '专业领域 (可存储多个，例如JSON或逗号分隔字符串)',
  `years_experience` INT NULL DEFAULT NULL COMMENT '相关专业工作年限',
  `employer_org_id` BIGINT NULL DEFAULT NULL COMMENT '工作单位组织机构ID (逻辑外键至 sys_organizations.org_id, 如适用)',
  `cv_document_id` BIGINT NULL DEFAULT NULL COMMENT '简历文档ID (逻辑外键至 sys_documents.document_id)',
  `is_in_pool` TINYINT(1) NULL DEFAULT 1 COMMENT '是否当前在专家库中活跃 (字典: 0[否], 1[是])',
  `performance_record` TEXT NULL DEFAULT NULL COMMENT '历史业绩/评审记录备注',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`expert_id`),
  UNIQUE INDEX `user_id_UNIQUE` (`user_id` ASC, `del_flag` ASC),
  INDEX `idx_sys_experts_employer_org_id` (`employer_org_id` ASC),
  INDEX `idx_sys_experts_cv_document_id` (`cv_document_id` ASC))
ENGINE = InnoDB
COMMENT = '[核心系统] 存储系统中专家的详细信息';

-- -----------------------------------------------------
-- 表 `sys_certificates` (证书信息表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `sys_certificates`;

CREATE TABLE IF NOT EXISTS `sys_certificates` (
  `certificate_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '证书ID (主键)',
  `certificate_name` VARCHAR(255) NOT NULL COMMENT '证书名称 (例如: "企业资质证书", "安全生产许可证", "项目负责人安全生产考核合格证")',
  `certificate_type` VARCHAR(100) NOT NULL COMMENT '证书类型 (字典: CORP_QUALIFICATION[企业资质], SAFETY_PRODUCTION_LICENSE[安全生产许可], LEGAL_PERSON_SAFETY_CERT[法定代表人安全考核], TECH_LEAD_SAFETY_CERT[单位技术负责人安全考核], PROJECT_MANAGER_SAFETY_CERT[项目负责人安全考核], SAFETY_OFFICER_SAFETY_CERT[安全负责人安全考核], FULLTIME_SAFETY_PERSONNEL_CERT[专职安全员安全考核], CHIEF_SUPERVISOR_CERT[总监理工程师执业资格], SPECIALTY_SUPERVISOR_CERT[专业监理工程师证书], SUBCONTRACTOR_QUALIFICATION[分包单位企业资质], SUBCONTRACTOR_SAFETY_LICENSE[分包单位安全生产许可] 等)',
  `certificate_no` VARCHAR(255) NOT NULL COMMENT '证书编号',
  `issuing_authority` VARCHAR(255) NULL DEFAULT NULL COMMENT '发证机关',
  `issue_date` DATE NULL DEFAULT NULL COMMENT '发证日期',
  `expiry_date` DATE NULL DEFAULT NULL COMMENT '有效期至 (NULL表示长期有效)',
  `status` VARCHAR(50) NULL DEFAULT 'VALID' COMMENT '证书状态 (字典: VALID[有效], EXPIRED[已过期], REVOKED[已吊销], PENDING_VERIFICATION[待核验])',
  `certificate_level` VARCHAR(100) NULL DEFAULT NULL COMMENT '资质等级 (例如: "壹级", "乙级", 针对企业资质证书)',
  `scope_of_qualification` TEXT NULL DEFAULT NULL COMMENT '资质范围/专业 (针对企业资质证书或个人执业资格)',
  `related_entity_type` VARCHAR(50) NOT NULL COMMENT '关联实体类型 (字典: ORGANIZATION[组织机构], USER[系统用户], PROJECT_PERSONNEL[项目人员])',
  `related_entity_id` BIGINT NOT NULL COMMENT '关联实体ID (对应上述类型的具体记录ID)',
  `document_id` BIGINT NULL DEFAULT NULL COMMENT '证书扫描件/电子版文档ID (逻辑外键至 sys_documents.document_id)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`certificate_id`),
  INDEX `idx_sys_certificates_related_entity` (`related_entity_type` ASC, `related_entity_id` ASC),
  INDEX `idx_sys_certificates_document_id` (`document_id` ASC),
  INDEX `idx_sys_certificates_type_no` (`certificate_type` ASC, `certificate_no` ASC),
  UNIQUE INDEX `uk_certificate_no_type_entity` (`certificate_no` ASC, `certificate_type` ASC, `related_entity_id` ASC, `related_entity_type` ASC, `del_flag` ASC)
)
ENGINE = InnoDB
COMMENT = '[核心系统] 存储各类资质证书及考核合格证的详细信息';


-- =============================================
-- 项目管理 (prj_)
-- =============================================

-- -----------------------------------------------------
-- 表 `prj_projects` (项目表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `prj_projects` ;

CREATE TABLE IF NOT EXISTS `prj_projects` (
  `project_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '项目ID (主键)',
  `project_name` VARCHAR(255) NOT NULL COMMENT '项目名称',
  `project_code` VARCHAR(100) NULL DEFAULT NULL UNIQUE COMMENT '项目编码/标识 (若有，应唯一)',
  `project_overview` TEXT NULL DEFAULT NULL COMMENT '工程概况 (对应附件一.1)',
  `construction_permit_no` VARCHAR(255) NULL DEFAULT NULL COMMENT '施工许可证编号 (对应附件一.2)',
  `construction_permit_doc_id` BIGINT NULL DEFAULT NULL COMMENT '施工许可证扫描件文档ID (逻辑外键至 sys_documents.document_id)',
  `province_code` VARCHAR(20) NULL DEFAULT NULL COMMENT '项目所在省/直辖市编码 (参照国家行政区划代码)',
  `province_name` VARCHAR(100) NULL DEFAULT NULL COMMENT '项目所在省/直辖市名称',
  `city_code` VARCHAR(20) NULL DEFAULT NULL COMMENT '项目所在市编码',
  `city_name` VARCHAR(100) NULL DEFAULT NULL COMMENT '项目所在市名称',
  `district_code` VARCHAR(20) NULL DEFAULT NULL COMMENT '项目所在区/县编码',
  `district_name` VARCHAR(100) NULL DEFAULT NULL COMMENT '项目所在区/县名称',
  `county_code` VARCHAR(20) NULL DEFAULT NULL COMMENT '项目所在乡镇/街道编码 (可选)',
  `county_name` VARCHAR(100) NULL DEFAULT NULL COMMENT '项目所在乡镇/街道名称 (可选)',
  `location_detail` TEXT NULL DEFAULT NULL COMMENT '项目详细地址 (补充区划代码后的具体位置)',
  `status` VARCHAR(50) NULL DEFAULT 'PLANNING' COMMENT '项目状态 (字典: PLANNING[计划阶段], BIDDING[招标阶段], DESIGN[设计阶段], PRE_CONSTRUCTION[施工准备], IN_PROGRESS[施工中], SUSPENDED[暂停施工], ACCEPTANCE[验收阶段], COMPLETED[已竣工], ARCHIVED[已归档])',
  `start_date` DATE NULL DEFAULT NULL COMMENT '计划开工日期',
  `planned_end_date` DATE NULL DEFAULT NULL COMMENT '计划竣工日期',
  `actual_start_date` DATE NULL DEFAULT NULL COMMENT '实际开工日期',
  `actual_end_date` DATE NULL DEFAULT NULL COMMENT '实际竣工日期',
  `client_org_id` BIGINT NULL DEFAULT NULL COMMENT '建设单位ID (逻辑外键至 sys_organizations.org_id)',
  `construction_org_id` BIGINT NULL DEFAULT NULL COMMENT '施工总承包单位ID (逻辑外键至 sys_organizations.org_id)',
  `supervision_org_id` BIGINT NULL DEFAULT NULL COMMENT '监理单位ID (逻辑外键至 sys_organizations.org_id)',
  `design_org_id` BIGINT NULL DEFAULT NULL COMMENT '设计单位ID (逻辑外键至 sys_organizations.org_id)',
  `survey_org_id` BIGINT NULL DEFAULT NULL COMMENT '勘察单位ID (逻辑外键至 sys_organizations.org_id)',
  `subcontractor_org_ids` JSON NULL DEFAULT NULL COMMENT '专业分包单位ID列表 (JSON数组，元素为 sys_organizations.org_id)',
  `project_manager_user_id` BIGINT NULL DEFAULT NULL COMMENT '施工总承包单位的项目负责人用户ID (逻辑外键至 sys_users.user_id)',
  `supervision_chief_eng_user_id` BIGINT NULL DEFAULT NULL COMMENT '监理单位的总监理工程师用户ID (逻辑外键至 sys_users.user_id)',
  `safety_measures_fee_doc_id` BIGINT NULL DEFAULT NULL COMMENT '危大工程安全防护文明施工措施费财务凭证文档ID (逻辑外键至 sys_documents.document_id, 对应附件三.1)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`project_id`),
  UNIQUE INDEX `project_code_UNIQUE` (`project_code` ASC, `del_flag` ASC),
  INDEX `idx_prj_projects_construction_permit_doc_id` (`construction_permit_doc_id` ASC),
  INDEX `idx_prj_projects_safety_measures_fee_doc_id` (`safety_measures_fee_doc_id` ASC),
  INDEX `idx_prj_projects_client_org_id` (`client_org_id` ASC),
  INDEX `idx_prj_projects_construction_org_id` (`construction_org_id` ASC),
  INDEX `idx_prj_projects_supervision_org_id` (`supervision_org_id` ASC),
  INDEX `idx_prj_projects_design_org_id` (`design_org_id` ASC),
  INDEX `idx_prj_projects_survey_org_id` (`survey_org_id` ASC),
  INDEX `idx_prj_projects_pm_user_id` (`project_manager_user_id` ASC),
  INDEX `idx_prj_projects_sce_user_id` (`supervision_chief_eng_user_id` ASC),
  INDEX `idx_prj_projects_province_code` (`province_code` ASC),
  INDEX `idx_prj_projects_city_code` (`city_code` ASC),
  INDEX `idx_prj_projects_district_code` (`district_code` ASC),
  INDEX `idx_prj_projects_status` (`status` ASC)
)
ENGINE = InnoDB
COMMENT = '[项目管理] 存储建设工程项目的详细信息，包括地区、许可证和关键参建单位及人员';


-- -----------------------------------------------------
-- 表 `prj_personnel` (项目人员表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `prj_personnel`;

CREATE TABLE IF NOT EXISTS `prj_personnel` (
  `project_personnel_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '项目人员关联ID (主键)',
  `project_id` BIGINT NOT NULL COMMENT '项目ID (逻辑外键至 prj_projects.project_id)',
  `user_id` BIGINT NOT NULL COMMENT '用户ID (逻辑外键至 sys_users.user_id)',
  `org_id` BIGINT NULL DEFAULT NULL COMMENT '该人员在项目中的所属单位ID (逻辑外键至 sys_organizations.org_id, 例如其所属的施工单位、监理单位或分包单位)',
  `role_on_project` VARCHAR(100) NOT NULL COMMENT '在本项目中的具体角色/岗位 (字典, 例如: "项目负责人(施工方)", "技术负责人(施工方)", "安全负责人(施工方)", "法定代表人(施工方)", "专职安全生产管理人员(施工方)", "总监理工程师", "专业监理工程师", "特种作业人员-起重", "专项施工方案编制人" 等，需根据附件二、附件三、附件六等内容定义详细字典)',
  `is_special_ops` TINYINT(1) NULL DEFAULT 0 COMMENT '是否特种作业人员 (字典: 0[否], 1[是])',
  `start_date_on_project` DATE NULL DEFAULT NULL COMMENT '进入项目日期/岗位任命日期',
  `end_date_on_project` DATE NULL DEFAULT NULL COMMENT '离开项目日期/岗位解除日期',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`project_personnel_id`),
  INDEX `idx_prj_personnel_project_id` (`project_id` ASC),
  INDEX `idx_prj_personnel_user_id` (`user_id` ASC),
  INDEX `idx_prj_personnel_org_id` (`org_id` ASC),
  INDEX `idx_prj_personnel_role` (`role_on_project` ASC),
  UNIQUE INDEX `uk_project_user_role_org` (`project_id` ASC, `user_id` ASC, `role_on_project` ASC, `org_id` ASC, `del_flag` ASC)
)
ENGINE = InnoDB
COMMENT = '[项目管理] 关联用户到具体项目及其角色和所属单位，人员证书信息通过sys_certificates表关联';


-- -----------------------------------------------------
-- 表 `prj_hazardous_items` (危大工程清单表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `prj_hazardous_items` ;

CREATE TABLE IF NOT EXISTS `prj_hazardous_items` (
  `item_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '危大工程项ID (主键)',
  `project_id` BIGINT NOT NULL COMMENT '所属项目ID (逻辑外键至 prj_projects.project_id)',
  `item_name` VARCHAR(255) NOT NULL COMMENT '危大工程名称/描述 (例如: "1号深基坑工程", "高支模(教学楼3层)")',
  `hazardous_work_category` VARCHAR(100) NULL DEFAULT NULL COMMENT '危大工程类别 (字典, 参照国家《危险性较大的分部分项工程安全管理规定》附件1，例如: DEEP_EXCAVATION[深基坑工程], HIGH_FORMWORK[高大模板支撑体系], HEAVY_LIFTING[起重吊装及安装拆卸工程], SCAFFOLDING[脚手架工程] 等)',
  `scope_details` TEXT NULL DEFAULT NULL COMMENT '具体范围、部位或参数描述 (例如: "开挖深度6米，长50米，宽20米")',
  `is_exceeding_scale` TINYINT(1) NOT NULL COMMENT '是否超过一定规模需要专家论证 (字典: 0[否], 1[是], 参照国家《危险性较大的分部分项工程安全管理规定》附件2)',
  `status` VARCHAR(50) NULL DEFAULT 'PLANNED' COMMENT '危大工程项状态 (字典: PLANNED[计划中], PLAN_SUBMITTED[方案已提交], PLAN_APPROVED[方案已批准], IN_PROGRESS[施工中], MONITORING[监测中], ACCEPTANCE_PENDING[待验收], ACCEPTED[已验收], COMPLETED[完成])',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`item_id`),
  INDEX `idx_prj_hazardous_items_project_id` (`project_id` ASC),
  INDEX `idx_prj_hazardous_items_category` (`hazardous_work_category` ASC),
  INDEX `idx_prj_hazardous_items_status` (`status` ASC)
)
ENGINE = InnoDB
COMMENT = '[项目管理] 列出项目内具体的危险性较大的分部分项工程 (对应附件三.2)';


-- -----------------------------------------------------
-- 表 `prj_construction_plans` (专项施工方案表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `prj_construction_plans` ;

CREATE TABLE IF NOT EXISTS `prj_construction_plans` (
  `plan_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '方案ID (主键)',
  `item_id` BIGINT NOT NULL COMMENT '关联的危大工程项ID (逻辑外键至 prj_hazardous_items.item_id)',
  `plan_name` VARCHAR(255) NOT NULL COMMENT '专项施工方案名称 (对应附件四.(一).1)',
  `plan_version` VARCHAR(50) NULL DEFAULT '1.0' COMMENT '方案版本号',
  `plan_document_id` BIGINT NOT NULL COMMENT '专项施工方案电子版文档ID (逻辑外键至 sys_documents.document_id, 对应附件四.(一).2)',
  `approval_form_doc_id` BIGINT NULL DEFAULT NULL COMMENT '专项施工方案审批表文档ID (逻辑外键至 sys_documents.document_id, 对应附件四.(一).1 及 附件三末尾)',
  `submission_date` DATETIME NULL DEFAULT NULL COMMENT '方案提交日期',
  `review_status` VARCHAR(50) NULL DEFAULT 'PENDING_SUBMISSION' COMMENT '审核状态 (字典: PENDING_SUBMISSION[待提交], PENDING_SUPERVISOR_REVIEW[待监理审核], PENDING_EXPERT_REVIEW[待专家论证], PENDING_CLIENT_APPROVAL[待建设单位批准], APPROVED[已批准], REJECTED[已驳回/不通过], REVISION_NEEDED[修改后通过/需修改])',
  `ai_defect_warning_details` TEXT NULL DEFAULT NULL COMMENT 'AI对比方案缺陷预警提示内容 (对应附件四.(一).2)',
  `approved_by_user_id` BIGINT NULL DEFAULT NULL COMMENT '最终批准人用户ID (逻辑外键至 sys_users.user_id)',
  `approval_date` DATETIME NULL DEFAULT NULL COMMENT '最终批准日期',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`plan_id`),
  INDEX `idx_prj_plans_item_id` (`item_id` ASC),
  INDEX `idx_prj_plans_document_id` (`plan_document_id` ASC),
  INDEX `idx_prj_plans_approval_form_doc_id` (`approval_form_doc_id` ASC),
  INDEX `idx_prj_plans_approved_by_user_id` (`approved_by_user_id` ASC),
  INDEX `idx_prj_plans_status` (`review_status` ASC)
)
ENGINE = InnoDB
COMMENT = '[项目管理] 存储危大工程专项施工方案信息及其审批状态';


-- -----------------------------------------------------
-- 表 `prj_expert_reviews` (专家论证会信息表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `prj_expert_reviews`;

CREATE TABLE IF NOT EXISTS `prj_expert_reviews` (
  `review_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '专家论证ID (主键)',
  `plan_id` BIGINT NOT NULL COMMENT '被论证的专项施工方案ID (逻辑外键至 prj_construction_plans.plan_id)',
  `review_date` DATETIME NOT NULL COMMENT '论证会议日期',
  `meeting_location` VARCHAR(255) NULL DEFAULT NULL COMMENT '会议地点',
  `conclusion` VARCHAR(50) NOT NULL COMMENT '论证结论 (字典: PASS[通过], PASS_WITH_MODIFICATIONS[修改后通过], FAIL[不通过], 对应附件三.论证意见)',
  `expert_opinion_summary` TEXT NULL DEFAULT NULL COMMENT '专家组论证意见摘要/主要修改要求',
  `conflict_of_interest_warning` TEXT NULL DEFAULT NULL COMMENT '专家利害关系AI比对预警信息 (对应附件四.(二).1)',
  `report_document_id` BIGINT NULL DEFAULT NULL COMMENT '专家论证报告电子版文档ID (逻辑外键至 sys_documents.document_id, 对应附件四.(二).2)',
  `convenor_user_id` BIGINT NULL DEFAULT NULL COMMENT '会议组织者/召集人用户ID (逻辑外键至 sys_users.user_id)',
  `is_re_review_after_modification` TINYINT(1) DEFAULT 0 COMMENT '是否为修改后重新审核的论证 (字典: 0[首次论证], 1[修改后复审])',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`review_id`),
  INDEX `idx_prj_expert_reviews_plan_id` (`plan_id` ASC),
  INDEX `idx_prj_expert_reviews_report_doc_id` (`report_document_id` ASC),
  INDEX `idx_prj_expert_reviews_convenor_user_id` (`convenor_user_id` ASC),
  INDEX `idx_prj_expert_reviews_conclusion` (`conclusion` ASC)
)
ENGINE = InnoDB
COMMENT = '[项目管理] 记录专项施工方案专家论证会的信息，包含利害关系预警和论证结论';


-- -----------------------------------------------------
-- 表 `prj_expert_review_participants` (专家论证会参会人员表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `prj_expert_review_participants` ;

CREATE TABLE IF NOT EXISTS `prj_expert_review_participants` (
  `participant_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '参会记录ID (主键)',
  `review_id` BIGINT NOT NULL COMMENT '专家论证会议ID (逻辑外键至 prj_expert_reviews.review_id)',
  `user_id` BIGINT NULL DEFAULT NULL COMMENT '参会人员的用户ID (逻辑外键至 sys_users.user_id, 若系统中存在该用户)',
  `personnel_name` VARCHAR(100) NOT NULL COMMENT '参会人员姓名 (当user_id为空时，直接记录姓名)',
  `organization_name` VARCHAR(255) NULL DEFAULT NULL COMMENT '参会人员所属单位名称',
  `role_in_meeting` VARCHAR(100) NOT NULL COMMENT '在会议中的角色/身份 (字典, 对应附件三.论证会参会人员 a,b,c,d,e 各类人员的具体描述，例如: "论证专家", "建设单位项目负责人", "设计单位技术负责人", "施工方案编制人", "总监理工程师")',
  `is_attending_expert` TINYINT(1) NULL DEFAULT 0 COMMENT '是否为本次论证的评审专家 (字典: 0[否], 1[是])',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`participant_id`),
  INDEX `idx_prj_expert_participants_review_id` (`review_id` ASC),
  INDEX `idx_prj_expert_participants_user_id` (`user_id` ASC),
  INDEX `idx_prj_expert_participants_role` (`role_in_meeting` ASC),
  UNIQUE INDEX `uk_review_user_role` (`review_id` ASC, `user_id` ASC, `role_in_meeting` ASC, `del_flag` ASC)
)
ENGINE = InnoDB
COMMENT = '[项目管理] 列出专家论证会的参会人员及其角色 (对应附件三.论证会参会人员)';

-- -----------------------------------------------------
-- 表 `prj_acceptance` (危大工程验收信息表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `prj_acceptance`;

CREATE TABLE IF NOT EXISTS `prj_acceptance` (
  `acceptance_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '验收ID (主键)',
  `item_id` BIGINT NOT NULL COMMENT '关联的危大工程项ID (逻辑外键至 prj_hazardous_items.item_id)',
  `acceptance_date` DATE NULL DEFAULT NULL COMMENT '验收日期',
  `acceptance_result` VARCHAR(50) NULL DEFAULT NULL COMMENT '验收结果 (字典: QUALIFIED[合格], UNQUALIFIED[不合格], QUALIFIED_AFTER_RECTIFICATION[整改后合格])',
  `acceptance_personnel_info` JSON NULL DEFAULT NULL COMMENT '验收人员信息 (JSON对象或数组, 存储附件六中a,b,c各类人员的姓名、单位、职务等详细信息)',
  `acceptance_report_doc_id` BIGINT NULL DEFAULT NULL COMMENT '危大工程验收表文档ID (逻辑外键至 sys_documents.document_id, 对应附件六.1)',
  `remarks` TEXT NULL DEFAULT NULL COMMENT '验收备注',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`acceptance_id`),
  INDEX `idx_prj_acceptance_item_id` (`item_id` ASC),
  INDEX `idx_prj_acceptance_report_doc_id` (`acceptance_report_doc_id` ASC),
  INDEX `idx_prj_acceptance_result` (`acceptance_result` ASC)
)
ENGINE = InnoDB
COMMENT = '[项目管理] 存储危大工程的验收信息 (对应附件六)';


-- =============================================
-- 监测与预警 (mon_)
-- =============================================

-- -----------------------------------------------------
-- 表 `mon_plans` (监测方案表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `mon_plans` ;

CREATE TABLE IF NOT EXISTS `mon_plans` (
  `monitoring_plan_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '监测方案ID (主键)',
  `item_id` BIGINT NOT NULL COMMENT '关联的危大工程项ID (逻辑外键至 prj_hazardous_items.item_id)',
  `monitoring_org_id` BIGINT NULL DEFAULT NULL COMMENT '第三方监测单位ID (逻辑外键至 sys_organizations.org_id, org_type为THIRD_PARTY_MONITORING)',
  `plan_document_id` BIGINT NOT NULL COMMENT '监测方案文档ID (逻辑外键至 sys_documents.document_id)',
  `monitoring_content` TEXT NULL DEFAULT NULL COMMENT '主要监测内容、测点布置、监测方法',
  `monitoring_frequency` VARCHAR(100) NULL DEFAULT NULL COMMENT '监测频率 (例如: "每日2次", "每2小时一次")',
  `warning_thresholds` TEXT NULL DEFAULT NULL COMMENT '各项监测指标的预警阈值标准',
  `start_date` DATE NULL DEFAULT NULL COMMENT '监测开始日期',
  `end_date` DATE NULL DEFAULT NULL COMMENT '监测结束日期',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`monitoring_plan_id`),
  INDEX `idx_mon_plans_item_id` (`item_id` ASC),
  INDEX `idx_mon_plans_org_id` (`monitoring_org_id` ASC),
  INDEX `idx_mon_plans_doc_id` (`plan_document_id` ASC))
ENGINE = InnoDB
COMMENT = '[监测与预警] 存储危大工程的第三方监测方案信息';


-- -----------------------------------------------------
-- 表 `mon_data` (监测数据表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `mon_data` ;

CREATE TABLE IF NOT EXISTS `mon_data` (
  `data_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '监测数据记录ID (主键)',
  `monitoring_plan_id` BIGINT NOT NULL COMMENT '关联的监测方案ID (逻辑外键至 mon_plans.monitoring_plan_id)',
  `measurement_time` DATETIME NOT NULL COMMENT '测量时间戳',
  `sensor_id` VARCHAR(100) NULL DEFAULT NULL COMMENT '传感器或测点编号/名称',
  `monitored_parameter` VARCHAR(100) NOT NULL COMMENT '监测参数名称 (例如: "沉降", "位移", "应力")',
  `data_value` VARCHAR(255) NOT NULL COMMENT '测量值 (可为数值或JSON存储复合值)',
  `unit` VARCHAR(50) NULL DEFAULT NULL COMMENT '测量单位 (例如: "mm", "MPa")',
  `is_threshold_exceeded` TINYINT(1) NULL DEFAULT 0 COMMENT '是否超出预警阈值 (字典: 0[否], 1[是])',
  `remarks` TEXT NULL DEFAULT NULL COMMENT '数据备注',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`data_id`),
  INDEX `idx_mon_data_plan_id` (`monitoring_plan_id` ASC),
  INDEX `idx_measurement_time` (`measurement_time` ASC),
  INDEX `idx_mon_data_parameter` (`monitored_parameter` ASC))
ENGINE = InnoDB
COMMENT = '[监测与预警] 存储实际的监测数据点';


-- -----------------------------------------------------
-- 表 `mon_warnings` (预警信息表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `mon_warnings`;

CREATE TABLE IF NOT EXISTS `mon_warnings` (
  `warning_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '预警ID (主键)',
  `project_id` BIGINT NOT NULL COMMENT '预警所属项目ID (逻辑外键至 prj_projects.project_id)',
  `warning_type` VARCHAR(50) NOT NULL COMMENT '预警类型 (字典: AI_RISK[AI风险预警], ATTENDANCE_ABNORMAL[考勤异常], MONITORING_THRESHOLD_EXCEEDED[监测数据超阈值], MANUAL_REPORTED_HAZARD[人工上报隐患], PLAN_DEFECT_AI[方案AI缺陷预警], EXPERT_COI_WARNING[专家利害关系预警])',
  `source_type` VARCHAR(50) NULL DEFAULT NULL COMMENT '预警来源类型 (字典: CAMERA[摄像头], E_DOG[电子狗AI], SENSOR[监测传感器], ATTENDANCE_SYSTEM[考勤系统], USER_APP_REPORT[用户App上报], SYSTEM_AI_ANALYSIS[系统AI分析])',
  `source_id` VARCHAR(100) NULL DEFAULT NULL COMMENT '预警来源的具体标识 (例如: 摄像头编号, 传感器ID, 上报用户ID, 方案ID)',
  `trigger_time` DATETIME NOT NULL COMMENT '预警触发时间戳',
  `related_item_id` BIGINT NULL DEFAULT NULL COMMENT '关联的危大工程项ID (逻辑外键至 prj_hazardous_items.item_id, 若适用)',
  `related_plan_id` BIGINT NULL DEFAULT NULL COMMENT '关联的施工方案ID (逻辑外键至 prj_construction_plans.plan_id, 若为方案缺陷预警)',
  `related_personnel_id` BIGINT NULL DEFAULT NULL COMMENT '关联的项目人员ID (逻辑外键至 prj_personnel.project_personnel_id, 若与特定人员相关)',
  `related_equipment_id` BIGINT NULL DEFAULT NULL COMMENT '关联的特种设备ID (逻辑外键至 insp_special_equipment.equipment_id, 若与设备相关)',
  `severity_level` VARCHAR(50) NULL DEFAULT 'MEDIUM' COMMENT '严重级别 (字典: LOW[低], MEDIUM[中], HIGH[高], CRITICAL[危急])',
  `description` TEXT NOT NULL COMMENT '预警/风险详细描述',
  `status` VARCHAR(50) NULL DEFAULT 'NEW' COMMENT '预警处理状态 (字典: NEW[新增待处理], ACKNOWLEDGED[已确认/已知晓], UNDER_INVESTIGATION[核查中], ACTION_PENDING[待处置/待派单], WORK_ORDER_ISSUED[已生成工单], RESOLVED[已解决/闭环], FALSE_ALARM[误报], ARCHIVED[已归档])',
  `archived_flag` TINYINT(1) NULL DEFAULT 0 COMMENT '是否已存档 (字典: 0[否], 1[是])',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`warning_id`),
  INDEX `idx_mon_warnings_project_id` (`project_id` ASC),
  INDEX `idx_mon_warnings_related_item_id` (`related_item_id` ASC),
  INDEX `idx_mon_warnings_related_plan_id` (`related_plan_id` ASC),
  INDEX `idx_mon_warnings_related_personnel_id` (`related_personnel_id` ASC),
  INDEX `idx_mon_warnings_related_equipment_id` (`related_equipment_id` ASC),
  INDEX `idx_trigger_time` (`trigger_time` ASC),
  INDEX `idx_warning_type` (`warning_type` ASC),
  INDEX `idx_warning_status` (`status` ASC)
)
ENGINE = InnoDB
COMMENT = '[监测与预警] 记录所有由系统或用户生成的各类预警和警报信息';


-- -----------------------------------------------------
-- 表 `mon_attendance_records` (考勤记录表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `mon_attendance_records` ;

CREATE TABLE IF NOT EXISTS `mon_attendance_records` (
  `record_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '考勤记录ID (主键)',
  `project_id` BIGINT NOT NULL COMMENT '考勤所属项目ID (逻辑外键至 prj_projects.project_id)',
  `project_personnel_id` BIGINT NOT NULL COMMENT '考勤的项目人员ID (逻辑外键至 prj_personnel.project_personnel_id)',
  `check_in_time` DATETIME NULL DEFAULT NULL COMMENT '上班签到时间戳',
  `check_out_time` DATETIME NULL DEFAULT NULL COMMENT '下班签出时间戳',
  `attendance_date` DATE NOT NULL COMMENT '考勤日期',
  `source` VARCHAR(50) NULL DEFAULT NULL COMMENT '打卡记录来源 (字典: GATE_SCAN[门禁刷卡], APP_CHECKIN[手机App签到], MANUAL_ENTRY[人工补录], FACE_RECOGNITION[人脸识别])',
  `is_abnormal` TINYINT(1) NULL DEFAULT 0 COMMENT '考勤是否异常 (字典: 0[正常], 1[异常])',
  `abnormality_type` VARCHAR(50) NULL DEFAULT NULL COMMENT '异常类型 (字典: LATE[迟到], EARLY_LEAVE[早退], ABSENT[缺勤], MISSED_CHECK_IN[漏打上班卡], MISSED_CHECK_OUT[漏打下班卡], LOCATION_MISMATCH[地点不符], OTHER[其他异常], 当is_abnormal=1时有效)',
  `abnormality_reason` TEXT NULL DEFAULT NULL COMMENT '异常原因说明/备注',
  `related_warning_id` BIGINT NULL DEFAULT NULL COMMENT '关联的考勤异常预警ID (逻辑外键至 mon_warnings.warning_id)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`record_id`),
  INDEX `idx_mon_attendance_project_id` (`project_id` ASC),
  INDEX `idx_mon_attendance_personnel_id` (`project_personnel_id` ASC),
  INDEX `idx_mon_attendance_warning_id` (`related_warning_id` ASC),
  INDEX `idx_attendance_date` (`attendance_date` ASC),
  INDEX `idx_mon_attendance_abnormal_type` (`abnormality_type` ASC)
)
ENGINE = InnoDB
COMMENT = '[监测与预警] 存储项目人员的考勤打卡记录';


-- =============================================
-- 打卡规则 (att_rule_)
-- =============================================

-- -----------------------------------------------------
-- 表 `att_rule_definitions` (打卡规则定义表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `att_rule_definitions`;

CREATE TABLE IF NOT EXISTS `att_rule_definitions` (
  `rule_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '规则ID (主键)',
  `rule_name` VARCHAR(255) NOT NULL COMMENT '规则名称 (例如: "标准工作日打卡规则", "特种作业弹性打卡")',
  `description` TEXT NULL DEFAULT NULL COMMENT '规则描述',
  `workday_type` VARCHAR(50) NOT NULL DEFAULT 'STANDARD' COMMENT '工作日类型 (字典: STANDARD[标准工作日], FLEXIBLE[弹性工作日], SHIFT[轮班制])',
  `check_in_start_time` TIME NULL DEFAULT NULL COMMENT '上班打卡允许开始时间 (HH:MM:SS)',
  `standard_check_in_time` TIME NOT NULL COMMENT '标准上班时间 (HH:MM:SS)',
  `check_in_end_time` TIME NULL DEFAULT NULL COMMENT '上班打卡允许结束时间 (HH:MM:SS)',
  `late_buffer_minutes` INT NULL DEFAULT 0 COMMENT '迟到缓冲分钟数 (例如: 15分钟内不算迟到)',
  `check_out_start_time` TIME NULL DEFAULT NULL COMMENT '下班打卡允许开始时间 (HH:MM:SS)',
  `standard_check_out_time` TIME NOT NULL COMMENT '标准下班时间 (HH:MM:SS)',
  `check_out_end_time` TIME NULL DEFAULT NULL COMMENT '下班打卡允许结束时间 (HH:MM:SS)',
  `early_leave_buffer_minutes` INT NULL DEFAULT 0 COMMENT '早退缓冲分钟数 (例如: 提前15分钟内不算早退)',
  `min_work_duration_hours` DECIMAL(4,2) NULL DEFAULT NULL COMMENT '每日最短工作时长 (小时，可选，用于判断是否满足工时)',
  `is_location_required` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否需要地理位置校验 (字典: 0[否], 1[是])',
  `allowed_distance_meters` INT NULL DEFAULT 500 COMMENT '允许打卡地点偏差范围 (米, 当is_location_required=1时有效)',
  `check_in_frequency` VARCHAR(50) DEFAULT 'ONCE_PER_DAY' COMMENT '打卡频率 (字典: ONCE_PER_DAY[每日一次上下班], TWICE_PER_DAY[每日两次签到退], MULTIPLE[多次签到])',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '规则是否启用 (字典: 0[否], 1[是])',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`rule_id`),
  UNIQUE INDEX `uk_rule_name` (`rule_name` ASC, `del_flag` ASC)
)
ENGINE = InnoDB
COMMENT = '[打卡规则] 定义通用的打卡规则模板';


-- -----------------------------------------------------
-- 表 `att_rule_assignments` (打卡规则分配表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `att_rule_assignments`;

CREATE TABLE IF NOT EXISTS `att_rule_assignments` (
  `assignment_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '分配ID (主键)',
  `rule_id` BIGINT NOT NULL COMMENT '关联的打卡规则ID (逻辑外键至 att_rule_definitions.rule_id)',
  `assignment_level` VARCHAR(50) NOT NULL COMMENT '分配层级 (字典: PROJECT[项目级], ORG_TYPE[组织类型级], PERSONNEL_ROLE[项目角色级], SPECIFIC_PERSONNEL[特定人员级])',
  `project_id` BIGINT NULL DEFAULT NULL COMMENT '适用的项目ID (逻辑外键至 prj_projects.project_id, 根据assignment_level填写)',
  `org_type_key` VARCHAR(50) NULL DEFAULT NULL COMMENT '适用的组织类型Key (逻辑参照 sys_organizations.org_type, 根据assignment_level填写)',
  `project_role_key` VARCHAR(100) NULL DEFAULT NULL COMMENT '适用的项目角色Key (逻辑参照 prj_personnel.role_on_project, 根据assignment_level填写)',
  `user_id` BIGINT NULL DEFAULT NULL COMMENT '适用的特定用户ID (逻辑外键至 sys_users.user_id, 根据assignment_level填写)',
  `priority` INT NOT NULL DEFAULT 10 COMMENT '规则优先级 (数字越小优先级越高，用于处理规则冲突)',
  `effective_start_date` DATE NOT NULL COMMENT '规则适用开始日期',
  `effective_end_date` DATE NULL DEFAULT NULL COMMENT '规则适用结束日期 (NULL表示永久有效)',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '此分配是否启用 (字典: 0[否], 1[是])',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`assignment_id`),
  INDEX `idx_att_rule_assignments_rule_id` (`rule_id` ASC),
  INDEX `idx_att_rule_assignments_project_id` (`project_id` ASC),
  INDEX `idx_att_rule_assignments_user_id` (`user_id` ASC),
  INDEX `idx_att_rule_assignments_level_keys` (`assignment_level` ASC, `org_type_key` ASC, `project_role_key` ASC),
  INDEX `idx_att_rule_assignments_active_dates` (`is_active` ASC, `effective_start_date` ASC, `effective_end_date` ASC),
  INDEX `idx_att_rule_assignments_priority` (`priority` ASC)
)
ENGINE = InnoDB
COMMENT = '[打卡规则] 将打卡规则模板分配给不同的实体（项目、组织类型、角色、人员）';


-- -----------------------------------------------------
-- 表 `att_rule_holidays` (节假日与特殊工作日表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `att_rule_holidays`;

CREATE TABLE IF NOT EXISTS `att_rule_holidays` (
  `holiday_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID (主键)',
  `holiday_date` DATE NOT NULL COMMENT '日期',
  `day_type` VARCHAR(50) NOT NULL COMMENT '日期类型 (字典: HOLIDAY[法定节假日], SPECIAL_WORKDAY[特殊工作日/调休上班])',
  `description` VARCHAR(255) NULL DEFAULT NULL COMMENT '描述 (例如: "国庆节", "周末调休上班")',
  `applicable_scope` VARCHAR(50) DEFAULT 'GLOBAL' COMMENT '适用范围 (字典: GLOBAL[全局适用], SPECIFIC_PROJECT[特定项目], SPECIFIC_REGION[特定地区])',
  `project_id` BIGINT NULL DEFAULT NULL COMMENT '适用项目ID (逻辑外键至 prj_projects.project_id, 当 applicable_scope=SPECIFIC_PROJECT 时填写)',
  `region_code` VARCHAR(50) NULL DEFAULT NULL COMMENT '适用地区代码 (例如行政区划代码, 当 applicable_scope=SPECIFIC_REGION 时填写)',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '此记录是否启用 (字典: 0[否], 1[是])',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`holiday_id`),
  UNIQUE INDEX `uk_holiday_date_scope_project_region` (`holiday_date` ASC, `applicable_scope` ASC, `project_id` ASC, `region_code` ASC, `del_flag` ASC),
  INDEX `idx_att_rule_holidays_project_id` (`project_id` ASC),
  INDEX `idx_att_rule_holidays_date_type` (`holiday_date` ASC, `day_type` ASC)
)
ENGINE = InnoDB
COMMENT = '[打卡规则] 定义节假日和特殊工作日，用于辅助考勤计算';


-- =============================================
-- 工单与整改 (wo_)
-- =============================================

-- -----------------------------------------------------
-- 表 `wo_orders` (工单表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `wo_orders` ;

CREATE TABLE IF NOT EXISTS `wo_orders` (
  `order_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '工单ID (主键)',
  `project_id` BIGINT NOT NULL COMMENT '工单所属项目ID (逻辑外键至 prj_projects.project_id)',
  `order_type` VARCHAR(50) NOT NULL COMMENT '工单类型 (字典: EXPERT_INVESTIGATION[专家调查委派], EXPERT_REVIEW_RECTIFICATION[专家复核整改], GOV_INSPECTION_RECTIFY[监管抽检整改], AI_ANALYSIS_FOLLOWUP[智能隐患分析处置], DOCUMENT_REVIEW_RECTIFY[资料审核整改], GENERAL_HAZARD_RECTIFY[一般隐患整改])',
  `related_warning_id` BIGINT NULL DEFAULT NULL COMMENT '关联的触发预警ID (逻辑外键至 mon_warnings.warning_id)',
  `related_inspection_id` BIGINT NULL DEFAULT NULL COMMENT '关联的触发巡检ID (逻辑外键至 insp_inspections.inspection_id)',
  `related_spot_check_id` BIGINT NULL DEFAULT NULL COMMENT '关联的项目抽检ID (逻辑外键至 insp_spot_checks.spot_check_id)',
  `related_document_review_id` BIGINT NULL DEFAULT NULL COMMENT '关联的资料审核记录ID (逻辑外键至 prj_document_item_reviews.review_id)',
  `related_ai_analysis_task_id` BIGINT NULL DEFAULT NULL COMMENT '关联的智能隐患分析任务ID (逻辑外键至 ai_haz_analysis_tasks.task_id)',
  `issued_by_org_id` BIGINT NULL DEFAULT NULL COMMENT '签发单位组织机构ID (逻辑外键至 sys_organizations.org_id)',
  `issued_by_user_id` BIGINT NULL DEFAULT NULL COMMENT '签发人用户ID (逻辑外键至 sys_users.user_id)',
  `issue_date` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '签发时间戳',
  `assigned_to_org_id` BIGINT NULL DEFAULT NULL COMMENT '主要处理/整改单位ID (逻辑外键至 sys_organizations.org_id, 通常是施工单位)',
  `assigned_to_expert_user_id` BIGINT NULL DEFAULT NULL COMMENT '指派的专家用户ID (逻辑外键至 sys_users.user_id, 该用户需是专家, 用于专家调查或复核类工单)',
  `expert_acceptance_time` DATETIME NULL DEFAULT NULL COMMENT '专家接单时间 (App操作)',
  `expert_opinion` TEXT NULL DEFAULT NULL COMMENT '专家初步判断/现场核查意见 (App填写)',
  `expert_rectification_advice` TEXT NULL DEFAULT NULL COMMENT '专家提出的整改要求/建议 (App填写)',
  `expert_process_deadline` DATETIME NULL DEFAULT NULL COMMENT '专家处理截止时间 (例如现场核查时限)',
  `description` TEXT NOT NULL COMMENT '工单任务/问题详细描述',
  `rectification_deadline_by_issuer` DATETIME NULL DEFAULT NULL COMMENT '签发方/专家为施工方设定的整改截止日期',
  `status` VARCHAR(50) NULL DEFAULT 'PENDING_ACCEPTANCE' COMMENT '工单状态 (字典: PENDING_ACCEPTANCE[待接收/待专家接单], EXPERT_ACCEPTED[专家已接单], UNDER_INVESTIGATION[调查/核查中], PENDING_RECTIFICATION[待整改], RECTIFICATION_IN_PROGRESS[整改中], PENDING_REVIEW[待复核/待专家复核], REVIEW_PASSED[复核通过], REVIEW_FAILED[复核不通过], COMPLETED_CLOSED[完成关闭], CANCELLED[已取消])',
  `priority` VARCHAR(50) NULL DEFAULT 'MEDIUM' COMMENT '优先级 (字典: LOW[低], MEDIUM[中], HIGH[高], URGENT[紧急])',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`order_id`),
  INDEX `idx_wo_orders_project_id` (`project_id` ASC),
  INDEX `idx_wo_orders_warning_id` (`related_warning_id` ASC),
  INDEX `idx_wo_orders_inspection_id` (`related_inspection_id` ASC),
  INDEX `idx_wo_orders_spot_check_id` (`related_spot_check_id` ASC),
  INDEX `idx_wo_orders_doc_review_id` (`related_document_review_id` ASC),
  INDEX `idx_wo_orders_ai_task_id` (`related_ai_analysis_task_id` ASC),
  INDEX `idx_wo_orders_issued_org_id` (`issued_by_org_id` ASC),
  INDEX `idx_wo_orders_issued_user_id` (`issued_by_user_id` ASC),
  INDEX `idx_wo_orders_assigned_org_id` (`assigned_to_org_id` ASC),
  INDEX `idx_wo_orders_assigned_expert_id` (`assigned_to_expert_user_id` ASC),
  INDEX `idx_work_order_type` (`order_type` ASC),
  INDEX `idx_work_order_status` (`status` ASC)
)
ENGINE = InnoDB
COMMENT = '[工单与整改] 包含专家处理流程的工单信息，是隐患排查治理的核心';


-- -----------------------------------------------------
-- 表 `wo_rectifications` (整改记录表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `wo_rectifications` ;

CREATE TABLE IF NOT EXISTS `wo_rectifications` (
  `rectification_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '整改记录ID (主键)',
  `work_order_id` BIGINT NOT NULL COMMENT '关联的工单ID (逻辑外键至 wo_orders.order_id)',
  `submitted_by_user_id` BIGINT NULL DEFAULT NULL COMMENT '提交整改的施工单位用户ID (逻辑外键至 sys_users.user_id)',
  `submission_org_id` BIGINT NULL DEFAULT NULL COMMENT '提交整改的单位ID (逻辑外键至 sys_organizations.org_id)',
  `submission_date` DATETIME NULL DEFAULT NULL COMMENT '整改信息提交日期',
  `rectification_description` TEXT NULL DEFAULT NULL COMMENT '施工方采取的整改措施详细描述',
  `completion_date` DATETIME NULL DEFAULT NULL COMMENT '施工方声明的整改完成日期',
  `expert_review_user_id` BIGINT NULL DEFAULT NULL COMMENT '执行复核的专家用户ID (逻辑外键至 sys_users.user_id)',
  `expert_review_time` DATETIME NULL DEFAULT NULL COMMENT '专家复核时间 (App操作)',
  `expert_review_status` VARCHAR(50) NULL DEFAULT NULL COMMENT '专家复核结果 (字典: PASSED[通过], FAILED[不通过需再整改], PENDING[待复核])',
  `expert_review_comments` TEXT NULL DEFAULT NULL COMMENT '专家复核的具体意见 (App填写)',
  `is_overdue_by_construction` TINYINT(1) NULL DEFAULT 0 COMMENT '施工方整改是否超期 (字典: 0[否], 1[是])',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`rectification_id`),
  INDEX `idx_wo_rectifications_work_order_id` (`work_order_id` ASC),
  INDEX `idx_wo_rectifications_submitted_user_id` (`submitted_by_user_id` ASC),
  INDEX `idx_wo_rectifications_expert_review_user_id` (`expert_review_user_id` ASC),
  INDEX `idx_wo_rectifications_expert_review_status` (`expert_review_status` ASC)
)
ENGINE = InnoDB
COMMENT = '[工单与整改] 记录施工方对工单问题的整改过程及专家复核信息';


-- -----------------------------------------------------
-- 表 `wo_rectification_evidence` (整改证据表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `wo_rectification_evidence` ;

CREATE TABLE IF NOT EXISTS `wo_rectification_evidence` (
  `evidence_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '证据关联ID (主键)',
  `rectification_id` BIGINT NOT NULL COMMENT '关联的整改记录ID (逻辑外键至 wo_rectifications.rectification_id)',
  `document_id` BIGINT NOT NULL COMMENT '关联的证据文档ID (逻辑外键至 sys_documents.document_id, 文档类别通常为EVIDENCE或PHOTO)',
  `description` VARCHAR(255) NULL DEFAULT NULL COMMENT '证据描述',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`evidence_id`),
  INDEX `idx_wo_rect_evidence_rectification_id` (`rectification_id` ASC),
  INDEX `idx_wo_rect_evidence_document_id` (`document_id` ASC),
  UNIQUE INDEX `uk_rectification_document` (`rectification_id` ASC, `document_id` ASC, `del_flag` ASC)
)
ENGINE = InnoDB
COMMENT = '[工单与整改] 关联整改记录到支持性的证据文档（如照片、说明文件）';


-- =============================================
-- 巡检与特种设备 (insp_)
-- =============================================

-- -----------------------------------------------------
-- 表 `insp_inspections` (巡检记录表 - 计划性/正式巡检)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `insp_inspections` ;

CREATE TABLE IF NOT EXISTS `insp_inspections` (
  `inspection_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '巡检记录ID (主键)',
  `project_id` BIGINT NOT NULL COMMENT '巡检所属项目ID (逻辑外键至 prj_projects.project_id)',
  `inspection_date` DATETIME NOT NULL COMMENT '巡检日期时间',
  `inspector_user_id` BIGINT NOT NULL COMMENT '执行巡检的人员用户ID (逻辑外键至 sys_users.user_id)',
  `inspector_role` VARCHAR(100) NULL DEFAULT NULL COMMENT '巡检员在项目中的角色 (例如: "专职安全生产管理人员", "监理工程师", "项目经理")',
  `inspection_type` VARCHAR(50) NOT NULL COMMENT '巡检类型 (字典: ROUTINE_SAFETY[日常安全巡检], SPECIAL_HAZARD_INSPECTION[专项危险源检查], SUPERVISOR_PATROL[监理巡视], CONSTRUCTION_SELF_CHECK[施工单位自查], QUALITY_CHECK[质量检查])',
  `location_on_site` VARCHAR(255) NULL DEFAULT NULL COMMENT '检查的具体部位/区域',
  `findings` TEXT NULL DEFAULT NULL COMMENT '巡检发现的问题和情况描述 (对应附件五.1, .2, .3 中的监督记录、施工监测、安全巡视记录的主要内容)',
  `rectification_status_desc` TEXT NULL DEFAULT NULL COMMENT '与本次巡检直接相关的即时整改情况描述 (若有)',
  `related_work_order_id` BIGINT NULL DEFAULT NULL COMMENT '因此次巡检发现问题而生成的工单ID (逻辑外键至 wo_orders.order_id, 可选)',
  `report_document_id` BIGINT NULL DEFAULT NULL COMMENT '巡检记录表/报告的文档ID (逻辑外键至 sys_documents.document_id, 支持本地下载)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`inspection_id`),
  INDEX `idx_insp_inspections_project_id` (`project_id` ASC),
  INDEX `idx_insp_inspections_inspector_user_id` (`inspector_user_id` ASC),
  INDEX `idx_insp_inspections_work_order_id` (`related_work_order_id` ASC),
  INDEX `idx_insp_inspections_report_doc_id` (`report_document_id` ASC),
  INDEX `idx_inspection_date` (`inspection_date` ASC),
  INDEX `idx_insp_inspections_type` (`inspection_type` ASC)
)
ENGINE = InnoDB
COMMENT = '[巡检与设备] 记录各方执行的计划性或正式的现场巡检详情及初步整改情况 (对应附件五)';


-- -----------------------------------------------------
-- 表 `insp_special_equipment` (特种设备表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `insp_special_equipment` ;

CREATE TABLE IF NOT EXISTS `insp_special_equipment` (
  `equipment_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '特种设备ID (主键)',
  `project_id` BIGINT NOT NULL COMMENT '设备所在项目ID (逻辑外键至 prj_projects.project_id)',
  `equipment_type` VARCHAR(100) NOT NULL COMMENT '设备类型 (字典, 例如: TOWER_CRANE[塔式起重机], LIFTER[施工升降机], SCAFFOLDING_SYSTEM[附着式升降脚手架], CONCRETE_PUMP[混凝土泵车] 等)',
  `equipment_code` VARCHAR(100) NULL DEFAULT NULL UNIQUE COMMENT '设备唯一编号/资产标签 (若有，应唯一)',
  `model` VARCHAR(100) NULL DEFAULT NULL COMMENT '设备型号',
  `manufacturer` VARCHAR(100) NULL DEFAULT NULL COMMENT '生产厂家',
  `registration_info` TEXT NULL DEFAULT NULL COMMENT '备案登记信息、产权单位等',
  `installation_date` DATE NULL DEFAULT NULL COMMENT '进场安装日期',
  `removal_date` DATE NULL DEFAULT NULL COMMENT '拆卸离场日期',
  `status` VARCHAR(50) NULL DEFAULT 'ACTIVE' COMMENT '设备状态 (字典: ACTIVE[在用], IDLE[闲置/停用], UNDER_MAINTENANCE[维保中], PENDING_INSPECTION[待安检], DISMANTLED[已拆卸])',
  `last_inspection_date` DATE NULL DEFAULT NULL COMMENT '最近一次安全检查/检测日期',
  `next_inspection_date` DATE NULL DEFAULT NULL COMMENT '下次安全检查/检测到期日',
  `operator_personnel_id` BIGINT NULL DEFAULT NULL COMMENT '主要操作员的项目人员ID (逻辑外键至 prj_personnel.project_personnel_id, 该人员应有相应特种作业资格)',
  `certificate_doc_id` BIGINT NULL DEFAULT NULL COMMENT '设备合格证/检测报告文档ID (逻辑外键至 sys_documents.document_id)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`equipment_id`),
  UNIQUE INDEX `equipment_code_UNIQUE` (`equipment_code` ASC, `project_id` ASC, `del_flag` ASC), -- 同一项目下设备编号唯一
  INDEX `idx_insp_special_equipment_project_id` (`project_id` ASC),
  INDEX `idx_insp_special_equipment_operator_id` (`operator_personnel_id` ASC),
  INDEX `idx_insp_special_equipment_type` (`equipment_type` ASC),
  INDEX `idx_insp_special_equipment_status` (`status` ASC)
)
ENGINE = InnoDB
COMMENT = '[巡检与设备] 管理项目上使用的特种设备信息、状态及相关证件';

-- -----------------------------------------------------
-- 表 `insp_spot_checks` (项目抽检记录表 - App端快速抽检)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `insp_spot_checks`;

CREATE TABLE IF NOT EXISTS `insp_spot_checks` (
  `spot_check_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '抽检记录ID (主键)',
  `project_id` BIGINT NOT NULL COMMENT '被抽检的项目ID (逻辑外键至 prj_projects.project_id)',
  `inspector_user_id` BIGINT NOT NULL COMMENT '执行抽检的监管人员用户ID (逻辑外键至 sys_users.user_id)',
  `inspecting_org_id` BIGINT NOT NULL COMMENT '监管人员所属单位ID (逻辑外键至 sys_organizations.org_id, org_type为GOV_*)',
  `check_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '抽检时间 (App上传时间)',
  `check_location_gps` VARCHAR(100) NULL DEFAULT NULL COMMENT '抽检时GPS坐标 (格式: "经度,纬度")',
  `check_location_desc` TEXT NULL DEFAULT NULL COMMENT '抽检位置文字描述 (例如: "2号楼东侧基坑旁")',
  `non_compliance_items` JSON NULL DEFAULT NULL COMMENT '不符合项列表 (JSON数组，每项可包含：description[问题描述], photo_doc_ids[照片文档ID列表], severity[严重程度:LOW,MEDIUM,HIGH], suggested_action[建议措施])',
  `overall_assessment` TEXT NULL DEFAULT NULL COMMENT '对抽检情况的总体评估意见',
  `related_work_order_id` BIGINT NULL DEFAULT NULL COMMENT '因此次抽检发现问题而生成的工单ID (逻辑外键至 wo_orders.order_id, 可选)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`spot_check_id`),
  INDEX `idx_insp_spot_checks_project_id` (`project_id` ASC),
  INDEX `idx_insp_spot_checks_inspector_id` (`inspector_user_id` ASC),
  INDEX `idx_insp_spot_checks_inspecting_org_id` (`inspecting_org_id` ASC),
  INDEX `idx_insp_spot_checks_wo_id` (`related_work_order_id` ASC),
  INDEX `idx_insp_spot_checks_time` (`check_time` ASC)
)
ENGINE = InnoDB
COMMENT = '[巡检与设备] 存储监管人员通过App端对项目进行的即时抽检记录';


-- =============================================
-- 智能隐患分析 (ai_haz_)
-- =============================================

-- -----------------------------------------------------
-- 表 `ai_haz_analysis_tasks` (智能隐患分析任务表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `ai_haz_analysis_tasks`;

CREATE TABLE IF NOT EXISTS `ai_haz_analysis_tasks` (
  `task_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '分析任务ID (主键)',
  `project_id` BIGINT NOT NULL COMMENT '关联项目ID (逻辑外键至 prj_projects.project_id)',
  `expert_user_id` BIGINT NOT NULL COMMENT '提交分析的专家用户ID (逻辑外键至 sys_users.user_id, 该用户需是专家)',
  `upload_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '照片上传时间 (App提交时间)',
  `photo_document_id` BIGINT NOT NULL COMMENT '上传的照片文档ID (逻辑外键至 sys_documents.document_id)',
  `gps_location` VARCHAR(100) NULL DEFAULT NULL COMMENT '拍照时GPS坐标 (格式: "经度,纬度")',
  `location_description` TEXT NULL DEFAULT NULL COMMENT '拍照位置文字描述 (例如："2号楼3层东北角")',
  `ai_recognition_raw_result` TEXT NULL DEFAULT NULL COMMENT 'AI模型识别输出结果 (例如JSON字符串)',
  `ai_identified_hazards` JSON NULL DEFAULT NULL COMMENT 'AI识别出的潜在危大工程类型/场景列表 (JSON数组，每项可包含：hazard_type[危大工程类别Key], confidence[置信度], bounding_box[图片中位置坐标])',
  `expert_selected_hazard_types` JSON NULL DEFAULT NULL COMMENT '专家最终选择确认的与照片内容相关的危大工程类型列表 (JSON数组，元素为危大工程类别Key, 可能来自AI识别或专家补充)',
  `expert_manual_input_details` TEXT NULL DEFAULT NULL COMMENT '专家手动输入的具体隐患描述或补充说明 (当AI识别不足或不准确时，或对选定类型进行细化描述)',
  `related_hazardous_item_id` BIGINT NULL DEFAULT NULL COMMENT '关联的已知危大工程项ID (逻辑外键至 prj_hazardous_items.item_id, 如果此分析任务是针对某个已登记的危大工程项，或专家将其归类于此)',
  `expert_confirmation_time` DATETIME NULL DEFAULT NULL COMMENT '专家确认/手动输入完成时间 (App操作)',
  `status` VARCHAR(50) DEFAULT 'PENDING_AI_ANALYSIS' COMMENT '任务状态 (字典: PENDING_AI_ANALYSIS[待AI分析], AI_ANALYSING[AI分析中], AI_ANALYSIS_COMPLETED[AI分析完成], PENDING_EXPERT_CONFIRMATION[待专家确认], EXPERT_CONFIRMED[专家已确认], TASK_COMPLETED[任务完成/结果已处理])',
  `related_work_order_id` BIGINT NULL DEFAULT NULL COMMENT '因此次分析发现问题而生成的工单ID (逻辑外键至 wo_orders.order_id, 可选)',
  -- 标准审计字段
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`task_id`),
  INDEX `idx_ai_haz_tasks_project_id` (`project_id` ASC),
  INDEX `idx_ai_haz_tasks_expert_id` (`expert_user_id` ASC),
  INDEX `idx_ai_haz_tasks_photo_doc_id` (`photo_document_id` ASC),
  INDEX `idx_ai_haz_tasks_related_haz_item_id` (`related_hazardous_item_id` ASC), -- 新增索引
  INDEX `idx_ai_haz_tasks_wo_id` (`related_work_order_id` ASC),
  INDEX `idx_ai_haz_tasks_status` (`status` ASC)
)
ENGINE = InnoDB
COMMENT = '[智能隐患] 存储专家通过App端拍照进行智能分析危大工程的任务记录，可关联到已知危大工程项';


-- =============================================
-- 政府执法 (gov_)
-- =============================================

-- -----------------------------------------------------
-- 表 `gov_supervision_records` (监管机构排查治理记录表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `gov_supervision_records`;

CREATE TABLE IF NOT EXISTS `gov_supervision_records` (
  `record_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID (主键)',
  `project_id` BIGINT NOT NULL COMMENT '关联的项目ID (逻辑外键至 prj_projects.project_id)',
  `item_id` BIGINT NULL DEFAULT NULL COMMENT '关联的危大工程项ID (可选, 逻辑外键至 prj_hazardous_items.item_id)',
  `supervising_org_id` BIGINT NOT NULL COMMENT '执行排查的监管机构ID (逻辑外键至 sys_organizations.org_id, org_type为GOV_*)',
  `inspector_user_ids` JSON NULL DEFAULT NULL COMMENT '参与检查的监管人员用户ID列表 (JSON数组, 元素为sys_users.user_id)',
  `inspection_date` DATE NOT NULL COMMENT '检查日期',
  `problem_list_doc_id` BIGINT NULL DEFAULT NULL COMMENT '问题清单文档ID (逻辑外键至 sys_documents.document_id, 对应附件七.1.a)',
  `rectification_notice_doc_id` BIGINT NULL DEFAULT NULL COMMENT '整改通知单文档ID (逻辑外键至 sys_documents.document_id, 对应附件七.1.a)',
  `enforcement_advice_doc_id` BIGINT NULL DEFAULT NULL COMMENT '执法建议书文档ID (逻辑外键至 sys_documents.document_id, 对应附件七.1.a)',
  `is_partial_shutdown_required` TINYINT(1) NULL DEFAULT 0 COMMENT '是否需要局部停工整改 (字典: 0[否], 1[是], 对应附件七.1.b)',
  `is_full_shutdown_required` TINYINT(1) NULL DEFAULT 0 COMMENT '是否需要全面停工整改 (字典: 0[否], 1[是], 对应附件七.1.b)',
  `rectification_details_by_gov` TEXT NULL DEFAULT NULL COMMENT '监管机构记录的整改情况描述 (对应附件七.1末尾的“及整改情况”)',
  `inspection_record_form_doc_id` BIGINT NULL DEFAULT NULL COMMENT '检查记录表文档ID (逻辑外键至 sys_documents.document_id, 对应附件七.1.a “相应检查记录表”)',
  `remarks` TEXT NULL DEFAULT NULL COMMENT '其他备注信息',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门ID (逻辑外键)',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者用户ID (逻辑外键至 sys_users.user_id)',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者用户ID (逻辑外键至 sys_users.user_id)',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (字典: 0[存在], 1[删除])',
  PRIMARY KEY (`record_id`),
  INDEX `idx_gov_supervision_project_id` (`project_id` ASC),
  INDEX `idx_gov_supervision_item_id` (`item_id` ASC),
  INDEX `idx_gov_supervision_org_id` (`supervising_org_id` ASC),
  INDEX `idx_gov_supervision_problem_list_doc_id` (`problem_list_doc_id` ASC),
  INDEX `idx_gov_supervision_rect_notice_doc_id` (`rectification_notice_doc_id` ASC),
  INDEX `idx_gov_supervision_enforce_advice_doc_id` (`enforcement_advice_doc_id` ASC),
  INDEX `idx_gov_supervision_inspect_form_doc_id` (`inspection_record_form_doc_id` ASC),
  INDEX `idx_gov_supervision_date` (`inspection_date` ASC)
)
ENGINE = InnoDB
COMMENT = '[政府执法] 存储监管机构对危大工程的隐患排查治理情况记录 (对应附件七)';

-- 恢复之前的设置
SET SQL_MODE=@OLD_SQL_MODE;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;

-- -----------------------------------------------------
-- 脚本结束
-- -----------------------------------------------------
```

这份数据字典应该比较完整地覆盖了我们之前讨论的所有内容，并且为关键的枚举字段提供了中文含义的注解。希望这份详细的文档对您有所帮助！
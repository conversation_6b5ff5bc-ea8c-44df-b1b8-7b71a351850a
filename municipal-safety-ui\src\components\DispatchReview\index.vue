<template>
  <div class="dispatchReview">
    <!-- 派遣审查弹框 -->
    <el-dialog :title="dispatchDialog.title" v-model="dispatchDialog.visible" append-to-body width="100%"
      style="height: 100%;" class="dispatchDialog" @close="handleDispatchClose">
      <el-row :gutter="10" style="width: 100%;height: 100%;">
        <el-col :span="5" style="display: flex;flex-direction: column;align-items: flex-start;">
          <div style="margin-bottom: 10px;">
            <p style="text-align: start;color: #409EFF;margin: 0 0 16px;">分析前</p>
            <HeaderPrewiew :src="aiDetailData?.photoDocumentUrl" width="18.3vw" height="18.3vw"
              :preview-src-list="[aiDetailData?.photoDocumentUrl]">
            </HeaderPrewiew>
          </div>
          <div>
            <p style="text-align: start;color: #67C23A;margin: 10px 0 16px;">分析后</p>
            <HeaderPrewiew :src="aiDetailData?.aiPhotoDocumentUrl" width="18.3vw" height="18.3vw"
              :preview-src-list="[aiDetailData?.aiPhotoDocumentUrl]">
            </HeaderPrewiew>
          </div>
        </el-col>
        <el-col ref="ref1" :span="10" style="display: flex;flex-direction: column;height: 100%;">
          <div style="width: 100%; height: calc(90vh - 50px);overflow-y: auto;">
            <el-card v-for="(item, index) in aiDetailData?.violations" :key="index" class="hiddenDangerCard">
              <template #header>
                <div class="card-header">
                  <div style="display: flex;align-items: center;">
                    <span style="display: block;padding-bottom: 3px;margin-left: 8px;">问题 {{ index + 1 }}</span>
                  </div>
                  <div style="display: flex;align-items: center;">
                    <span style="color: #409EFF;">危险级别：</span>
                    <dict-tag :options="hidden_danger_type" :value="item.level" />
                  </div>
                </div>
              </template>
              <div style="display: flex;">
                <span style="color: #666;display: block;width: 70px;">隐患描述：</span>
                <span style="display: block;flex: 1;">{{ item.violation }}</span>
              </div>
              <div style="display: flex;margin: 15px 0;">
                <span style="color: #666;display: block;width: 70px;">违反条款：</span>
                <span style="display: block;flex: 1;">{{ item.regulation }}</span>
              </div>
              <div style="display: flex;">
                <span style="color: #666;display: block;width: 70px;">整改意见：</span>
                <span style="display: block;flex: 1;">{{ item.measure
                }}</span>
              </div>
            </el-card>
          </div>
        </el-col>
        <el-col :span="9" style="padding-left: 20px;height: 100%;">
          <div style="width: 100%;height: calc(90vh - 50px);overflow-y: auto;padding-right: 20px;">
            <slot name="dispatchRight" :data="1"></slot>
          </div>
        </el-col>
      </el-row>
      <!-- <el-tour v-model="tourOpen" @change="tourChange" @close="handleTourClose" :z-index="3001" close-icon="CircleClose"
      class="tourClass">
      <el-tour-step :target="ref1?.$el" title="选择问题点" description="双击选择左侧问题点，可多选" placement="right" />
      <el-tour-step :target="ref2?.$el" title="选择整改措施" description="选择整改措施，可多选" />
      <el-tour-step :target="ref3?.$el" title="编辑内容" description="编辑问题点内容" />
      <el-tour-step :target="ref4?.$el" title="下载模板" description="下载需要上传的文件模板" />
      <el-tour-step :target="ref5?.$el" title="上传文件" description="上传盖章后的文件" />
      <el-tour-step :target="ref6?.$el" title="提交" description="一键提交所有内容" />
    </el-tour> -->
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleDispatchSubmit">提交工单</el-button>
          <el-button @click="dispatchDialog.visible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import HeaderPrewiew from '@/components/ImagePreview/index.vue';
import { getPrj_hazardous_items_ai_detail } from '@/api/projects/prj_hazardous_items/index';
import { listByIds } from '@/api/system/oss/index'
import { addHazardousItemsSpecialWarningFirst } from '@/api/second/index'

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { hidden_danger_type } = toRefs<any>(proxy?.useDict('hidden_danger_type'));

// 接收父组件传过来的参数
const props = defineProps({
  dispatchDialogTitle: { // 派遣审查弹框的标题
    type: String,
    default: '派遣审查',
  },
  isDispatchModel: { // 控制派遣审查显隐弹框的判断
    type: Boolean,
    default: false,
  },
  taskId: { // 任务id
    type: String,
    default: '',
  },
  dispatchRightData: { // 派遣审查右侧数据
    type: Object,
    default: () => { },
  }
})
// 触发父组件的自定义事件
const emit = defineEmits(['dispatchClose'])
// 派遣审查弹框
const dispatchDialog = reactive({
  visible: false,
  title: '',
})
// 存放隐患清单详情的数据
const aiDetailData = ref();
// 提交工单的表单参数
const formCorrectData = reactive({
  specialist: '', // 专家id(多个根据顺序用,隔开拼接)
  instruction: '', // 补充说明
  aiHazAnalysisId: '', // 隐患id
})
watch(() => props.isDispatchModel, (newVal) => {
  dispatchDialog.visible = newVal;
  dispatchDialog.title = props.dispatchDialogTitle;
  if (props.taskId) {
    formCorrectData.aiHazAnalysisId = props.taskId;
    getAiDetail(props.taskId)
  }
})
watch(() => props.dispatchRightData, (newVal) => {
  if (newVal) {
    formCorrectData.specialist = newVal.specialist;
    formCorrectData.instruction = newVal.instruction;
  }
}, { deep: true })
// 使用ossId查询图片的url地址
const getImageUrl = async (ossId: string | number) => {
  const { data } = await listByIds(ossId);
  return data[0]?.url;
}
// 获取隐患详情数据
const getAiDetail = async (taskId: string) => {
  const res = await getPrj_hazardous_items_ai_detail(taskId);
  if (res.code === 200) {
    aiDetailData.value = res.data;
    aiDetailData.value.photoDocumentUrl = await getImageUrl(aiDetailData.value.photoDocumentId);
    aiDetailData.value.aiPhotoDocumentUrl = await getImageUrl(aiDetailData.value.aiPhotoDocumentId);
  }
}
// 派遣审查发起工单
const handleDispatchSubmit = () => {
  if (formCorrectData.specialist === '') {
    proxy?.$modal.msgError('至少选择一个专家！');
    return;
  }
  proxy?.$modal.confirm('是否确认发起派遣审查工单？').then(async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '提交中，请稍等...',
      background: 'rgba(255, 255, 255, 0.8)',
    })
    const res = await addHazardousItemsSpecialWarningFirst(formCorrectData);
    if (res.code === 200) {
      proxy?.$modal.msgSuccess(res.msg);
    }
    loading.close();
    dispatchDialog.visible = false;
  }).catch(() => { });
}
// 派遣审查弹框关闭事件
const handleDispatchClose = () => {
  formCorrectData.specialist = ''; // 重置专家身份证号;
  formCorrectData.instruction = ''; // 重置补充说明;
  emit('dispatchClose', false)
}
</script>

<style lang="scss">
.dispatchReview {}

.dispatchDialog {
  .el-dialog__body {
    overflow-y: hidden;
    max-height: calc(90vh - 50px) !important;
  }
}
</style>
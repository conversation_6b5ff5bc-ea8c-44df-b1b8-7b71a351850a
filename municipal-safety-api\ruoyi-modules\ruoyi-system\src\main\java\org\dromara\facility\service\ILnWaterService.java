package org.dromara.facility.service;

import org.dromara.facility.domain.vo.LnWaterVo;
import org.dromara.facility.domain.bo.LnWaterBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 绿能水表Service接口
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface ILnWaterService extends BaseFacilityHandle{

    /**
     * 查询绿能水表
     *
     * @param id 主键
     * @return 绿能水表
     */
    LnWaterVo queryById(Long id);

    /**
     * 分页查询绿能水表列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能水表分页列表
     */
    TableDataInfo<LnWaterVo> queryPageList(LnWaterBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的绿能水表列表
     *
     * @param bo 查询条件
     * @return 绿能水表列表
     */
    List<LnWaterVo> queryList(LnWaterBo bo);

    /**
     * 新增绿能水表
     *
     * @param bo 绿能水表
     * @return 是否新增成功
     */
    Boolean insertByBo(LnWaterBo bo);

    /**
     * 修改绿能水表
     *
     * @param bo 绿能水表
     * @return 是否修改成功
     */
    Boolean updateByBo(LnWaterBo bo);

    /**
     * 校验并批量删除绿能水表信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

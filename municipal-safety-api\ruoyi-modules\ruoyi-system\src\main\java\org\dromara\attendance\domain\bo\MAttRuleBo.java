package org.dromara.attendance.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.attendance.domain.MAttRule;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 考勤规则业务对象 m_att_rule
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MAttRule.class, reverseConvertGenerate = false)
public class MAttRuleBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 规则类型（0：模板规则，1：普通规则）
     */
    @NotNull(message = "规则类型（0：模板规则，1：普通规则）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long ruleType;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 人员类型
     */
    private String personType;

    /**
     * 是否全部人员通用
     */
    private Integer isAll;

    /**
     * 设置打卡时间（支持多时段，如午休分段）。
     */
    private String checkTime;

    /**
     * 弹性时间：允许迟到/早退的宽限时间（如上班后30分钟内打卡不算迟到）
     */
    private Long elasticTime;

    /**
     * 预警机制：根据对应的漏卡次数设置（黄色、橙色、红色）
     */
    private String warning;

    /**
     * 外勤打卡：0：关，1：开
     */
    private Long fieldCheck;

    /**
     * 自定义内容
     */
    private String content;

}

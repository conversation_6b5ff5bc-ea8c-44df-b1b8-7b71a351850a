import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MonitoVO, MonitoForm, MonitoQuery } from '@/api/monito/monito/types';

/**
 * 查询监控管理列表
 * @param query
 * @returns {*}
 */

export const listMonito = (query?: MonitoQuery): AxiosPromise<MonitoVO[]> => {
  return request({
    url: '/monito/monito/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询监控管理详细
 * @param monitoId
 */
export const getMonito = (monitoId: string | number): AxiosPromise<MonitoVO> => {
  return request({
    url: '/monito/monito/' + monitoId,
    method: 'get'
  });
};

/**
 * 新增监控管理
 * @param data
 */
export const addMonito = (data: MonitoForm) => {
  return request({
    url: '/monito/monito',
    method: 'post',
    data: data
  });
};

/**
 * 修改监控管理
 * @param data
 */
export const updateMonito = (data: MonitoForm) => {
  return request({
    url: '/monito/monito',
    method: 'put',
    data: data
  });
};

/**
 * 删除监控管理
 * @param monitoId
 */
export const delMonito = (monitoId: string | number | Array<string | number>) => {
  return request({
    url: '/monito/monito/' + monitoId,
    method: 'delete'
  });
};


// 查询监控视频时需要的accessToken
export const getAccessToken = (): AxiosPromise<string> => {
  return request({
    url: '/monito/monito/getToken',
    method: 'get'
  });
}
// 获取监控视频的url
export const getPlayMonitorUrl = (deviceCode: string | number, channelNo: string | number): AxiosPromise<string> => {
  return request({
    url: `/monito/monito/playUrl/${deviceCode}/${channelNo}`,
    method: 'get'
  });
}
// 传递截图给后端进行AI分析
export const capturePictureAnalysis = (data: any): AxiosPromise<any> => {
  return request({
    url: '/monito/monito/capture',
    method: 'post',
    data
  });
};

// 获取左侧树结构数据
export const leftTreeData = (): AxiosPromise<any> => {
  return request({
    url: '/monito/monito/showList',
    method: 'get'
  });
}
// 无人机截图
export const capture = (data): AxiosPromise<any> => {
  return request({
    url: '/monito/monito/capture',
    method: 'post',
    data
  });
}




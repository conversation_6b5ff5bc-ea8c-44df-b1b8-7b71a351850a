package org.dromara.monito.service;

import jakarta.validation.constraints.NotNull;
import org.dromara.dp.domain.bo.DataViewBo;
import org.dromara.monito.domain.bo.CaptureBo;
import org.dromara.monito.domain.vo.DeviceMonitoVo;
import org.dromara.monito.domain.bo.DeviceMonitoBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.system.domain.vo.DivisionVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 监控管理Service接口
 *
 * <AUTHOR> Li
 * @date 2025-05-18
 */
public interface IDeviceMonitoService {

    /**
     * 查询监控管理
     *
     * @param monitoId 主键
     * @return 监控管理
     */
    DeviceMonitoVo queryById(Long monitoId);

    /**
     * 分页查询监控管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 监控管理分页列表
     */
    TableDataInfo<DeviceMonitoVo> queryPageList(DeviceMonitoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的监控管理列表
     *
     * @param bo 查询条件
     * @return 监控管理列表
     */
    List<DeviceMonitoVo> queryList(DeviceMonitoBo bo);

    /**
     * 新增监控管理
     *
     * @param bo 监控管理
     * @return 是否新增成功
     */
    Boolean insertByBo(DeviceMonitoBo bo);

    /**
     * 修改监控管理
     *
     * @param bo 监控管理
     * @return 是否修改成功
     */
    Boolean updateByBo(DeviceMonitoBo bo);

    /**
     * 校验并批量删除监控管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<DeviceMonitoVo> pageList(DeviceMonitoBo bo);


    DeviceMonitoVo selectById(@NotNull(message = "主键不能为空") Long monitoId);

    Boolean capture(CaptureBo captureBo);

    public List<Map<String, Object>> getDeviceMonitoTreeInfo(DeviceMonitoBo bo);

//    Boolean capturePlane(CaptureBo captureBo);


    //大屏相关

    /**
     * 根据危大类型查询工程数量
     */
    public List<Map<String, Object>> getItemNum(DataViewBo bo);


    /**
     * 根据危大类型查询父级工程清单，清单下面的工程数量，隐患(已处理和未处理的数量)
     */
    public List<Map<String, Object>> getDangerNum(DataViewBo bo);

    /**
     * 根据危大类型和清单父级名称查询工程列表
     */
    public List<Map<String, Object>> getProItemList(DataViewBo bo);

    /**
     * 根据年份查询危大工程数量（危大、超危大）
     */
    public  Map<String, Object> getYearItemNum(DataViewBo bo);


    /**
     * 根据区域查询危大工程数量（危大、超危大）
     */
    public Map<String, Object> getAreaItemNum(DataViewBo bo);
}


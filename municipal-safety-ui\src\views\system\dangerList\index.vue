<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="危大类型" prop="type">
              <el-select v-model="queryParams.type" placeholder="请选择危大类型" clearable>
                <el-option v-for="dict in danger_list_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd()" v-hasPermi="['system:dangerList:add']">新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="handleToggleExpandAll">展开/折叠</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>
      <el-table
        ref="dangerListTableRef"
        v-loading="loading"
        :data="dangerListList"
        row-key="dangerId"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column label="名称" prop="name" align="left" />
        <el-table-column label="危大类型" align="center" prop="type" width="100">
          <template #default="scope">
            <dict-tag :options="danger_list_type" :value="scope.row.type" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:dangerList:edit']" />
            </el-tooltip>
            <el-tooltip content="新增" placement="top">
              <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['system:dangerList:add']" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:dangerList:remove']" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 添加或修改dangerList对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="dangerListFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="父级id" prop="preId">
          <el-tree-select
            v-model="form.preId"
            :data="dangerListOptions"
            :props="{ value: 'dangerId', label: 'name', children: 'children' }"
            value-key="dangerId"
            placeholder="请选择父级id"
            check-strictly
          />
        </el-form-item>
        <el-form-item label="危大类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择危大类型">
            <el-option v-for="dict in danger_list_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DangerList" lang="ts">
import { addDangerList, getDangerList, listDangerList, updateDangerList, delDangerList } from '@/api/system/dangerList';
import { DangerListVO, DangerListQuery, DangerListForm } from '@/api/system/dangerList/types';

type DangerListOption = {
  dangerId: number;
  name: string;
  children?: DangerListOption[];
};

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { danger_list_type } = toRefs<any>(proxy?.useDict('danger_list_type'));

const dangerListList = ref<DangerListVO[]>([]);
const dangerListOptions = ref<DangerListOption[]>([]);
const buttonLoading = ref(false);
const showSearch = ref(true);
const isExpandAll = ref(true);
const loading = ref(false);

const queryFormRef = ref<ElFormInstance>();
const dangerListFormRef = ref<ElFormInstance>();
const dangerListTableRef = ref<ElTableInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: DangerListForm = {
  name: undefined,
  preId: undefined,
  type: undefined,
  remark: undefined
};

const data = reactive<PageData<DangerListForm, DangerListQuery>>({
  form: { ...initFormData },
  queryParams: {
    name: undefined,
    preId: undefined,
    type: undefined,
    params: {}
  },
  rules: {
    name: [
      {
        required: true,
        message: '名称不能为空',
        trigger: 'blur'
      }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询dangerList列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDangerList(queryParams.value);
  const data = proxy?.handleTree<DangerListVO>(res.data, 'dangerId', 'preId');
  if (data) {
    dangerListList.value = data;
    loading.value = false;
  }
};

/** 查询dangerList下拉树结构 */
const getTreeselect = async () => {
  const res = await listDangerList();
  dangerListOptions.value = [];
  const data: DangerListOption = { dangerId: 0, name: '顶级节点', children: [] };
  data.children = proxy?.handleTree<DangerListOption>(res.data, 'dangerId', 'preId');
  dangerListOptions.value.push(data);
};

// 取消按钮
const cancel = () => {
  reset();
  dialog.visible = false;
};

// 表单重置
const reset = () => {
  form.value = { ...initFormData };
  dangerListFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 新增按钮操作 */
const handleAdd = (row?: DangerListVO) => {
  reset();
  getTreeselect();
  if (row != null && row.dangerId) {
    form.value.preId = row.dangerId;
  } else {
    form.value.preId = 0;
  }
  dialog.visible = true;
  dialog.title = '添加dangerList';
};

/** 展开/折叠操作 */
const handleToggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value;
  toggleExpandAll(dangerListList.value, isExpandAll.value);
};

/** 展开/折叠操作 */
const toggleExpandAll = (data: DangerListVO[], status: boolean) => {
  data.forEach((item) => {
    dangerListTableRef.value?.toggleRowExpansion(item, status);
    if (item.children && item.children.length > 0) toggleExpandAll(item.children, status);
  });
};

/** 修改按钮操作 */
const handleUpdate = async (row: DangerListVO) => {
  reset();
  await getTreeselect();
  if (row != null) {
    form.value.preId = row.preId;
  }
  const res = await getDangerList(row.dangerId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改dangerList';
};

/** 提交按钮 */
const submitForm = () => {
  dangerListFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.dangerId) {
        await updateDangerList(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addDangerList(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row: DangerListVO) => {
  await proxy?.$modal
    .confirm('是否确认删除"' + row.name + '"的数据项？')
    .then(async () => {
      const { code } = await delDangerList(row.dangerId);
      if (code == 200) {
        await proxy?.$modal.msgSuccess('删除成功!');
        getList();
      }
    })
    .catch(() => {
      console.log('取消');
    });
};

onMounted(() => {
  getList();
});
</script>

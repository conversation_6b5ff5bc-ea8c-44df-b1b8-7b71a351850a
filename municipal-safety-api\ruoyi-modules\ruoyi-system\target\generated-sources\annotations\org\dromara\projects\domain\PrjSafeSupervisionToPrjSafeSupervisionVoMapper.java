package org.dromara.projects.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.bo.PrjSafeSupervisionBoToPrjSafeSupervisionMapper;
import org.dromara.projects.domain.vo.PrjSafeSupervisionVo;
import org.dromara.projects.domain.vo.PrjSafeSupervisionVoToPrjSafeSupervisionMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjSafeSupervisionBoToPrjSafeSupervisionMapper.class,PrjSafeSupervisionVoToPrjSafeSupervisionMapper.class},
    imports = {}
)
public interface PrjSafeSupervisionToPrjSafeSupervisionVoMapper extends BaseMapper<PrjSafeSupervision, PrjSafeSupervisionVo> {
}

{"doc": " 考勤记录\n\n <AUTHOR>\n @date 2025-05-06\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRecordBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询考勤记录列表\n"}, {"name": "export", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRecordBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出考勤记录列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取考勤记录详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRecordBo"], "doc": " 新增考勤记录\n"}, {"name": "edit", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRecordBo"], "doc": " 修改考勤记录\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除考勤记录\n\n @param ids 主键串\n"}, {"name": "get<PERSON>erson<PERSON>tt", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 获取人员考勤列表\n @param personId\n @return\n"}, {"name": "getMonthAtt", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 获取人员考勤列表\n @param personId\n @return\n"}, {"name": "isDateInMonth", "paramTypes": ["java.lang.String", "java.time.Year<PERSON><PERSON><PERSON>"], "doc": " 检查考勤记录是否属于指定月份\n"}, {"name": "normalizeDate", "paramTypes": ["java.lang.Object"], "doc": " 标准化日期格式为\"MM-dd\"用于匹配\n"}, {"name": "getPersonAttByMonth", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 获取人员考勤列表\n @param personId\n @return\n"}, {"name": "getFaceImage", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": " 从设备上传考勤记录\n\n @param capJson\n @return\n"}], "constructors": []}
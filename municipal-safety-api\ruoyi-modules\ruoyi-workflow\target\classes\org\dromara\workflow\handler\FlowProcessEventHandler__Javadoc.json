{"doc": " 流程监听服务\n\n <AUTHOR>\n @date 2024-06-02\n", "fields": [], "enumConstants": [], "methods": [{"name": "processHandler", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.util.Map", "boolean"], "doc": " 总体流程监听(例如: 草稿，撤销，退回，作废，终止，已完成，单任务完成等)\n\n @param flowCode   流程定义编码\n @param businessId 业务id\n @param status     状态\n @param submit     当为true时为申请人节点办理\n"}, {"name": "processCreateTaskHandler", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long", "java.lang.String"], "doc": " 执行创建任务监听\n\n @param flowCode   流程定义编码\n @param nodeCode   审批节点编码\n @param taskId     任务id\n @param businessId 业务id\n"}, {"name": "processDeleteHandler", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 删除流程监听\n\n @param flowCode   流程定义编码\n @param businessId 业务ID\n"}], "constructors": []}
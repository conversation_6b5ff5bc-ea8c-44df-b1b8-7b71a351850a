package org.dromara.monito.handle;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpRequest;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.system.domain.CJDW;
import org.dromara.system.domain.QZJX;
import org.dromara.system.domain.XGRY;
import org.dromara.system.domain.XMXX;
import org.dromara.system.mapper.CJDWMapper;
import org.dromara.system.mapper.QZJXMapper;
import org.dromara.system.mapper.XGRYMapper;
import org.dromara.system.mapper.XMXXMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@JobExecutor(name = "projectSyncTask")
public class ProjectSyncTask {
    @Resource
    private XMXXMapper xmxxMapper;

    @Resource
    private CJDWMapper cjdwMapper;

    @Resource
    private XGRYMapper xgryMapper;

    @Resource
    private QZJXMapper qzjxMapper;

    public ExecuteResult jobExecute(JobArgs jobArgs) {
        //同步前先清理所有数据
        xmxxMapper.deleteAllPhysically();
        cjdwMapper.deleteAllPhysically();
        xgryMapper.deleteAllPhysically();
        qzjxMapper.deleteAllPhysically();

        String body = HttpRequest.post("https://zhaj.zjt.gansu.gov.cn:10124/dataexchangeserver/GGBApi/getGCXMAllInfo?pageSize=0&startIndex=0")
            .header("ryxxSecretKey", "1211f5ea378f4f858ba796336e01ab78")
            .timeout(20000)
            .execute().body();
        if (StringUtils.isNotEmpty(body)) {
            JSONObject jsonObject = JSON.parseObject(body);
            if ("true".equals(jsonObject.getString("Success"))) {
                JSONObject jsonObjectData = JSON.parseObject(jsonObject.getString("Data"));
                JSONArray xmArray = jsonObjectData.getJSONArray("XMXX");
                log.info("共{}个项目待同步！", xmArray.size());
                List<XMXX> xmxxList = null;
                List<CJDW> cjdwList = null;
                List<XGRY> xgryList = null;
                List<QZJX> qzjxList = null;
                int successCount = 0;
                int failCount = 0;

                for (int i = 0; i < xmArray.size(); i++) {
                    //通过施工许可证编号同步项目信息及关联的参建单位、相关人员、起重机械信息
                    String builderLicenceNum = "";
                    if (xmArray.getJSONObject(i).containsKey("BUILDERLICENCENUM")) {
                        builderLicenceNum = xmArray.getJSONObject(i).getString("BUILDERLICENCENUM");
                        successCount += 1;
                    } else {
                        failCount += 1;
                        log.info("第{}条数据，BUILDERLICENCENUM(施工许可证编号) 不存在！", i + 1);
                    }
                    if (StringUtils.isNotEmpty(builderLicenceNum)) {
                        String data = HttpRequest.post("https://zhaj.zjt.gansu.gov.cn:10124/dataexchangeserver/GGBApi/getGCXMInfo?SGXKZH=" + builderLicenceNum)
                            .header("ryxxSecretKey", "1211f5ea378f4f858ba796336e01ab78")
                            .timeout(20000)
                            .execute().body();
                        JSONObject xmJsonObject = JSON.parseObject(data);
                        if ("true".equals(xmJsonObject.getString("Success"))) {
                            JSONObject xmData = JSON.parseObject(xmJsonObject.getString("Data"));
                            JSONArray xmxxArray = xmData.getJSONArray("XMXX");
                            JSONArray cjdwArray = xmData.getJSONArray("CJDW");
                            JSONArray xgryArray = xmData.getJSONArray("XGRY");
                            JSONArray qzjxArray = xmData.getJSONArray("QZJX");
                            xmxxList = new ArrayList<>();
                            cjdwList = new ArrayList<>();
                            xgryList = new ArrayList<>();
                            qzjxList = new ArrayList<>();

                            //处理项目信息
                            for (int x = 0; x < xmxxArray.size(); x++) {
                                XMXX xmxx = new XMXX();
                                xmxx.setXmszs(xmxxArray.getJSONObject(x).getString("XMSZS"));                           // 项目所在市
                                xmxx.setXmszq(xmxxArray.getJSONObject(x).getString("XMSZQ"));                           // 项目所在区
                                xmxx.setProjectCode(xmxxArray.getJSONObject(x).getString("PROJECTCODE"));               // 项目代码
                                xmxx.setBuilderLicenceNum(xmxxArray.getJSONObject(x).getString("BUILDERLICENCENUM"));   // 施工许可证编号
                                xmxx.setProjectName(xmxxArray.getJSONObject(x).getString("PROJECTNAME"));               // 工程名称
                                xmxx.setAddress(xmxxArray.getJSONObject(x).getString("ADDRESS"));                       // 工程地址
                                xmxx.setCost(NumberUtil.toBigDecimal(xmxxArray.getJSONObject(x).getString("COST")));    // 工程总造价（万元）
                                xmxx.setStructureTypeNum(xmxxArray.getJSONObject(x).getString("STRUCTURETYPENUM"));     // 结构体系
                                xmxx.setPrjFunctionNum(xmxxArray.getJSONObject(x).getString("PRJFUNCTIONNUM"));         // 工程用途
                                xmxx.setInvestType(xmxxArray.getJSONObject(x).getString("INVESTTYPE"));                 // 项目投资类型
                                xmxx.setBaseType(xmxxArray.getJSONObject(x).getString("BASETYPE"));                     // 基础类型
                                xmxx.setArea(NumberUtil.toBigDecimal(xmxxArray.getJSONObject(x).getString("AREA")));    // 总面积（㎡）
                                xmxx.setBeginDate(DateUtil.parse(xmxxArray.getJSONObject(x).getString("BEGINDATE")));   // 计划开工日期
                                xmxx.setEndDate(DateUtil.parse(xmxxArray.getJSONObject(x).getString("ENDDATE")));       // 计划竣工日期
                                xmxx.setSuperviseProgress(xmxxArray.getJSONObject(x).getString("SUPERVISEPROGRESS"));   // 报监形象进度
                                xmxx.setInformDate(DateUtil.parse(xmxxArray.getJSONObject(x).getString("INFORMDATE"))); // 告知时间
                                xmxx.setDeclareDescribe(xmxxArray.getJSONObject(x).getString("DECLAREDESCRIBE"));       // 申报需要说明的情况
                                xmxx.setSuperviseOrgan(xmxxArray.getJSONObject(x).getString("SUPERVISEORGAN"));         // 质量监督机构
                                xmxx.setSuperviseOrganSocialCreditCode(xmxxArray.getJSONObject(x).getString("SUPERVISEORGANSOCIALCREDITCODE"));         // 质量监督机构统一社会信用代码
                                xmxx.setAreaCode(xmxxArray.getJSONObject(x).getString("AREACODE"));                     // 报监工程所在辖区
                                xmxx.setSuperviseUser(xmxxArray.getJSONObject(x).getString("SUPERVISEUSER"));           // 主监人
                                xmxx.setLocationX(xmxxArray.getJSONObject(x).getString("LOCATIONX"));                   // 项目坐标(经度)
                                xmxx.setLocationY(xmxxArray.getJSONObject(x).getString("LOCATIONY"));                   // 项目坐标(纬度)
                                xmxxList.add(xmxx);
                            }
                            //处理参建单位
                            for (int c = 0; c < cjdwArray.size(); c++) {
                                CJDW cjdw = new CJDW();
                                cjdw.setBuilderLicenceNum(cjdwArray.getJSONObject(c).getString("BUILDERLICENCENUM"));           // 施工许可证编号
                                cjdw.setCorpType(cjdwArray.getJSONObject(c).getString("CORPTYPE"));                             // 企业类型
                                cjdw.setCorpName(cjdwArray.getJSONObject(c).getString("CORPNAME"));                             // 企业名称
                                cjdw.setSocialCreditCode(cjdwArray.getJSONObject(c).getString("SOCIALCREDITCODE"));             // 社会信用代码
                                cjdw.setLegalMan(cjdwArray.getJSONObject(c).getString("LEGALMAN"));                             // 法人名称
                                cjdw.setCorpQualification(cjdwArray.getJSONObject(c).getString("CORPQUALIFICATION"));           // 企业资质类型
                                cjdw.setCorpQualificationCode(cjdwArray.getJSONObject(c).getString("CORPQUALIFICATIONCODE"));   // 企业资质类型证号
                                cjdwList.add(cjdw);
                            }
                            //处理相关人员
                            for (int r = 0; r < xgryArray.size(); r++) {
                                XGRY xgry = new XGRY();
                                xgry.setRecordNum(xgryArray.getJSONObject(r).getString("RECORDNUM"));                         // 备案编号
                                xgry.setConstructionPermitNum(xgryArray.getJSONObject(r).getString("CONSTRUCTIONPERMITNUM")); // 施工许可证编号
                                xgry.setName(xgryArray.getJSONObject(r).getString("NAME"));                                   // 姓名
                                xgry.setIdentityCard(xgryArray.getJSONObject(r).getString("IDENTITYCARD"));                   // 身份证号码
                                xgry.setCorpName(xgryArray.getJSONObject(r).getString("CORPNAME"));                           // 单位名称
                                xgry.setCorpCode(xgryArray.getJSONObject(r).getString("CORPCODE"));                           // 单位统一社会信用代码
                                xgry.setLxdh(xgryArray.getJSONObject(r).getString("LXDH"));                                   // 联系电话
                                xgry.setLbfl(xgryArray.getJSONObject(r).getString("LBFL"));                                   // 类别分类
                                xgry.setPersonType(xgryArray.getJSONObject(r).getString("PERSONTYPE"));                       // 人员类别
                                xgry.setZsbh(xgryArray.getJSONObject(r).getString("ZSBH"));                                   //证书编号
                                xgryList.add(xgry);
                            }
                            //处理起重机械
                            for (int q = 0; q < qzjxArray.size(); q++) {
                                QZJX qzjx = new QZJX();
                                qzjx.setConstructionPermitNum(qzjxArray.getJSONObject(q).getString("CONSTRUCTIONPERMITNUM"));   // 施工许可证编号
                                qzjx.setSbbabh(qzjxArray.getJSONObject(q).getString("SBBABH"));                                 // 设备备案编号
                                qzjx.setSblx(qzjxArray.getJSONObject(q).getString("SBLX"));                                     // 设备类型
                                qzjx.setSbmc(qzjxArray.getJSONObject(q).getString("SBMC"));                                     // 设备名称
                                qzjx.setGgxh(qzjxArray.getJSONObject(q).getString("GGXH"));                                     // 规格型号
                                qzjx.setSccs(qzjxArray.getJSONObject(q).getString("SCCS"));                                     // 生产厂商
                                qzjx.setSccstyshxydm(qzjxArray.getJSONObject(q).getString("SCCSTYSHXYDM"));                     // 生产厂商统一社会信用代码
                                qzjx.setCcbh(qzjxArray.getJSONObject(q).getString("CCBH"));                                     // 出厂编号
                                qzjx.setCcrq(DateUtil.parse(qzjxArray.getJSONObject(q).getString("CCRQ")));                     // 出厂日期
                                qzjx.setZzxkzh(qzjxArray.getJSONObject(q).getString("ZZXKZH"));                                 // 特种设备生产许可证编号
                                qzjx.setSynx(String.valueOf(qzjxArray.getJSONObject(q).getString("SYNX")));                     // 使用年限
                                qzjx.setCqdw(qzjxArray.getJSONObject(q).getString("CQDW"));                                     // 产权单位
                                qzjx.setCqdwtyshxydm(qzjxArray.getJSONObject(q).getString("CQDWTYSHXYDM"));                     // 产权单位统一社会信用代码
                                qzjx.setCqdwdz(qzjxArray.getJSONObject(q).getString("CQDWDZ"));                                 // 产权单位地址
                                qzjx.setQyfrdb(qzjxArray.getJSONObject(q).getString("QYFRDB"));                                 // 企业法人代表
                                qzjx.setFrsfzh(qzjxArray.getJSONObject(q).getString("FRSFZH"));                                 // 法定代表人身份证件号
                                qzjx.setLxr(qzjxArray.getJSONObject(q).getString("LXR"));                                       // 联系人
                                qzjx.setLxdh(qzjxArray.getJSONObject(q).getString("LXDH"));                                     // 联系电话
                                qzjx.setGzjg(NumberUtil.toBigDecimal(qzjxArray.getJSONObject(q).getString("GZJG")));            // 出厂价格（万元）
                                qzjx.setGzny(DateUtil.parse(qzjxArray.getJSONObject(q).getString("GZNY")));                     // 购置年月
                                qzjx.setSbbajg(qzjxArray.getJSONObject(q).getString("SBBAJG"));                                 // 设备备案机关
                                qzjx.setFzjgtryshdm(qzjxArray.getJSONObject(q).getString("FZJGTYSHDM"));                        // 发证机关代码
                                qzjx.setJxszds(qzjxArray.getJSONObject(q).getString("JXSZDS"));                                 // 机械所在地市
                                qzjx.setJxszqx(qzjxArray.getJSONObject(q).getString("JXSZQX"));                                 // 机械所在区县
                                qzjx.setJxqy(qzjxArray.getJSONObject(q).getString("JXQY"));                                     // 机械区域
                                qzjx.setEdqzl(NumberUtil.toBigDecimal(qzjxArray.getJSONObject(q).getString("EDQZL")));          // 额定起重量（T）
                                qzjx.setEdqzlj(NumberUtil.toBigDecimal(qzjxArray.getJSONObject(q).getString("EDQZLJ")));        // 额定起重力矩（T·M）
                                qzjx.setZdgzfd(NumberUtil.toBigDecimal(qzjxArray.getJSONObject(q).getString("ZDGZFD")));        // 最大工作幅度（M）
                                qzjx.setQzcb(NumberUtil.toBigDecimal(qzjxArray.getJSONObject(q).getString("TSQZJQZCB")));       // 起重臂长度（M）
                                qzjx.setZdfdedqzl(NumberUtil.toBigDecimal(qzjxArray.getJSONObject(q).getString("TSQZJZDFDQZL"))); // 最大幅度额定起重量（T）
                                qzjx.setZddlqsgd(NumberUtil.toBigDecimal(qzjxArray.getJSONObject(q).getString("ZDDLQSGD")));    // 独起升高度（M）
                                qzjx.setZdqsgd(NumberUtil.toBigDecimal(qzjxArray.getJSONObject(q).getString("ZDQSGD")));        // 最大起升高度（M）
                                qzjx.setZyjgjwybh(qzjxArray.getJSONObject(q).getString("ZYJGJWYBH"));                           // 主要结构件唯一编号
                                qzjx.setNazzdgd(NumberUtil.toBigDecimal(qzjxArray.getJSONObject(q).getString("TSQZJNAZZDGD"))); // 拟安装最大高度（m）
                                qzjx.setZyjgjgg(qzjxArray.getJSONObject(q).getString("ZYJGJGG"));                               // 标准节主要结构件规格
                                qzjx.setJqjcs(qzjxArray.getJSONObject(q).getString("TAQZJJQJCS"));                              // 加强节参数（长×宽×高）mm
                                qzjx.setBzjcs(qzjxArray.getJSONObject(q).getString("TSQZJBZJCS"));                              // 标准节参数（长×宽×高）mm
                                qzjx.setSgsjjytlx(qzjxArray.getJSONObject(q).getString("SGSJJYTLX"));                           // 施工升降机用途类型
                                qzjx.setDdjzgl(qzjxArray.getJSONObject(q).getString("DDJZGL"));                                 // 电动机总功率（kW）
                                qzjx.setEdtssd(NumberUtil.toBigDecimal(qzjxArray.getJSONObject(q).getString("EDTSSD")));        // 额定提升速度（m/min）
                                qzjx.setFzaqqxh(qzjxArray.getJSONObject(q).getString("FZAQQXH"));                               // 防坠安全器型号
                                qzjx.setJkcc(qzjxArray.getJSONObject(q).getString("JKCC"));                                     // 运载装置（吊笼）净空尺寸（长×宽×高）m
                                qzjx.setSblb(qzjxArray.getJSONObject(q).getString("SBLB"));                                     // 设备类别
                                qzjx.setMsqzjkd(qzjxArray.getJSONObject(q).getString("MSQZJKD"));                               // 门式起重机跨度（m）
                                qzjxList.add(qzjx);
                            }


                            if (!CollectionUtils.isEmpty(xmxxList)) {
                                xmxxMapper.batchInsertXmxx(xmxxList);
                            }
                            if (!CollectionUtils.isEmpty(cjdwList)) {
                                cjdwMapper.batchInsertCjdw(cjdwList);
                            }
                            if (!CollectionUtils.isEmpty(xgryList)) {
                                xgryMapper.batchInsertXgry(xgryList);
                            }
                            if (!CollectionUtils.isEmpty(qzjxList)) {
                                qzjxMapper.batchInsertQzjx(qzjxList);
                            }
                            log.info("第{}个项目信息已同步成功！项目信息：【项目名称：{} 施工许可证编号：{},当前项目下存在{}个参建单位;{}个起重机械;{}个相关人员信息。】",
                                i + 1, xmxxList.get(0).getProjectName(), xmxxList.get(0).getBuilderLicenceNum(), cjdwList.size(), qzjxList.size(), xgryList.size());
                        }
                    }
                }
                log.info("共同步{}个项目信息;同步成功{}个;同步失败(没有施工许可证编号){}个。", xmArray.size(), successCount, failCount);
            }
        }
        return ExecuteResult.success("全省项目信息更新成功!");
    }
}

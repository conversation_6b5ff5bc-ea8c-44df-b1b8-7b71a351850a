<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SysEnterpriseInfoMapper">


    <select id="list" parameterType="org.dromara.system.domain.SysEnterpriseInfo" resultType="org.dromara.system.domain.SysEnterpriseInfo">
        select
            sei.enterprise_id,
            sei.enterprise_name,
            sei.unified_social_credit_code,
            sei.enterprise_type,
            sei.business_address,
            sei.legal_representative,
            sei.registration_region_province,
            sei.registration_region_city,
            sei.registration_region_area,
            sei.registration_date,
            sei.office_phone,
            sei.business_license_path,
            sei.legal_id_card_path,
            sei.qualification_certificate_path,
            sei.safety_production_license_path,
            sei.user_id,
            sei.dept_id,
            sei.del_flag,
            sei.enterprise_status,
            sei.enterprise_user_id,
            sei.enterprise_time,
            sei.enterprise_reason,
            sei.create_dept,
            sei.create_by,
            sei.create_time,
            sei.update_by,
            sei.update_time,
            su.nick_name as enterpriseUserName,
            zdp.division_name as provinceName,
            zdc.division_name as cityName,
            zda.division_name as areaName
        from sys_enterprise_info  sei
        left join sys_user su on su.user_id = sei.enterprise_user_id
        left join z_division zdp on sei.registration_region_province = zdp.division_code
        left join z_division zdc on sei.registration_region_city = zdc.division_code
        left join z_division zda on sei.registration_region_area = zda.division_code
        <where>
            <if test="enterpriseName != null and enterpriseName != ''">
                and enterprise_name like concat('%', #{enterpriseName}, '%')
            </if>
            <if test="unifiedSocialCreditCode != null and unifiedSocialCreditCode != ''">
                and unified_social_credit_code = #{unifiedSocialCreditCode}
            </if>
            <if test="enterpriseType != null and enterpriseType != ''">
                and enterprise_type = #{enterpriseType}
            </if>
            <if test="legalRepresentative != null and legalRepresentative != ''">
                and legal_representative like concat('%', #{legalRepresentative}, '%')
            </if>
            <if test="registrationRegionProvince != null and registrationRegionProvince != ''">
                and registration_region_province = #{registrationRegionProvince}
            </if>
            <if test="registrationRegionCity != null and registrationRegionCity != ''">
                and registration_region_city = #{registrationRegionCity}
            </if>
            <if test="registrationRegionArea != null and registrationRegionArea != ''">
                and registration_region_area = #{registrationRegionArea}
            </if>
            <if test="enterpriseStatus != null and enterpriseStatus != ''">
                and enterprise_status = #{enterpriseStatus}
            </if>
        </where>
    </select>

    <select id="getOneInfo" parameterType="org.dromara.system.domain.SysEnterpriseInfo" resultType="org.dromara.system.domain.SysEnterpriseInfo">
        select
            sei.enterprise_id,
            sei.enterprise_name,
            sei.unified_social_credit_code,
            sei.enterprise_type,
            sei.business_address,
            sei.legal_representative,
            sei.registration_region_province,
            sei.registration_region_city,
            sei.registration_region_area,
            sei.registration_date,
            sei.office_phone,
            sei.business_license_path,
            sei.legal_id_card_path,
            sei.qualification_certificate_path,
            sei.safety_production_license_path,
            sei.user_id,
            sei.dept_id,
            sei.del_flag,
            sei.enterprise_status,
            sei.enterprise_user_id,
            sei.enterprise_time,
            sei.enterprise_reason,
            sei.create_dept,
            sei.create_by,
            sei.create_time,
            sei.update_by,
            sei.update_time,
            su.nick_name as enterpriseUserName,
            zdp.division_name as provinceName,
            zdc.division_name as cityName,
            zda.division_name as areaName
        from sys_enterprise_info  sei
                 left join sys_user su on su.user_id = sei.enterprise_user_id
                 left join z_division zdp on sei.registration_region_province = zdp.division_code
                 left join z_division zdc on sei.registration_region_city = zdc.division_code
                 left join z_division zda on sei.registration_region_area = zda.division_code
        where sei.enterprise_id = #{enterpriseId}
    </select>
    <select id="selectByDeptId" resultType="org.dromara.system.domain.SysEnterpriseInfo">
        select * from sys_enterprise_info where dept_id = #{deptId}
    </select>


</mapper>

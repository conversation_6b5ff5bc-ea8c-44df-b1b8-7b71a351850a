package org.dromara.projects.service;

import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.person.domain.bo.SysPersonBo;
import org.dromara.person.domain.vo.SysPersonVo;
import org.dromara.projects.domain.bo.PrjProjectsBo;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.system.domain.vo.EnterpriseNameAndId;

import java.util.Collection;
import java.util.List;

/**
 * 项目录入Service接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
public interface IPrjProjectsService {

    /**
     * 查询项目录入
     *
     * @param projectId 主键
     * @return 项目录入
     */
    PrjProjectsVo queryById(Long projectId);

    /**
     * 分页查询项目录入列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目录入分页列表
     */
    TableDataInfo<PrjProjectsVo> queryPageList(PrjProjectsBo bo, PageQuery pageQuery);

    TableDataInfo<PrjProjectsVo> queryPageListSq(PrjProjectsBo bo, PageQuery pageQuery);

    List<PrjProjectsVo> selectAll(PrjProjectsBo bo);

    List<PrjProjectsVo> listAiAll(PrjProjectsBo bo);

    /**
     * 查询符合条件的项目录入列表
     *
     * @param bo 查询条件
     * @return 项目录入列表
     */
    List<PrjProjectsVo> queryList(PrjProjectsBo bo);

    /**
     * 新增项目录入
     *
     * @param bo 项目录入
     * @return 是否新增成功
     */
    Boolean insertByBo(PrjProjectsBo bo);

    /**
     * 修改项目录入
     *
     * @param bo 项目录入
     * @return 是否修改成功
     */
    Boolean updateByBo(PrjProjectsBo bo);

    /**
     * 校验并批量删除项目录入信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询五方单位下任意一方的人员列表
     *
     * @param bo        人员基本信息
     * @param pageQuery 分页参数
     * @return 五方单位下任意一方的人员列表
     */
    TableDataInfo<SysPersonVo> selectFivePartyUsers(SysPersonBo bo, PageQuery pageQuery);

    R<List<EnterpriseNameAndId>> getSearchData();
}

<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item v-hasPermi="['system:enterpriseInfo:projectData']" label="项目查询" prop="enterpriseId">
              <el-select v-model="queryParams.projectId" filterable placeholder="请选择项目" style="width: 240px" clearable>
                <el-option v-for="item in projectSelectData" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="危大工程名称" prop="itemName" label-width="96px">
              <el-input v-model="queryParams.itemName" placeholder="请输入危大工程名称/描述" clearable
                @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="危大类型" prop="dangerListType">
              <el-select v-model="queryParams.dangerListType" placeholder="请选择危大类型" clearable>
                <el-option v-for="dict in danger_list_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item v-hasPermi="['system:enterpriseInfo:searchData']" label="企业查询" prop="enterpriseId">
              <el-select v-model="queryParams.enterpriseId" filterable placeholder="请选择企业" style="width: 240px"
                clearable>
                <el-option v-for="item in enterpriseSelectData" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <!-- <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['projects:prj_hazardous_items:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template> -->

      <el-table class="expertTable6" v-loading="loading" :data="prj_hazardous_itemsList"
        @selection-change="handleSelectionChange" show-overflow-tooltip>
        <el-table-column type="index" label="序号" width="55" align="center" />
        <el-table-column label="项目名称" align="center" prop="projectName" min-width="130px" />
        <el-table-column label="危大工程名称" align="center" prop="itemName" min-width="100">
          <template #default="scope">
            <el-link type="primary" @click="handleDetail(scope.row)">{{ scope.row.itemName }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="危大类型" align="center" prop="dangerListType">
          <template #default="scope">
            <dict-tag :options="danger_list_type" :value="scope.row.dangerListType" />
          </template>
        </el-table-column>
        <el-table-column label="涉危工程" align="center" prop="parentName" min-width="150px" />
        <el-table-column label="具体范围" align="center" prop="scopeDetails" min-width="100" />
        <el-table-column label="计划开工日期" align="center" prop="startDate">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="计划竣工日期" align="center" prop="plannedEndDate">
          <template #default="scope">
            <span>{{ parseTime(scope.row.plannedEndDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="project_item_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="110px">
          <template #default="scope">
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 项目工程管理详情弹框 -->
    <prj_hazardous_items_detail :itemId="itemId" :isShowModel="isShowModel" @update:isShowModel="handleModelDetail">
    </prj_hazardous_items_detail>

    <!-- 添加或修改[项目管理] 列出项目内具体的危险性较大的分部分项工程对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="800px" append-to-body>
      <el-form ref="prj_hazardous_itemsFormRef" :model="form" :rules="rules">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="危大工程名称" prop="itemName">
              <el-input v-model="form.itemName" placeholder="请输入危大工程名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="危大类型" prop="dangerListType">
              <el-select v-model="form.dangerListType" placeholder="请选择危大类型" @change="selectTypeChange">
                <el-option v-for="dict in danger_list_type" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="涉危工程" prop="dangerGcVal" label-width="105px">
              <el-select v-model="form.dangerGcVal" placeholder="请选择涉危工程" @change="selectGcChange"
                :fit-input-width="true">
                <el-option v-for="item in dangerListList" :key="item.dangerId" :label="item.name"
                  :value="item.dangerId" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="涉危工程类型" prop="dangerGcTypeVal" label-width="106px">
              <el-select v-model="form.dangerGcTypeVal" multiple placeholder="请选择涉危工程类型" :fit-input-width="true"
                clearable @change="selectGcTypeChange">
                <el-option v-for="item in dangerListType" :key="item.dangerId" :label="item.name"
                  :value="item.dangerId" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="具体范围详情" prop="scopeDetails" label-width="105px">
              <el-input v-model="form.scopeDetails" type="textarea" :rows="5" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="计划开工日期" prop="startDate" label-width="105px">
              <el-date-picker clearable v-model="form.startDate" type="date" value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择计划开工日期" :disabled-date="startPickerOptions" style="width: 100%;">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划竣工日期" prop="plannedEndDate" label-width="105px">
              <el-date-picker clearable v-model="form.plannedEndDate" type="date" value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择计划竣工日期" :disabled-date="endPickerOptions" style="width: 100%;">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="实际开工日期" prop="actualStartDate" label-width="105px">
              <el-date-picker clearable v-model="form.actualStartDate" type="date" value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择实际开工日期" :disabled-date="startPickerOptions1" style="width: 100%;">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际竣工日期" prop="actualEndDate" label-width="105px">
              <el-date-picker clearable v-model="form.actualEndDate" type="date" value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择实际竣工日期" :disabled-date="endPickerOptions1" style="width: 100%;">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Prj_hazardous_items" lang="ts">
import {
  listPrj_hazardous_items_admin,
  getPrj_hazardous_items,
  delPrj_hazardous_items,
  addPrj_hazardous_items,
  updatePrj_hazardous_items,
  get_prj_search_data
} from '@/api/projects/prj_hazardous_items';
import { Prj_hazardous_itemsVO, Prj_hazardous_itemsQuery, Prj_hazardous_itemsForm } from '@/api/projects/prj_hazardous_items/types';
import { listDangerList } from '@/api/system/dangerList';
import { DangerListVO } from '@/api/system/dangerList/types';
import { getSearchData } from '@/api/system/enterpriseInfo';
import prj_hazardous_items_detail from '@/components/prj_hazardous_items_detail/index.vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { danger_list_type, project_item_status } = toRefs<any>(proxy?.useDict('danger_list_type', 'project_item_status'));

const prj_hazardous_itemsList = ref<Prj_hazardous_itemsVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const enterpriseSelectData = ref([])
const projectSelectData = ref([])
const itemId = ref<string>('');
const isShowModel = ref<boolean>(false);

const handleModelDetail = (val: boolean) => {
  isShowModel.value = val;
  itemId.value = '';
}

const handleDetail = (row: Prj_hazardous_itemsVO) => {
  itemId.value = row.itemId as string;
  isShowModel.value = true;
}

const dangerList = ref<DangerListVO[]>([]);
const dangerListList = ref([]);
const dangerListType = ref([]);
const queryFormRef = ref<ElFormInstance>();
const prj_hazardous_itemsFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: Prj_hazardous_itemsForm = {
  dangerGcVal: undefined,
  dangerGcTypeVal: undefined,
  itemId: undefined,
  projectId: undefined,
  dangerId: undefined,
  itemName: undefined,
  scopeDetails: undefined,
  dangerListType: undefined,
  startDate: undefined,
  plannedEndDate: undefined,
  actualEndDate: undefined,
  actualStartDate: undefined,
  status: undefined,
}
const data = reactive<PageData<Prj_hazardous_itemsForm, Prj_hazardous_itemsQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    dangerId: undefined,
    itemName: undefined,
    scopeDetails: undefined,
    dangerListType: undefined,
    startDate: undefined,
    plannedEndDate: undefined,
    actualEndDate: undefined,
    actualStartDate: undefined,
    status: undefined,
    enterpriseId: undefined,
    params: {
    }
  },
  rules: {
    itemName: [
      { required: true, message: "危大工程名称不能为空", trigger: "blur" }
    ],
    dangerListType: [
      { required: true, message: "危大类型不能为空", trigger: "change" }
    ],
    dangerGcVal: [
      { required: true, message: "涉危工程不能为空", trigger: "change" }
    ],
    dangerGcTypeVal: [
      { required: true, message: "涉危工程类型不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 判断计划开工开始日期的范围
const startPickerOptions = (date: Date) => {
  if (form.value.plannedEndDate) {
    return date.getTime() > new Date(form.value.plannedEndDate).getTime();
  }
}
// 判断计划结束日期的范围
const endPickerOptions = (date: Date) => {
  if (form.value.startDate) {
    return date.getTime() < new Date(form.value.startDate).getTime();
  }
}
// 判断实际开工开始日期的范围
const startPickerOptions1 = (date: Date) => {
  if (form.value.actualEndDate) {
    return date.getTime() > new Date(form.value.actualEndDate).getTime();
  }
}
// 判断实际结束日期的范围
const endPickerOptions1 = (date: Date) => {
  if (form.value.actualStartDate) {
    return date.getTime() < new Date(form.value.actualStartDate).getTime();
  }
}
/** 查询dangerList列表 */
const getDangerList = async () => {
  const res = await listDangerList(queryParams.value);
  const data = proxy?.handleTree<DangerListVO>(res.data, 'dangerId', 'preId');
  if (data) {
    dangerList.value = data;
  }
};
/** 查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPrj_hazardous_items_admin(queryParams.value);
  prj_hazardous_itemsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  prj_hazardous_itemsFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value.enterpriseId = undefined
  queryParams.value.projectId = undefined
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: Prj_hazardous_itemsVO[]) => {
  ids.value = selection.map(item => item.itemId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加分部分项工程";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: Prj_hazardous_itemsVO) => {
  reset();
  const _itemId = row?.itemId || ids.value[0]
  const res = await getPrj_hazardous_items(_itemId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改分部分项工程";
  await selectTypeChange(form.value.dangerListType);
  for (let m = 0; m < dangerListList.value.length; m++) {
    for (let n = 0; n < dangerListList.value[m].children.length; n++) {
      if ((form.value.dangerId as string).includes(dangerListList.value[m].children[n].dangerId)) {
        form.value.dangerGcVal = dangerListList.value[m].dangerId;
      }
    }
  }
  await selectGcChange(form.value.dangerGcVal);
  // 修改时的回显
  form.value.dangerGcTypeVal = (form.value.dangerId as string).split(',');
}

/** 提交按钮 */
const submitForm = () => {
  prj_hazardous_itemsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.itemId) {
        await updatePrj_hazardous_items(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addPrj_hazardous_items(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: Prj_hazardous_itemsVO) => {
  const _itemIds = row?.itemId || ids.value;
  await proxy?.$modal.confirm(`是否确认删除该分部分项工程名称为(${row.itemName})?`).finally(() => loading.value = false);
  await delPrj_hazardous_items(_itemIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('projects/prj_hazardous_items/export', {
    ...queryParams.value
  }, `prj_hazardous_items_${new Date().getTime()}.xlsx`)
}
/** 返回按钮操作 */
const handleClose = () => {
  const obj: RouteLocationNormalized = {
    fullPath: '',
    hash: '',
    matched: [],
    meta: undefined,
    name: undefined,
    params: undefined,
    query: undefined,
    redirectedFrom: undefined,
    path: '/prj/prj_projects'
  };
  proxy?.$tab.closeOpenPage(obj);
};
const selectTypeChange = (id: number) => {
  form.value.dangerGcVal = '';
  form.value.dangerGcTypeVal = [];
  dangerList.value.forEach(item => {
    if (item.type == id) {
      dangerListList.value = item.children;
    }
  })
}
const selectGcChange = (id: number | string) => {
  form.value.dangerGcTypeVal = [];
  dangerListList.value.forEach(item => {
    if (item.dangerId == id) {
      dangerListType.value = item.children;
    }
  })
}
const selectGcTypeChange = (idArr: string[]) => {
  form.value.dangerId = idArr.join(',')
}
// 企业查询
const getEnterpriseSelectData = async () => {
  const { data } = await getSearchData()
  enterpriseSelectData.value = data
}

//查询项目选择框数据
const getProjectSelectData = async () => {
  const { data } = await get_prj_search_data();
  projectSelectData.value = data
}
onMounted(() => {
  getEnterpriseSelectData()
  getProjectSelectData()
  // 接收动态路由的参数
  getList();
  getDangerList();
});
</script>
<style lang="scss">
.expertTable6 {

  .el-popper,
  .is-dark {
    max-width: 300px;
  }
}
</style>

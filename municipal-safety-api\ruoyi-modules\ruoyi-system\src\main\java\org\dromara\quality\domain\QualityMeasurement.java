package org.dromara.quality.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 实测实量对象 quality_measurement
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("quality_measurement")
public class QualityMeasurement extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 测量ID
     */
    @TableId(value = "measurement_id")
    private Long measurementId;

    /**
     * 测量时间
     */
    private Date measurementTime;

    /**
     * 测量事项
     */
    private String measurementItem;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 测量结果
     */
    private String measurementResult;

    /**
     * 是否合规（0合规 1不合规）
     */
    private String isCompliant;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 测量位置
     */
    private String measurementLocation;

    /**
     * 标准值
     */
    private String standardValue;

    /**
     * 偏差值
     */
    private String deviationValue;

    /**
     * 测量人员
     */
    private String measurementPerson;

    /**
     * 是否标记隐患（0否 1是）
     */
    private String isHazardMarked;

    /**
     * 隐患描述
     */
    private String hazardDescription;

    /**
     * 状态（0正常 1已推送 2已处理）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 版本
     */
    @Version
    private Long version;

    /**
     * 删除标志
     */
    @TableLogic
    private Long delFlag;

}

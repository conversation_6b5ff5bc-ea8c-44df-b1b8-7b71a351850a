import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MonitorFacilityVO, MonitorFacilityForm, MonitorFacilityQuery } from '@/api/projects/facility/types';

/**
 * 查询监测设备列表
 * @param query
 * @returns {*}
 */

export const listMonitorFacility = (query?: MonitorFacilityQuery): AxiosPromise<MonitorFacilityVO[]> => {
  return request({
    url: '/system/monitorFacility/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询监测设备详细
 * @param id
 */
export const getMonitorFacility = (id: string | number): AxiosPromise<MonitorFacilityVO> => {
  return request({
    url: '/system/monitorFacility/' + id,
    method: 'get'
  });
};

/**
 * 新增监测设备
 * @param data
 */
export const addMonitorFacility = (data: MonitorFacilityForm) => {
  return request({
    url: '/system/monitorFacility',
    method: 'post',
    data: data
  });
};

/**
 * 修改监测设备
 * @param data
 */
export const updateMonitorFacility = (data: MonitorFacilityForm) => {
  return request({
    url: '/system/monitorFacility',
    method: 'put',
    data: data
  });
};

/**
 * 删除监测设备
 * @param id
 */
export const delMonitorFacility = (id: string | number | Array<string | number>) => {
  return request({
    url: '/system/monitorFacility/' + id,
    method: 'delete'
  });
};
// 环境监测
export const listHjjc = (params: { pageNum: number; pageSize: number; devNo: string }): AxiosPromise<any[]> => {
  return request({
    url: '/system/dustReal/list',
    method: 'get',
    params
  });
};
// 临边防护
export const listEdgeGuard = (params: { pageNum: number; pageSize: number; devNo: string }): AxiosPromise<any[]> => {
  return request({
    url: '/system/edgeGuard/list',
    method: 'get',
    params
  });
};
// 卸料平台
export const listDumpPlat = (params: { pageNum: number; pageSize: number; devNo: string }): AxiosPromise<any[]> => {
  return request({
    url: '/system/lnDumpPlat/list',
    method: 'get',
    params
  });
};
// 智能喷淋
export const listSpraying = (params: { pageNum: number; pageSize: number; devNo: string }): AxiosPromise<any[]> => {
  return request({
    url: '/system/lnSpraying/list',
    method: 'get',
    params
  });
};
// 智能电表
export const listEnergy = (params: { pageNum: number; pageSize: number; devNo: string }): AxiosPromise<any[]> => {
  return request({
    url: '/system/lnEnergy/list',
    method: 'get',
    params
  });
};
// 智能螺母
export const listLnNut = (params: { pageNum: number; pageSize: number; devNo: string }): AxiosPromise<any[]> => {
  return request({
    url: '/system/lnNut/list',
    method: 'get',
    params
  });
};
// 智能水表
export const listLnWater = (params: { pageNum: number; pageSize: number; devNo: string }): AxiosPromise<any[]> => {
  return request({
    url: '/system/lnWater/list',
    method: 'get',
    params
  });
};
// 智能烟感
export const listSmoke = (params: { pageNum: number; pageSize: number; devNo: string }): AxiosPromise<any[]> => {
  return request({
    url: '/system/lnSmoke/list',
    method: 'get',
    params
  });
};
// 高支模
export const listHighFormWorkReal = (params: { pageNum: number; pageSize: number; devNo: string }): AxiosPromise<any[]> => {
  return request({
    url: '/system/lnHighFormworkReal/list',
    method: 'get',
    params
  });
};

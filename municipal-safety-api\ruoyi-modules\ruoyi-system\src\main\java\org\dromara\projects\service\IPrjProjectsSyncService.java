package org.dromara.projects.service;

import org.dromara.common.core.domain.R;
import org.dromara.projects.domain.dto.sync.SyncResponseDto;
import org.dromara.projects.domain.vo.PrjProjectsVo;

/**
 * 项目同步Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IPrjProjectsSyncService {

    void batchSync();

    /**
     * 根据施工许可证编号同步项目信息
     *
     * @param constructionPermitNo 施工许可证编号
     * @return 同步结果
     */
    R<PrjProjectsVo> syncProjectByPermitNo(String constructionPermitNo);

    /**
     * 处理同步数据
     *
     * @param syncData             同步响应数据
     * @param constructionPermitNo 施工许可证编号
     * @return 处理后的项目信息
     */
    PrjProjectsVo processSyncData(SyncResponseDto syncData, String constructionPermitNo);

    /**
     * 获取外部API数据
     *
     * @param constructionPermitNo 施工许可证编号
     * @return 外部API响应数据
     */
    SyncResponseDto fetchExternalData(String constructionPermitNo);

    /**
     * 根据施工许可证编号同步项目信息 （施工方账号审核通过 同步所有信息）
     * @return 同步结果
     */
    R<PrjProjectsVo> syncProjects(String constructionPermitNo);
}

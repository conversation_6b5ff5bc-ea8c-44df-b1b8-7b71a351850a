package org.dromara.facility.domain.vo;

import java.util.Date;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.facility.domain.LnEnergy;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 绿能用电监测视图对象 ln_energy
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LnEnergy.class)
public class LnEnergyVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * A相电压 0.1V
     */
    @ExcelProperty(value = "A相电压 0.1V")
    private Long ua;

    /**
     * B相电压 0.1V
     */
    @ExcelProperty(value = "B相电压 0.1V")
    private Long ub;

    /**
     * C相电压 0.1V
     */
    @ExcelProperty(value = "C相电压 0.1V")
    private Long uc;

    /**
     * A相电流 0.1V
     */
    @ExcelProperty(value = "A相电流 0.1V")
    private Long ia;

    /**
     * B相电流 0.1V
     */
    @ExcelProperty(value = "B相电流 0.1V")
    private Long ib;

    /**
     * C相电流 0.1V
     */
    @ExcelProperty(value = "C相电流 0.1V")
    private Long ic;

    /**
     * 漏电流 0.1V
     */
    @ExcelProperty(value = "漏电流 0.1V")
    private Long il;

    /**
     * A相温度 0.1°
     */
    @ExcelProperty(value = "A相温度 0.1°")
    private Long ta;

    /**
     * B相温度 0.1°
     */
    @ExcelProperty(value = "B相温度 0.1°")
    private Long tb;

    /**
     * C相温度 0.1°
     */
    @ExcelProperty(value = "C相温度 0.1°")
    private Long tc;

    /**
     * N相温度 0.1°
     */
    @ExcelProperty(value = "N相温度 0.1°")
    private Long tn;

    /**
     * 采集时间
     */
    @ExcelProperty(value = "采集时间")
    private Date recordTime;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String deviceNo;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String devNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

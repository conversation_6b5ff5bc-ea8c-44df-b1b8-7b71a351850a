{"doc": " 设备管理Service接口\n\n <AUTHOR>\n @date 2024-01-01\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询设备管理\n\n @param deviceId 设备ID\n @return 设备管理\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.quality.domain.bo.QualityDeviceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询设备管理列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 设备管理集合\n"}, {"name": "queryList", "paramTypes": ["org.dromara.quality.domain.bo.QualityDeviceBo"], "doc": " 查询设备管理列表\n\n @param bo 查询条件\n @return 设备管理集合\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.quality.domain.bo.QualityDeviceBo"], "doc": " 新增设备管理\n\n @param bo 设备管理\n @return 结果\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.quality.domain.bo.QualityDeviceBo"], "doc": " 修改设备管理\n\n @param bo 设备管理\n @return 结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除设备管理信息\n\n @param ids     需要删除的设备管理主键集合\n @param isValid 是否校验,true-删除前校验,false-不校验\n @return 结果\n"}, {"name": "generateDeviceCode", "paramTypes": ["java.lang.String"], "doc": " 生成设备编号\n\n @param deviceType 设备类型（10xx：靠尺、20xx：角尺、30xx：测距仪、40xx：楼板测厚仪、50xx：钢筋检测仪、60xx：回弹仪）\n @return 设备编号\n"}, {"name": "checkDeviceCodeUnique", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 校验设备编号是否唯一\n\n @param deviceCode 设备编号\n @param deviceId   设备ID\n @return 结果\n"}, {"name": "uploadDeviceImage", "paramTypes": ["java.lang.Long", "org.springframework.web.multipart.MultipartFile"], "doc": " 上传设备图片\n\n @param deviceId 设备ID（可为空，新增时为空）\n @param file     图片文件\n @return OSS ID\n"}, {"name": "uploadManualFile", "paramTypes": ["java.lang.Long", "org.springframework.web.multipart.MultipartFile"], "doc": " 上传使用说明书\n\n @param deviceId 设备ID（可为空，新增时为空）\n @param file     说明书文件\n @return OSS ID\n"}], "constructors": []}
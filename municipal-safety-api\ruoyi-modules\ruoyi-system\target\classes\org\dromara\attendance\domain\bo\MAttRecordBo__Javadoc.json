{"doc": " 考勤记录业务对象 m_att_record\n\n <AUTHOR>\n @date 2025-05-06\n", "fields": [{"name": "id", "doc": " id\n"}, {"name": "ruleId", "doc": " 规则id\n"}, {"name": "personId", "doc": " 项目人员id\n"}, {"name": "personType", "doc": " 人员岗位/角色\n"}, {"name": "realName", "doc": " 真实姓名\n"}, {"name": "idNumber", "doc": " 身份Id\n"}, {"name": "realTimeFace", "doc": " 实时人脸\n"}, {"name": "sn", "doc": " 设备号\n"}, {"name": "source", "doc": " 打卡来源\n"}, {"name": "content", "doc": " 自定义内容\n"}, {"name": "attTime", "doc": " 考勤时间\n"}, {"name": "attDate", "doc": " 考勤日期\n"}, {"name": "attResult", "doc": " 打卡结果\n"}, {"name": "whichTime", "doc": " 第几次打卡\n"}], "enumConstants": [], "methods": [], "constructors": []}
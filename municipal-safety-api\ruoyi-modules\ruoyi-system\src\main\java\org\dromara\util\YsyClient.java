package org.dromara.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.aizuda.snailjob.client.job.core.enums.AllocationAlgorithmEnum;
import com.aizuda.snailjob.client.job.core.enums.TriggerTypeEnum;
import com.aizuda.snailjob.client.job.core.handler.add.ClusterAddHandler;
import com.aizuda.snailjob.client.job.core.openapi.SnailJobOpenApi;
import com.aizuda.snailjob.common.core.enums.JobBlockStrategyEnum;
import com.aizuda.snailjob.common.core.enums.StatusEnum;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR> Zu Da
 * @date 2025/5/18 10:53
 * @Description TODO
 * @Version 1.0
 */
@Component
public class YsyClient implements InitializingBean {

    private DateTime expireTime;

    /**
     * 用户token 萤石云平台获取
     */
    private String accessToken;

    @Value("${ysy.app-key}")
    private String appKey;

    @Value("${ysy.secret}")
    private String secret;

    /**
     * 刷新锁
     */
    private ReentrantLock refreshLock = new ReentrantLock();

    /**
     * 项目id
     */
    @Value("${ysy.projectId}")
    private String projectId;

    private void refreshToken() {
        refreshLock.lock();
        try {
            if (this.expireTime == null || this.expireTime.isAfterOrEquals(new Date())) {
                //视频抽帧截取图片
                Map<String, Object> pushMap = new HashMap<>();
                pushMap.put("appKey", this.appKey);
                pushMap.put("appSecret", this.secret);

                String result = HttpUtil.post("https://open.ys7.com/api/lapp/token/get", pushMap);

                JSONObject jsonObject = JSON.parseObject(result);

                JSONObject object = jsonObject.getJSONObject("data");

                this.accessToken = object.getString("accessToken");
                this.expireTime = DateUtil.date(Long.parseLong(object.getString("expireTime")));
            }
        } finally {
            //释放锁
            refreshLock.unlock();
        }
    }

    /**
     * 截图获取到流
     *
     * @param deviceSerial
     * @return
     */
    public String capture(String deviceSerial, Integer channelNo) {
        refreshToken();
        //视频抽帧截取图片
        Map<String, Object> pushMap = new HashMap<>();
        pushMap.put("accessToken", this.accessToken);
        pushMap.put("deviceSerial", deviceSerial);
        pushMap.put("captureType", "2");
        pushMap.put("projectId", this.projectId);
        pushMap.put("channelNo", channelNo);

        //返回值
        String resultMap = HttpUtil.post("https://open.ys7.com/api/open/cloud/v1/capture/save", pushMap);

        JSONObject jsonObject = JSON.parseObject(resultMap);

        String fileId = jsonObject.getString("data");

        return fileId;
    }

    /**
     * 下载图片
     *
     * @param fileId 文件id
     * @return
     */
    public InputStream downloadImg(String fileId) {
        refreshToken();
        //获取下载图片地址
        Map<String, Object> downMap = new HashMap<>();
        downMap.put("accessToken", this.accessToken);
        downMap.put("projectId", this.projectId);
        downMap.put("fileId", fileId);

        //返回值
        String downResult = HttpUtil.get("https://open.ys7.com/api/service/cloudrecord/file/official/download", downMap);

        JSONObject object = JSON.parseObject(downResult);

        JSONObject data = object.getJSONObject("data");

        JSONArray array = data.getJSONArray("urls");

        String downUrl = array.getString(0);

        byte[] bytes = HttpUtil.downloadBytes(downUrl);

        return new ByteArrayInputStream(bytes);
    }

    /**
     * 删除文件
     *
     * @param fileId 文件id
     * @return
     */
    public boolean removeFile(String fileId) {
        refreshToken();
        Map<String, Object> map = new HashMap<>();
        map.put("accessToken", this.accessToken);
        map.put("projectId", this.projectId);
        map.put("fileId", fileId);

        HttpResponse execute = HttpUtil.createRequest(Method.DELETE, "https://open.ys7.com/api/open/cloud/v1/file").form(map).execute();

        String body = execute.body();

        JSONObject jsonObject = JSON.parseObject(body);

        JSONObject object = jsonObject.getJSONObject("meta");

        if (object != null) {
            Integer code = object.getInteger("code");
            if (code != null && code == 200) {
                return true;
            }
        }

        return false;
    }


    /**
     * 获取rtmp地址
     *
     * @param deviceSerial 设备序列号
     * @param protocol     获取流类型
     * @return
     */
    private String getStream(String deviceSerial, String protocol, Integer chanelNo) {
        refreshToken();
        Map<String, Object> map = new HashMap<>();
        map.put("accessToken", this.accessToken);
        map.put("deviceSerial", deviceSerial);
        map.put("channelNo", chanelNo);
        map.put("protocol", protocol);
        map.put("quality", "1");

        String result = HttpUtil.post("https://open.ys7.com/api/lapp/v2/live/address/get", map);

        JSONObject jsonObject = JSON.parseObject(result);

        JSONObject data = jsonObject.getJSONObject("data");

        String rtmpUrl = data.getString("url");

        return rtmpUrl;
    }

    /**
     * 获取rtmp流
     *
     * @param deviceSerial
     * @return
     */
    public String getRtmp(String deviceSerial, Integer chanelNo) {
        return getStream(deviceSerial, "3", chanelNo);
    }

    /**
     * 获取萤石云流地址
     *
     * @param deviceSerial
     * @return
     */
    public String getEzopen(String deviceSerial, Integer chanelNo) {
        return getStream(deviceSerial, "1", chanelNo);
    }

    /**
     * 新增集群模式的任务
     *
     * @param jobName 任务名称
     * @return 任务id
     */
    public Long addClusterJob(Long monitoId, String jobName, Integer intervalTime) {
        ClusterAddHandler retryInterval = SnailJobOpenApi.addClusterJob().setRouteKey(AllocationAlgorithmEnum.RANDOM).setJobName(jobName).setExecutorInfo("deviceJobHandle")
            //超时时间
            .setExecutorTimeout(30).setDescription("添加摄像头定时任务" + monitoId).setBlockStrategy(JobBlockStrategyEnum.OVERLAY).setMaxRetryTimes(1)
            //固定时间
            .setTriggerType(TriggerTypeEnum.SCHEDULED_TIME)
            //间隔时长
            .setTriggerInterval(String.valueOf(intervalTime)).setRetryInterval(2);
        //创建不开启
        retryInterval.getReqDTO().setJobStatus(StatusEnum.NO.getStatus());

        return retryInterval.execute();
    }

    /**
     * 修改定时任务任务的间隔时间
     *
     * @param jobId
     * @param intervalTime
     * @return
     */
    public Boolean updateClusterJob(Long jobId, Integer intervalTime) {
        return SnailJobOpenApi.updateClusterJob(jobId).setTriggerType(TriggerTypeEnum.SCHEDULED_TIME).setTriggerInterval(String.valueOf(intervalTime)).execute();
    }

    /**
     * 删除定时任务
     *
     * @param jobId
     * @return
     */
    public boolean deleteJob(Long jobId) {
        return SnailJobOpenApi.deleteJob(Set.of(jobId)).execute();
    }

    /**
     * 启动任务
     *
     * @param jobId
     * @return
     */
    public boolean startJob(Long jobId) {
        return SnailJobOpenApi.updateJobStatus(jobId).setStatus(StatusEnum.YES).execute();
    }

    /**
     * 停止任务
     *
     * @param jobId
     * @return
     */
    public boolean stopJob(Long jobId) {
        return SnailJobOpenApi.updateJobStatus(jobId).setStatus(StatusEnum.NO).execute();
    }

    /**
     * 通过流地址截取图片
     *
     * @param streamUrl
     * @param fileSaveParentPath
     * @return
     * @throws IOException
     */
    public byte[] getImageByStreamUrl(String streamUrl, String fileSaveParentPath) throws IOException {
        return UrlScreenshotUtil.getSnap(streamUrl, fileSaveParentPath);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.refreshToken();
    }


    public String getAccessToken() {
        refreshToken();
        return accessToken;
    }

    /**
     * 获取在线的设备序列号列表
     *
     * @return
     */
    public List<String> onLineDeviceSerials() {

        int pageSize = 50;

        int pageStart = 0;

        boolean hasClose = true;

        List<String> onLineLis = new ArrayList<>();

        do {
            Map<String, Object> map = new HashMap<>();
            map.put("accessToken", this.accessToken);
            map.put("pageStart", pageStart);
            map.put("pageSize", pageSize);

            String result = HttpUtil.post("https://open.ys7.com/api/lapp/device/list", map);

            JSONObject object = JSON.parseObject(result);
            JSONArray array = object.getJSONArray("data");
            if (array == null || array.isEmpty()) {
                hasClose = false;
            } else {
                for (int i = 0; i < array.size(); i++) {
                    JSONObject jsonObject = array.getJSONObject(i);
                    if (jsonObject != null && jsonObject.containsKey("deviceSerial") && jsonObject.containsKey("status")) {
                        int status = jsonObject.getIntValue("status");
                        if (status == 1) {
                            String deviceSerial = jsonObject.getString("deviceSerial");
                            onLineLis.add(deviceSerial);
                        }
                    }
                }
            }
            pageStart++;
        } while (hasClose);

        return onLineLis;
    }

    /**
     * 获取在线的设备序列号和通道号
     *
     * @return
     */
    public List<CameraStatus> onLineChannelNoDeviceSerials() {

        int pageSize = 50;

        int pageStart = 0;

        boolean hasClose = true;

        List<CameraStatus> onLineList = new ArrayList<>();

        do {
            Map<String, Object> map = new HashMap<>();
            map.put("accessToken", this.accessToken);
            map.put("pageStart", pageStart);
            map.put("pageSize", pageSize);

            String result = HttpUtil.post("https://open.ys7.com/api/lapp/camera/list", map);

            JSONObject object = JSON.parseObject(result);
            JSONArray array = object.getJSONArray("data");
            if (array == null || array.isEmpty()) {
                hasClose = false;
            } else {
                for (int i = 0; i < array.size(); i++) {
                    JSONObject jsonObject = array.getJSONObject(i);
                    if (jsonObject != null && jsonObject.containsKey("deviceSerial") && jsonObject.containsKey("status")) {
                        int status = jsonObject.getIntValue("status");
                        if (status == 1) {
                            String deviceSerial = jsonObject.getString("deviceSerial");
                            onLineList.add(new CameraStatus(deviceSerial, jsonObject.getInteger("channelNo")));
                        }
                    }
                }
            }
            pageStart++;
        } while (hasClose);

        return onLineList;
    }

    @AllArgsConstructor
    @Data
    class CameraStatus {

        /**
         * 设备号
         */
        private String deviceSerial;

        /**
         * 通道号
         */
        private Integer channelNo;
    }
}

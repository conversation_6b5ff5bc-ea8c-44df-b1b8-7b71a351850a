{"doc": " <AUTHOR>\n @date 2025/5/18 15:36\n @Description 摄像头定时任务处理类\n @Version 1.0\n", "fields": [{"name": "JOB_MAP", "doc": " 定时任务对应摄像头\n"}], "enumConstants": [], "methods": [{"name": "jobExecute", "paramTypes": ["com.aizuda.snailjob.client.job.core.dto.JobArgs"], "doc": " 抓拍核心\n\n @param jobArgs\n @return\n"}, {"name": "uploadHazRecord", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String", "java.lang.Long"], "doc": " 创建隐患分析任务\n\n @param projectId   项目id\n @param itemId      分部分析id\n @param description 隐患描述\n @param imageOssId  截图存储id\n @return\n"}, {"name": "pushAiResolver", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksBo", "java.lang.String"], "doc": " @param bo\n @param imageUrl\n @return\n"}, {"name": "registerJob", "paramTypes": ["org.dromara.monito.domain.DeviceMonito"], "doc": " 注册任务\n\n @param deviceMonito\n"}, {"name": "registerJobAndStart", "paramTypes": ["org.dromara.monito.domain.DeviceMonito"], "doc": " 注册任务\n\n @param deviceMonito\n"}, {"name": "unloadAndStop", "paramTypes": ["java.lang.Long"], "doc": " 卸载任务\n\n @param jobId\n"}], "constructors": []}
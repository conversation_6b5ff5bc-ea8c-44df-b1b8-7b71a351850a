<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.special.mapper.SpecialEquipmentMapper">

    <select id="getAuthorityList" resultType="org.dromara.special.domain.vo.SpecialEquipmentVo">
        SELECT DISTINCT(authority) FROM special_equipment where authority is not null
    </select>

</mapper>

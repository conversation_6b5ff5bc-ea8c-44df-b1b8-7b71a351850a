package org.dromara.facility.domain;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 扬尘数据对象 jl_dust_real
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("jl_dust_real")
public class JlDustReal {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 扬尘设备唯一编号
     */
    private String mn;

    /**
     * 设备时间
     */
    private Date datatime;

    /**
     * 噪音平均值
     */
    private Long b03Avg;

    /**
     * 噪音传感器状态
     */
    private String b03Flag;

    /**
     * pm2.5
     */
    private Long pm25Avg;

    /**
     * pm2.5传感器状态
     */
    private String pm25Flag;

    /**
     * pm10
     */
    private Long pm10Avg;

    /**
     * pm10传感器状态
     */
    private String pm10Flag;

    /**
     * 风速
     */
    private Long w02Avg;

    /**
     * 风速传感器状态
     */
    private String w02Flag;

    /**
     * 风向
     */
    private Long w01Avg;

    /**
     * 风向传感器状态
     */
    private String w01Flag;

    /**
     * 温度
     */
    private Long t01Avg;

    /**
     * 温度传感器状态
     */
    private String t01Flag;

    /**
     * 湿度
     */
    private Long h01Avg;

    /**
     * 湿度传感器状态
     */
    private String h01Flag;

    /**
     * 总悬浮微粒
     */
    private Long tspAvg;

    /**
     * 总悬浮微粒传感器状态
     */
    private String tspFlag;

    /**
     * 臭氧
     */
    private Long o3Avg;

    /**
     * 臭氧状态
     */
    private String o3Flag;

    /**
     * 一氧化碳
     */
    private Long coAvg;

    /**
     * 一氧化碳传感器状态
     */
    private String coFlag;

    /**
     * 二氧化碳
     */
    private Long so2Avg;

    /**
     * 二氧化碳传感器状态
     */
    private String so2Flag;

    /**
     * 二氧化氮
     */
    private Long no2Avg;

    /**
     * 二氧化氮传感器状态
     */
    private String no2Flag;

    /**
     * 大气压
     */
    private Long a01006Rtd;

    /**
     * 大气压传感器状态
     */
    private String a01006Flag;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

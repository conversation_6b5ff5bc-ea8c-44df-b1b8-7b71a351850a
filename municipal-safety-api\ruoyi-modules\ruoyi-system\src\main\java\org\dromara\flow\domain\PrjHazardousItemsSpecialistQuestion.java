package org.dromara.flow.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 省厅自动工单专家建议对象 prj_hazardous_items_specialist_question
 *
 * <AUTHOR> Li
 * @date 2025-06-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prj_hazardous_items_specialist_question")
public class PrjHazardousItemsSpecialistQuestion extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 专家名称
     */
    private String name;

    /**
     * 专家意见
     */
    private String detail;

    /**
     * 问题id(ai_haz_analysis_tasks_result.id)
     */
    private Long resultId;

    /**
     * 省厅工单数据id(prj_hazardous_items_specialist.id)
     */
    private Long specialist;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;
}

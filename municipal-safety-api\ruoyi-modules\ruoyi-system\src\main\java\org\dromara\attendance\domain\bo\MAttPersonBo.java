package org.dromara.attendance.domain.bo;

import org.dromara.attendance.domain.MAttPerson;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * attPerson业务对象 m_att_person
 *
 * <AUTHOR> Li
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MAttPerson.class, reverseConvertGenerate = false)
public class MAttPersonBo extends BaseEntity {

    /**
     * 设备id
     */
    @NotNull(message = "设备id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 人员id
     */
    private Long personId;

    /**
     * 设备id
     */
    private Long snId;


}

{"doc": " 实测实量视图对象 quality_measurement\n\n <AUTHOR>\n @date 2024-01-01\n", "fields": [{"name": "measurementId", "doc": " 测量ID\n"}, {"name": "measurementTime", "doc": " 测量时间\n"}, {"name": "measurementItem", "doc": " 测量事项\n"}, {"name": "deviceId", "doc": " 设备ID\n"}, {"name": "deviceName", "doc": " 设备名称\n"}, {"name": "deviceCode", "doc": " 设备编号\n"}, {"name": "measurementResult", "doc": " 测量结果\n"}, {"name": "isCompliant", "doc": " 是否合规\n"}, {"name": "projectName", "doc": " 项目名称\n"}, {"name": "measurementLocation", "doc": " 测量位置\n"}, {"name": "standardValue", "doc": " 标准值\n"}, {"name": "deviationValue", "doc": " 偏差值\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": " 测量人员\n"}, {"name": "isHazardMarked", "doc": " 是否标记隐患\n"}, {"name": "hazardDescription", "doc": " 隐患描述\n"}, {"name": "status", "doc": " 状态\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "createBy", "doc": " 创建人\n"}, {"name": "createByName", "doc": " 创建人账号\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "updateBy", "doc": " 更新人\n"}, {"name": "updateByName", "doc": " 更新人账号\n"}], "enumConstants": [], "methods": [], "constructors": []}
package org.dromara.projects.controller;

import java.util.List;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.projects.domain.bo.AnalyseResultBo;
import org.dromara.projects.domain.bo.PrjConstructionPlansBo;
import org.dromara.projects.domain.vo.PrjConstructionPlansVo;
import org.dromara.projects.service.IPrjConstructionPlansService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;

/**
 * [项目管理] 存储危大工程专项施工方案信息及其审批状态
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/constructionPlans")
public class PrjConstructionPlansController extends BaseController {

    private final IPrjConstructionPlansService prjConstructionPlansService;

    /**
     * 查询[项目管理] 存储危大工程专项施工方案信息及其审批状态列表
     */
    @GetMapping("/list")
    public TableDataInfo<PrjConstructionPlansVo> list(PrjConstructionPlansBo bo, PageQuery pageQuery) {
        return prjConstructionPlansService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出[项目管理] 存储危大工程专项施工方案信息及其审批状态列表
     */
    @SaCheckPermission("system:constructionPlans:export")
    @Log(title = "[项目管理] 存储危大工程专项施工方案信息及其审批状态", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PrjConstructionPlansBo bo, HttpServletResponse response) {
        List<PrjConstructionPlansVo> list = prjConstructionPlansService.queryList(bo);
        ExcelUtil.exportExcel(list, "[项目管理] 存储危大工程专项施工方案信息及其审批状态", PrjConstructionPlansVo.class, response);
    }

    /**
     * 获取[项目管理] 存储危大工程专项施工方案信息及其审批状态详细信息
     *
     * @param planId 主键
     */
    @SaCheckPermission("system:constructionPlans:query")
    @GetMapping("/{planId}")
    public R<PrjConstructionPlansVo> getInfo(@NotNull(message = "主键不能为空")
                                             @PathVariable Long planId) {
        return R.ok(prjConstructionPlansService.queryById(planId));
    }

    /**
     * 新增[项目管理] 存储危大工程专项施工方案信息及其审批状态
     */
    @Log(title = "[项目管理] 存储危大工程专项施工方案信息及其审批状态", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PrjConstructionPlansBo bo) {
        return toAjax(prjConstructionPlansService.insertByBo(bo));
    }

    /**
     * 修改[项目管理] 存储危大工程专项施工方案信息及其审批状态
     */
    @SaCheckPermission("system:constructionPlans:edit")
    @Log(title = "[项目管理] 存储危大工程专项施工方案信息及其审批状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PrjConstructionPlansBo bo) {
        return toAjax(prjConstructionPlansService.updateByBo(bo));
    }

    /**
     * 删除[项目管理] 存储危大工程专项施工方案信息及其审批状态
     *
     * @param planIds 主键串
     */
    @SaCheckPermission("system:constructionPlans:remove")
    @Log(title = "[项目管理] 存储危大工程专项施工方案信息及其审批状态", businessType = BusinessType.DELETE)
    @DeleteMapping("/{planIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] planIds) {
        return toAjax(prjConstructionPlansService.deleteWithValidByIds(List.of(planIds), true));
    }

    /**
     * 接收文档分析回调接口
     *
     * @param resultBo
     * @return
     */
    @SaIgnore
    @PostMapping("/analyseResult")
    public R<Void> analyseResult(@RequestBody AnalyseResultBo resultBo) {
        prjConstructionPlansService.analyse(resultBo);
        return R.ok();
    }
}

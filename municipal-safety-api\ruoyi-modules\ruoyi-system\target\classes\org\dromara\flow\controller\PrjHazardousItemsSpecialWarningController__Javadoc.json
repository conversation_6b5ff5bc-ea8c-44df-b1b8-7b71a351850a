{"doc": " 特殊预警\n\n <AUTHOR>\n @date 2025-06-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialWarningBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询特殊预警列表\n"}, {"name": "export", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialWarningBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出特殊预警列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取特殊预警详细信息\n\n @param warningId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialWarningBo"], "doc": " 新增特殊预警\n"}, {"name": "edit", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialWarningBo"], "doc": " 修改特殊预警\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除特殊预警\n\n @param warningIds 主键串\n"}], "constructors": []}
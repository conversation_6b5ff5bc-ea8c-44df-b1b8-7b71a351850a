package org.dromara.facility.service;

import org.dromara.facility.domain.vo.LnSmokeVo;
import org.dromara.facility.domain.bo.LnSmokeBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 绿能烟感Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
public interface ILnSmokeService extends BaseFacilityHandle {

    /**
     * 查询绿能烟感
     *
     * @param id 主键
     * @return 绿能烟感
     */
    LnSmokeVo queryById(Long id);

    /**
     * 分页查询绿能烟感列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能烟感分页列表
     */
    TableDataInfo<LnSmokeVo> queryPageList(LnSmokeBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的绿能烟感列表
     *
     * @param bo 查询条件
     * @return 绿能烟感列表
     */
    List<LnSmokeVo> queryList(LnSmokeBo bo);

    /**
     * 新增绿能烟感
     *
     * @param bo 绿能烟感
     * @return 是否新增成功
     */
    Boolean insertByBo(LnSmokeBo bo);

    /**
     * 修改绿能烟感
     *
     * @param bo 绿能烟感
     * @return 是否修改成功
     */
    Boolean updateByBo(LnSmokeBo bo);

    /**
     * 校验并批量删除绿能烟感信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

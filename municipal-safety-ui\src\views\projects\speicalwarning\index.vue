<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="预警类型" prop="reasonType">
              <el-select v-model="queryParams.reasonType" placeholder="请选择预警类型" clearable>
                <el-option v-for="dict in special_warning_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <el-table v-loading="loading" :data="specialwarningList">
        <el-table-column type="index" width="50" />
        <el-table-column label="项目名称" align="center" prop="projectName">
          <template #default="scope">
            <div class="prodetail" style="cursor: pointer" @click="handleViewDetail(scope.row)">{{ scope.row.projectName
            }}</div>
          </template>
        </el-table-column>
        <el-table-column label="工程名称" align="center" prop="itemName" />
        <el-table-column label="预警原因" align="center" prop="reason" />
        <el-table-column label="预警类型" align="center" prop="reasonType" width="100px">
          <template #default="scope">
            <dict-tag :options="special_warning_type" :value="scope.row.reasonType" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" />

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="整改流程详情" placement="top">
              <el-button link type="primary" icon="Tickets" @click="handleApprovalRecord(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="派遣审查" placement="top">
              <el-button link type="primary" icon="Connection" @click="handleDispatchReview(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="View" @click="handleViewDetail(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 项目详情弹窗 -->
    <el-dialog title="项目详情" v-model="isDetail" width="90vw" style="height: 90vh; overflow-y: auto" append-to-body>
      <div v-if="detailForm">
        <el-card style="margin-bottom: 10px">
          <div class="basicinfo">
            <!-- 基本信息 -->
            <div class="basicinfo-left">
              <div class="basicinfotitle">
                <div class="gang"></div>
                <div class="basicinfoname">{{ detailForm.projectName }}</div>
              </div>
              <el-descriptions label-width="140px" :column="3" border>
                <el-descriptions-item label="项目编码">{{ detailForm.projectCode }}</el-descriptions-item>
                <el-descriptions-item label="工程概况" :span="2">{{ detailForm.projectOverview }}</el-descriptions-item>
                <el-descriptions-item label="施工许可证编号">{{ detailForm.constructionPermitNo }}</el-descriptions-item>
                <el-descriptions-item label="占地面积" v-if="detailForm.siteArea">{{ detailForm.siteArea }}
                  平方米</el-descriptions-item>
                <el-descriptions-item label="预算投资总额" v-if="detailForm.budgetTotal">{{ detailForm.budgetTotal }}
                  万元</el-descriptions-item>
                <el-descriptions-item label="项目位置地图">
                  {{ detailForm.lola }}
                  <el-button v-if="detailForm.lola" type="primary" link @click="viewMap(detailForm.lola)">查看</el-button>
                </el-descriptions-item>
                <el-descriptions-item label="所在地区">
                  {{ detailForm.provinceName || '-' }} {{ detailForm.cityName ? '/ ' + detailForm.cityName : '' }}
                  {{ detailForm.districtName ? '/ ' + detailForm.districtName : '' }}
                </el-descriptions-item>
                <el-descriptions-item label="详细地址">{{ detailForm.locationDetail || '-' }}</el-descriptions-item>
                <el-descriptions-item label="项目状态" v-if="detailForm.status">
                  <dict-tag :options="prj_projects_status" :value="detailForm.status" />
                </el-descriptions-item>
                <el-descriptions-item label="计划工期">
                  {{ parseTime(detailForm.startDate, '{y}-{m}-{d}') || '-' }} 至 {{ parseTime(detailForm.plannedEndDate,
                    '{y}-{m}-{d}') || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="实际工期" v-if="detailForm.actualStartDate">
                  {{ parseTime(detailForm.actualStartDate, '{y}-{m}-{d}') || '-' }} 至 {{
                    parseTime(detailForm.actualEndDate,
                      '{y}-{m}-{d}') || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="施工许可证扫描件" v-if="detailForm.constructionPermitDocId">
                  <div @click="viewfile(detailForm.constructionPermitDocId)" style="cursor: pointer; color: #409eff">
                    点击查看
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="所属监督站">{{ detailForm.supervisingQsOrgName || '-' }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-card>

        <!-- 参建单位 -->
        <el-card style="margin-bottom: 10px">
          <div slot="header">
            <div class="punits">
              <div class="basicinfotitle">
                <div class="gang"></div>
                <div class="basicinfoname">参建单位</div>
              </div>
            </div>
          </div>
          <!-- 单位信息 -->
          <el-table stripe :data="detailForm.enterpriseList" border style="width: 100%">
            <el-table-column prop="enterpriseType" label="企业类型" width="180">
              <template #default="scope">
                <div v-for="item in enterprise_type" :key="item.value">
                  <span v-if="item.value === scope.row.enterpriseType">{{ item.label }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="enterpriseName" label="单位名称" min-width="180" />
            <el-table-column prop="unifiedSocialCreditCode" label="统一社会信用代码" />
            <el-table-column prop="legalRepresentative" label="法人代表" />
            <el-table-column prop="officePhone" label="办公电话" min-width="120" />
          </el-table>
        </el-card>
      </div>
    </el-dialog>
    <approvalRecord ref="approvalRecordRef" />
    <!-- 人工复检和派遣审查组件 -->
    <ManualDispatch ref="ManualDispatchRef" />
  </div>
</template>

<script setup name="OperationPersonnel" lang="ts">
import {
  listHazardousItemsSpecialWarning
} from '@/api/specialWarning/index';
import { HazardousItemsSpecialWarningVO, HazardousItemsSpecialWarningForm, HazardousItemsSpecialWarningQuery } from '@/api/specialWarning/types';
import { Prj_projectsForm } from '@/api/projects/prj_projects/types';
import { listByIds } from '@/api/system/oss';
import ApprovalRecord from '@/components/Process/approvalRecord.vue';
import ManualDispatch from '@/components/ManualDispatch/ManualDispatch.vue';

import { useRoute } from 'vue-router';
import { getPrj_projects } from '@/api/projects/prj_projects';

const route = useRoute();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { special_warning_type } = toRefs<any>(proxy?.useDict('special_warning_type'));
const { enterprise_type } = toRefs<any>(proxy?.useDict('enterprise_type'));
const { prj_projects_status } = toRefs<any>(proxy?.useDict('prj_projects_status'));

const specialwarningList = ref<HazardousItemsSpecialWarningVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();

const initFormData = {
  warningId: undefined,
  taskId: undefined,
  reason: undefined,
  reasonType: undefined
};
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    reasonType: null
  },
});

const { queryParams } = toRefs(data);

// 详情
let isDetail = ref(false);
// 控制派遣弹框的变量
const ManualDispatchRef = ref<InstanceType<typeof ManualDispatch>>();

const detailForm = reactive<Prj_projectsForm>({});

/** 派遣审查点击事件 */
const handleDispatchReview = (row: HazardousItemsSpecialWarningVO) => {
  if (ManualDispatchRef.value) {
    // 调用子组件的方法，传递当前行数据
    ManualDispatchRef.value.handleDispatchReview(row);
  }
};
/** 查看详情按钮操作 */
const handleViewDetail = async (row: HazardousItemsSpecialWarningVO) => {

  isDetail.value = true;
  getPrj_projects(row.projectId).then(
    (res) => {
      isDetail.value = true;
      Object.assign(detailForm, res.data);
    },
    (error) => {
      proxy?.$modal.msgError('获取项目详情失败');
    }
  );
  //   loading.value = true;
};

/** 查看地图 */
const viewMap = (lola: string) => {
  if (lola) {
    window.open(`https://uri.amap.com/marker?position=${lola}`, '_blank');
  }
};

let fileurl = ref('');
const viewfile = (id) => {
  listByIds(id).then((res) => {
    fileurl.value = res.data[0].url;
    window.open(fileurl.value);
  });
};
/** 查询特殊预警信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listHazardousItemsSpecialWarning(queryParams.value);
  specialwarningList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {

  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};
//审批记录组件
const approvalRecordRef = ref<InstanceType<typeof ApprovalRecord>>();

const handleApprovalRecord = (row) => {

  approvalRecordRef.value.init(row.businessId);
};


onMounted(() => {
  getList();
});

</script>
<style lang="scss" scoped>
.basicinfo {
  display: flex;
  align-items: center;

  .ercode {
    text-align: center;
    margin-right: 40px;
  }

  .basicinfo-left {
    flex: 1;
  }
}

.basicinfotitle {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .gang {
    height: 15px;
    width: 3px;
    background-color: #409eff;
    border-radius: 6px;
    margin-right: 5px;
  }

  .basicinfoname {
    font-weight: 600;
  }
}

/* 固定标签宽度并禁止换行 */
.el-descriptions-item__label {
  width: 120px !important;
  /* 根据需求调整 */
  white-space: nowrap;
  /* 禁止标签换行 */
  text-overflow: ellipsis;
  /* 溢出显示省略号 */
  overflow: hidden;
}

/* 内容区域允许换行 */
.el-descriptions-item__content {
  word-break: break-word;
  /* 长文本自动换行 */
}

.el-descriptions-item__label {
  flex-shrink: 0;
  /* 禁止标签收缩 */
}

/* ::v-deep(.el-upload-list__item) {
  border: none !important;
} */
.prodetail {
  border-bottom: 1px solid rgba(64, 158, 255, 0);
}

.prodetail:hover {
  color: #409eff;
  border-bottom: 1px solid rgb(64, 158, 255);
}

.normalefficacy {
  background-color: rgb(64, 158, 255);
  margin-right: 6px;
}

.loseefficacy {
  background-color: rgb(255, 0, 0);
  margin-right: 6px;
}

.detailinfo {
  display: flex;
  align-items: center;
}

.absence {
  background-color: rgb(248, 146, 3);
  margin-right: 6px;
}

.dian {
  height: 10px;
  width: 10px;
  border-radius: 50%;
}

.Calendarselect {
  position: absolute;
  right: 200px;
  top: 11px;
}

::v-deep .el-calendar__body {
  height: 73vh !important;
}

::v-deep .el-card__body {
  max-height: 90%;
  overflow: auto;
}

:deep(.el-dialog__body) {
  max-height: 80% !important;
  overflow: hidden !important;
}
</style>

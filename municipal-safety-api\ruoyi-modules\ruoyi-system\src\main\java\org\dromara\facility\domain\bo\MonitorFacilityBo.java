package org.dromara.facility.domain.bo;

import org.dromara.facility.domain.MonitorFacility;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 监测设备业务对象 monitor_facility
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MonitorFacility.class, reverseConvertGenerate = false)
public class MonitorFacilityBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 设备类型
     */
    @NotBlank(message = "设备类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deviceType;

    /**
     * 生产厂家
     */
    @NotBlank(message = "生产厂家不能为空", groups = { AddGroup.class, EditGroup.class })
    private String manufacturers;

    /**
     * 数据来源
     */
    private String dataSources;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 工程id
     */
    private Long itemId;

    /**
     * 备注
     */
    private String remark;
}

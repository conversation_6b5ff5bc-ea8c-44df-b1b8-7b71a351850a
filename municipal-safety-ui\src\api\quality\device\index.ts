import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DeviceForm, DeviceQuery, DeviceVO,  FileUploadResponse } from './types';

/**
 * 查询设备管理列表
 * @param query 查询参数
 */
export const listDevice = (query: DeviceQuery): AxiosPromise<DeviceVO[]> => {
  return request({
    url: '/quality/device/list',
    method: 'get',
    params: query
  });
};

/**
 * 导出设备管理列表
 * @param query 查询参数
 */
export const exportDevice = (query: DeviceQuery) => {
  return request({
    url: '/quality/device/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  });
};

/**
 * 获取设备管理详细信息
 * @param deviceId 设备ID
 */
export const getDevice = (deviceId: string | number): AxiosPromise<DeviceVO> => {
  return request({
    url: '/quality/device/' + deviceId,
    method: 'get'
  });
};

/**
 * 新增设备管理
 * @param data 设备数据
 */
export const addDevice = (data: DeviceForm) => {
  return request({
    url: '/quality/device',
    method: 'post',
    data: data
  });
};

/**
 * 修改设备管理
 * @param data 设备数据
 */
export const updateDevice = (data: DeviceForm) => {
  return request({
    url: '/quality/device',
    method: 'put',
    data: data
  });
};

/**
 * 删除设备管理
 * @param deviceId 设备ID
 */
export const delDevice = (deviceId: Array<string | number> | string | number) => {
  return request({
    url: '/quality/device/' + deviceId,
    method: 'delete'
  });
};

/**
 * 生成设备编号
 */
export const generateDeviceCode = (deviceType: string) => {
  return request({
    url: '/quality/device/generateCode/' + deviceType,
    method: 'get'
  });
};

/**
 * 校验设备编号是否唯一
 * @param deviceCode 设备编号
 */
export const checkDeviceCodeUnique = (deviceCode: string) => {
  return request({
    url: '/quality/device/checkCodeUnique',
    method: 'get',
    params: { deviceCode }
  });
};

/**
 * 上传设备图片
 * @param data 图片文件
 */
export const uploadDeviceImage = (data: FormData): AxiosPromise<FileUploadResponse> => {
  return request({
    url: '/quality/device/uploadImage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

/**
 * 上传使用说明书
 * @param data 文件
 */
export const uploadManual = (data: FormData): AxiosPromise<FileUploadResponse> => {
  return request({
    url: '/quality/device/uploadManual',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

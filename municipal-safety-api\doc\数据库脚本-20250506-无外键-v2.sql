-- -----------------------------------------------------
-- 危大工程监管系统数据库 Schema 创建脚本 (无外键约束 - 带模块前缀 - 完整版)
-- 版本: 1.3
-- 创建日期: 2025-05-06
-- 注意: 此版本移除了所有外键约束
-- -----------------------------------------------------

-- 设置会话变量
SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='TRADITIONAL,ALLOW_INVALID_DATES';

-- 设置字符集
SET NAMES utf8mb4;

-- =============================================
-- 核心系统 (sys_)
-- =============================================

-- -----------------------------------------------------
-- 表 `sys_organizations` (组织机构表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `sys_organizations` ;

CREATE TABLE IF NOT EXISTS `sys_organizations` (
  `org_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '组织机构ID',
  `org_name` VARCHAR(255) NOT NULL COMMENT '组织机构名称',
  `org_type` VARCHAR(50) NOT NULL COMMENT '组织机构类型 (例如: CLIENT[建设方], CONSTRUCTION[施工方], SUPERVISION[监理], GOV_MINISTRY[部级监管], GOV_PROVINCE[省级监管], GOV_CITY[市级监管], GOV_DISTRICT[区县级监管/质监站], DESIGN[设计院], SURVEY[勘察单位], EXPERT_ORG[专家库管理单位])',
  `org_code` VARCHAR(100) NULL DEFAULT NULL UNIQUE COMMENT '组织机构代码 (例如: 统一社会信用代码)',
  `address` TEXT NULL DEFAULT NULL COMMENT '地址',
  `contact_person` VARCHAR(100) NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` VARCHAR(50) NULL DEFAULT NULL COMMENT '联系电话',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`org_id`),
  UNIQUE INDEX `org_code_UNIQUE` (`org_code` ASC))
ENGINE = InnoDB
COMMENT = '[核心系统] 存储参与系统的各组织机构信息';


-- -----------------------------------------------------
-- 表 `sys_roles` (角色表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `sys_roles` ;

CREATE TABLE IF NOT EXISTS `sys_roles` (
  `role_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` VARCHAR(100) NOT NULL COMMENT '角色名称 (例如: 系统管理员, 建设单位用户, 施工单位用户, 监理工程师, 政府监管人员, 专家)',
  `role_key` VARCHAR(100) NOT NULL COMMENT '角色权限关键字 (例如: admin, client, construction, supervisor, gov_district, expert)',
  `description` TEXT NULL DEFAULT NULL COMMENT '角色描述',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`role_id`),
  UNIQUE INDEX `role_name_UNIQUE` (`role_name` ASC),
  UNIQUE INDEX `role_key_UNIQUE` (`role_key` ASC))
ENGINE = InnoDB
COMMENT = '[核心系统] 定义系统角色及其权限标识';


-- -----------------------------------------------------
-- 表 `sys_users` (用户表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `sys_users` ;

CREATE TABLE IF NOT EXISTS `sys_users` (
  `user_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` VARCHAR(100) NOT NULL COMMENT '登录用户名',
  `password_hash` VARCHAR(255) NOT NULL COMMENT '密码哈希值',
  `full_name` VARCHAR(100) NULL DEFAULT NULL COMMENT '用户姓名',
  `org_id` BIGINT NULL DEFAULT NULL COMMENT '所属组织机构ID (逻辑外键至 sys_dept.dept_id)',
  `role_id` BIGINT NULL DEFAULT NULL COMMENT '主要角色ID (逻辑外键至 sys_roles.role_id)',
  `phone_number` VARCHAR(50) NULL DEFAULT NULL UNIQUE COMMENT '手机号码',
  `email` VARCHAR(100) NULL DEFAULT NULL UNIQUE COMMENT '电子邮箱',
  `avatar_url` VARCHAR(255) NULL DEFAULT NULL COMMENT '用户头像URL',
  `status` CHAR(1) NOT NULL DEFAULT '0' COMMENT '用户状态 (0=正常, 1=停用)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`user_id`),
  UNIQUE INDEX `username_UNIQUE` (`username` ASC),
  UNIQUE INDEX `phone_number_UNIQUE` (`phone_number` ASC),
  UNIQUE INDEX `email_UNIQUE` (`email` ASC),
  INDEX `idx_sys_users_org_id` (`org_id` ASC),
  INDEX `idx_sys_users_role_id` (`role_id` ASC))
ENGINE = InnoDB
COMMENT = '[核心系统] 存储系统用户账号信息';

-- -----------------------------------------------------
-- 表 `sys_documents` (文档表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `sys_documents` ;

CREATE TABLE IF NOT EXISTS `sys_documents` (
  `document_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `project_id` BIGINT NULL DEFAULT NULL COMMENT '关联的项目ID (逻辑外键至 prj_projects.project_id)',
  `related_entity_type` VARCHAR(50) NULL DEFAULT NULL COMMENT '关联实体类型',
  `related_entity_id` BIGINT NULL DEFAULT NULL COMMENT '关联实体ID',
  `document_category` VARCHAR(100) NULL DEFAULT NULL COMMENT '文档类别',
  `file_name` VARCHAR(255) NOT NULL COMMENT '原始文件名',
  `file_path` VARCHAR(512) NOT NULL COMMENT '存储路径',
  `file_type` VARCHAR(50) NULL DEFAULT NULL COMMENT '文件MIME类型或扩展名',
  `file_size` BIGINT NULL DEFAULT NULL COMMENT '文件大小 (字节)',
  `upload_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间戳',
  `uploaded_by_user_id` BIGINT NULL DEFAULT NULL COMMENT '上传用户ID (逻辑外键至 sys_users.user_id)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`document_id`),
  INDEX `idx_sys_documents_project_id` (`project_id` ASC),
  INDEX `idx_sys_documents_uploaded_by_user_id` (`uploaded_by_user_id` ASC),
  INDEX `idx_related_entity` (`related_entity_type` ASC, `related_entity_id` ASC))
ENGINE = InnoDB
COMMENT = '[核心系统] 存储所有上传文档的元数据信息';


-- -----------------------------------------------------
-- 表 `sys_experts` (专家表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `sys_experts` ;

CREATE TABLE IF NOT EXISTS `sys_experts` (
  `expert_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '专家ID',
  `user_id` BIGINT NOT NULL UNIQUE COMMENT '关联的用户账号ID (逻辑外键至 sys_users.user_id)',
  `qualification_level` VARCHAR(100) NULL DEFAULT NULL COMMENT '职称/资格',
  `specialization` TEXT NULL DEFAULT NULL COMMENT '专业领域',
  `years_experience` INT NULL DEFAULT NULL COMMENT '相关专业工作年限',
  `employer_org_id` BIGINT NULL DEFAULT NULL COMMENT '工作单位组织机构ID (逻辑外键至 sys_dept.dept_id)',
  `cv_document_id` BIGINT NULL DEFAULT NULL COMMENT '简历文档ID (逻辑外键至 sys_documents.document_id)',
  `is_in_pool` TINYINT(1) NULL DEFAULT 1 COMMENT '是否当前在专家库中活跃 (0:否, 1:是)',
  `performance_record` TEXT NULL DEFAULT NULL COMMENT '历史业绩/评审记录备注',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`expert_id`),
  UNIQUE INDEX `user_id_UNIQUE` (`user_id` ASC),
  INDEX `idx_sys_experts_employer_org_id` (`employer_org_id` ASC),
  INDEX `idx_sys_experts_cv_document_id` (`cv_document_id` ASC))
ENGINE = InnoDB
COMMENT = '[核心系统] 存储系统中专家的详细信息';

-- -----------------------------------------------------
-- 表 `sys_certificates` (证书信息表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `sys_certificates`;

CREATE TABLE IF NOT EXISTS `sys_certificates` (
  `certificate_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '证书ID',
  `certificate_name` VARCHAR(255) NOT NULL COMMENT '证书名称 (如：企业资质证书、安全生产许可证、项目负责人安全生产考核合格证)',
  `certificate_type` VARCHAR(100) NOT NULL COMMENT '证书类型 (参考附件分类，如：CORP_QUALIFICATION[企业资质], SAFETY_PRODUCTION_LICENSE[安全生产许可], LEGAL_PERSON_SAFETY_CERT[法定代表人安全考核], PROJECT_MANAGER_SAFETY_CERT[项目负责人安全考核], ETC.)',
  `certificate_no` VARCHAR(255) NOT NULL COMMENT '证书编号',
  `issuing_authority` VARCHAR(255) NULL DEFAULT NULL COMMENT '发证机关',
  `issue_date` DATE NULL DEFAULT NULL COMMENT '发证日期',
  `expiry_date` DATE NULL DEFAULT NULL COMMENT '有效期至',
  `status` VARCHAR(50) NULL DEFAULT 'VALID' COMMENT '证书状态 (例如: VALID[有效], EXPIRED[已过期], REVOKED[已吊销])',
  `certificate_level` VARCHAR(100) NULL DEFAULT NULL COMMENT '资质等级 (针对企业资质证书)',
  `scope_of_qualification` TEXT NULL DEFAULT NULL COMMENT '资质范围 (针对企业资质证书)',
  `related_entity_type` VARCHAR(50) NOT NULL COMMENT '关联实体类型 (ORGANIZATION[组织机构], USER[人员], PROJECT_PERSONNEL[项目人员])',
  `related_entity_id` BIGINT NOT NULL COMMENT '关联实体ID',
  `document_id` BIGINT NULL DEFAULT NULL COMMENT '证书扫描件/电子版文档ID (逻辑外键至 sys_documents.document_id)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`certificate_id`),
  INDEX `idx_sys_certificates_related_entity` (`related_entity_type` ASC, `related_entity_id` ASC),
  INDEX `idx_sys_certificates_document_id` (`document_id` ASC),
  UNIQUE INDEX `uk_certificate_no_type` (`certificate_no` ASC, `certificate_type` ASC, `related_entity_id` ASC, `related_entity_type` ASC)
)
ENGINE = InnoDB
COMMENT = '[核心系统] 存储各类资质证书及考核合格证的详细信息';


-- =============================================
-- 项目管理 (prj_)
-- =============================================

-- -----------------------------------------------------
-- 表 `prj_projects` (项目表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `prj_projects` ;

CREATE TABLE IF NOT EXISTS `prj_projects` (
  `project_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `project_name` VARCHAR(255) NOT NULL COMMENT '项目名称',
  `project_code` VARCHAR(100) NULL DEFAULT NULL UNIQUE COMMENT '项目编码/标识',
  `project_overview` TEXT NULL DEFAULT NULL COMMENT '工程概况 (对应附件一.1)',
  `construction_permit_no` VARCHAR(255) NULL DEFAULT NULL COMMENT '施工许可证编号 (对应附件一.2)',
  `construction_permit_doc_id` BIGINT NULL DEFAULT NULL COMMENT '施工许可证扫描件文档ID (逻辑外键至 sys_documents.document_id)',
  `province_code` VARCHAR(20) NULL DEFAULT NULL COMMENT '省/直辖市编码',
  `province_name` VARCHAR(100) NULL DEFAULT NULL COMMENT '省/直辖市名称',
  `city_code` VARCHAR(20) NULL DEFAULT NULL COMMENT '市编码',
  `city_name` VARCHAR(100) NULL DEFAULT NULL COMMENT '市名称',
  `district_code` VARCHAR(20) NULL DEFAULT NULL COMMENT '区/县编码',
  `district_name` VARCHAR(100) NULL DEFAULT NULL COMMENT '区/县名称',
  `county_code` VARCHAR(20) NULL DEFAULT NULL COMMENT '乡镇/街道编码 (可选)',
  `county_name` VARCHAR(100) NULL DEFAULT NULL COMMENT '乡镇/街道名称 (可选)',
  `location_detail` TEXT NULL DEFAULT NULL COMMENT '详细地址',
  `status` VARCHAR(50) NULL DEFAULT 'PLANNING' COMMENT '项目状态',
  `start_date` DATE NULL DEFAULT NULL COMMENT '计划开工日期',
  `planned_end_date` DATE NULL DEFAULT NULL COMMENT '计划竣工日期',
  `actual_start_date` DATE NULL DEFAULT NULL COMMENT '实际开工日期',
  `actual_end_date` DATE NULL DEFAULT NULL COMMENT '实际竣工日期',
  `client_org_id` BIGINT NULL DEFAULT NULL COMMENT '建设单位ID (逻辑外键至 sys_dept.dept_id)',
  `construction_org_id` BIGINT NULL DEFAULT NULL COMMENT '施工总包单位ID (逻辑外键至 sys_dept.dept_id)',
  `supervision_org_id` BIGINT NULL DEFAULT NULL COMMENT '监理单位ID (逻辑外键至 sys_dept.dept_id)',
  `design_org_id` BIGINT NULL DEFAULT NULL COMMENT '设计单位ID (逻辑外键至 sys_dept.dept_id)',
  `survey_org_id` BIGINT NULL DEFAULT NULL COMMENT '勘察单位ID (逻辑外键至 sys_dept.dept_id)',
  `subcontractor_org_ids` JSON NULL DEFAULT NULL COMMENT '专业分包单位ID列表 (JSON数组，元素为 sys_organizations.org_id)',
  `project_manager_user_id` BIGINT NULL DEFAULT NULL COMMENT '施工单位项目负责人ID (逻辑外键至 sys_users.user_id)',
  `supervision_chief_eng_user_id` BIGINT NULL DEFAULT NULL COMMENT '监理单位总监ID (逻辑外键至 sys_users.user_id)',
  `safety_measures_fee_doc_id` BIGINT NULL DEFAULT NULL COMMENT '危大工程安全防护文明施工措施费财务凭证文档ID (对应附件三.1)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`project_id`),
  UNIQUE INDEX `project_code_UNIQUE` (`project_code` ASC),
  INDEX `idx_prj_projects_construction_permit_doc_id` (`construction_permit_doc_id` ASC),
  INDEX `idx_prj_projects_safety_measures_fee_doc_id` (`safety_measures_fee_doc_id` ASC),
  INDEX `idx_prj_projects_client_org_id` (`client_org_id` ASC),
  INDEX `idx_prj_projects_construction_org_id` (`construction_org_id` ASC),
  INDEX `idx_prj_projects_supervision_org_id` (`supervision_org_id` ASC),
  INDEX `idx_prj_projects_design_org_id` (`design_org_id` ASC),
  INDEX `idx_prj_projects_survey_org_id` (`survey_org_id` ASC),
  INDEX `idx_prj_projects_pm_user_id` (`project_manager_user_id` ASC),
  INDEX `idx_prj_projects_sce_user_id` (`supervision_chief_eng_user_id` ASC),
  INDEX `idx_prj_projects_province_code` (`province_code` ASC),
  INDEX `idx_prj_projects_city_code` (`city_code` ASC),
  INDEX `idx_prj_projects_district_code` (`district_code` ASC)
)
ENGINE = InnoDB
COMMENT = '[项目管理] 存储建设工程项目的详细信息，包括地区和许可证';


-- -----------------------------------------------------
-- 表 `prj_personnel` (项目人员表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `prj_personnel`;

CREATE TABLE IF NOT EXISTS `prj_personnel` (
  `project_personnel_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '项目人员关联ID',
  `project_id` BIGINT NOT NULL COMMENT '项目ID (逻辑外键至 prj_projects.project_id)',
  `user_id` BIGINT NOT NULL COMMENT '用户ID (逻辑外键至 sys_users.user_id)',
  `org_id` BIGINT NULL DEFAULT NULL COMMENT '该人员在项目中的所属单位ID (逻辑外键至 sys_dept.dept_id)',
  `role_on_project` VARCHAR(100) NOT NULL COMMENT '在本项目中的具体角色/岗位',
  `is_special_ops` TINYINT(1) NULL DEFAULT 0 COMMENT '是否特种作业人员 (0:否, 1:是)',
  `start_date_on_project` DATE NULL DEFAULT NULL COMMENT '进入项目日期',
  `end_date_on_project` DATE NULL DEFAULT NULL COMMENT '离开项目日期',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`project_personnel_id`),
  INDEX `idx_prj_personnel_project_id` (`project_id` ASC),
  INDEX `idx_prj_personnel_user_id` (`user_id` ASC),
  INDEX `idx_prj_personnel_org_id` (`org_id` ASC),
  UNIQUE INDEX `uk_project_user_role` (`project_id` ASC, `user_id` ASC, `role_on_project` ASC, `org_id` ASC)
)
ENGINE = InnoDB
COMMENT = '[项目管理] 关联用户到具体项目及其角色和所属单位';


-- -----------------------------------------------------
-- 表 `prj_hazardous_items` (危大工程清单表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `prj_hazardous_items` ;

CREATE TABLE IF NOT EXISTS `prj_hazardous_items` (
  `item_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '危大工程项ID',
  `project_id` BIGINT NOT NULL COMMENT '所属项目ID (逻辑外键至 prj_projects.project_id)',
  `item_name` VARCHAR(255) NOT NULL COMMENT '危大工程名称/描述',
  `hazardous_work_category` VARCHAR(100) NULL DEFAULT NULL COMMENT '危大工程类别',
  `scope_details` TEXT NULL DEFAULT NULL COMMENT '具体范围详情',
  `is_exceeding_scale` TINYINT(1) NOT NULL COMMENT '是否超规模 (0:否, 1:是)',
  `status` VARCHAR(50) NULL DEFAULT 'PLANNED' COMMENT '状态',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`item_id`),
  INDEX `idx_prj_hazardous_items_project_id` (`project_id` ASC))
ENGINE = InnoDB
COMMENT = '[项目管理] 列出项目内具体的危险性较大的分部分项工程';


-- -----------------------------------------------------
-- 表 `prj_construction_plans` (专项施工方案表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `prj_construction_plans` ;

CREATE TABLE IF NOT EXISTS `prj_construction_plans` (
  `plan_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '方案ID',
  `item_id` BIGINT NOT NULL COMMENT '危大工程项ID (逻辑外键至 prj_hazardous_items.item_id)',
  `plan_version` VARCHAR(50) NULL DEFAULT '1.0' COMMENT '方案版本号',
  `plan_document_id` BIGINT NOT NULL COMMENT '方案文档ID (逻辑外键至 sys_documents.document_id)',
  `submission_date` DATETIME NULL DEFAULT NULL COMMENT '方案提交日期',
  `review_status` VARCHAR(50) NULL DEFAULT 'PENDING' COMMENT '审核状态',
  `approved_by_user_id` BIGINT NULL DEFAULT NULL COMMENT '批准人ID (逻辑外键至 sys_users.user_id)',
  `approval_date` DATETIME NULL DEFAULT NULL COMMENT '批准日期',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`plan_id`),
  INDEX `idx_prj_plans_item_id` (`item_id` ASC),
  INDEX `idx_prj_plans_document_id` (`plan_document_id` ASC),
  INDEX `idx_prj_plans_approved_by_user_id` (`approved_by_user_id` ASC))
ENGINE = InnoDB
COMMENT = '[项目管理] 存储危大工程专项施工方案';


-- -----------------------------------------------------
-- 表 `prj_expert_reviews` (专家论证表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `prj_expert_reviews`;

CREATE TABLE IF NOT EXISTS `prj_expert_reviews` (
  `review_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '专家论证ID',
  `plan_id` BIGINT NOT NULL COMMENT '施工方案ID (逻辑外键至 prj_construction_plans.plan_id)',
  `review_date` DATETIME NOT NULL COMMENT '论证会议日期',
  `meeting_location` VARCHAR(255) NULL DEFAULT NULL COMMENT '会议地点',
  `conclusion` VARCHAR(50) NOT NULL COMMENT '论证结论 (通过, 修改后通过, 不通过)',
  `expert_opinion_summary` TEXT NULL DEFAULT NULL COMMENT '专家意见摘要/修改要求',
  `conflict_of_interest_warning` TEXT NULL DEFAULT NULL COMMENT '专家利害关系预警信息 (对应附件四.(二).1)',
  `report_document_id` BIGINT NULL DEFAULT NULL COMMENT '论证报告文档ID (逻辑外键至 sys_documents.document_id)',
  `convenor_user_id` BIGINT NULL DEFAULT NULL COMMENT '会议组织者ID (逻辑外键至 sys_users.user_id)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`review_id`),
  INDEX `idx_prj_expert_reviews_plan_id` (`plan_id` ASC),
  INDEX `idx_prj_expert_reviews_report_doc_id` (`report_document_id` ASC),
  INDEX `idx_prj_expert_reviews_convenor_user_id` (`convenor_user_id` ASC)
)
ENGINE = InnoDB
COMMENT = '[项目管理] 记录专项施工方案专家论证会议，包含利害关系预警';


-- -----------------------------------------------------
-- 表 `prj_expert_review_participants` (专家论证参与人员表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `prj_expert_review_participants` ;

CREATE TABLE IF NOT EXISTS `prj_expert_review_participants` (
  `participant_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '参与记录ID',
  `review_id` BIGINT NOT NULL COMMENT '专家论证会议ID (逻辑外键至 prj_expert_reviews.review_id)',
  `user_id` BIGINT NOT NULL COMMENT '参与者用户ID (逻辑外键至 sys_users.user_id)',
  `role_in_meeting` VARCHAR(100) NOT NULL COMMENT '会议中的角色',
  `is_attending_expert` TINYINT(1) NULL DEFAULT 0 COMMENT '是否评审专家 (0:否, 1:是)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`participant_id`),
  INDEX `idx_prj_expert_participants_review_id` (`review_id` ASC),
  INDEX `idx_prj_expert_participants_user_id` (`user_id` ASC),
  UNIQUE INDEX `uk_review_user` (`review_id` ASC, `user_id` ASC))
ENGINE = InnoDB
COMMENT = '[项目管理] 列出专家论证会议的参会人员';

-- -----------------------------------------------------
-- 表 `prj_acceptance` (危大工程验收信息表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `prj_acceptance`;

CREATE TABLE IF NOT EXISTS `prj_acceptance` (
  `acceptance_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '验收ID',
  `item_id` BIGINT NOT NULL COMMENT '关联的危大工程项ID (逻辑外键至 prj_hazardous_items.item_id)',
  `acceptance_date` DATE NULL DEFAULT NULL COMMENT '验收日期',
  `acceptance_result` VARCHAR(50) NULL DEFAULT NULL COMMENT '验收结果 (例如：合格, 不合格, 整改后合格)',
  `acceptance_personnel_info` JSON NULL DEFAULT NULL COMMENT '验收人员信息 (JSON对象，包含附件六中a,b,c各类人员姓名、单位、职务等)',
  `acceptance_report_doc_id` BIGINT NULL DEFAULT NULL COMMENT '危大工程验收表文档ID (逻辑外键至 sys_documents.document_id)',
  `remarks` TEXT NULL DEFAULT NULL COMMENT '备注',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`acceptance_id`),
  INDEX `idx_prj_acceptance_item_id` (`item_id` ASC),
  INDEX `idx_prj_acceptance_report_doc_id` (`acceptance_report_doc_id` ASC)
)
ENGINE = InnoDB
COMMENT = '[项目管理] 存储危大工程的验收信息';


-- =============================================
-- 监测与预警 (mon_)
-- =============================================

-- -----------------------------------------------------
-- 表 `mon_plans` (监测方案表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `mon_plans` ;

CREATE TABLE IF NOT EXISTS `mon_plans` (
  `monitoring_plan_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '监测方案ID',
  `item_id` BIGINT NOT NULL COMMENT '危大工程项ID (逻辑外键至 prj_hazardous_items.item_id)',
  `monitoring_org_id` BIGINT NULL DEFAULT NULL COMMENT '监测单位ID (逻辑外键至 sys_dept.dept_id)',
  `plan_document_id` BIGINT NOT NULL COMMENT '监测方案文档ID (逻辑外键至 sys_documents.document_id)',
  `monitoring_content` TEXT NULL DEFAULT NULL COMMENT '监测内容',
  `monitoring_frequency` VARCHAR(100) NULL DEFAULT NULL COMMENT '监测频率',
  `warning_thresholds` TEXT NULL DEFAULT NULL COMMENT '预警阈值',
  `start_date` DATE NULL DEFAULT NULL COMMENT '监测开始日期',
  `end_date` DATE NULL DEFAULT NULL COMMENT '监测结束日期',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`monitoring_plan_id`),
  INDEX `idx_mon_plans_item_id` (`item_id` ASC),
  INDEX `idx_mon_plans_org_id` (`monitoring_org_id` ASC),
  INDEX `idx_mon_plans_doc_id` (`plan_document_id` ASC))
ENGINE = InnoDB
COMMENT = '[监测与预警] 存储危大工程的第三方监测方案';


-- -----------------------------------------------------
-- 表 `mon_data` (监测数据表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `mon_data` ;

CREATE TABLE IF NOT EXISTS `mon_data` (
  `data_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '监测数据记录ID',
  `monitoring_plan_id` BIGINT NOT NULL COMMENT '监测方案ID (逻辑外键至 mon_plans.monitoring_plan_id)',
  `measurement_time` DATETIME NOT NULL COMMENT '测量时间戳',
  `sensor_id` VARCHAR(100) NULL DEFAULT NULL COMMENT '传感器或测点编号',
  `data_value` VARCHAR(255) NOT NULL COMMENT '测量值',
  `unit` VARCHAR(50) NULL DEFAULT NULL COMMENT '测量单位',
  `is_threshold_exceeded` TINYINT(1) NULL DEFAULT 0 COMMENT '是否超阈值 (0:否, 1:是)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`data_id`),
  INDEX `idx_mon_data_plan_id` (`monitoring_plan_id` ASC),
  INDEX `idx_measurement_time` (`measurement_time` ASC))
ENGINE = InnoDB
COMMENT = '[监测与预警] 存储实际的监测数据点';


-- -----------------------------------------------------
-- 表 `mon_warnings` (预警信息表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `mon_warnings`;

CREATE TABLE IF NOT EXISTS `mon_warnings` (
  `warning_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '预警ID',
  `project_id` BIGINT NOT NULL COMMENT '项目ID (逻辑外键至 prj_projects.project_id)',
  `warning_type` VARCHAR(50) NOT NULL COMMENT '预警类型 (例如: AI_RISK[AI风险], ATTENDANCE[考勤异常], MONITORING_THRESHOLD[监测超阈值], MANUAL_REPORT[人工上报], PLAN_DEFECT[方案缺陷])',
  `source_type` VARCHAR(50) NULL DEFAULT NULL COMMENT '来源类型',
  `source_id` VARCHAR(100) NULL DEFAULT NULL COMMENT '来源标识 (如方案ID)',
  `trigger_time` DATETIME NOT NULL COMMENT '预警触发时间戳',
  `related_item_id` BIGINT NULL DEFAULT NULL COMMENT '危大工程项ID (逻辑外键至 prj_hazardous_items.item_id)',
  `related_plan_id` BIGINT NULL DEFAULT NULL COMMENT '关联的施工方案ID (逻辑外键至 prj_construction_plans.plan_id, 针对方案缺陷预警)',
  `related_personnel_id` BIGINT NULL DEFAULT NULL COMMENT '项目人员ID (逻辑外键至 prj_personnel.project_personnel_id)',
  `related_equipment_id` BIGINT NULL DEFAULT NULL COMMENT '特种设备ID (逻辑外键至 insp_special_equipment.equipment_id)',
  `severity_level` VARCHAR(50) NULL DEFAULT 'MEDIUM' COMMENT '严重级别',
  `description` TEXT NOT NULL COMMENT '预警/风险描述 (可包含AI对比方案缺陷的具体内容)',
  `status` VARCHAR(50) NULL DEFAULT 'NEW' COMMENT '状态',
  `archived_flag` TINYINT(1) NULL DEFAULT 0 COMMENT '是否已存档 (0:否, 1:是)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`warning_id`),
  INDEX `idx_mon_warnings_project_id` (`project_id` ASC),
  INDEX `idx_mon_warnings_related_item_id` (`related_item_id` ASC),
  INDEX `idx_mon_warnings_related_plan_id` (`related_plan_id` ASC),
  INDEX `idx_mon_warnings_related_personnel_id` (`related_personnel_id` ASC),
  INDEX `idx_mon_warnings_related_equipment_id` (`related_equipment_id` ASC),
  INDEX `idx_trigger_time` (`trigger_time` ASC),
  INDEX `idx_warning_status` (`status` ASC))
ENGINE = InnoDB
COMMENT = '[监测与预警] 记录各类预警和警报信息，包括方案缺陷预警';


-- -----------------------------------------------------
-- 表 `mon_attendance_records` (考勤记录表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `mon_attendance_records` ;

CREATE TABLE IF NOT EXISTS `mon_attendance_records` (
  `record_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '考勤记录ID',
  `project_id` BIGINT NOT NULL COMMENT '项目ID (逻辑外键至 prj_projects.project_id)',
  `project_personnel_id` BIGINT NOT NULL COMMENT '项目人员ID (逻辑外键至 prj_personnel.project_personnel_id)',
  `check_in_time` DATETIME NULL DEFAULT NULL COMMENT '签到时间戳',
  `check_out_time` DATETIME NULL DEFAULT NULL COMMENT '签出时间戳',
  `attendance_date` DATE NOT NULL COMMENT '考勤日期',
  `source` VARCHAR(50) NULL DEFAULT NULL COMMENT '记录来源',
  `is_abnormal` TINYINT(1) NULL DEFAULT 0 COMMENT '是否异常 (0:否, 1:是)',
  `abnormality_reason` TEXT NULL DEFAULT NULL COMMENT '异常原因',
  `related_warning_id` BIGINT NULL DEFAULT NULL COMMENT '关联预警ID (逻辑外键至 mon_warnings.warning_id)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`record_id`),
  INDEX `idx_mon_attendance_project_id` (`project_id` ASC),
  INDEX `idx_mon_attendance_personnel_id` (`project_personnel_id` ASC),
  INDEX `idx_mon_attendance_warning_id` (`related_warning_id` ASC),
  INDEX `idx_attendance_date` (`attendance_date` ASC))
ENGINE = InnoDB
COMMENT = '[监测与预警] 存储项目人员的考勤记录';


-- =============================================
-- 工单与整改 (wo_)
-- =============================================

-- -----------------------------------------------------
-- 表 `wo_orders` (工单表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `wo_orders` ;

CREATE TABLE IF NOT EXISTS `wo_orders` (
  `order_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '工单ID',
  `project_id` BIGINT NOT NULL COMMENT '项目ID (逻辑外键至 prj_projects.project_id)',
  `order_type` VARCHAR(50) NOT NULL COMMENT '工单类型',
  `related_warning_id` BIGINT NULL DEFAULT NULL COMMENT '关联预警ID (逻辑外键至 mon_warnings.warning_id)',
  `related_inspection_id` BIGINT NULL DEFAULT NULL COMMENT '关联巡检ID (逻辑外键至 insp_inspections.inspection_id)',
  `issued_by_org_id` BIGINT NULL DEFAULT NULL COMMENT '签发单位ID (逻辑外键至 sys_dept.dept_id)',
  `issued_by_user_id` BIGINT NULL DEFAULT NULL COMMENT '签发人ID (逻辑外键至 sys_users.user_id)',
  `issue_date` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '签发时间戳',
  `assigned_to_org_id` BIGINT NULL DEFAULT NULL COMMENT '处理单位ID (逻辑外键至 sys_dept.dept_id)',
  `assigned_to_user_id` BIGINT NULL DEFAULT NULL COMMENT '处理人ID (逻辑外键至 sys_users.user_id)',
  `expert_user_id` BIGINT NULL DEFAULT NULL COMMENT '指派专家用户ID (逻辑外键至 sys_users.user_id)',
  `description` TEXT NOT NULL COMMENT '任务/问题描述',
  `rectification_deadline` DATETIME NULL DEFAULT NULL COMMENT '整改截止日期',
  `status` VARCHAR(50) NULL DEFAULT 'ISSUED' COMMENT '状态',
  `priority` VARCHAR(50) NULL DEFAULT 'MEDIUM' COMMENT '优先级',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`order_id`),
  INDEX `idx_wo_orders_project_id` (`project_id` ASC),
  INDEX `idx_wo_orders_warning_id` (`related_warning_id` ASC),
  INDEX `idx_wo_orders_inspection_id` (`related_inspection_id` ASC),
  INDEX `idx_wo_orders_issued_org_id` (`issued_by_org_id` ASC),
  INDEX `idx_wo_orders_issued_user_id` (`issued_by_user_id` ASC),
  INDEX `idx_wo_orders_assigned_org_id` (`assigned_to_org_id` ASC),
  INDEX `idx_wo_orders_assigned_user_id` (`assigned_to_user_id` ASC),
  INDEX `idx_wo_orders_expert_user_id` (`expert_user_id` ASC),
  INDEX `idx_work_order_status` (`status` ASC))
ENGINE = InnoDB
COMMENT = '[工单与整改] 追踪调查、整改或专家复核的工单';


-- -----------------------------------------------------
-- 表 `wo_rectifications` (整改记录表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `wo_rectifications` ;

CREATE TABLE IF NOT EXISTS `wo_rectifications` (
  `rectification_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '整改记录ID',
  `work_order_id` BIGINT NOT NULL COMMENT '工单ID (逻辑外键至 wo_orders.order_id)',
  `submitted_by_user_id` BIGINT NULL DEFAULT NULL COMMENT '提交人ID (逻辑外键至 sys_users.user_id)',
  `submission_date` DATETIME NULL DEFAULT NULL COMMENT '整改提交日期',
  `rectification_description` TEXT NULL DEFAULT NULL COMMENT '整改措施描述',
  `completion_date` DATETIME NULL DEFAULT NULL COMMENT '整改完成日期',
  `review_status` VARCHAR(50) NULL DEFAULT 'PENDING_REVIEW' COMMENT '复核状态',
  `reviewed_by_user_id` BIGINT NULL DEFAULT NULL COMMENT '复核人ID (逻辑外键至 sys_users.user_id)',
  `review_date` DATETIME NULL DEFAULT NULL COMMENT '复核日期',
  `review_comments` TEXT NULL DEFAULT NULL COMMENT '复核意见',
  `is_overdue` TINYINT(1) NULL DEFAULT 0 COMMENT '是否超期 (0:否, 1:是)',
  `final_result` VARCHAR(50) NULL DEFAULT NULL COMMENT '最终结果',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`rectification_id`),
  INDEX `idx_wo_rectifications_work_order_id` (`work_order_id` ASC),
  INDEX `idx_wo_rectifications_submitted_user_id` (`submitted_by_user_id` ASC),
  INDEX `idx_wo_rectifications_reviewed_user_id` (`reviewed_by_user_id` ASC))
ENGINE = InnoDB
COMMENT = '[工单与整改] 追踪工单问题的整改过程和结果';


-- -----------------------------------------------------
-- 表 `wo_rectification_evidence` (整改证据表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `wo_rectification_evidence` ;

CREATE TABLE IF NOT EXISTS `wo_rectification_evidence` (
  `evidence_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '证据关联ID',
  `rectification_id` BIGINT NOT NULL COMMENT '整改记录ID (逻辑外键至 wo_rectifications.rectification_id)',
  `document_id` BIGINT NOT NULL COMMENT '证据文档ID (逻辑外键至 sys_documents.document_id)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`evidence_id`),
  INDEX `idx_wo_rect_evidence_rectification_id` (`rectification_id` ASC),
  INDEX `idx_wo_rect_evidence_document_id` (`document_id` ASC),
  UNIQUE INDEX `uk_rectification_document` (`rectification_id` ASC, `document_id` ASC))
ENGINE = InnoDB
COMMENT = '[工单与整改] 关联整改记录到支持性证据文档';


-- =============================================
-- 巡检与特种设备 (insp_)
-- =============================================

-- -----------------------------------------------------
-- 表 `insp_inspections` (巡检记录表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `insp_inspections` ;

CREATE TABLE IF NOT EXISTS `insp_inspections` (
  `inspection_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '巡检记录ID',
  `project_id` BIGINT NOT NULL COMMENT '项目ID (逻辑外键至 prj_projects.project_id)',
  `inspection_date` DATETIME NOT NULL COMMENT '巡检日期时间',
  `inspector_user_id` BIGINT NOT NULL COMMENT '巡检员ID (逻辑外键至 sys_users.user_id)',
  `inspector_role` VARCHAR(100) NULL DEFAULT NULL COMMENT '巡检员角色',
  `inspection_type` VARCHAR(50) NOT NULL COMMENT '巡检类型',
  `location_on_site` VARCHAR(255) NULL DEFAULT NULL COMMENT '检查部位',
  `findings` TEXT NULL DEFAULT NULL COMMENT '巡检发现 (对应附件五.1, .2, .3 中的监督记录、施工监测、安全巡视记录)',
  `rectification_status_desc` TEXT NULL DEFAULT NULL COMMENT '整改情况描述 (对应附件五.1, .2, .3 中的整改情况)',
  `related_work_order_id` BIGINT NULL DEFAULT NULL COMMENT '关联工单ID (逻辑外键至 wo_orders.order_id)',
  `report_document_id` BIGINT NULL DEFAULT NULL COMMENT '巡检记录/报告文档ID (逻辑外键至 sys_documents.document_id, 支持本地下载)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`inspection_id`),
  INDEX `idx_insp_inspections_project_id` (`project_id` ASC),
  INDEX `idx_insp_inspections_inspector_user_id` (`inspector_user_id` ASC),
  INDEX `idx_insp_inspections_work_order_id` (`related_work_order_id` ASC),
  INDEX `idx_insp_inspections_report_doc_id` (`report_document_id` ASC),
  INDEX `idx_inspection_date` (`inspection_date` ASC))
ENGINE = InnoDB
COMMENT = '[巡检与设备] 记录现场巡检详情及初步整改情况';


-- -----------------------------------------------------
-- 表 `insp_special_equipment` (特种设备表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `insp_special_equipment` ;

CREATE TABLE IF NOT EXISTS `insp_special_equipment` (
  `equipment_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '特种设备ID',
  `project_id` BIGINT NOT NULL COMMENT '项目ID (逻辑外键至 prj_projects.project_id)',
  `equipment_type` VARCHAR(100) NOT NULL COMMENT '设备类型',
  `equipment_code` VARCHAR(100) NULL DEFAULT NULL UNIQUE COMMENT '设备唯一编号',
  `model` VARCHAR(100) NULL DEFAULT NULL COMMENT '型号',
  `manufacturer` VARCHAR(100) NULL DEFAULT NULL COMMENT '生产厂家',
  `registration_info` TEXT NULL DEFAULT NULL COMMENT '备案登记信息',
  `installation_date` DATE NULL DEFAULT NULL COMMENT '进场安装日期',
  `removal_date` DATE NULL DEFAULT NULL COMMENT '拆卸离场日期',
  `status` VARCHAR(50) NULL DEFAULT 'ACTIVE' COMMENT '状态',
  `last_inspection_date` DATE NULL DEFAULT NULL COMMENT '最近安检日期',
  `next_inspection_date` DATE NULL DEFAULT NULL COMMENT '下次安检到期日',
  `operator_personnel_id` BIGINT NULL DEFAULT NULL COMMENT '操作员ID (逻辑外键至 prj_personnel.project_personnel_id)',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`equipment_id`),
  UNIQUE INDEX `equipment_code_UNIQUE` (`equipment_code` ASC),
  INDEX `idx_insp_special_equipment_project_id` (`project_id` ASC),
  INDEX `idx_insp_special_equipment_operator_id` (`operator_personnel_id` ASC))
ENGINE = InnoDB
COMMENT = '[巡检与设备] 管理项目上使用的特种设备信息';


-- =============================================
-- 政府执法文书 (gov_)
-- =============================================

-- -----------------------------------------------------
-- 表 `gov_supervision_records` (监管机构排查治理记录表)
-- -----------------------------------------------------
DROP TABLE IF EXISTS `gov_supervision_records`;

CREATE TABLE IF NOT EXISTS `gov_supervision_records` (
  `record_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `project_id` BIGINT NOT NULL COMMENT '关联的项目ID (逻辑外键至 prj_projects.project_id)',
  `item_id` BIGINT NULL DEFAULT NULL COMMENT '关联的危大工程项ID (可选, 逻辑外键至 prj_hazardous_items.item_id)',
  `supervising_org_id` BIGINT NOT NULL COMMENT '执行排查的监管机构ID (逻辑外键至 sys_dept.dept_id, 类型为GOV_*)',
  `inspection_date` DATE NOT NULL COMMENT '检查日期',
  `problem_list_doc_id` BIGINT NULL DEFAULT NULL COMMENT '问题清单文档ID (逻辑外键至 sys_documents.document_id)',
  `rectification_notice_doc_id` BIGINT NULL DEFAULT NULL COMMENT '整改通知单文档ID (逻辑外键至 sys_documents.document_id)',
  `enforcement_advice_doc_id` BIGINT NULL DEFAULT NULL COMMENT '执法建议书文档ID (逻辑外键至 sys_documents.document_id)',
  `is_partial_shutdown_required` TINYINT(1) NULL DEFAULT 0 COMMENT '是否需要局部停工整改 (0:否, 1:是)',
  `is_full_shutdown_required` TINYINT(1) NULL DEFAULT 0 COMMENT '是否需要全面停工整改 (0:否, 1:是)',
  `rectification_details` TEXT NULL DEFAULT NULL COMMENT '整改情况描述',
  `inspection_record_form_doc_id` BIGINT NULL DEFAULT NULL COMMENT '检查记录表文档ID (逻辑外键至 sys_documents.document_id)',
  `remarks` TEXT NULL DEFAULT NULL COMMENT '备注',
  `create_dept` BIGINT NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` BIGINT NULL DEFAULT NULL COMMENT '创建者',
  `create_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` BIGINT NULL DEFAULT NULL COMMENT '更新者',
  `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
  PRIMARY KEY (`record_id`),
  INDEX `idx_gov_supervision_project_id` (`project_id` ASC),
  INDEX `idx_gov_supervision_item_id` (`item_id` ASC),
  INDEX `idx_gov_supervision_org_id` (`supervising_org_id` ASC),
  INDEX `idx_gov_supervision_problem_list_doc_id` (`problem_list_doc_id` ASC),
  INDEX `idx_gov_supervision_rect_notice_doc_id` (`rectification_notice_doc_id` ASC),
  INDEX `idx_gov_supervision_enforce_advice_doc_id` (`enforcement_advice_doc_id` ASC),
  INDEX `idx_gov_supervision_inspect_form_doc_id` (`inspection_record_form_doc_id` ASC)
)
ENGINE = InnoDB
COMMENT = '[政府执法] 存储监管机构对危大工程的隐患排查治理情况记录';

-- 原 `gov_enforcement_documents` 表已通过 `gov_supervision_records` 中的具体文书关联字段体现。
-- 如果仍需要一个更通用的执法文书汇总表，可以取消以下注释并定义。
/*
DROP TABLE IF EXISTS `gov_enforcement_documents` ;
CREATE TABLE IF NOT EXISTS `gov_enforcement_documents` ( ... );
*/


-- 恢复之前的设置
SET SQL_MODE=@OLD_SQL_MODE;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;

-- -----------------------------------------------------
-- 脚本结束
-- -----------------------------------------------------

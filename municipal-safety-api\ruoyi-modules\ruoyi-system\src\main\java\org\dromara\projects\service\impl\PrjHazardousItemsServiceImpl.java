package org.dromara.projects.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.dromara.ai.domain.AiHazAnalysisTasks;
import org.dromara.ai.domain.AiHazAnalysisTasksResult;
import org.dromara.ai.domain.dto.AiHazAnalysisResultResetDTO;
import org.dromara.ai.domain.dto.AiHazAnalysisTasksDto;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksResultVo;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksVo;
import org.dromara.ai.enums.AiHazAnalysisTasksReCheckStatus;
import org.dromara.ai.mapper.AiHazAnalysisTasksMapper;
import org.dromara.ai.mapper.AiHazAnalysisTasksResultMapper;
import org.dromara.ai.service.IAiHazAnalysisTasksService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.projects.convert.PrjHazardousItemsConvert;
import org.dromara.projects.domain.PrjHazardousItems;
import org.dromara.projects.domain.PrjProjects;
import org.dromara.projects.domain.bo.PrjHazardousItemsAppBo;
import org.dromara.projects.domain.bo.PrjHazardousItemsBo;
import org.dromara.projects.domain.vo.*;
import org.dromara.projects.mapper.PrjHazardousItemsMapper;
import org.dromara.projects.mapper.PrjProjectsMapper;
import org.dromara.projects.service.IPrjHazardousItemsService;
import org.dromara.system.domain.DangerList;
import org.dromara.system.domain.SysEnterpriseInfo;
import org.dromara.system.mapper.DangerListMapper;
import org.dromara.system.mapper.SysEnterpriseInfoMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * [项目管理] 列出项目内具体的危险性较大的分部分项工程Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@RequiredArgsConstructor
@Service
public class PrjHazardousItemsServiceImpl implements IPrjHazardousItemsService {

    private final PrjHazardousItemsMapper baseMapper;
    private final PrjProjectsMapper prjProjectsMapper;
    private final SysEnterpriseInfoMapper sysEnterpriseInfoMapper;
    private final AiHazAnalysisTasksMapper aiHazAnalysisTasksMapper;
    private final DangerListMapper dangerListMapper;
    private final AiHazAnalysisTasksResultMapper aiHazAnalysisTasksResultMapper;

    private final IAiHazAnalysisTasksService aiHazAnalysisTasksService;

    /**
     * 查询[项目管理] 列出项目内具体的危险性较大的分部分项工程
     *
     * @param itemId 主键
     * @return [项目管理] 列出项目内具体的危险性较大的分部分项工程
     */
    @Override
    public PrjHazardousItemsVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    @Override
    public PrjHazardousItemsVo queryByIdApp(Long itemId) {
        PrjHazardousItemsVo prjHazardousItemsVo = baseMapper.selectVoById(itemId);
        if (prjHazardousItemsVo.getDangerId() != null) {
            String parentName = aiHazAnalysisTasksService.getParentName(prjHazardousItemsVo.getDangerId());
            prjHazardousItemsVo.setParentName(parentName);
        }
        // 关联的危大项目信息
        if (prjHazardousItemsVo.getProjectId() != null) {
            PrjProjectsVo prjProjectsVo = prjProjectsMapper.selectVoById(prjHazardousItemsVo.getProjectId());
            prjHazardousItemsVo.setPrjProjectsVo(prjProjectsVo);
        }
        return prjHazardousItemsVo;
    }

    /**
     * 查询分部分项详情信息
     *
     * @param itemId
     * @return
     */
    @Override
    public PrjHazardousItemsVo queryDetailById(Long itemId) {
        PrjHazardousItemsVo itemsVo = baseMapper.selectVoById(itemId);

        //获取项目名称
        PrjProjectsVo prjProjectsVo = prjProjectsMapper.selectVoById(itemsVo.getProjectId());
        itemsVo.setProjectName(prjProjectsVo.getProjectName());

        //获取隐患清单
        LambdaQueryWrapper<AiHazAnalysisTasks> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiHazAnalysisTasks::getItemId, itemsVo.getItemId())
            .select(AiHazAnalysisTasks::getTaskId
                , AiHazAnalysisTasks::getLocationDescription
                , AiHazAnalysisTasks::getAiPhotoDocumentId
                , AiHazAnalysisTasks::getStatus
                , AiHazAnalysisTasks::getUploadTime
                , AiHazAnalysisTasks::getRecheckStatus
            ).orderByDesc(AiHazAnalysisTasks::getUploadTime);

        List<AiHazAnalysisTasks> analysisTasks = aiHazAnalysisTasksMapper.selectList(wrapper);

        if (!analysisTasks.isEmpty()) {
            List<ItemsAiListVO> aiListVOS = new ArrayList<>();
            analysisTasks.forEach(s ->
                aiListVOS.add(new ItemsAiListVO(
                    s.getUploadTime(), s.getTaskId(), s.getAiPhotoDocumentId(), s.getLocationDescription(), s.getStatus(), s.getRecheckStatus()
                )));

            //隐患清单里列表
            itemsVo.setAiListVOS(aiListVOS);
        }

        return itemsVo;
    }

    @Override
    public ItemsAiDetailVO getAiHazAnalysisTaskDetail(Long taskId) {

        ItemsAiDetailVO detailVO = new ItemsAiDetailVO();

        AiHazAnalysisTasksVo tasksVo = aiHazAnalysisTasksMapper.selectVoById(taskId);

        detailVO.setPhotoDocumentId(tasksVo.getPhotoDocumentId());
        detailVO.setAiPhotoDocumentId(tasksVo.getAiPhotoDocumentId());
        detailVO.setLocationDescription(tasksVo.getLocationDescription());

        LambdaQueryWrapper<AiHazAnalysisTasksResult> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AiHazAnalysisTasksResult::getTaskId, taskId);

        //查询问题列表
        List<AiHazAnalysisTasksResultVo> tasksResultVos = aiHazAnalysisTasksResultMapper.selectVoList(wrapper);

        //解析json
        if (ArrayUtil.isNotEmpty(tasksResultVos)) {

            List<ViolationVO> violationVOS = new ArrayList<>();

            for (AiHazAnalysisTasksResultVo resultVo : tasksResultVos) {
                ViolationVO violationVO = new ViolationVO();

                violationVO.setLevel(resultVo.getLevel());
                violationVO.setViolation(resultVo.getViolation());
                violationVO.setRegulation(resultVo.getRegulation());
                violationVO.setMeasure(resultVo.getMeasure());
                violationVO.setResultId(resultVo.getResultId());

                violationVOS.add(violationVO);
            }
            detailVO.setViolations(violationVOS);
        }

        return detailVO;
    }

    @Override
    public IPage<PrjHazardousItemsVo> queryAppList(PrjHazardousItemsAppBo bo) {
        LambdaQueryWrapper<PrjHazardousItems> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(PrjHazardousItems::getItemName
            , PrjHazardousItems::getItemId
            , PrjHazardousItems::getDangerListType
            , PrjHazardousItems::getScopeDetails
            , PrjHazardousItems::getStartDate
            , PrjHazardousItems::getPlannedEndDate
            , PrjHazardousItems::getDangerId
            , PrjHazardousItems::getProjectId
        ).orderByDesc(BaseEntity::getCreateTime);

        if (ObjectUtils.isNotEmpty(bo.getProjectId())) {
            //项目钻进来的
            wrapper.eq(PrjHazardousItems::getProjectId, bo.getProjectId());
        } else {
            LambdaQueryWrapper<PrjProjects> lqw = Wrappers.lambdaQuery();
            lqw.eq(PrjProjects::getDelFlag, 0);
            List<PrjProjectsVo> prjProjectsVos = prjProjectsMapper.selectAllSq(lqw);

            List<Long> projectIds = prjProjectsVos.stream().map(PrjProjectsVo::getProjectId).toList();
            if (projectIds.isEmpty()) {
                projectIds = new ArrayList<>();
                projectIds.add(-1L);
            }
            wrapper.in(PrjHazardousItems::getProjectId, projectIds);
        }

        //通过查询
        if (StringUtils.isNotBlank(bo.getQueryData())) {
            if (bo.getQueryType() == 1) {
                LambdaQueryWrapper<PrjProjects> projectWrapper = new LambdaQueryWrapper<>();
                projectWrapper.likeLeft(PrjProjects::getProjectName, bo.getQueryData())
                    .select(PrjProjects::getProjectId);

                List<PrjProjects> prjProjects = prjProjectsMapper.selectList(projectWrapper);
                List<Long> projectIds = prjProjects.stream().map(PrjProjects::getProjectId).collect(Collectors.toList());

                if (projectIds.isEmpty()) {
                    projectIds = new ArrayList<>();
                    projectIds.add(-1L);
                }
                wrapper.in(PrjHazardousItems::getProjectId, projectIds);
            } else if (bo.getQueryType() == 2) {
                wrapper.likeLeft(PrjHazardousItems::getItemName, bo.getQueryData());
            }
        }

        Page<PrjHazardousItems> itemsVoIPage = this.baseMapper.selectPage(new Page<>(bo.getPageNum(), bo.getPageSize()), wrapper);
        Page<PrjHazardousItemsVo> prjHazardousItemsVoPage = PrjHazardousItemsConvert.INSTANCE.page2voPage(itemsVoIPage);

        List<PrjHazardousItemsVo> records = prjHazardousItemsVoPage.getRecords();

        if (!records.isEmpty()) {
            //获取项目名称
            List<Long> projectIds = records.stream().map(PrjHazardousItemsVo::getProjectId).toList();
            List<PrjProjectsVo> prjProjectsVos = prjProjectsMapper.selectVoByIds(projectIds);

            Map<Long, List<PrjProjectsVo>> projectMap = prjProjectsVos.stream().collect(Collectors.groupingBy(PrjProjectsVo::getProjectId));

            //涉危工程名称
            Map<Long, DangerList> dangerListMap = getLongDangerListMap();


            records.forEach(s -> {
                //项目名称
                s.setProjectName(Optional.ofNullable(projectMap.get(s.getProjectId()))
                    .map(p -> p.get(0).getProjectName()).orElse(null));
                if (StringUtils.isNotBlank(s.getDangerId())) {
                    //涉危工程名称
                    String[] split = s.getDangerId().split(",");
                    if (split.length > 0) {
                        String danger = split[0];
                        DangerList list = dangerListMap.get(Long.valueOf(danger));
                        if (list != null) {
                            s.setDangerName(Optional.ofNullable(dangerListMap
                                .get(list.getPreId())).map(DangerList::getName).orElse(null));
                        }
                    }
                }
                s.setDangerId(null);
            });

        }

        return prjHazardousItemsVoPage;
    }


    /**
     * 审核ai隐患任务
     *
     * @param dto 包含任务信息的数据传输对象
     * @return 保存成功返回true，否则返回false
     */
    @Transactional
    @Override
    public Boolean saveAiTaskAndResult(AiHazAnalysisResultResetDTO dto) {

        AiHazAnalysisTasks analysisTasks = aiHazAnalysisTasksMapper.selectById(dto.getTaskId());

        analysisTasks.setAiPhotoDocumentId(dto.getAiPhotoDocumentId());
        analysisTasks.setPhotoDocumentId(dto.getPhotoDocumentId());
        analysisTasks.setRecheckStatus(AiHazAnalysisTasksReCheckStatus.PENDING_RECHECK.getType());

        aiHazAnalysisTasksMapper.updateById(analysisTasks);

        List<AiHazAnalysisTasksResult> violations = dto.getViolations();

        //删除关联的任务

        if (violations != null && !violations.isEmpty()) {
            LambdaQueryWrapper<AiHazAnalysisTasksResult> removeWrapper = Wrappers.lambdaQuery();
            removeWrapper.eq(AiHazAnalysisTasksResult::getTaskId, dto.getTaskId());

            aiHazAnalysisTasksResultMapper.delete(removeWrapper);

            //组装新的
            violations.forEach(t -> {
                t.setTaskId(dto.getTaskId());
                t.setCoordinate("0");
            });
            return aiHazAnalysisTasksResultMapper.insertBatch(violations);
        }
        return true;
    }

    /**
     * 分页查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return [项目管理] 列出项目内具体的危险性较大的分部分项工程分页列表
     */
    @Override
    public TableDataInfo<PrjHazardousItemsVo> queryPageList(PrjHazardousItemsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PrjHazardousItems> lqw = buildQueryWrapper(bo);
        Page<PrjHazardousItemsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        TableDataInfo<PrjHazardousItemsVo> tableDataInfo = TableDataInfo.build(result);
        List<PrjHazardousItemsVo> rows = tableDataInfo.getRows();

        if (!rows.isEmpty()) {
            //获取项目名称
            List<Long> projectIds = rows.stream().map(PrjHazardousItemsVo::getProjectId).toList();
            List<PrjProjectsVo> prjProjectsVos = prjProjectsMapper.selectVoByIds(projectIds);

            Map<Long, List<PrjProjectsVo>> projectMap = prjProjectsVos.stream().collect(Collectors.groupingBy(PrjProjectsVo::getProjectId));

            //父级工程名称
            Map<Long, DangerList> dangerListMap = getLongDangerListMap();

            rows.forEach(s -> {
                s.setProjectName(Optional.ofNullable(projectMap.get(s.getProjectId()))
                    .map(p -> p.get(0).getProjectName()).orElse(null));

                setParentDangerName(s, dangerListMap);
            });
        }

        return tableDataInfo;
    }

    /**
     * 赋值父类涉危工程名称
     *
     * @param itemsVo
     * @param dangerListMap
     */
    private static void setParentDangerName(PrjHazardousItemsVo itemsVo, Map<Long, DangerList> dangerListMap) {
        if (itemsVo != null && ObjectUtils.isNotEmpty(itemsVo.getDangerId())) {
            String[] split = itemsVo.getDangerId().split(",");
            String preName = Optional.ofNullable(dangerListMap.get(Long.valueOf(split[0])))
                .map(DangerList::getPreId)
                .map(dangerListMap::get)
                .map(DangerList::getName)
                .orElse(null);
            itemsVo.setParentName(preName);
        }
    }

    @Override
    public TableDataInfo<PrjHazardousItemsVo> queryAdminPageList(PrjHazardousItemsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PrjHazardousItems> lqw = buildQueryWrapper(bo);

        //企业查询
        if (ObjectUtils.isNotEmpty(bo.getEnterpriseId())) {

            SysEnterpriseInfo enterpriseInfo = sysEnterpriseInfoMapper.selectById(bo.getEnterpriseId());

            LambdaQueryWrapper<PrjProjects> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(PrjProjects::getConstructionOrgId, enterpriseInfo.getDeptId())
                .select(PrjProjects::getProjectId);

            List<PrjProjects> prjProjects = prjProjectsMapper.selectList(wrapper);
            List<Long> projectIds = prjProjects.stream().map(PrjProjects::getProjectId).toList();
            if (projectIds.isEmpty()) {
                projectIds = new ArrayList<>();
                projectIds.add(-1L);
            }
            lqw.in(PrjHazardousItems::getProjectId, projectIds);
        }

        Page<PrjHazardousItemsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        TableDataInfo<PrjHazardousItemsVo> tableDataInfo = TableDataInfo.build(result);

        List<PrjHazardousItemsVo> rows = tableDataInfo.getRows();

        if (!rows.isEmpty()) {
            //获取项目名称
            List<Long> projectIds = rows.stream().map(PrjHazardousItemsVo::getProjectId).toList();
            List<PrjProjectsVo> prjProjectsVos = prjProjectsMapper.selectVoByIds(projectIds);

            Map<Long, List<PrjProjectsVo>> projectMap = prjProjectsVos.stream().collect(Collectors.groupingBy(PrjProjectsVo::getProjectId));

            Map<Long, DangerList> dangerListMap = getLongDangerListMap();

            rows.forEach(s -> {
                s.setProjectName(Optional.ofNullable(projectMap.get(s.getProjectId()))
                    .map(p -> p.get(0).getProjectName()).orElse(null));
                setParentDangerName(s, dangerListMap);
            });
        }

        return tableDataInfo;
    }

    /**
     * 查询符合条件的[项目管理] 列出项目内具体的危险性较大的分部分项工程列表
     *
     * @param bo 查询条件
     * @return [项目管理] 列出项目内具体的危险性较大的分部分项工程列表
     */
    @Override
    public List<PrjHazardousItemsVo> queryList(PrjHazardousItemsBo bo) {
        LambdaQueryWrapper<PrjHazardousItems> lqw = buildQueryWrapper(bo);
        List<PrjHazardousItemsVo> itemsVos = baseMapper.selectVoList(lqw);

        if (!itemsVos.isEmpty()) {
            Map<Long, DangerList> dangerListMap = getLongDangerListMap();

            for (PrjHazardousItemsVo itemsVo : itemsVos) {
                setParentDangerName(itemsVo, dangerListMap);
            }

        }

        return itemsVos;
    }

    /**
     * 获取所有危大清单列表集合
     *
     * @return
     */
    @NotNull
    private Map<Long, DangerList> getLongDangerListMap() {
        //涉危工程名称
        LambdaQueryWrapper<DangerList> dangerWrapper = new LambdaQueryWrapper<>();
        dangerWrapper.select(DangerList::getDangerId, DangerList::getName, DangerList::getPreId);
        List<DangerList> dangerLists = dangerListMapper.selectList(dangerWrapper);
        Map<Long, DangerList> dangerListMap = dangerLists.stream().collect(Collectors.toMap(DangerList::getDangerId, t -> t));
        return dangerListMap;
    }

    private LambdaQueryWrapper<PrjHazardousItems> buildQueryWrapper(PrjHazardousItemsBo bo) {
        LambdaQueryWrapper<PrjHazardousItems> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PrjHazardousItems::getItemId);
        lqw.like(StringUtils.isNotBlank(bo.getItemName()), PrjHazardousItems::getItemName, bo.getItemName());
        lqw.eq(bo.getDangerListType() != null, PrjHazardousItems::getDangerListType, bo.getDangerListType());
        lqw.eq(bo.getStartDate() != null, PrjHazardousItems::getStartDate, bo.getStartDate());
        lqw.eq(bo.getPlannedEndDate() != null, PrjHazardousItems::getPlannedEndDate, bo.getPlannedEndDate());
        lqw.eq(bo.getActualEndDate() != null, PrjHazardousItems::getActualEndDate, bo.getActualEndDate());
        lqw.eq(bo.getActualStartDate() != null, PrjHazardousItems::getActualStartDate, bo.getActualStartDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PrjHazardousItems::getStatus, bo.getStatus());

        if (!LoginHelper.isSuperAdmin() && ObjectUtils.isEmpty(bo.getProjectId())) {
            //不是超管必须传项目id
            throw new ServiceException("未选择指定项目！");
        }
        lqw.eq(ObjectUtils.isNotEmpty(bo.getProjectId()), PrjHazardousItems::getProjectId, bo.getProjectId());

        return lqw;
    }

    /**
     * 新增[项目管理] 列出项目内具体的危险性较大的分部分项工程
     *
     * @param bo [项目管理] 列出项目内具体的危险性较大的分部分项工程
     * @return 是否新增成功
     */
    @Override
    public String insertByBo(PrjHazardousItemsBo bo) {
        PrjHazardousItems add = MapstructUtils.convert(bo, PrjHazardousItems.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
            return String.valueOf(add.getItemId());
        }
        return null;
    }

    /**
     * 修改[项目管理] 列出项目内具体的危险性较大的分部分项工程
     *
     * @param bo [项目管理] 列出项目内具体的危险性较大的分部分项工程
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PrjHazardousItemsBo bo) {
        PrjHazardousItems update = MapstructUtils.convert(bo, PrjHazardousItems.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PrjHazardousItems entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除[项目管理] 列出项目内具体的危险性较大的分部分项工程信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}

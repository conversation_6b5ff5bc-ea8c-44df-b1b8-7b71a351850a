package org.dromara.facility.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.bo.LnSprayingBoToLnSprayingMapper;
import org.dromara.facility.domain.vo.LnSprayingVo;
import org.dromara.facility.domain.vo.LnSprayingVoToLnSprayingMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {LnSprayingBoToLnSprayingMapper.class,LnSprayingVoToLnSprayingMapper.class},
    imports = {}
)
public interface LnSprayingToLnSprayingVoMapper extends BaseMapper<LnSpraying, LnSprayingVo> {
}

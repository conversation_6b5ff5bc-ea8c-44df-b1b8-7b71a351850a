package org.dromara.attendance.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.attendance.domain.MAttRecord;
import org.dromara.attendance.domain.bo.MAttRecordBo;
import org.dromara.attendance.domain.vo.MAttRecordVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 考勤记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
public interface MAttRecordMapper extends BaseMapperPlus<MAttRecord, MAttRecordVo> {

    MAttRecordVo selectMAttRuleByAttDate(@Param("attDate") String format, @Param("whichTime") int i, @Param("personId") Long personId);

    void insertMAttRecord(MAttRecordBo mAttRecordBo);

    List<MAttRecordVo> selectMAttRecordByPersonIdAndAttDate(@Param("personId") Long personId, @Param("attDate") String attDate);

    List<MAttRecordVo> selectMAttRecordByPersonId(@Param("personId") Long personId);
}

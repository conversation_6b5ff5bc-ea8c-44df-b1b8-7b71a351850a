package org.dromara.ai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 智能隐患分析任务状态
 */
@AllArgsConstructor
@Getter
public enum AiHazAnalysisTasksStatus {

    PENDING_AI_ANALYSIS("PENDING_AI_ANALYSIS", "待AI分析"),
    AI_ANALYSING("AI_ANALYSING", "AI分析中"),
    AI_ANALYSIS_COMPLETED("AI_ANALYSIS_COMPLETED", "有隐患"),
    AI_ANALYSIS_NOT_CONSTRUCTION_SITE("AI_ANALYSIS_NOT_CONSTRUCTION_SITE", "非工地现场"),
    AI_ANALYSIS_NO_SAFETY_HAZARDS("AI_ANALYSIS_NO_SAFETY_HAZARDS", "无安全隐患"),

    PENDING_EXPERT_CONFIRMATION("PENDING_EXPERT_CONFIRMATION", "待专家确认"),
    EXPERT_CONFIRMED("EXPERT_CONFIRMED", "专家已确认"),
    TASK_COMPLETED("TASK_COMPLETED", "任务完成/结果已处理"),
    ;

    private final String type;
    private final String name;

}

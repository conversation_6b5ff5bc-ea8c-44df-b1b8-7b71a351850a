package org.dromara.attendance.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 考勤记录对象 m_att_record
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("m_att_record")
public class MAttRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 规则id
     */
    private Long ruleId;

    /**
     * 项目人员id
     */
    private Long personId;

    /**
     * 人员岗位/角色
     */
    private String personType;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份Id
     */
    private String idNumber;

    /**
     * 实时人脸
     */
    private String realTimeFace;

    /**
     * 设备号
     */
    private String sn;

    /**
     * 打卡来源
     */
    private Integer source;

    /**
     * 自定义内容
     */
    private String content;

    /**
     * 考勤时间
     */
    private Date attTime;

    /**
     * 考勤日期
     */
    private String attDate;

    /**
     * 打卡结果
     */
    private Integer attResult;

    /**
     * 第几次打卡
     */
    private Integer whichTime;
}

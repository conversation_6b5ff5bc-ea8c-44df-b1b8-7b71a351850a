<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="身份证号码" prop="idCard" label-width="82px">
              <el-input v-model="queryParams.idCard" placeholder="请输入身份证号码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="手机号码" prop="phone">
              <el-input v-model="queryParams.phone" placeholder="请输入手机号码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="性别" prop="gender">
              <el-select v-model="queryParams.gender" placeholder="请选择性别" clearable>
                <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="文化程度" prop="education">
              <el-select v-model="queryParams.education" placeholder="请选择文化程度" clearable>
                <el-option v-for="dict in educational_level_code" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="证书种类" prop="certSearchValue" label-width="81px">
              <el-cascader ref="refHandle" popper-class="certCityNames" v-model="certSearchValue"
                :options="QualificationDictOptions" @change="handleSearchChange" :props="{
                  expandTrigger: 'hover' as const,
                  value: 'id',
                  label: 'name',
                  children: 'children',
                  checkStrictly: true
                }" clearable placeholder="请选择证书类别" :popper-append-to-body="false"></el-cascader>
            </el-form-item>
            <el-form-item v-hasPermi="['system:enterpriseInfo:searchData']" label="企业查询" prop="certSearchValue">
              <el-select v-model="queryParams.enterpriseId" filterable placeholder="请选择企业" style="width: 240px"
                clearable>
                <el-option v-for="item in enterpriseSelectData" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['system:person:add']">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              v-hasPermi="['system:person:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['system:person:remove']">删除</el-button>
          </el-col> -->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['system:person:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="personList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="企业名称" align="center" prop="enterpriseName" min-width="150px" show-overflow-tooltip />
        <el-table-column label="企业统一信用代码" align="center" prop="unifiedSocialCreditCode" min-width="130px"
          show-overflow-tooltip />
        <el-table-column label="证书种类" align="center" prop="unifiedSocialCreditCode" min-width="100px">
          <template #default="scope">
            <el-tooltip placement="top" v-if="scope.row.qualificationDetail">
              <template #content>
                <div v-html="scope.row.qualificationDetail"></div>
              </template>
              <p class="textOnlyOneLine" style="width: 100%;display: block;cursor: pointer;color: #409EFF;">
                {{ scope.row.qualificationDetail?.split('<br>')[0] }}
              </p>
            </el-tooltip>
            <p v-else>暂无</p>
          </template>
        </el-table-column>
        <el-table-column label="头像" align="center" prop="headImgUrl" min-width="80px">
          <template #default="scope">
            <header-prewiew :src="scope.row.headImgUrl" :width="50" :height="50"
              :preview-src-list="[scope.row.headImgUrl]" />
          </template>
        </el-table-column>
        <el-table-column label="姓名" align="center" prop="name" min-width="90px">
          <template #default="scope">
            <el-button text type="primary" @click="handleDetailChange(scope.row)">{{ scope.row.name }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="身份证号码" align="center" prop="idCard" min-width="130px" />
        <el-table-column label="手机号码" align="center" prop="phone" min-width="110px" />
        <el-table-column label="籍贯" align="center" prop="nativePlace" min-width="120px" show-overflow-tooltip />
        <el-table-column label="性别" align="center" prop="gender" min-width="60px">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
          </template>
        </el-table-column>
        <el-table-column label="政治面貌" align="center" prop="politicalStatus" min-width="100px">
          <template #default="scope">
            <dict-tag :options="politics_status" :value="scope.row.politicalStatus" />
          </template>
        </el-table-column>
        <el-table-column label="文化程度" align="center" prop="education" min-width="80px">
          <template #default="scope">
            <dict-tag :options="educational_level_code" :value="scope.row.education" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="100px">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['system:person:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="资格证书管理" placement="top">
              <el-button link type="primary" icon="DocumentAdd" @click="handleUpload(scope.row)"
                v-hasPermi="['system:person:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="迁出" placement="top">
              <el-button link type="primary" icon="FolderRemove" @click="handleSignOut(scope.row)"
                v-hasPermi="['system:person:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改人员基本信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="850px" append-to-body>
      <el-form ref="personFormRef" :model="form" :rules="rules">
        <el-row :gutter="20">
          <el-col :span="17">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="姓名" prop="name" label-width="77px">
                  <el-input v-model="form.name" placeholder="请输入姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别" prop="gender" label-width="77px">
                  <el-select v-model="form.gender" placeholder="请选择性别">
                    <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label"
                      :value="dict.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="身份证号" prop="idCard">
                  <el-input v-model="form.idCard" placeholder="请输入身份证号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机号码" prop="phone">
                  <el-input v-model="form.phone" placeholder="请输入手机号码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="籍贯" prop="nativePlace" label-width="77px">
                  <el-input v-model="form.nativePlace" placeholder="请输入籍贯" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="文化程度" prop="education">
                  <el-select v-model="form.education" placeholder="请选择文化程度">
                    <el-option v-for="dict in educational_level_code" :key="dict.value" :label="dict.label"
                      :value="dict.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="政治面貌" prop="politicalStatus" label-width="77px">
                  <el-select v-model="form.politicalStatus" placeholder="请选择政治面貌">
                    <el-option v-for="dict in politics_status" :key="dict.value" :label="dict.label"
                      :value="dict.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="7">
            <el-form-item label="头像" prop="headImgId">
              <header-upload @update:modelValue="handleAvatar" :limit="1" :fileSize="20" :modelValue="form.headImgId"
                :isShowTip="false"></header-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 上传证书对话框 -->
    <el-dialog :title="uploadDialog.title" v-model="uploadDialog.visible" width="70%" append-to-body>
      <el-card shadow="never">
        <template #header>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" plain icon="Plus" @click="handleCertAdd"
                v-hasPermi="['system:qualification:add']">新增</el-button>
            </el-col>
          </el-row>
        </template>
        <el-table v-loading="certLoading" :data="personCertList" @selection-change="handleCertSelectChange"
          show-overflow-tooltip>
          <el-table-column label="专业" align="center" prop="correspondingPosition" min-width="90px" />
          <el-table-column label="证书名称" align="center" prop="certificateType" min-width="130px" />
          <el-table-column label="证书图片" align="center" prop="uploadPhotoUrl" min-width="90px">
            <template #default="scope">
              <header-prewiew :src="scope.row.uploadPhotoUrl" :width="50" :height="50"
                :preview-src-list="[scope.row.uploadPhotoUrl]" />
            </template>
          </el-table-column>
          <el-table-column label="证书编号" align="center" prop="certificateNumber" min-width="130px" />
          <el-table-column label="证书种类" align="center" prop="certificateName" min-width="130px" />
          <el-table-column label="证书等级" align="center" prop="certificateLevel" min-width="80px">
            <template #default="scope">
              <dict-tag :options="certificate_level" :value="scope.row.certificateLevel" />
            </template>
          </el-table-column>
          <el-table-column label="发证机关" align="center" prop="issuingAuthority" min-width="130px" />
          <el-table-column label="证书获取时间" align="center" prop="acquisitionTime" min-width="100px" />
          <el-table-column label="证书有效期" align="center" prop="validTo" min-width="100px" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="70px">
            <template #default="scope">
              <el-tooltip content="修改证书上传" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleCertUpdate(scope.row)"
                  v-hasPermi="['system:person:edit']"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button link type="primary" icon="Delete" @click="handleCertDelete(scope.row)"
                  v-hasPermi="['system:person:edit']"></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="uploadCancel">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 添加资格证书弹框 -->
    <el-dialog :title="addCertDialog.title" v-model="addCertDialog.visible" width="1200px" append-to-body>
      <el-form ref="certFormRef" :model="certFormData" :rules="certRules">
        <el-row :gutter="20">
          <el-col :span="18">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="证书种类" prop="certificateType" label-width="120px">
                  <el-cascader v-model="certValue" :options="QualificationDictOptions" :props="certProps"
                    @change="handleChange" style="width: 100%;" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="证书编号" prop="certificateNumber" label-width="120px">
                  <el-input v-model="certFormData.certificateNumber" placeholder="请输入证书编号" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="证书等级" prop="certificateLevel" label-width="120px">
                  <el-select v-model="certFormData.certificateLevel" placeholder="请选证书等级" style="width: 100%;">
                    <el-option v-for="dict in certificate_level" :key="dict.value" :label="dict.label"
                      :value="dict.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发证机关" prop="issuingAuthority" label-width="120px">
                  <el-input v-model="certFormData.issuingAuthority" placeholder="请输入发证机关" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="发证日期" prop="acquisitionTime" label-width="120px">
                  <el-date-picker clearable v-model="certFormData.acquisitionTime" type="date"
                    value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择发证日期" style="width: 100%;">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="专业" prop="correspondingPosition" label-width="120px">
                  <el-input v-model="certFormData.correspondingPosition" placeholder="请输入专业" />
                  <!-- <el-select v-model="certFormData.correspondingPosition" placeholder="请选专业">
                    <el-option v-for="dict in educational_level_code" :key="dict.value" :label="dict.label"
                      :value="dict.value"></el-option>
                  </el-select> -->
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="证书有效期起始" prop="validFrom" label-width="120px">
                  <el-date-picker clearable v-model="certFormData.validFrom" type="date"
                    value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择证书有效期起始" :disabled-date="startPickerOptions"
                    style="width: 100%;">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="证书有效期结束" prop="validFrom" label-width="120px">
                  <el-date-picker clearable v-model="certFormData.validTo" type="date"
                    value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择证书有效期结束" :disabled-date="endPickerOptions"
                    style="width: 100%;">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="6">
            <el-form-item label="证书照片上传" prop="uploadPhoto">
              <header-upload @update:modelValue="handleUploadCert" :limit="1" :modelValue="certFormData.uploadPhoto"
                :isShowTip="false"></header-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading1" type="primary" @click="addSubmitForm">确 定</el-button>
          <el-button @click="addCertCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 人员详情信息弹框组件 -->
    <PersonDetail :personId="_personid" :isShowModel="isShowModelDetail" @update:isShowModel="handleShowModel">
    </PersonDetail>
  </div>
</template>

<script setup name="Person" lang="ts">
import { listPerson, getPerson, delPerson, addPerson, updatePerson, listQualification,signOut,
  addQualification, updateQualification, getQualification, delQualification } from '@/api/person/basic/api';
import { listQualificationDict } from '@/api/person/qualificationDict/api';
import { QualificationDictVO } from '@/api/person/qualificationDict/types';
import { PersonVO, PersonQuery, PersonForm, QualificationQuery, QualificationForm } from '@/api/person/basic/types';
import HeaderUpload from '@/components/ImageUpload/index.vue';
import HeaderPrewiew from '@/components/ImagePreview/index.vue';
import { getSearchData } from '@/api/system/enterpriseInfo';
import PersonDetail from '@/components/personDetail/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { educational_level_code, sys_user_sex, politics_status, certificate_level } = toRefs<any>(proxy?.useDict('educational_level_code', 'sys_user_sex', 'politics_status', 'certificate_level'));

const personList = ref<PersonVO[]>([]);
const buttonLoading = ref(false);
const buttonLoading1 = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

// 证书上传的相关字段
const personCertList = ref<QualificationQuery[]>([]);
const buttonCertLoading = ref(false);
const certLoading = ref(true);
const idsCert = ref<Array<string | number>>([]);
const singleCert = ref(true);
const multipleCert = ref(true);
const totalCert = ref(0);
// 存放证书类型的所有数据的数组
const qualificationDict = ref<QualificationDictVO[]>([]);
// 证书提交数据
const certFormData = reactive<QualificationForm>({
  qualificationId: undefined,
  personId: undefined,
  certificateType: undefined,
  certificateName: undefined,
  certificateNumber: undefined,
  acquisitionTime: undefined,
  issuingAuthority: undefined,
  certificateLevel: undefined,
  correspondingPosition: undefined,
  validFrom: undefined,
  validTo: undefined,
  uploadPhoto: undefined
})
const certRules = reactive({
  certificateName: [
    { required: true, message: "请选择证书名称", trigger: "change" }
  ],
  certificateType: [
    { required: true, message: "请选择证书种类", trigger: "change" }
  ],
  certificateNumber: [
    { required: true, message: "请输入证书编号", trigger: "blur" }
  ],
  certificateLevel: [
    { required: true, message: "请选择证书等级", trigger: "change" }
  ],
  issuingAuthority: [
    { required: true, message: "请输入发证机关", trigger: "blur" }
  ],
  acquisitionTime: [
    { required: true, message: "请选择发证日期", trigger: "change" }
  ],
  correspondingPosition: [
    { required: true, message: "请输入专业", trigger: "blur" }
  ],
  validFrom: [
    { required: true, message: "请选择证书有效期起始", trigger: "change" }
  ],
  validTo: [
    { required: true, message: "请选择证书有效期结束", trigger: "change" }
  ],
  uploadPhoto: [
    { required: true, message: "请上传证书", trigger: "change" }
  ]
})
const certFormRef = ref<ElFormInstance>()
// 新增证书弹框中的证书种类级联选择框的props配置
const certProps = {
  expandTrigger: 'hover' as const,
  value: 'id',
  label: 'name',
  children: 'children',
}
// 新增证书弹框中的证书种类级联选择框的options配置
const QualificationDictOptions = ref<QualificationDictVO[]>([])
// 新增证书弹框中的证书种类级联选择框的value配置
const certValue = ref()
// 添加资格证书弹框显隐
const addCertDialog = reactive<DialogOption>({
  visible: false,
  title: ''
})
const refHandle = ref();
const queryFormRef = ref<ElFormInstance>();
const personFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const uploadDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const initFormData: PersonForm = {
  personId: undefined,
  headImgId: undefined,
  name: undefined,
  idCard: undefined,
  phone: undefined,
  nativePlace: undefined,
  gender: undefined,
  politicalStatus: undefined,
  education: undefined
}
const data = reactive<PageData<PersonForm, PersonQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    enterpriseId: undefined,
    name: undefined,
    idCard: undefined,
    phone: undefined,
    nativePlace: undefined,
    gender: undefined,
    politicalStatus: undefined,
    education: undefined,
    certificateType: undefined,
    certificateName: undefined,
    params: {
    }
  },
  rules: {
    headImgId: [
      { required: true, message: "请上传头像", trigger: "change" }
    ],
    name: [
      { required: true, message: "姓名不能为空", trigger: "blur" }
    ],
    idCard: [
      { required: true, message: "身份证号码不能为空", trigger: "blur" },
      { pattern: /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/, message: "身份证号码格式不正确", trigger: "blur" }
    ],
    phone: [
      { required: true, message: "手机号码不能为空", trigger: "blur" },
      { pattern: /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[1589]))\d{8}$/, message: "手机号码格式不正确", trigger: "blur" }
    ],
    gender: [
      { required: true, message: "性别不能为空", trigger: "change" }
    ],
    education: [
      { required: true, message: "文化程度不能为空", trigger: "change" }
    ],
  }
});
// 搜索的value配置
const certSearchValue = ref()

const { queryParams, form, rules } = toRefs(data)

//企业搜索选定的value
const enterpriseSelectData = ref([])
// 人员信息的id
const _personid = ref()
// 控制人员信息详情的弹框显隐变量
const isShowModelDetail = ref<boolean>(false)

const getEnterpriseSelectData = async () => {
  const { data } = await getSearchData()
  enterpriseSelectData.value = data
}

/** 查询人员基本信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPerson(queryParams.value);
  personList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}
// 查询证书列表信息
const getQualificationList = async (qualificationParams: QualificationQuery) => {
  certLoading.value = true;
  const res = await listQualification(qualificationParams);
  personCertList.value = res.rows;
  for (let i = 0; i < qualificationDict.value.length; i++) {
    for (let n = 0; n < res.rows.length; n++) {
      if (qualificationDict.value[i].id == res.rows[n].certificateName) {
        personCertList.value[n].certificateName = qualificationDict.value[i].name;
      }
      if (qualificationDict.value[i].id == res.rows[n].certificateType) {
        personCertList.value[n].certificateType = qualificationDict.value[i].name;
      }
    }
  }
  totalCert.value = res.total;
  certLoading.value = false;
}
// 获取证书种类字典数据
const getQualificationDict = async () => {
  const res = await listQualificationDict();
  const data = proxy?.handleTree<QualificationDictVO>(res.data, "id", "preId");
  if (data) {
    QualificationDictOptions.value = data;
  }
  qualificationDict.value = res.data;
}
/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}
/** 上传证书取消按钮 */
const uploadCancel = () => {
  reset();
  uploadDialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  personFormRef.value?.resetFields();
}
// 重置添加证书的表单数据
const resetCert = () => {
  certFormRef.value?.resetFields();
  certFormData.qualificationId = undefined;
  certFormData.certificateType = undefined;
  certFormData.certificateName = undefined;
  certFormData.certificateNumber = undefined;
  certFormData.acquisitionTime = undefined;
  certFormData.issuingAuthority = undefined;
  certFormData.certificateLevel = undefined;
  certFormData.correspondingPosition = undefined;
  certFormData.validFrom = undefined;
  certFormData.validTo = undefined;
  certFormData.uploadPhoto = undefined;
  certValue.value = undefined;
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}
// 头部搜索时的证书类型和证书名称的value
const handleSearchChange = (value: string[]) => {
  if (!value) {
    queryParams.value.certificateName = undefined
    queryParams.value.certificateType = undefined
    return
  }
  queryParams.value.certificateName = value[0];
  queryParams.value.certificateType = value[1];
}
/** 重置按钮操作 */
const resetQuery = () => {
  if (queryParams.value.certificateName || queryParams.value.certificateType) {
    refHandle.value.togglePopperVisible(); //监听值发生变化就关闭它
  }
  certSearchValue.value = undefined;
  queryParams.value.certificateName = undefined;
  queryParams.value.certificateType = undefined;
  queryParams.value.enterpriseId = undefined;
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: PersonVO[]) => {
  ids.value = selection.map(item => item.personId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加人员基本信息";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: PersonVO) => {
  reset();
  const _personId = row?.personId || ids.value[0]
  const res = await getPerson(_personId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改人员基本信息";
}
/** 证书多选框选中数据 */
const handleCertSelectChange = (selection: PersonVO[]) => {
  idsCert.value = selection.map(item => item.personId);
  singleCert.value = selection.length != 1;
  multipleCert.value = !selection.length;
}
// 修改证书上传
const handleCertUpdate = async (row?: QualificationForm) => {
  resetCert();
  const _qualificationId = row?.qualificationId
  const res = await getQualification(_qualificationId);
  Object.assign(certFormData, res.data);
  certValue.value = [res.data.certificateName, res.data.certificateType];
  certFormData.acquisitionTime = res.data.acquisitionTime + ' 00:00:00';
  certFormData.validFrom = res.data.validFrom + ' 00:00:00';
  certFormData.validTo = res.data.validTo + ' 00:00:00';
  addCertDialog.visible = true;
  addCertDialog.title = "修改资格证书";
}
// 删除证书
const handleCertDelete = async (row?: QualificationForm) => {
  const _qualificationId = row?.qualificationId
  await proxy?.$modal.confirm('是否确认删除资格证书编号为"' + _qualificationId + '"的数据项？').then(async () => {
    const res = await delQualification(_qualificationId);
    proxy?.$modal.msgSuccess(res.msg);
    getQualificationList({ personId: row?.personId, pageNum: 1, pageSize: 10 });
  }).catch(() => { });
}
/** 上传证书 */
const handleUpload = (row?: PersonVO) => {
  certFormData.personId = row?.personId
  getQualificationList({ personId: row?.personId, pageNum: 1, pageSize: 10 });
  uploadDialog.visible = true;
  uploadDialog.title = "资格证书管理";
}
// 迁出人员
const handleSignOut = (row?: PersonVO) => {
    ElMessageBox.confirm(
      '是否确认迁出此用户？',
      '提示',
    )
    .then(async () => {
      const res = await signOut({personId:row.personId});
      if(res.code == 200){
        proxy?.$modal.msgSuccess(res.msg);
        getList()
      }
    })
}

// 添加资格证书事件方法
const handleCertAdd = () => {
  addCertDialog.visible = true;
  addCertDialog.title = "新增资格证书";
  resetCert();
}
const handleChange = (value: string[]) => {
  certFormData.certificateName = value[0];
  certFormData.certificateType = value[1];
}
// 判断证书有效期起始
const startPickerOptions = (date: Date) => {
  if (certFormData.validTo) {
    return date.getTime() > new Date(certFormData.validTo).getTime();
  }
}
// 判断证书有效期结束
const endPickerOptions = (date: Date) => {
  if (certFormData.validFrom) {
    return date.getTime() < new Date(certFormData.validFrom).getTime();
  }
}
// 新增证书提交方法
const addSubmitForm = () => {
  certFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading1.value = true;
      if (!certFormData.qualificationId) {
        await addQualification(certFormData).finally(() => buttonLoading1.value = false);
      } else {
        await updateQualification(certFormData).finally(() => buttonLoading1.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      addCertDialog.visible = false;
      await getQualificationList({ personId: certFormData.personId, pageNum: 1, pageSize: 10 });;
    }
  })
}
// 点击列表中的姓名字段的点击事件
const handleDetailChange = (row: PersonVO) => {
  _personid.value = row.personId;
  isShowModelDetail.value = true;
}
const handleShowModel = (value: boolean) => {
  isShowModelDetail.value = value;
}
// 证书下载
const handleCertDownload = (row: QualificationForm) => {
  proxy?.$download.oss(row.uploadPhoto);
}
// 新增证书弹框取消事件方法
const addCertCancel = () => {
  addCertDialog.visible = false;
}
/** 提交按钮 */
const submitForm = () => {
  personFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.personId) {
        await updatePerson(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addPerson(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: PersonVO) => {
  const _personIds = row?.personId || ids.value;
  await proxy?.$modal.confirm('是否确认删除人员基本信息编号为"' + _personIds + '"的数据项？').finally(() => loading.value = false);
  await delPerson(_personIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('system/person/export', {
    ...queryParams.value
  }, `person_${new Date().getTime()}.xlsx`)
}
// 上传头像的回调函数
const handleAvatar = (avatarOssId: string) => {
  form.value.headImgId = avatarOssId;
}
// 上传证书回调函数
const handleUploadCert = (certOssId: string) => {
  certFormData.uploadPhoto = certOssId;
}
watch(() => certSearchValue.value, (newVal, oldVal) => {
  if (refHandle.value) {
    refHandle.value.togglePopperVisible(); //监听值发生变化就关闭它
  }
})
onMounted(() => {
  getList();
  getQualificationDict();
  getEnterpriseSelectData();
});
</script>
<style lang="scss">
.certCityNames {
  .el-cascader-panel .el-radio {
    position: absolute;
    z-index: 10;
    padding: 0 10px;
    width: 100%;
    height: 34px;
    line-height: 34px;
  }

  .el-cascader-panel .el-radio__input {
    visibility: hidden;
  }

  .el-cascader-panel .el-input-node__postfix {
    top: 0;
  }
}

.textOnlyOneLine {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

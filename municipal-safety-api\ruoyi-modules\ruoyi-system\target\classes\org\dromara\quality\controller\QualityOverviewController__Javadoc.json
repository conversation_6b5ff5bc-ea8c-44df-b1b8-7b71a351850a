{"doc": " 质量管理概览Controller\n\n <AUTHOR>\n @date 2024-01-01\n", "fields": [], "enumConstants": [], "methods": [{"name": "getOverviewData", "paramTypes": [], "doc": " 获取质量管理概览数据\n"}, {"name": "getDeviceStatistics", "paramTypes": [], "doc": " 获取设备统计数据\n"}, {"name": "getMeasurementStatistics", "paramTypes": [], "doc": " 获取测量统计数据\n"}, {"name": "getChartData", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 获取测量趋势图表数据\n\n @param period    时间周期：week/month/quarter\n @param startDate 开始日期 (YYYY-MM-DD)\n @param endDate   结束日期 (YYYY-MM-DD)\n"}, {"name": "getDeviceStatusDistribution", "paramTypes": [], "doc": " 获取设备状态分布数据\n"}], "constructors": []}
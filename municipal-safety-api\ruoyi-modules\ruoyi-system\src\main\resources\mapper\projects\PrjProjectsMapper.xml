<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.projects.mapper.PrjProjectsMapper">

    <resultMap type="org.dromara.projects.domain.vo.PrjProjectsVo" id="PrjProjectsResult">
        <id property="projectId" column="project_id"/>
        <result property="projectName" column="project_name"/>
        <result property="projectCode" column="project_code"/>
        <result property="projectOverview" column="project_overview"/>
        <result property="constructionPermitNo" column="construction_permit_no"/>
        <result property="constructionPermitDocId" column="construction_permit_doc_id"/>
        <result property="lola" column="lola"/>
        <result property="provinceCode" column="province_code"/>
        <result property="provinceName" column="province_name"/>
        <result property="cityCode" column="city_code"/>
        <result property="cityName" column="city_name"/>
        <result property="districtCode" column="district_code"/>
        <result property="districtName" column="district_name"/>
        <result property="countyCode" column="county_code"/>
        <result property="countyName" column="county_name"/>
        <result property="locationDetail" column="location_detail"/>
        <result property="status" column="status"/>
        <result property="startDate" column="start_date"/>
        <result property="plannedEndDate" column="planned_end_date"/>
        <result property="actualStartDate" column="actual_start_date"/>
        <result property="actualEndDate" column="actual_end_date"/>
        <result property="clientOrgId" column="client_org_id"/>
        <result property="clientOrgName" column="client_org_name"/>
        <result property="constructionOrgId" column="construction_org_id"/>
        <result property="constructionOrgName" column="construction_org_name"/>
        <result property="supervisionOrgId" column="supervision_org_id"/>
        <result property="supervisionOrgName" column="supervision_org_name"/>
        <result property="designOrgId" column="design_org_id"/>
        <result property="designOrgName" column="design_org_name"/>
        <result property="surveyOrgId" column="survey_org_id"/>
        <result property="surveyOrgName" column="survey_org_name"/>
        <result property="installationDismantlingOrgId" column="installation_dismantling_org_id"/>
        <result property="installationDismantlingOrgName" column="installation_dismantling_org_name"/>
        <result property="maintenanceOrgId" column="maintenance_org_id"/>
        <result property="maintenanceOrgName" column="maintenance_org_name"/>
        <result property="subcontractorOrgIds" column="subcontractor_org_ids"/>
        <result property="projectManagerUserId" column="project_manager_user_id"/>
        <result property="supervisionChiefEngUserId" column="supervision_chief_eng_user_id"/>
        <result property="safetyMeasuresFeeDocId" column="safety_measures_fee_doc_id"/>

        <result property="supervisingQsOrgName" column="supervising_qs_org_name"/>

        <!-- 企业信息集合 -->
        <collection property="enterpriseList" ofType="org.dromara.projects.domain.vo.PrjSysEnterpriseInfoVo">
            <id property="enterpriseId" column="enterprise_id"/>
            <result property="enterpriseName" column="enterprise_name"/>
            <result property="unifiedSocialCreditCode" column="unified_social_credit_code"/>
            <result property="enterpriseType" column="enterprise_type"/>
            <result property="businessAddress" column="business_address"/>
            <result property="legalRepresentative" column="legal_representative"/>
            <result property="registrationRegionProvince" column="registration_region_province"/>
            <result property="registrationRegionCity" column="registration_region_city"/>
            <result property="registrationRegionArea" column="registration_region_area"/>
            <result property="registrationDate" column="registration_date"/>
            <result property="officePhone" column="office_phone"/>
            <result property="enterpriseStatus" column="enterprise_status"/>
            <result property="deptId" column="dept_id"/>
            <result property="enterpriseRole" column="enterprise_role"/>
        </collection>
    </resultMap>

    <sql id="selectPrjProjectsVo">
        SELECT *
        FROM (
                 SELECT p.*,
                     d1.dept_name as client_org_name,
                     d2.dept_name as construction_org_name,
                     d3.dept_name as supervision_org_name,
                     d4.dept_name as design_org_name,
                     d5.dept_name as survey_org_name,
                     d6.dept_name as installation_dismantling_org_name,
                     d7.dept_name as maintenance_org_name
              FROM prj_projects p
                       LEFT JOIN sys_dept d1 ON p.client_org_id = d1.dept_id
                       LEFT JOIN sys_dept d2 ON p.construction_org_id = d2.dept_id
                       LEFT JOIN sys_dept d3 ON p.supervision_org_id = d3.dept_id
                       LEFT JOIN sys_dept d4 ON p.design_org_id = d4.dept_id
                       LEFT JOIN sys_dept d5 ON p.survey_org_id = d5.dept_id
                       LEFT JOIN sys_dept d6 ON p.installation_dismantling_org_id = d6.dept_id
                       LEFT JOIN sys_dept d7 ON p.maintenance_org_id = d7.dept_id
             ) AS RESULT
    </sql>

    <select id="selectVoList" resultMap="PrjProjectsResult">
        <include refid="selectPrjProjectsVo"/>
        ${ew.getCustomSqlSegment()}
    </select>

    <select id="selectVoListSq" resultMap="PrjProjectsResult">
        <include refid="selectPrjProjectsVo"/>
        ${ew.getCustomSqlSegment()}
    </select>

    <select id="selectVoPage" resultMap="PrjProjectsResult">
        <include refid="selectPrjProjectsVo"/>
        ${ew.getCustomSqlSegment()}
    </select>

    <select id="selectVoPageSq" resultMap="PrjProjectsResult">
        <include refid="selectPrjProjectsVo"/>
        ${ew.getCustomSqlSegment()}
    </select>

    <select id="selectVoById" resultMap="PrjProjectsResult">
        SELECT
            p.*,
            d1.dept_name as client_org_name,
            d2.dept_name as construction_org_name,
            d3.dept_name as supervision_org_name,
            d4.dept_name as design_org_name,
            d5.dept_name as survey_org_name,
            d6.dept_name as supervising_qs_org_name,
            d7.dept_name as installation_dismantling_org_name,
            d8.dept_name as maintenance_org_name,
            e.enterprise_id,
            e.enterprise_name,
            e.unified_social_credit_code,
            e.enterprise_type,
            e.business_address,
            e.legal_representative,
            e.registration_region_province,
            e.registration_region_city,
            e.registration_region_area,
            e.registration_date,
            e.office_phone,
            e.enterprise_status,
            e.dept_id
        FROM prj_projects p
        LEFT JOIN sys_dept d1 ON p.client_org_id = d1.dept_id
        LEFT JOIN sys_dept d2 ON p.construction_org_id = d2.dept_id
        LEFT JOIN sys_dept d3 ON p.supervision_org_id = d3.dept_id
        LEFT JOIN sys_dept d4 ON p.design_org_id = d4.dept_id
        LEFT JOIN sys_dept d5 ON p.survey_org_id = d5.dept_id
        LEFT JOIN sys_dept d6 ON p.supervising_qs_org_id = d6.dept_id
        LEFT JOIN sys_dept d7 ON p.installation_dismantling_org_id = d7.dept_id
        LEFT JOIN sys_dept d8 ON p.maintenance_org_id = d8.dept_id
        LEFT JOIN sys_enterprise_info e ON e.dept_id IN (
            p.client_org_id,
            p.construction_org_id,
            p.supervision_org_id,
            p.design_org_id,
            p.survey_org_id,
            p.installation_dismantling_org_id,
            p.maintenance_org_id
        )
        WHERE p.project_id = #{projectId} and p.del_flag = 0
    </select>

    <select id="selectAll" resultMap="PrjProjectsResult">
        select project_id, project_name, project_code, construction_org_id
        from prj_projects
        ${ew.getCustomSqlSegment()}
    </select>

    <select id="selectAllSq" resultMap="PrjProjectsResult">
        select project_id, project_name, project_code, construction_org_id
        from prj_projects
        ${ew.getCustomSqlSegment()}
    </select>
</mapper>

package org.dromara.monito.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.monito.domain.DeviceMonito;
import org.dromara.monito.domain.DeviceMonitoToDeviceMonitoVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {DeviceMonitoToDeviceMonitoVoMapper.class},
    imports = {}
)
public interface DeviceMonitoVoToDeviceMonitoMapper extends BaseMapper<DeviceMonitoVo, DeviceMonito> {
}

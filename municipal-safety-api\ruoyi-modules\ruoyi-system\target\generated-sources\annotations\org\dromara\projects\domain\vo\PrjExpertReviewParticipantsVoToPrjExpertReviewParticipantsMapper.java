package org.dromara.projects.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.PrjExpertReviewParticipants;
import org.dromara.projects.domain.PrjExpertReviewParticipantsToPrjExpertReviewParticipantsVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjExpertReviewParticipantsToPrjExpertReviewParticipantsVoMapper.class},
    imports = {}
)
public interface PrjExpertReviewParticipantsVoToPrjExpertReviewParticipantsMapper extends BaseMapper<PrjExpertReviewParticipantsVo, PrjExpertReviewParticipants> {
}

package org.dromara.special.controller;

import java.util.List;
import java.util.stream.Collectors;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.service.IPrjProjectsService;
import org.dromara.special.domain.SpecialOperationPersonnel;
import org.dromara.system.service.ISysDictDataService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.special.domain.vo.SpecialOperationPersonnelVo;
import org.dromara.special.domain.bo.SpecialOperationPersonnelBo;
import org.dromara.special.service.ISpecialOperationPersonnelService;

/**
 * 特种作业人员信息
 *
 * <AUTHOR> Li
 * @date 2025-05-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/special/operationPersonnel")
public class SpecialOperationPersonnelController extends BaseController {

    private final ISpecialOperationPersonnelService specialOperationPersonnelService;
    private final ISysDictDataService dictDataService;
    private final IPrjProjectsService prjProjectsService;

    /**
     * 查询特种作业人员信息列表
     */
    @GetMapping("/list")
    public TableDataInfo<SpecialOperationPersonnelVo> list(SpecialOperationPersonnelBo bo, PageQuery pageQuery) {
        TableDataInfo<SpecialOperationPersonnelVo> specialOperationPersonnelVoTableDataInfo = specialOperationPersonnelService.queryPageList(bo, pageQuery);
        for (SpecialOperationPersonnelVo row : specialOperationPersonnelVoTableDataInfo.getRows()) {
            PrjProjectsVo prjProjectsVo = prjProjectsService.queryById(row.getProjectId());
            if (prjProjectsVo != null) {
                row.setProjectName(prjProjectsVo.getProjectName());
            }
        }
        return specialOperationPersonnelVoTableDataInfo;
    }

    /**
     * 导出特种作业人员信息列表
     */
    @Log(title = "特种作业人员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SpecialOperationPersonnelBo bo, HttpServletResponse response) {
        List<SpecialOperationPersonnelVo> list = specialOperationPersonnelService.queryList(bo);
        ExcelUtil.exportExcel(list, "特种作业人员信息", SpecialOperationPersonnelVo.class, response);
    }

    /**
     * 获取特种作业人员信息详细信息
     *
     * @param sopId 主键
     */
    @GetMapping("/{sopId}")
    public R<SpecialOperationPersonnelVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long sopId) {
        SpecialOperationPersonnelVo specialOperationPersonnelVo = specialOperationPersonnelService.queryById(sopId);
        PrjProjectsVo prjProjectsVo = prjProjectsService.queryById(specialOperationPersonnelVo.getProjectId());
        if (prjProjectsVo != null) {
            specialOperationPersonnelVo.setProjectName(prjProjectsVo.getProjectName());
        }
        return R.ok(specialOperationPersonnelVo);
    }

    /**
     * 新增特种作业人员信息
     */
    @Log(title = "特种作业人员信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SpecialOperationPersonnelBo bo) {
        SpecialOperationPersonnelBo query1 = new SpecialOperationPersonnelBo();
        query1.setCertificateNumber(bo.getCertificateNumber());
        List<SpecialOperationPersonnelVo> certificateList = specialOperationPersonnelService.queryList(query1);
        if (!certificateList.isEmpty()){
            return R.fail("该证件编号已存在!");
        }
        SpecialOperationPersonnelBo query = new SpecialOperationPersonnelBo();
        query.setIdCard(bo.getIdCard());
        List<SpecialOperationPersonnelVo> specialOperationPersonnelVos = specialOperationPersonnelService.queryList(query);
        if (!specialOperationPersonnelVos.isEmpty()) {
            List<String> operationCategories = specialOperationPersonnelVos.stream().map(SpecialOperationPersonnelVo::getOperationCategory).toList();
            if (operationCategories.contains(bo.getOperationCategory())){
                String label = dictDataService.selectDictLabel("special_operation_type", bo.getOperationCategory());
                return R.fail("该【"+label+"】类别下已存在证书信息!");
            }
        }
        return toAjax(specialOperationPersonnelService.insertByBo(bo));
    }

    /**
     * 修改特种作业人员信息
     */
    @Log(title = "特种作业人员信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SpecialOperationPersonnelBo bo) {
        SpecialOperationPersonnelBo query = new SpecialOperationPersonnelBo();
        query.setCertificateNumber(bo.getCertificateNumber());
        List<SpecialOperationPersonnelVo> specialOperationPersonnelVos = specialOperationPersonnelService.queryList(query);
        if (!specialOperationPersonnelVos.isEmpty()) {
            List<String> operationCategories = specialOperationPersonnelVos.stream().map(SpecialOperationPersonnelVo::getOperationCategory).toList();
            if (operationCategories.contains(bo.getOperationCategory())){
                String label = dictDataService.selectDictLabel("special_operation_type", bo.getOperationCategory());
                return R.fail("该【"+label+"】类别下已存在证书信息!");
            }
        }
        return toAjax(specialOperationPersonnelService.updateByBo(bo));
    }

    /**
     * 删除特种作业人员信息
     *
     * @param sopIds 主键串
     */
    @Log(title = "特种作业人员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{sopIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] sopIds) {
        return toAjax(specialOperationPersonnelService.deleteWithValidByIds(List.of(sopIds), true));
    }
}

export interface QualificationDictVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 名称
   */
  name: string;

  /**
   * 父级id
   */
  preId: string | number;

  /**
   * 子对象
   */
  children: QualificationDictVO[];
}

export interface QualificationDictForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 名称
   */
  name?: string;

  /**
   * 父级id
   */
  preId?: string | number;

}

export interface QualificationDictQuery {

  /**
   * 名称
   */
  name?: string;

  /**
   * 父级id
   */
  preId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}

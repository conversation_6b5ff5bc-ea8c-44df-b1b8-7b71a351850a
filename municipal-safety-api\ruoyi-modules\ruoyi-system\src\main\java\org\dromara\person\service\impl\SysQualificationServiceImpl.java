package org.dromara.person.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.person.domain.SysQualification;
import org.dromara.person.domain.bo.SysQualificationBo;
import org.dromara.person.domain.vo.SysQualificationVo;
import org.dromara.person.mapper.SysQualificationMapper;
import org.dromara.person.service.ISysQualificationService;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.ISysOssService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 人员资格证书Service业务层处理
 *
 * <AUTHOR> zu da
 * @date 2025-05-10
 */
@RequiredArgsConstructor
@Service
public class SysQualificationServiceImpl implements ISysQualificationService {

    private final SysQualificationMapper baseMapper;
    private final ISysOssService iSysOssService;

    /**
     * 查询人员资格证书
     *
     * @param qualificationId 主键
     * @return 人员资格证书
     */
    @Override
    public SysQualificationVo queryById(Long qualificationId) {
        return baseMapper.selectVoById(qualificationId);
    }

    /**
     * 分页查询人员资格证书列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 人员资格证书分页列表
     */
    @Override
    public TableDataInfo<SysQualificationVo> queryPageList(SysQualificationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysQualification> lqw = buildQueryWrapper(bo);
        Page<SysQualificationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        TableDataInfo<SysQualificationVo> dataInfo = TableDataInfo.build(result);

        List<SysQualificationVo> rows = dataInfo.getRows();

        if (!rows.isEmpty()) {
            List<Long> imageId = rows.stream().map(SysQualificationVo::getUploadPhoto).collect(Collectors.toList());

            List<SysOssVo> sysOssVos = iSysOssService.listByIds(imageId);

            Map<Long, String> ossMap = sysOssVos.stream().collect(Collectors.toMap(SysOssVo::getOssId, SysOssVo::getUrl));

            for (SysQualificationVo row : rows) {
                row.setUploadPhotoUrl(ossMap.get(row.getUploadPhoto()));
            }
        }

        return dataInfo;
    }

    /**
     * 查询符合条件的人员资格证书列表
     *
     * @param bo 查询条件
     * @return 人员资格证书列表
     */
    @Override
    public List<SysQualificationVo> queryList(SysQualificationBo bo) {
        LambdaQueryWrapper<SysQualification> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysQualification> buildQueryWrapper(SysQualificationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysQualification> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SysQualification::getQualificationId);
        lqw.eq(bo.getPersonId() != null, SysQualification::getPersonId, bo.getPersonId());
        lqw.eq(StringUtils.isNotBlank(bo.getCertificateType()), SysQualification::getCertificateType, bo.getCertificateType());
        lqw.like(StringUtils.isNotBlank(bo.getCertificateName()), SysQualification::getCertificateName, bo.getCertificateName());
        lqw.eq(StringUtils.isNotBlank(bo.getCertificateNumber()), SysQualification::getCertificateNumber, bo.getCertificateNumber());
        lqw.eq(bo.getAcquisitionTime() != null, SysQualification::getAcquisitionTime, bo.getAcquisitionTime());
        lqw.eq(StringUtils.isNotBlank(bo.getIssuingAuthority()), SysQualification::getIssuingAuthority, bo.getIssuingAuthority());
        lqw.eq(StringUtils.isNotBlank(bo.getCertificateLevel()), SysQualification::getCertificateLevel, bo.getCertificateLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrespondingPosition()), SysQualification::getCorrespondingPosition, bo.getCorrespondingPosition());
        lqw.eq(bo.getValidFrom() != null, SysQualification::getValidFrom, bo.getValidFrom());
        lqw.eq(bo.getValidTo() != null, SysQualification::getValidTo, bo.getValidTo());
        lqw.eq(bo.getUploadPhoto() != null, SysQualification::getUploadPhoto, bo.getUploadPhoto());
        return lqw;
    }

    /**
     * 新增人员资格证书
     *
     * @param bo 人员资格证书
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SysQualificationBo bo) {
        SysQualification add = MapstructUtils.convert(bo, SysQualification.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setQualificationId(add.getQualificationId());
        }
        return flag;
    }

    /**
     * 修改人员资格证书
     *
     * @param bo 人员资格证书
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SysQualificationBo bo) {
        SysQualification update = MapstructUtils.convert(bo, SysQualification.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysQualification entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除人员资格证书信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
            List<SysQualificationVo> voList = this.baseMapper.selectVoByIds(ids);
            List<Long> ossIds = voList.stream().map(SysQualificationVo::getUploadPhoto).toList();
            iSysOssService.deleteWithValidByIds(ossIds, true);
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}

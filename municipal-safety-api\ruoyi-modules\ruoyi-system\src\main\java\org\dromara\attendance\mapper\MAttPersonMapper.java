package org.dromara.attendance.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.attendance.domain.MAttPerson;
import org.dromara.attendance.domain.vo.MAttPersonVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * attPersonMapper接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface MAttPersonMapper extends BaseMapperPlus<MAttPerson, MAttPersonVo> {

    List<MAttPersonVo> selectMAttPersonByPersonId(@Param("projectPersonnelId") Long projectPersonnelId);

    List<MAttPersonVo> selectMAttPersonBySnId(@Param("snId") Long snId);

    MAttPersonVo selectMAttPersonByPersonIdAndSnId(@Param("snId") String snId, @Param("personId") Long personId);
}

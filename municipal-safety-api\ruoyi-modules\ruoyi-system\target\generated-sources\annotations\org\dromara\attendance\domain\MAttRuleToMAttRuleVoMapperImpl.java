package org.dromara.attendance.domain;

import javax.annotation.processing.Generated;
import org.dromara.attendance.domain.vo.MAttRuleVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class MAttRuleToMAttRuleVoMapperImpl implements MAttRuleToMAttRuleVoMapper {

    @Override
    public MAttRuleVo convert(MAttRule arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MAttRuleVo mAttRuleVo = new MAttRuleVo();

        mAttRuleVo.setId( arg0.getId() );
        mAttRuleVo.setRuleType( arg0.getRuleType() );
        mAttRuleVo.setProjectId( arg0.getProjectId() );
        mAttRuleVo.setPersonType( arg0.getPersonType() );
        mAttRuleVo.setIsAll( arg0.getIsAll() );
        mAttRuleVo.setCheckTime( arg0.getCheckTime() );
        mAttRuleVo.setElasticTime( arg0.getElasticTime() );
        mAttRuleVo.setWarning( arg0.getWarning() );
        mAttRuleVo.setFieldCheck( arg0.getFieldCheck() );
        mAttRuleVo.setContent( arg0.getContent() );

        return mAttRuleVo;
    }

    @Override
    public MAttRuleVo convert(MAttRule arg0, MAttRuleVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setRuleType( arg0.getRuleType() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setPersonType( arg0.getPersonType() );
        arg1.setIsAll( arg0.getIsAll() );
        arg1.setCheckTime( arg0.getCheckTime() );
        arg1.setElasticTime( arg0.getElasticTime() );
        arg1.setWarning( arg0.getWarning() );
        arg1.setFieldCheck( arg0.getFieldCheck() );
        arg1.setContent( arg0.getContent() );

        return arg1;
    }
}

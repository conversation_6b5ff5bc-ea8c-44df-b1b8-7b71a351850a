package org.dromara.projects.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.projects.domain.PrjConstructionPlans;

import java.io.Serial;
import java.io.Serializable;


/**
 * [项目管理] 存储危大工程专项施工方案信息及其审批状态视图对象 prj_construction_plans
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjConstructionPlans.class)
public class PrjConstructionPlansVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 方案ID (主键)
     */
    @ExcelProperty(value = "方案ID (主键)")
    private Long planId;

    /**
     * 危大工程项ID
     */
    @ExcelProperty(value = "危大工程项ID")
    private Long itemId;

    /**
     * 专项施工方案名称
     */
    @ExcelProperty(value = "专项施工方案名称")
    private String planName;

    /**
     * 方案版本号
     */
    @ExcelProperty(value = "方案版本号")
    private String planVersion;

    /**
     * 专项施工方案电子版文件ID
     */
    @ExcelProperty(value = "专项施工方案电子版文件ID")
    private Long planDocumentId;

    /**
     * 专项施工方案审批表文件ID
     */
    @ExcelProperty(value = "专项施工方案审批表文件ID ")
    private Long approvalFormDocId;

    /**
     * AI校验状态
     */
    @ExcelProperty(value = "AI校验状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "plan_ai_check_status")
    private String reviewStatus;

    /**
     * AI对比方案缺陷预警提示内容
     */
    @ExcelProperty(value = "AI对比方案缺陷预警提示内容")
    private String aiDefectWarningDetails;

    /**
     * 论证结论 (通过, 修改后通过, 不通过)
     */
    @ExcelProperty(value = "论证结论 (通过, 修改后通过, 不通过)")
    private String conclusion;
}

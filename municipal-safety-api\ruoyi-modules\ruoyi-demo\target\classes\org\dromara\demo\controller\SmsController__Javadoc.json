{"doc": " 短信演示案例\n 请先阅读文档 否则无法使用\n\n <AUTHOR> Li\n @version 4.2.0\n", "fields": [], "enumConstants": [], "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 发送短信Aliyun\n\n @param phones     电话号\n @param templateId 模板ID\n"}, {"name": "sendTencent", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 发送短信Tencent\n\n @param phones     电话号\n @param templateId 模板ID\n"}, {"name": "addBlacklist", "paramTypes": ["java.lang.String"], "doc": " 添加黑名单\n\n @param phone 手机号\n"}, {"name": "removeBlacklist", "paramTypes": ["java.lang.String"], "doc": " 移除黑名单\n\n @param phone 手机号\n"}], "constructors": []}
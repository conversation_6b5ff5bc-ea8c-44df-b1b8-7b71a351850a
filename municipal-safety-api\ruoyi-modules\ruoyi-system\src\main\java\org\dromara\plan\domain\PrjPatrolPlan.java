package org.dromara.plan.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 巡检计划对象 prj_patrol_plan
 *
 * <AUTHOR> Li
 * @date 2025-06-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prj_patrol_plan")
public class PrjPatrolPlan extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "plan_id")
    private Long planId;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 计划开始时间
     */
    private Date beginTime;

    /**
     * 计划结束时间
     */
    private Date endTime;

    /**
     * 参与机构id
     */
    private String deptIds;

    /**
     * 检查项目id
     */
    private String projectIds;

    /**
     * 专家ids
     */
    private String expertIds;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;


}

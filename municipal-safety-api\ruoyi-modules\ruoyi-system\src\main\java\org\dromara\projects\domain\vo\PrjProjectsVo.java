package org.dromara.projects.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.monito.domain.vo.DeviceMonitoVo;
import org.dromara.projects.domain.PrjProjects;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 项目录入视图对象 prj_projects
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjProjects.class)
public class PrjProjectsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID")
    private Long projectId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目编码/标识
     */
    @ExcelProperty(value = "项目编码/标识")
    private String projectCode;

    /**
     * 工程概况 (对应附件一.1)
     */
    @ExcelProperty(value = "工程概况 (对应附件一.1)")
    private String projectOverview;

    /**
     * 施工许可证编号 (对应附件一.2)
     */
    @ExcelProperty(value = "施工许可证编号 (对应附件一.2)")
    private String constructionPermitNo;

    /**
     * 施工许可证扫描件文档ID (逻辑外键至 sys_documents.document_id)
     */
    @ExcelProperty(value = "施工许可证扫描件文档ID (逻辑外键至 sys_documents.document_id)")
    private Long constructionPermitDocId;

    /**
     * 项目位置（经纬度）
     */
    @ExcelProperty(value = "项目位置", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "经=纬度")
    private String lola;

    /**
     * 省/直辖市编码
     */
    @ExcelProperty(value = "省/直辖市编码")
    private String provinceCode;

    /**
     * 省/直辖市名称
     */
    @ExcelProperty(value = "省/直辖市名称")
    private String provinceName;

    /**
     * 市编码
     */
    @ExcelProperty(value = "市编码")
    private String cityCode;

    /**
     * 市名称
     */
    @ExcelProperty(value = "市名称")
    private String cityName;

    /**
     * 区/县编码
     */
    @ExcelProperty(value = "区/县编码")
    private String districtCode;

    /**
     * 区/县名称
     */
    @ExcelProperty(value = "区/县名称")
    private String districtName;

    /**
     * 乡镇/街道编码 (可选)
     */
    @ExcelProperty(value = "乡镇/街道编码 (可选)")
    private String countyCode;

    /**
     * 乡镇/街道名称 (可选)
     */
    @ExcelProperty(value = "乡镇/街道名称 (可选)")
    private String countyName;

    /**
     * 详细地址
     */
    @ExcelProperty(value = "详细地址")
    private String locationDetail;

    /**
     * 占地面积(平方米)
     */
    @ExcelProperty(value = "占地面积(平方米)")
    private BigDecimal siteArea;

    /**
     * 预算投资总额(万元)
     */
    @ExcelProperty(value = "预算投资总额(万元)")
    private BigDecimal budgetTotal;

    /**
     * 项目所属质监站机构ID
     */
    private Long supervisingQsOrgId;

    /**
     * 项目状态
     */
    @ExcelProperty(value = "项目状态")
    private String status;

    /**
     * 计划开工日期
     */
    @ExcelProperty(value = "计划开工日期")
    private Date startDate;

    /**
     * 计划竣工日期
     */
    @ExcelProperty(value = "计划竣工日期")
    private Date plannedEndDate;

    /**
     * 实际开工日期
     */
    @ExcelProperty(value = "实际开工日期")
    private Date actualStartDate;

    /**
     * 实际竣工日期
     */
    @ExcelProperty(value = "实际竣工日期")
    private Date actualEndDate;

    /**
     * 建设单位ID (逻辑外键至 sys_dept.dept_id)
     */
    @ExcelProperty(value = "建设单位ID (逻辑外键至 sys_dept.dept_id)")
    private Long clientOrgId;

    /**
     * 建设单位名称
     */
    @ExcelProperty(value = "建设单位名称")
    private String clientOrgName;

    /**
     * 施工总包单位ID (逻辑外键至 sys_dept.dept_id)
     */
    @ExcelProperty(value = "施工总包单位ID (逻辑外键至 sys_dept.dept_id)")
    private Long constructionOrgId;

    /**
     * 施工总包单位名称
     */
    @ExcelProperty(value = "施工总包单位名称")
    private String constructionOrgName;

    /**
     * 监理单位ID (逻辑外键至 sys_dept.dept_id)
     */
    @ExcelProperty(value = "监理单位ID (逻辑外键至 sys_dept.dept_id)")
    private Long supervisionOrgId;

    /**
     * 监理单位名称
     */
    @ExcelProperty(value = "监理单位名称")
    private String supervisionOrgName;

    /**
     * 设计单位ID (逻辑外键至 sys_dept.dept_id)
     */
    @ExcelProperty(value = "设计单位ID (逻辑外键至 sys_dept.dept_id)")
    private Long designOrgId;

    /**
     * 设计单位名称
     */
    @ExcelProperty(value = "设计单位名称")
    private String designOrgName;

    /**
     * 勘察单位ID (逻辑外键至 sys_dept.dept_id)
     */
    @ExcelProperty(value = "勘察单位ID (逻辑外键至 sys_dept.dept_id)")
    private Long surveyOrgId;

    /**
     * 勘察单位名称
     */
    @ExcelProperty(value = "勘察单位名称")
    private String surveyOrgName;

    /**
     * 安拆单位ID (逻辑外键至 sys_dept.dept_id)
     */
    @ExcelProperty(value = "安拆单位ID (逻辑外键至 sys_dept.dept_id)")
    private Long installationDismantlingOrgId;

    /**
     * 安拆单位名称
     */
    @ExcelProperty(value = "安拆单位名称")
    private String installationDismantlingOrgName;

    /**
     * 维保单位ID (逻辑外键至 sys_dept.dept_id)
     */
    @ExcelProperty(value = "维保单位ID (逻辑外键至 sys_dept.dept_id)")
    private Long maintenanceOrgId;

    /**
     * 维保单位名称
     */
    @ExcelProperty(value = "维保单位名称")
    private String maintenanceOrgName;


    /**
     * 专业分包单位ID列表 (JSON数组，元素为 sys_organizations.org_id)
     */
    @ExcelProperty(value = "专业分包单位ID列表 (JSON数组，元素为 sys_organizations.org_id)")
    private String subcontractorOrgIds;

    /**
     * 施工单位项目负责人ID (逻辑外键至 sys_users.user_id)
     */
    @ExcelProperty(value = "施工单位项目负责人ID (逻辑外键至 sys_users.user_id)")
    private Long projectManagerUserId;

    /**
     * 监理单位总监ID (逻辑外键至 sys_users.user_id)
     */
    @ExcelProperty(value = "监理单位总监ID (逻辑外键至 sys_users.user_id)")
    private Long supervisionChiefEngUserId;

    /**
     * 危大工程安全防护文明施工措施费财务凭证文档ID (对应附件三.1)
     */
    @ExcelProperty(value = "危大工程安全防护文明施工措施费财务凭证文档ID (对应附件三.1)")
    private Long safetyMeasuresFeeDocId;

    private String supervisingQsOrgName;

    /**
     * 关联的企业信息列表 (五方单位)
     */
    private List<PrjSysEnterpriseInfoVo> enterpriseList;

    /**
     * 项目人员列表
     */
    private List<PrjPersonnelVo> personnelList;

    /** 监控列表 */
    private List<DeviceMonitoVo> monitoList;
}

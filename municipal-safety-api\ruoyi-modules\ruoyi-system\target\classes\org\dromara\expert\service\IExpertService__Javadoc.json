{"doc": "  专家主Service接口\n @date 2025-05-03\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询 专家主\n\n @param expertId 主键\n @return  专家主\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.expert.domain.bo.ExpertBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询 专家主列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return  专家主分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.expert.domain.bo.ExpertBo"], "doc": " 查询符合条件的 专家主列表\n\n @param bo 查询条件\n @return  专家主列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.expert.domain.bo.ExpertBo"], "doc": " 新增 专家主\n\n @param bo  专家主\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.expert.domain.bo.ExpertBo"], "doc": " 修改 专家主\n\n @param bo  专家主\n @return 是否修改成功\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除 专家主信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
package org.dromara.facility.service.impl;

import com.alibaba.fastjson2.JSON;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.facility.domain.bo.JlDustRealBo;
import org.dromara.facility.domain.vo.JlDustRealVo;
import org.dromara.facility.domain.JlDustReal;
import org.dromara.facility.mapper.JlDustRealMapper;
import org.dromara.facility.service.IJlDustRealService;

import java.util.Date;
import java.util.List;
import java.util.Collection;

/**
 * 扬尘数据Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@RequiredArgsConstructor
@Service
public class JlDustRealServiceImpl implements IJlDustRealService {

    private final JlDustRealMapper baseMapper;

    /**
     * 查询扬尘数据
     *
     * @param id 主键
     * @return 扬尘数据
     */
    @Override
    public JlDustRealVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询扬尘数据列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 扬尘数据分页列表
     */
    @Override
    public TableDataInfo<JlDustRealVo> queryPageList(JlDustRealBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<JlDustReal> lqw = buildQueryWrapper(bo);
        Page<JlDustRealVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的扬尘数据列表
     *
     * @param bo 查询条件
     * @return 扬尘数据列表
     */
    @Override
    public List<JlDustRealVo> queryList(JlDustRealBo bo) {
        LambdaQueryWrapper<JlDustReal> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<JlDustReal> buildQueryWrapper(JlDustRealBo bo) {
        LambdaQueryWrapper<JlDustReal> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(JlDustReal::getCreateTime);
        lqw.eq(StringUtils.isNotBlank(bo.getMn()), JlDustReal::getMn, bo.getMn());
        return lqw;
    }

    /**
     * 新增扬尘数据
     *
     * @param bo 扬尘数据
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(JlDustRealBo bo) {
        JlDustReal add = MapstructUtils.convert(bo, JlDustReal.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改扬尘数据
     *
     * @param bo 扬尘数据
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(JlDustRealBo bo) {
        JlDustReal update = MapstructUtils.convert(bo, JlDustReal.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(JlDustReal entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除扬尘数据信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public void insertByJson(String jsonString) {
        JlDustRealBo bo = JSON.parseObject(jsonString, JlDustRealBo.class);

        JlDustReal add = MapstructUtils.convert(bo, JlDustReal.class);
        add.setCreateTime(new Date());
        baseMapper.insert(add);
    }
}

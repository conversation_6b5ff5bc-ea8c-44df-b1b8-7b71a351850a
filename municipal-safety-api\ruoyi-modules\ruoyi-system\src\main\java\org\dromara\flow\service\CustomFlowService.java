package org.dromara.flow.service;

import com.alibaba.fastjson2.JSONObject;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.projects.domain.vo.ItemsAiDetailVO;
import org.dromara.workflow.domain.bo.FlowTaskBo;
import org.dromara.workflow.domain.vo.FlowTaskVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28 15:10
 * @Description TODO
 * @Version 1.0
 */
public interface CustomFlowService {

    /**
     * 业务流程id
     *
     * @param busId
     * @return
     */
    JSONObject getVariable(String busId);

    ItemsAiDetailVO getAiHazAnalysisTaskDetail(Long id);

    TableDataInfo<FlowTaskVo> pageByTaskWaitZjj(FlowTaskBo flowTaskBo, PageQuery pageQuery);

    void removeByInstanceIds(List<Long> instanceIds);
}

{"doc": " 项目人员关联表\n\n <AUTHOR>\n @date 2025-05-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.projects.domain.bo.PrjPersonnelBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询项目人员关联表列表\n"}, {"name": "export", "paramTypes": ["org.dromara.projects.domain.bo.PrjPersonnelBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出项目人员关联表列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取项目人员关联表详细信息\n\n @param projectPersonnelId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.projects.domain.bo.PrjPersonnelBo"], "doc": " 新增项目人员关联表\n"}, {"name": "edit", "paramTypes": ["org.dromara.projects.domain.bo.PrjPersonnelBo"], "doc": " 修改项目人员关联表\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除项目人员关联表\n\n @param projectPersonnelIds 主键串\n"}, {"name": "listByProjectId", "paramTypes": ["java.lang.Long"], "doc": " 获取项目人员列表\n\n @param projectId 项目ID\n"}, {"name": "batchDistribution", "paramTypes": ["java.util.List"], "doc": " 批量下发人脸到考勤机(第三方接口)\n CompletableFuture<Void>\n @param baseVisitor\n @return\n"}, {"name": "removeSns", "paramTypes": ["org.dromara.attendance.domain.so.ClearFace"], "doc": " 删除人脸信息\n @param clearFace 人脸参数\n @return 返回值\n @throws IOException 异常处理\n"}, {"name": "sendPostRequest", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 发送POST请求到第三方接口\n\n @param data   要发送的数据列表\n @param apiUrl 完整的API地址\n @return HTTP状态码\n @throws Exception 如果发生网络或IO错误\n"}, {"name": "readResponse", "paramTypes": ["java.net.HttpURLConnection"], "doc": " 读取响应内容\n"}, {"name": "sendDeleteRequest", "paramTypes": ["java.lang.String"], "doc": " 发送 DELETE 请求\n\n @return HTTP状态码 (如 200, 404, 500等)\n @throws IOException 如果请求失败\n"}, {"name": "readResponseBody", "paramTypes": ["java.net.HttpURLConnection", "int"], "doc": " 读取响应内容\n"}], "constructors": []}
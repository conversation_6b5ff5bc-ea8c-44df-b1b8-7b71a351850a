{"doc": " 测试单表对象 test_demo\n\n <AUTHOR>\n @date 2021-07-26\n", "fields": [{"name": "id", "doc": " 主键\n"}, {"name": "deptId", "doc": " 部门id\n"}, {"name": "userId", "doc": " 用户id\n"}, {"name": "orderNum", "doc": " 排序号\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": " key键\n"}, {"name": "value", "doc": " 值\n"}, {"name": "version", "doc": " 版本\n"}, {"name": "delFlag", "doc": " 删除标志\n"}], "enumConstants": [], "methods": [], "constructors": []}
package org.dromara.quality.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 质量管理概览数据VO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QualityOverviewVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备统计
     */
    private DeviceStats deviceStats;

    /**
     * 测量统计
     */
    private MeasurementStats measurementStats;

    /**
     * 待处理任务
     */
    private PendingTasks pendingTasks;

    /**
     * 设备统计数据
     */
    @Data
    public static class DeviceStats implements Serializable {
        /**
         * 设备总数
         */
        private Integer total;

        /**
         * 设备数量较上月增长百分比
         */
        private Integer increase;
    }

    /**
     * 测量统计数据
     */
    @Data
    public static class MeasurementStats implements Serializable {
        /**
         * 测量记录总数
         */
        private Integer total;

        /**
         * 正常测量记录数
         */
        private Integer normal;

        /**
         * 隐患记录数
         */
        private Integer hazard;

        /**
         * 测量记录较上月增长百分比
         */
        private Integer increase;

        /**
         * 正常率百分比
         */
        private Integer normalRate;

        /**
         * 隐患率百分比
         */
        private Integer hazardRate;
    }

    /**
     * 待处理任务
     */
    @Data
    public static class PendingTasks implements Serializable {
        /**
         * 待处理隐患数量
         */
        private Integer hazardCount;

        /**
         * 待复测项目数量
         */
        private Integer retestCount;

        /**
         * 待生成报告数量
         */
        private Integer reportCount;
    }
} 
package org.dromara.quality.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 图表数据VO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChartDataVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日期数组，格式为 MM-DD
     */
    private List<String> dates;

    /**
     * 正常测量数据数组
     */
    private List<Integer> normalData;

    /**
     * 异常测量数据数组
     */
    private List<Integer> abnormalData;

    /**
     * 隐患记录数据数组
     */
    private List<Integer> hazardData;
} 
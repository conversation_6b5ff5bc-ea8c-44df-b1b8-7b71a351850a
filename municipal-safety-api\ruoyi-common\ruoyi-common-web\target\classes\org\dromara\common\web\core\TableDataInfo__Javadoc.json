{"doc": " 表格分页数据对象\n\n <AUTHOR>\n", "fields": [{"name": "total", "doc": "总记录数 "}, {"name": "rows", "doc": "列表数据 "}, {"name": "code", "doc": "消息状态码 "}, {"name": "msg", "doc": "消息内容 "}], "enumConstants": [], "methods": [], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " 表格数据对象\n"}, {"name": "<init>", "paramTypes": ["java.util.List", "int"], "doc": " 分页\n\n @param list 列表数据\n @param total 总记录数\n"}]}
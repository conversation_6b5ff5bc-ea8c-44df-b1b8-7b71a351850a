package org.dromara.facility.domain.bo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.facility.domain.LnEdgeGuard;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.Date;

/**
 * 绿能临边防护业务对象 ln_edge_guard
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LnEdgeGuard.class, reverseConvertGenerate = false)
public class LnEdgeGuardBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 编号
     */
    private Long dumpnumber;

    /**
     * 开关状态
     */
    private Long checkSensor;

    /**
     * 经度
     */
    private Long longitude;

    /**
     * 纬度
     */
    private Long latitude;

    /**
     * 电池电压
     */
    private Long batvolt;

    /**
     * 电池电量
     */
    private Long batPercent;

    /**
     * 报警信息
     */
    private Long alarmInfo;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

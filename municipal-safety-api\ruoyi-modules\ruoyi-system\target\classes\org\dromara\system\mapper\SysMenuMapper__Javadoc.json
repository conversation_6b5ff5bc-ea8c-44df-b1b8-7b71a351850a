{"doc": " 菜单表 数据层\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectMenuListByUserId", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 根据用户查询系统菜单列表\n\n @param queryWrapper 查询条件\n @return 菜单列表\n"}, {"name": "selectMenuPermsByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询权限\n\n @param userId 用户ID\n @return 权限列表\n"}, {"name": "selectMenuPermsByRoleId", "paramTypes": ["java.lang.Long"], "doc": " 根据角色ID查询权限\n\n @param roleId 角色ID\n @return 权限列表\n"}, {"name": "selectMenuTreeAll", "paramTypes": [], "doc": " 根据用户ID查询菜单\n\n @return 菜单列表\n"}, {"name": "selectMenuTreeByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询菜单\n\n @param userId 用户ID\n @return 菜单列表\n"}, {"name": "selectMenuListByRoleId", "paramTypes": ["java.lang.Long", "boolean"], "doc": " 根据角色ID查询菜单树信息\n\n @param roleId            角色ID\n @param menuCheckStrictly 菜单树选择项是否关联显示\n @return 选中菜单列表\n"}], "constructors": []}
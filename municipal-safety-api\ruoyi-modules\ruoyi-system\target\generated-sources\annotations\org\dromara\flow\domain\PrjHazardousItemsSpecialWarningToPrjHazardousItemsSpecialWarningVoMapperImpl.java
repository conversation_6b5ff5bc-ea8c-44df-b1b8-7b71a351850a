package org.dromara.flow.domain;

import javax.annotation.processing.Generated;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialWarningVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsSpecialWarningToPrjHazardousItemsSpecialWarningVoMapperImpl implements PrjHazardousItemsSpecialWarningToPrjHazardousItemsSpecialWarningVoMapper {

    @Override
    public PrjHazardousItemsSpecialWarningVo convert(PrjHazardousItemsSpecialWarning arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsSpecialWarningVo prjHazardousItemsSpecialWarningVo = new PrjHazardousItemsSpecialWarningVo();

        prjHazardousItemsSpecialWarningVo.setWarningId( arg0.getWarningId() );
        prjHazardousItemsSpecialWarningVo.setTaskId( arg0.getTaskId() );
        prjHazardousItemsSpecialWarningVo.setReason( arg0.getReason() );
        prjHazardousItemsSpecialWarningVo.setReasonType( arg0.getReasonType() );
        prjHazardousItemsSpecialWarningVo.setCreateTime( arg0.getCreateTime() );

        return prjHazardousItemsSpecialWarningVo;
    }

    @Override
    public PrjHazardousItemsSpecialWarningVo convert(PrjHazardousItemsSpecialWarning arg0, PrjHazardousItemsSpecialWarningVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setWarningId( arg0.getWarningId() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setReason( arg0.getReason() );
        arg1.setReasonType( arg0.getReasonType() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}

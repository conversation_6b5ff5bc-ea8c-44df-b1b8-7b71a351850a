<template>
  <div class="monitor">
    <!-- 监控视频的弹框 -->
    <el-dialog :title="monitorDialog.title" v-model="monitorDialog.visible" width="700px" append-to-body>
      <!-- <ifreme :src="monitorDialog.iframeUrl" frameborder="no" border="0" allowfullscreen width="100%" height="100%">
      </ifreme> -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
const monitorDialog = reactive<DialogOption>({
  visible: false,
  title: ''
})
const cancel = () => {
  monitorDialog.visible = false
}
</script>

<style scoped></style>
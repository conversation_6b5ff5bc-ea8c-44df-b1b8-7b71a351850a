package org.dromara.projects.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.bo.PrjSafeUserBoToPrjSafeUserMapper;
import org.dromara.projects.domain.vo.PrjSafeUserVo;
import org.dromara.projects.domain.vo.PrjSafeUserVoToPrjSafeUserMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjSafeUserBoToPrjSafeUserMapper.class,PrjSafeUserVoToPrjSafeUserMapper.class},
    imports = {}
)
public interface PrjSafeUserToPrjSafeUserVoMapper extends BaseMapper<PrjSafeUser, PrjSafeUserVo> {
}

package org.dromara.attendance.service;

import org.dromara.attendance.domain.vo.MAttSnVo;
import org.dromara.attendance.domain.bo.MAttSnBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 考勤设备Service接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface IMAttSnService {

    /**
     * 查询考勤设备
     *
     * @param snId 主键
     * @return 考勤设备
     */
    MAttSnVo queryById(Long snId);

    /**
     * 分页查询考勤设备列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 考勤设备分页列表
     */
    TableDataInfo<MAttSnVo> queryPageList(MAttSnBo bo, PageQuery pageQuery);

    /**
     * 分页查询考勤设备列表
     * @param bo
     * @return
     */
    List<MAttSnVo> selectMAttSnList(MAttSnBo bo);

    /**
     * 查询符合条件的考勤设备列表
     *
     * @param bo 查询条件
     * @return 考勤设备列表
     */
    List<MAttSnVo> queryList(MAttSnBo bo);

    /**
     * 新增考勤设备
     *
     * @param bo 考勤设备
     * @return 是否新增成功
     */
    Boolean insertByBo(MAttSnBo bo);

    /**
     * 修改考勤设备
     *
     * @param bo 考勤设备
     * @return 是否修改成功
     */
    Boolean updateByBo(MAttSnBo bo);

    /**
     * 校验并批量删除考勤设备信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    MAttSnVo selectMAttSnBySn(String sn);

    List<MAttSnVo> selectMAttSnByProjectId(Long projectId);
}

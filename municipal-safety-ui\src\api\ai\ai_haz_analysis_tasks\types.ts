export interface Ai_haz_analysis_tasksVO {
  /**
   * 分析任务ID
   */
  taskId: string | number;
 


  /**
   * 关联项目ID
   */
  projectId: string | number;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 工程名称
   */
  itemName?: string;

  /**
   * 危大类型
   */
  dangerListType?: string;

  /**
   * 涉危工程
   */
  parentName?: string;

  /**
   * 提交分析的专家用户ID
   */
  expertUserId: string | number;

  /**
   * 照片上传时间
   */
  uploadTime: string;

  /**
   * 上传的照片文档ID
   */
  photoDocumentId: string | number;

  /**
   * AI处理后的照片文档ID
   */
  aiPhotoDocumentId?: string;

  /**
   * 拍照时GPS坐标
   */
  gpsLocation: string;

  /**
   * 拍照位置文字描述
   */
  locationDescription: string;

  /**
   * AI模型识别原始输出结果
   */
  aiRecognitionRawResult: string;

  /**
   * 复查状态
   */
  recheckStatus?: string;

  /**
   * 项目信息
   */
  prjProjectsVo?: {
    projectId: string;
    projectName: string;
    projectCode: string;
    clientOrgName: string;
    constructionOrgName: string;
    supervisionOrgName: string;
    [key: string]: any;
  };

  /**
   * 危大工程信息
   */
  prjHazardousItemsVo?: {
    itemId: string;
    itemName: string;
    parentName: string;
    scopeDetails: string;
    [key: string]: any;
  };

  /**
   * AI识别出的潜在危大工程类型/场景列表
   */
  aiIdentifiedHazards: string | number;

  /**
   * 专家最终选择确认的与照片内容相关的危大工程类型列表
   */
  expertSelectedHazardTypes: string;

  /**
   * 专家手动输入的具体隐患描述或补充说明
   */
  expertManualInputDetails: string;

  /**
   * 关联的已知危大工程项ID
   */
  relatedHazardousItemId: string | number;

  /**
   * 专家确认/手动输入完成时间
   */
  expertConfirmationTime: string;

  /**
   * 任务状态
   */
  status: string;

  /**
   * 预警来源
   */
  sourceType: string;

  /**
   * 因此次分析发现问题而生成的工单ID
   */
  relatedWorkOrderId: string | number;

}

export interface Ai_haz_analysis_tasksForm extends BaseEntity {
  /**
   * 分析任务ID
   */
  taskId?: string | number;

  /**
   * 关联项目ID
   */
  projectId?: string | number;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 工程名称
   */
  itemName?: string;

  /**
   * 危大类型
   */
  dangerListType?: string;

  /**
   * 涉危工程
   */
  parentName?: string;

  /**
   * 提交分析的专家用户ID
   */
  expertUserId?: string | number;

  /**
   * 照片上传时间
   */
  uploadTime?: string;

  /**
   * 上传的照片文档ID
   */
  photoDocumentId?: string | number;

  /**
   * 上传的照片文档URL
   */
  photoDocumentUrl?: string;

  /**
   * AI处理后的照片文档ID
   */
  aiPhotoDocumentId?: string;

  /**
   * AI处理后的照片文档URL
   */
  aiPhotoDocumentUrl?: string;

  /**
   * 拍照时GPS坐标
   */
  gpsLocation?: string;

  /**
   * 拍照位置文字描述
   */
  locationDescription?: string;

  /**
   * AI模型识别原始输出结果
   */
  aiRecognitionRawResult?: string;

  /**
   * 复查状态
   */
  recheckStatus?: string;

  /**
   * 项目信息
   */
  prjProjectsVo?: {
    projectId: string;
    projectName: string;
    projectCode: string;
    clientOrgName: string;
    constructionOrgName: string;
    supervisionOrgName: string;
    [key: string]: any;
  };

  /**
   * 危大工程信息
   */
  prjHazardousItemsVo?: {
    itemId: string;
    itemName: string;
    parentName: string;
    scopeDetails: string;
    [key: string]: any;
  };

  /**
   * AI识别出的潜在危大工程类型/场景列表
   */
  aiIdentifiedHazards?: string | number;

  /**
   * 专家最终选择确认的与照片内容相关的危大工程类型列表
   */
  expertSelectedHazardTypes?: string;

  /**
   * 专家手动输入的具体隐患描述或补充说明
   */
  expertManualInputDetails?: string;

  /**
   * 关联的已知危大工程项ID
   */
  relatedHazardousItemId?: string | number;

  /**
   * 专家确认/手动输入完成时间
   */
  expertConfirmationTime?: string;

  /**
   * 任务状态
   */
  status?: string;

  /**
   * 预警来源
   */
  sourceType?: string;

  /**
   * 因此次分析发现问题而生成的工单ID
   */
  relatedWorkOrderId?: string | number;

}

export interface Ai_haz_analysis_tasksQuery extends PageQuery {
  planId: string | number |null;
  /**
   * 关联项目ID
   */
  projectId?: string | number;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 工程名称
   */
  itemName?: string;

  /**
   * 查询类型，project-项目，item-工程
   */
  type?: string;

  /**
   * 提交分析的专家用户ID
   */
  expertUserId?: string | number;

  /**
   * 照片上传时间
   */
  uploadTime?: string;

  /**
   * 上传的照片文档ID
   */
  photoDocumentId?: string | number;

  /**
   * 拍照时GPS坐标
   */
  gpsLocation?: string;

  /**
   * 拍照位置文字描述
   */
  locationDescription?: string;

  /**
   * AI模型识别原始输出结果
   */
  aiRecognitionRawResult?: string;

  /**
   * AI识别出的潜在危大工程类型/场景列表
   */
  aiIdentifiedHazards?: string | number;

  /**
   * 专家最终选择确认的与照片内容相关的危大工程类型列表
   */
  expertSelectedHazardTypes?: string;

  /**
   * 专家手动输入的具体隐患描述或补充说明
   */
  expertManualInputDetails?: string;

  /**
   * 关联的已知危大工程项ID
   */
  relatedHazardousItemId?: string | number;

  /**
   * 专家确认/手动输入完成时间
   */
  expertConfirmationTime?: string;

  /**
   * 任务状态
   */
  status?: string;

  /**
   * 因此次分析发现问题而生成的工单ID
   */
  relatedWorkOrderId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}




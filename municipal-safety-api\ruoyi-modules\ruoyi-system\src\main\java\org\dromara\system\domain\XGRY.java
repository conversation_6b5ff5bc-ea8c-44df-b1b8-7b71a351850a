package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("xgry")
public class XGRY extends BaseEntity {

    @TableId(value = "id")
    private Integer id;
    private String recordNum;             // 备案编号
    private String constructionPermitNum; // 施工许可证编号
    private String name;                  // 姓名
    private String identityCard;          // 身份证号码
    private String corpName;              // 单位名称
    private String corpCode;              // 单位统一社会信用代码
    private String lxdh;                  // 联系电话
    private String lbfl;                  // 类别分类
    private String personType;           // 人员类别
    private String zsbh;                  // 证书编号
    @TableLogic
    private String delFlag;  //删除标志（0代表存在 1代表删除）
}

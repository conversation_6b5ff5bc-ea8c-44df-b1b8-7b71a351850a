package org.dromara.attendance.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.attendance.domain.bo.MAttSnBo;
import org.dromara.attendance.domain.vo.MAttPersonVo;
import org.dromara.attendance.domain.vo.MAttSnVo;
import org.dromara.attendance.service.IMAttPersonService;
import org.dromara.attendance.service.IMAttSnService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.service.IPrjProjectsService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 考勤设备
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/attendance/attSn")
public class MAttSnController extends BaseController {

    private final IMAttSnService mAttSnService;

    @Resource
    private IMAttPersonService imAttPersonService;

    @Resource
    private IPrjProjectsService iPrjProjectsService;

    @Value("${face.apiUrl}")
    private String apiUrl;

    /**
     * 查询考勤设备列表
     */
    @SaCheckPermission("attendance:attSn:list")
    @GetMapping("/list")
    public TableDataInfo<MAttSnVo> list(MAttSnBo bo, PageQuery pageQuery) {
        return mAttSnService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询考勤设备列表
     */
    @SaCheckPermission("attendance:attSn:list")
    @GetMapping("/selectAll")
    public org.dromara.common.web.core.TableDataInfo selectAll(MAttSnBo bo) {
        startPage();
        return getDataTable(mAttSnService.selectMAttSnList(bo));
    }

    /**
     * 查询设备
     * @param projectId
     * @param personId
     * @return
     */
    @GetMapping("/selectMAttSn/{projectId}/{personId}")
    public R<List<MAttSnVo>> selectMAttSnByProjectId(@PathVariable Long projectId, @PathVariable Long personId) {
        // 1. 根据项目ID获取设备列表
        List<MAttSnVo> mAttSnVos = mAttSnService.selectMAttSnByProjectId(projectId);
        // 2. 根据人员ID获取人员关联的设备ID列表
        List<MAttPersonVo> mAttPersonVos = imAttPersonService.selectMAttPersonByPersonId(personId);
        // 3. 提取人员关联的所有设备ID（snId），用于快速比对
        Set<Long> personSnIds = mAttPersonVos.stream()
            .map(MAttPersonVo::getSnId)
            .collect(Collectors.toSet());
        // 4. 遍历设备列表，如果设备的ID存在于人员的关联设备ID中，则禁用（disabled=true）
        mAttSnVos.forEach(snVo -> {
            if (personSnIds.contains(snVo.getSnId())) {
                snVo.setDisabled(true);
            } else {
                snVo.setDisabled(false);
            }
        });
        // 5. 返回处理后的设备列表
        return R.ok(mAttSnVos);
    }

    /**
     * 导出考勤设备列表
     */
    @SaCheckPermission("attendance:attSn:export")
    @Log(title = "考勤设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MAttSnBo bo, HttpServletResponse response) {
        List<MAttSnVo> list = mAttSnService.queryList(bo);
        ExcelUtil.exportExcel(list, "考勤设备", MAttSnVo.class, response);
    }

    /**
     * 获取考勤设备详细信息
     *
     * @param snId 主键
     */
    @SaCheckPermission("attendance:attSn:query")
    @GetMapping("/{snId}")
    public R<MAttSnVo> getInfo(@NotNull(message = "主键不能为空")
                               @PathVariable Long snId) {
        return R.ok(mAttSnService.queryById(snId));
    }

    /**
     * 新增考勤设备(第三方接口)
     */
    @SaCheckPermission("attendance:attSn:add")
    @Log(title = "考勤设备", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public void add(@Validated(AddGroup.class) @RequestBody MAttSnBo bo) {
        MAttSnVo mAttSnVo = mAttSnService.selectMAttSnBySn(bo.getSn());
        if (mAttSnVo != null) {
            PrjProjectsVo prjProjectsVo = iPrjProjectsService.queryById(mAttSnVo.getProjectId());
            throw new RuntimeException("此设备已绑定【" + prjProjectsVo.getProjectName() + "】");
        }
        // -------------------------------------------------------------------------------------------------------------
        try {
            int statusCode = sendPostRequest(bo, apiUrl + "/modules/authFree/api/device/receive");
            if (statusCode == HttpURLConnection.HTTP_OK) {
                mAttSnService.insertByBo(bo);
                log.error("请求处理成功");
            } else {
                log.error("请求处理失败，状态码: " + statusCode);
                throw new RuntimeException("请求处理失败，状态码: " + statusCode);
            }
        } catch (Exception e) {
            log.error("请求发送失败: " + e.getMessage());
            throw new RuntimeException("请求发送失败: " + e.getMessage());
        }
        // -------------------------------------------------------------------------------------------------------------
    }

    /**
     * 修改考勤设备
     */
    @SaCheckPermission("attendance:attSn:edit")
    @Log(title = "考勤设备", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MAttSnBo bo) {
        return toAjax(mAttSnService.updateByBo(bo));
    }

    /**
     * 删除考勤设备
     *
     * @param snIds 主键串
     */
    @SaCheckPermission("attendance:attSn:remove")
    @Log(title = "考勤设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{snIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] snIds) {
        for (Long snId : snIds) {
            List<MAttPersonVo> mAttPersonVos = imAttPersonService.selectMAttPersonBySnId(snId);
            if (!mAttPersonVos.isEmpty()) {
                return R.fail("此设备已绑定人员，无法删除！");
            }
        }
        return toAjax(mAttSnService.deleteWithValidByIds(List.of(snIds), true));
    }

    /**
     * 发送POST请求到指定URL
     * @return 响应状态码
     * @throws Exception 如果请求过程中发生错误
     */
    public static int sendPostRequest(MAttSnBo bo, String urlString) throws Exception {
        HttpURLConnection connection = null;
        try {
            // 创建URL对象
            URL url = new URL(urlString);

            // 创建连接
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // 设置超时时间（可选）
            connection.setConnectTimeout(10000); // 5秒连接超时
            connection.setReadTimeout(15000);  // 10秒读取超时

            // 将对象转换为JSON字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonInputString = objectMapper.writeValueAsString(bo);

            // 发送请求数据
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonInputString.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            // 获取响应码
            int responseCode = connection.getResponseCode();

            // 读取响应内容（无论是成功还是错误）
            String responseBody;
            try (InputStream inputStream = responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()) {
                if (inputStream != null) {
                    try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                        StringBuilder response = new StringBuilder();
                        String responseLine;
                        while ((responseLine = br.readLine()) != null) {
                            response.append(responseLine.trim());
                        }
                        responseBody = response.toString();
                    }
                } else {
                    responseBody = "无响应内容";
                }
            }

            // 如果不是200状态码，抛出包含错误信息的异常
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw new RuntimeException("第三方接口返回错误: " + responseCode + ", 响应内容: " + responseBody);
            }

            // 返回响应码（如果是200）
            return responseCode;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
}

package org.dromara.attendance.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.attendance.domain.MAttRule;
import org.dromara.attendance.domain.bo.MAttRuleBo;
import org.dromara.attendance.domain.vo.MAttRuleVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 考勤规则Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
public interface MAttRuleMapper extends BaseMapperPlus<MAttRule, MAttRuleVo> {

    List<MAttRuleVo> selectMattRuleList(MAttRuleBo bo);

    MAttRuleVo selectMAttRuleByRoleIdAndByProjectId(@Param("personType") String personType, @Param("projectId") Long projectId);

    List<MAttRuleVo> selectMAttRuleByProjectId(@Param("projectId") Long projectId);
}

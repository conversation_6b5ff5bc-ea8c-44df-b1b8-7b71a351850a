package org.dromara.facility.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 监测设备对象 monitor_facility
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("monitor_facility")
public class MonitorFacility extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 生产厂家
     */
    private String manufacturers;

    /**
     * 数据来源
     */
    private String dataSources;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 工程id
     */
    private Long itemId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;
}

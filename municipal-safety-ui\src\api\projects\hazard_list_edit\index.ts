import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { hazard_upload_img, hazard_list_edit } from './types'

// 上传标注后的图片
export const uploadImage = (data: hazard_upload_img): AxiosPromise<any> => {
  return request({
    url: '/resource/oss/uploadImg',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json' }
  });
}
// 提交标注修改后的数据
export const submitAnnotationData = (data: hazard_list_edit): AxiosPromise<any> => {
  return request({
    url: '/projects/prj_hazardous_items/saveAiTaskAndResult',
    method: 'post',
    data
  });
}
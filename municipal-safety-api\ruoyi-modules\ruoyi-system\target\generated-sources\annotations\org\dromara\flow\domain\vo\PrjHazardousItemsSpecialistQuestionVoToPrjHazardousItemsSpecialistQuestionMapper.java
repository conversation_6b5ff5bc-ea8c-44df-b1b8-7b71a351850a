package org.dromara.flow.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.PrjHazardousItemsSpecialistQuestion;
import org.dromara.flow.domain.PrjHazardousItemsSpecialistQuestionToPrjHazardousItemsSpecialistQuestionVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjHazardousItemsSpecialistQuestionToPrjHazardousItemsSpecialistQuestionVoMapper.class},
    imports = {}
)
public interface PrjHazardousItemsSpecialistQuestionVoToPrjHazardousItemsSpecialistQuestionMapper extends BaseMapper<PrjHazardousItemsSpecialistQuestionVo, PrjHazardousItemsSpecialistQuestion> {
}

{"doc": " 工作流工具\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getFlowUserService", "paramTypes": [], "doc": " 获取工作流用户service\n"}, {"name": "buildUser", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": " 构建工作流用户\n\n @param userList 办理用户\n @param taskId   任务ID\n @return 用户\n"}, {"name": "buildFlowUser", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": " 构建工作流用户\n\n @param userIdList 办理用户\n @param taskId     任务ID\n @return 用户\n"}, {"name": "sendMessage", "paramTypes": ["java.lang.String", "java.lang.Long", "java.util.List", "java.lang.String"], "doc": " 发送消息\n\n @param flowName    流程定义名称\n @param messageType 消息类型\n @param message     消息内容，为空则发送默认配置的消息内容\n"}, {"name": "backTask", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 驳回\n\n @param message        审批意见\n @param instanceId     流程实例id\n @param targetNodeCode 目标节点\n @param flowStatus     流程状态\n @param flowHisStatus  节点操作状态\n"}, {"name": "applyNodeCode", "paramTypes": ["java.lang.Long"], "doc": " 申请人节点编码\n\n @param definitionId 流程定义id\n @return 申请人节点编码\n"}, {"name": "deleteRunTask", "paramTypes": ["java.util.List"], "doc": " 删除运行中的任务\n\n @param taskIds 任务id\n"}], "constructors": []}
package org.dromara.facility.service;

import org.dromara.facility.domain.vo.LnNutVo;
import org.dromara.facility.domain.bo.LnNutBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 绿能螺母Service接口
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface ILnNutService extends BaseFacilityHandle{

    /**
     * 查询绿能螺母
     *
     * @param id 主键
     * @return 绿能螺母
     */
    LnNutVo queryById(Long id);

    /**
     * 分页查询绿能螺母列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能螺母分页列表
     */
    TableDataInfo<LnNutVo> queryPageList(LnNutBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的绿能螺母列表
     *
     * @param bo 查询条件
     * @return 绿能螺母列表
     */
    List<LnNutVo> queryList(LnNutBo bo);

    /**
     * 新增绿能螺母
     *
     * @param bo 绿能螺母
     * @return 是否新增成功
     */
    Boolean insertByBo(LnNutBo bo);

    /**
     * 修改绿能螺母
     *
     * @param bo 绿能螺母
     * @return 是否修改成功
     */
    Boolean updateByBo(LnNutBo bo);

    /**
     * 校验并批量删除绿能螺母信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

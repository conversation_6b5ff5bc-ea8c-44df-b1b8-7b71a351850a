package org.dromara.projects.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.projects.domain.PrjHazardousItems;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * [项目管理] 列出项目内具体的危险性较大的分部分项工程视图对象 prj_hazardous_items
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjHazardousItems.class)
public class PrjHazardousItemsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 危大工程项ID
     */
    @ExcelProperty(value = "危大工程项ID")
    private Long itemId;

    /**
     * 所属项目ID (逻辑外键至 prj_projects.project_id)
     */
    @ExcelProperty(value = "所属项目ID (逻辑外键至 prj_projects.project_id)")
    private Long projectId;

    /**
     * 涉危工程清单ID（支持多选逗号隔开）
     */
    @ExcelProperty(value = "涉危工程清单ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "支持多选逗号隔开")
    private String dangerId;

    /**
     * 危大工程名称/描述
     */
    @ExcelProperty(value = "危大工程名称/描述")
    private String itemName;

    /**
     * 具体范围详情
     */
    @ExcelProperty(value = "具体范围详情")
    private String scopeDetails;

    /**
     * 危大类型 (1:危大, 2:超危大)
     */
    @ExcelProperty(value = "危大类型 (1:危大, 2:超危大)")
    private Long dangerListType;

    /**
     * 计划开工日期
     */
    @ExcelProperty(value = "计划开工日期")
    private Date startDate;

    /**
     * 计划竣工日期
     */
    @ExcelProperty(value = "计划竣工日期")
    private Date plannedEndDate;

    /**
     * 实际竣工日期
     */
    @ExcelProperty(value = "实际竣工日期")
    private Date actualEndDate;

    /**
     * 实际开工日期
     */
    @ExcelProperty(value = "实际开工日期")
    private Date actualStartDate;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 工程名称
     */
    private String projectName;

    /**
     * 涉危工程名称
     */
    private String dangerName;

    /**
     * 涉危工程清单父级名称
     */
    private String parentName;

    /**
     * 隐患清单详情
     */
    private List<ItemsAiListVO> aiListVOS;

    /**
     * 所属项目信息
     */
    PrjProjectsVo prjProjectsVo;

    /**
     *  危大工程父级名称
     */
    private String dangerFatherName;
}

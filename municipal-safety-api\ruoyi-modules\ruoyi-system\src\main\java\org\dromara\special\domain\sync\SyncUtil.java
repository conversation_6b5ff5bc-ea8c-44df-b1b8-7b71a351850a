package org.dromara.special.domain.sync;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import org.springframework.stereotype.Component;

@Component
public class SyncUtil {

    public static SyncDate syncDate(String constructionPermitNo){
        String data = HttpRequest.post("https://zhaj.zjt.gansu.gov.cn:10124/dataexchangeserver/GGBApi/getGCXMInfo?SGXKZH=" + constructionPermitNo)
            .header("ryxxSecretKey", "1211f5ea378f4f858ba796336e01ab78")
            .timeout(20000)
            .execute().body();
        SyncReceive result = JSONUtil.toBean(data, SyncReceive.class);
        SyncDate date = JSONUtil.toBean(result.getData(), SyncDate.class);
        return date;
    }

}

package org.dromara.projects.domain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/13 16:48
 * @Description TODO
 * @Version 1.0
 */
@Data
public class ViolationVO {

    /**
     * 问题id
     */
    private Long resultId;

    /**
     * 隐患描述
     */
    private String violation;

    /**
     *  参考条款
     */
    private String regulation;

    /**
     * 级别
     */
    private String level;

    /**
     *  整改意见
     */
    private String measure;

    /**
     * 预警是否真实 1是 0否
     */
    private Integer commentStatus;

    /**
     * 质监站整改意见
     */
    private String personComments;

    /**
     * 已整改图片
     */
    private Long abarbeitungImg;

    /**
     * 未整改图片
     */
    private Long noAbarbeitung;

    /**
     * 整改说明
     */
    private String abarbeitungComments;

    /**
     * 整改问题id
     */
    private Long commentsId;
}

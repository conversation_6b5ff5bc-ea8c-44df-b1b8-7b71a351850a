{"doc": " 监控管理\n\n <AUTHOR>\n @date 2025-05-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.monito.domain.bo.DeviceMonitoBo"], "doc": " 查询监控管理列表\n"}, {"name": "export", "paramTypes": ["org.dromara.monito.domain.bo.DeviceMonitoBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出监控管理列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取监控管理详细信息\n\n @param monitoId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.monito.domain.bo.DeviceMonitoBo"], "doc": " 新增监控管理\n"}, {"name": "edit", "paramTypes": ["org.dromara.monito.domain.bo.DeviceMonitoBo"], "doc": " 修改监控管理\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除监控管理\n\n @param monitoIds 主键串\n"}, {"name": "showList", "paramTypes": ["org.dromara.monito.domain.bo.DeviceMonitoBo"], "doc": " 监控列表  左边\n"}, {"name": "playUrl", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取播放地址\n"}, {"name": "getToken", "paramTypes": [], "doc": " 获取token\n"}, {"name": "capture", "paramTypes": ["org.dromara.monito.domain.bo.CaptureBo"], "doc": " 截图并送ai隐患\n\n @param captureBo\n @return\n"}], "constructors": []}
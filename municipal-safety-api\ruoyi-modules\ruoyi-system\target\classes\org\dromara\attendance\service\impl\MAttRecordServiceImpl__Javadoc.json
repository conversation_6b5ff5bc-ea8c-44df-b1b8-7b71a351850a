{"doc": " 考勤记录Service业务层处理\n\n <AUTHOR>\n @date 2025-05-06\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询考勤记录\n\n @param id 主键\n @return 考勤记录\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRecordBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询考勤记录列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 考勤记录分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRecordBo"], "doc": " 查询符合条件的考勤记录列表\n\n @param bo 查询条件\n @return 考勤记录列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRecordBo"], "doc": " 新增考勤记录\n\n @param bo 考勤记录\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.attendance.domain.bo.MAttRecordBo"], "doc": " 修改考勤记录\n\n @param bo 考勤记录\n @return 是否修改成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.attendance.domain.MAttRecord"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除考勤记录信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}, {"name": "getAttendanceCalendar", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 获取考勤日历（时段打卡优化版）\n\n @param personId 人员ID\n @param month    月份(yyyy-MM)\n @return 考勤日历列表\n"}, {"name": "calculateOptimizedAttendanceStatus", "paramTypes": ["org.dromara.attendance.domain.vo.CalendarDayVo", "java.util.List", "java.util.List"], "doc": " 优化版考勤状态计算\n"}, {"name": "parseTimeSlots", "paramTypes": ["java.lang.String"], "doc": " 解析打卡时段\n"}], "constructors": []}
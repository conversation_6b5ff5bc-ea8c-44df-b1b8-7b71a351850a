package org.dromara.facility.domain;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.vo.LnEdgeGuardVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnEdgeGuardToLnEdgeGuardVoMapperImpl implements LnEdgeGuardToLnEdgeGuardVoMapper {

    @Override
    public LnEdgeGuardVo convert(LnEdgeGuard arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnEdgeGuardVo lnEdgeGuardVo = new LnEdgeGuardVo();

        lnEdgeGuardVo.setId( arg0.getId() );
        lnEdgeGuardVo.setDumpnumber( arg0.getDumpnumber() );
        lnEdgeGuardVo.setCheckSensor( arg0.getCheckSensor() );
        lnEdgeGuardVo.setLongitude( arg0.getLongitude() );
        lnEdgeGuardVo.setLatitude( arg0.getLatitude() );
        lnEdgeGuardVo.setBatvolt( arg0.getBatvolt() );
        lnEdgeGuardVo.setBatPercent( arg0.getBatPercent() );
        lnEdgeGuardVo.setAlarmInfo( arg0.getAlarmInfo() );
        lnEdgeGuardVo.setDevNo( arg0.getDevNo() );
        lnEdgeGuardVo.setCreateTime( arg0.getCreateTime() );

        return lnEdgeGuardVo;
    }

    @Override
    public LnEdgeGuardVo convert(LnEdgeGuard arg0, LnEdgeGuardVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setDumpnumber( arg0.getDumpnumber() );
        arg1.setCheckSensor( arg0.getCheckSensor() );
        arg1.setLongitude( arg0.getLongitude() );
        arg1.setLatitude( arg0.getLatitude() );
        arg1.setBatvolt( arg0.getBatvolt() );
        arg1.setBatPercent( arg0.getBatPercent() );
        arg1.setAlarmInfo( arg0.getAlarmInfo() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}

package org.dromara.quality.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.quality.domain.QualityDevice;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class QualityDeviceBoToQualityDeviceMapperImpl implements QualityDeviceBoToQualityDeviceMapper {

    @Override
    public QualityDevice convert(QualityDeviceBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QualityDevice qualityDevice = new QualityDevice();

        qualityDevice.setSearchValue( arg0.getSearchValue() );
        qualityDevice.setCreateDept( arg0.getCreateDept() );
        qualityDevice.setCreateBy( arg0.getCreateBy() );
        qualityDevice.setCreateTime( arg0.getCreateTime() );
        qualityDevice.setUpdateBy( arg0.getUpdateBy() );
        qualityDevice.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            qualityDevice.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        qualityDevice.setDeviceId( arg0.getDeviceId() );
        qualityDevice.setDeviceName( arg0.getDeviceName() );
        qualityDevice.setSpecification( arg0.getSpecification() );
        qualityDevice.setDeviceCode( arg0.getDeviceCode() );
        qualityDevice.setDeviceType( arg0.getDeviceType() );
        qualityDevice.setQuantity( arg0.getQuantity() );
        qualityDevice.setDeviceImageOssId( arg0.getDeviceImageOssId() );
        qualityDevice.setDeviceDescription( arg0.getDeviceDescription() );
        qualityDevice.setDevicePurpose( arg0.getDevicePurpose() );
        qualityDevice.setUsageInstructions( arg0.getUsageInstructions() );
        qualityDevice.setManualFileOssId( arg0.getManualFileOssId() );
        qualityDevice.setStatus( arg0.getStatus() );

        return qualityDevice;
    }

    @Override
    public QualityDevice convert(QualityDeviceBo arg0, QualityDevice arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setDeviceId( arg0.getDeviceId() );
        arg1.setDeviceName( arg0.getDeviceName() );
        arg1.setSpecification( arg0.getSpecification() );
        arg1.setDeviceCode( arg0.getDeviceCode() );
        arg1.setDeviceType( arg0.getDeviceType() );
        arg1.setQuantity( arg0.getQuantity() );
        arg1.setDeviceImageOssId( arg0.getDeviceImageOssId() );
        arg1.setDeviceDescription( arg0.getDeviceDescription() );
        arg1.setDevicePurpose( arg0.getDevicePurpose() );
        arg1.setUsageInstructions( arg0.getUsageInstructions() );
        arg1.setManualFileOssId( arg0.getManualFileOssId() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}

package org.dromara.special.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.special.domain.bo.SpecialOperationPersonnelBo;
import org.dromara.special.domain.vo.SpecialOperationPersonnelVo;
import org.dromara.special.domain.SpecialOperationPersonnel;
import org.dromara.special.mapper.SpecialOperationPersonnelMapper;
import org.dromara.special.service.ISpecialOperationPersonnelService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 特种作业人员信息Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-05-13
 */
@RequiredArgsConstructor
@Service
public class SpecialOperationPersonnelServiceImpl implements ISpecialOperationPersonnelService {

    private final SpecialOperationPersonnelMapper baseMapper;

    /**
     * 查询特种作业人员信息
     *
     * @param sopId 主键
     * @return 特种作业人员信息
     */
    @Override
    public SpecialOperationPersonnelVo queryById(Long sopId){
        return baseMapper.selectVoById(sopId);
    }

    /**
     * 分页查询特种作业人员信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 特种作业人员信息分页列表
     */
    @Override
    public TableDataInfo<SpecialOperationPersonnelVo> queryPageList(SpecialOperationPersonnelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SpecialOperationPersonnel> lqw = buildQueryWrapper(bo);
        Page<SpecialOperationPersonnelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的特种作业人员信息列表
     *
     * @param bo 查询条件
     * @return 特种作业人员信息列表
     */
    @Override
    public List<SpecialOperationPersonnelVo> queryList(SpecialOperationPersonnelBo bo) {
        LambdaQueryWrapper<SpecialOperationPersonnel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SpecialOperationPersonnel> buildQueryWrapper(SpecialOperationPersonnelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SpecialOperationPersonnel> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getCertificateNumber()), SpecialOperationPersonnel::getCertificateNumber, bo.getCertificateNumber());
        lqw.like(StringUtils.isNotBlank(bo.getName()), SpecialOperationPersonnel::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getIdCard()), SpecialOperationPersonnel::getIdCard, bo.getIdCard());
        lqw.eq(StringUtils.isNotBlank(bo.getGender()), SpecialOperationPersonnel::getGender, bo.getGender());
        lqw.eq(bo.getBirthdate() != null, SpecialOperationPersonnel::getBirthdate, bo.getBirthdate());
        lqw.eq(StringUtils.isNotBlank(bo.getOperationCategory()), SpecialOperationPersonnel::getOperationCategory, bo.getOperationCategory());
        lqw.eq(StringUtils.isNotBlank(bo.getIssuer()), SpecialOperationPersonnel::getIssuer, bo.getIssuer());
        lqw.eq(bo.getFirstIssueDate() != null, SpecialOperationPersonnel::getFirstIssueDate, bo.getFirstIssueDate());
        lqw.eq(bo.getLastIssueDate() != null, SpecialOperationPersonnel::getLastIssueDate, bo.getLastIssueDate());
        lqw.eq(bo.getValidityStart() != null, SpecialOperationPersonnel::getValidityStart, bo.getValidityStart());
        lqw.eq(bo.getValidityEnd() != null, SpecialOperationPersonnel::getValidityEnd, bo.getValidityEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SpecialOperationPersonnel::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getElectronicLicenseUrl()), SpecialOperationPersonnel::getElectronicLicenseUrl, bo.getElectronicLicenseUrl());
        lqw.eq(bo.getElectronicLicenseId() != null, SpecialOperationPersonnel::getElectronicLicenseId, bo.getElectronicLicenseId());
        lqw.eq(bo.getProjectId() != null, SpecialOperationPersonnel::getProjectId, bo.getProjectId());
        lqw.orderByDesc(SpecialOperationPersonnel::getCreateTime);
        return lqw;
    }

    /**
     * 新增特种作业人员信息
     *
     * @param bo 特种作业人员信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SpecialOperationPersonnelBo bo) {
        SpecialOperationPersonnel add = MapstructUtils.convert(bo, SpecialOperationPersonnel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setSopId(add.getSopId());
        }
        return flag;
    }

    /**
     * 修改特种作业人员信息
     *
     * @param bo 特种作业人员信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SpecialOperationPersonnelBo bo) {
        SpecialOperationPersonnel update = MapstructUtils.convert(bo, SpecialOperationPersonnel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SpecialOperationPersonnel entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除特种作业人员信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}

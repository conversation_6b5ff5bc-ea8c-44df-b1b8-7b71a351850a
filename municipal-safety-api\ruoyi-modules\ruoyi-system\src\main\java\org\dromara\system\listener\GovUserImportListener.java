package org.dromara.system.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import cn.hutool.http.HtmlUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StreamUtils;
import org.dromara.common.core.utils.ValidatorUtils;
import org.dromara.common.excel.core.ExcelListener;
import org.dromara.common.excel.core.ExcelResult;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.SysDept;
import org.dromara.system.domain.SysUserRole;
import org.dromara.system.domain.bo.SysDeptBo;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.domain.vo.GovUserImportVo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.mapper.SysDeptMapper;
import org.dromara.system.mapper.SysUserRoleMapper;
import org.dromara.system.service.ISysConfigService;
import org.dromara.system.service.ISysDeptService;
import org.dromara.system.service.ISysRoleService;
import org.dromara.system.service.ISysUserService;

import java.util.List;

/**
 * 政府用户自定义导入
 *
 * <AUTHOR>
 */
@Slf4j
public class GovUserImportListener extends AnalysisEventListener<GovUserImportVo> implements ExcelListener<GovUserImportVo> {

    private final ISysUserService userService;
    private final ISysDeptService deptService;
    private final ISysRoleService roleService;
    private final SysDeptMapper deptMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final String password;
    private final Boolean isUpdateSupport;
    private final Long operUserId;

    private int successNum = 0;
    private int failureNum = 0;
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public GovUserImportListener(Boolean isUpdateSupport) {
        String initPassword = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.user.initPassword");
        this.userService = SpringUtils.getBean(ISysUserService.class);
        this.deptService = SpringUtils.getBean(ISysDeptService.class);
        this.roleService = SpringUtils.getBean(ISysRoleService.class);
        this.deptMapper = SpringUtils.getBean(SysDeptMapper.class);
        this.userRoleMapper = SpringUtils.getBean(SysUserRoleMapper.class);
        this.password = BCrypt.hashpw(initPassword);
        this.isUpdateSupport = isUpdateSupport;
        this.operUserId = LoginHelper.getUserId();
    }

    @Override
    public void invoke(GovUserImportVo userVo, AnalysisContext context) {
        try {
            // 处理部门信息
            Long deptId = processDepartment(userVo);

            // 检查用户是否已存在
            SysUserVo sysUser = this.userService.selectUserByUserName(userVo.getUserName());

            if (ObjectUtil.isNull(sysUser)) {
                // 创建新用户
                SysUserBo user = BeanUtil.toBean(userVo, SysUserBo.class);
                ValidatorUtils.validate(user);
                user.setPassword(password);
                user.setDeptId(deptId);
                user.setCreateBy(operUserId);
                user.setUserType("sys_user");
                user.setStatus("0");

                userService.insertUser(user);

                // 分配角色
                assignRoleToUser(user.getUserId(), userVo.getSupervisionLevelCode());

                successNum++;
                successMsg.append("<br/>").append(successNum).append("、账号 ").append(user.getUserName()).append(" 导入成功");
            } else if (isUpdateSupport) {
                // 更新已存在用户
                Long userId = sysUser.getUserId();
                SysUserBo user = BeanUtil.toBean(userVo, SysUserBo.class);
                user.setUserId(userId);
                user.setDeptId(deptId);
                ValidatorUtils.validate(user);
                userService.checkUserAllowed(user.getUserId());
                userService.checkUserDataScope(user.getUserId());
                user.setUpdateBy(operUserId);
                userService.updateUser(user);

                // 更新角色分配
                updateUserRole(userId, userVo.getSupervisionLevelCode());

                successNum++;
                successMsg.append("<br/>").append(successNum).append("、账号 ").append(user.getUserName()).append(" 更新成功");
            } else {
                failureNum++;
                failureMsg.append("<br/>").append(failureNum).append("、账号 ").append(sysUser.getUserName()).append(" 已存在");
            }
        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、账号 " + HtmlUtil.cleanHtmlTag(userVo.getUserName()) + " 导入失败：";
            String message = e.getMessage();
            if (e instanceof ConstraintViolationException cvException) {
                message = StreamUtils.join(cvException.getConstraintViolations(), ConstraintViolation::getMessage, ", ");
            }
            failureMsg.append(msg).append(message);
            log.error(msg, e);
        }
    }

    /**
     * 处理部门信息 - 支持层级结构
     */
    private Long processDepartment(GovUserImportVo userVo) {
        String deptName = userVo.getDeptName();
        if (StrUtil.isBlank(deptName)) {
            throw new ServiceException("部门名称不能为空");
        }

        // 根据监督级别编码确定部门类型
        String deptType = determineDeptType(userVo.getSupervisionLevelCode());

        // 根据监督级别和地区信息确定父级部门
        Long parentDeptId = determineParentDeptId(userVo.getSupervisionLevelCode(),
            userVo.getProvinceCode(),
            userVo.getCityCode(),
            userVo.getDistrictCode());

        // 查找是否已存在该部门
        List<SysDept> existingDepts = deptMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDeptName, deptName)
                .eq(SysDept::getParentId, parentDeptId)
        );

        if (!existingDepts.isEmpty()) {
            return existingDepts.get(0).getDeptId();
        }

        // 创建新部门
        SysDeptBo deptBo = new SysDeptBo();
        deptBo.setParentId(parentDeptId);
        deptBo.setDeptName(deptName);
        deptBo.setOrderNum(0);
        deptBo.setStatus("0");
        deptBo.setDeptType(deptType);
        deptBo.setCreateBy(operUserId);

        deptBo.setDeptCode(userVo.getUserName());

        // 设置省市区编码
        if (StrUtil.isNotBlank(userVo.getProvinceCode())) {
            deptBo.setProvinceCode(userVo.getProvinceCode());
        }
        if (StrUtil.isNotBlank(userVo.getCityCode())) {
            deptBo.setCityCode(userVo.getCityCode());
        }
        if (StrUtil.isNotBlank(userVo.getDistrictCode())) {
            deptBo.setDistrictCode(userVo.getDistrictCode());
        }

        deptService.insertDept(deptBo);

        // 获取新创建的部门ID
        List<SysDept> newDepts = deptMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDeptName, deptName)
                .eq(SysDept::getParentId, parentDeptId)
        );

        if (!newDepts.isEmpty()) {
            return newDepts.get(0).getDeptId();
        } else {
            throw new ServiceException("创建部门失败");
        }
    }

    /**
     * 根据监督级别编码确定部门类型
     *
     * @param supervisionLevelCode 监督级别编码
     * @return 部门类型
     */
    private String determineDeptType(String supervisionLevelCode) {
        if (StrUtil.isBlank(supervisionLevelCode)) {
            return "GOV_CITY_QS"; // 默认为市级质监站
        }

        return switch (supervisionLevelCode.trim()) {
            case "01" -> "GOV_PROVINCE"; // 省级
            case "02" -> "GOV_CITY_QS"; // 地市级质监站
            case "03" -> "GOV_DISTRICT_QS"; // 区县级质监站
            default -> "GOV_CITY_QS"; // 默认为市级质监站
        };
    }

    /**
     * 根据监督级别编码确定角色ID
     *
     * @param supervisionLevelCode 监督级别编码
     * @return 角色ID
     */
    private Long determineRoleId(String supervisionLevelCode) {
        if (StrUtil.isBlank(supervisionLevelCode)) {
            return 1953294174870577153L; // 默认为"本部门及以下"角色
        }

        return switch (supervisionLevelCode.trim()) {
            case "01" -> 1924986769282084865L; // 省级 - 超级管理员角色
            case "02" -> 1922459729990004738L; // 地市级 - 本部门及以下角色
            case "03" -> 1953294174870577153L; // 区县级 - 仅本人角色
            default -> 1953294174870577153L; // 默认为"本部门及以下"角色
        };
    }

    /**
     * 为新用户分配角色
     *
     * @param userId               用户ID
     * @param supervisionLevelCode 监督级别编码
     */
    private void assignRoleToUser(Long userId, String supervisionLevelCode) {
        Long roleId = determineRoleId(supervisionLevelCode);

        // 创建用户角色关联
        SysUserRole userRole = new SysUserRole();
        userRole.setUserId(userId);
        userRole.setRoleId(roleId);

        userRoleMapper.insert(userRole);

        log.info("为用户 {} 分配角色 {}", userId, roleId);
    }

    /**
     * 更新用户角色分配
     *
     * @param userId               用户ID
     * @param supervisionLevelCode 监督级别编码
     */
    private void updateUserRole(Long userId, String supervisionLevelCode) {
        Long roleId = determineRoleId(supervisionLevelCode);

        // 先删除现有角色
        userRoleMapper.delete(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<SysUserRole>()
                .eq(SysUserRole::getUserId, userId)
        );

        // 分配新角色
        SysUserRole userRole = new SysUserRole();
        userRole.setUserId(userId);
        userRole.setRoleId(roleId);

        userRoleMapper.insert(userRole);

        log.info("更新用户 {} 的角色为 {}", userId, roleId);
    }

    /**
     * 根据监督级别和地区信息确定父级部门ID
     */
    private Long determineParentDeptId(String supervisionLevelCode, String provinceCode, String cityCode, String districtCode) {
        if (StrUtil.isBlank(supervisionLevelCode)) {
            return 100L; // 默认为政府根部门
        }

        switch (supervisionLevelCode.trim()) {
            case "01": // 省级
                // 省级部门直接挂在政府根部门下
                return 100L;

            case "02": // 地市级
                // 查找或创建对应的市级部门作为父级
                return findOrCreateCityDeptParentId(provinceCode, cityCode);

            case "03": // 区县级
                // 查找或创建对应的市级质监站作为父级
                return findOrCreateDistrictDeptParentId(provinceCode, cityCode, districtCode);

            default:
                return 100L; // 默认为政府根部门
        }
    }

    /**
     * 查找或创建市级部门的父级部门ID
     */
    private Long findOrCreateCityDeptParentId(String provinceCode, String cityCode) {
        // 先查找省级部门
        List<SysDept> provinceDepts = deptMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDeptType, "GOV_PROVINCE")
                .eq(StrUtil.isNotBlank(provinceCode), SysDept::getProvinceCode, provinceCode)
        );

        Long provinceDeptId;
        if (!provinceDepts.isEmpty()) {
            provinceDeptId = provinceDepts.get(0).getDeptId();
        } else {
            // 如果省级部门不存在，创建省级部门
            provinceDeptId = createProvinceDept(provinceCode);
        }

        // 查找对应的市级节点
        List<SysDept> cityDepts = deptMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getParentId, provinceDeptId)
                .eq(StrUtil.isNotBlank(cityCode), SysDept::getCityCode, cityCode)
        );

        Long cityDeptId;
        if (!cityDepts.isEmpty()) {
            cityDeptId = cityDepts.get(0).getDeptId();
        } else {
            // 如果市级节点不存在，创建市级节点
            cityDeptId = createCityDept(provinceDeptId, cityCode);
        }

        // 查找市级住建部门
        List<SysDept> cityBuildDepts = deptMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getParentId, cityDeptId)
                .eq(SysDept::getDeptType, "GOV_CITY_DEPT")
        );

        if (!cityBuildDepts.isEmpty()) {
            return cityBuildDepts.get(0).getDeptId();
        } else {
            // 如果市级住建部门不存在，创建市级住建部门
            return createCityBuildDept(cityDeptId, provinceCode, cityCode);
        }
    }

    /**
     * 查找或创建区县级部门的父级部门ID
     */
    private Long findOrCreateDistrictDeptParentId(String provinceCode, String cityCode, String districtCode) {
        // 先查找市级质监站
        List<SysDept> cityQsDepts = deptMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDeptType, "GOV_CITY_QS")
                .eq(StrUtil.isNotBlank(provinceCode), SysDept::getProvinceCode, provinceCode)
                .eq(StrUtil.isNotBlank(cityCode), SysDept::getCityCode, cityCode)
        );

        if (!cityQsDepts.isEmpty()) {
            return cityQsDepts.get(0).getDeptId();
        } else {
            // 如果市级质监站不存在，先创建市级部门层级，然后创建市级质监站
            Long cityBuildDeptId = findOrCreateCityDeptParentId(provinceCode, cityCode);
            return createCityQsDept(cityBuildDeptId, provinceCode, cityCode);
        }
    }

    /**
     * 创建省级部门
     */
    private Long createProvinceDept(String provinceCode) {
        SysDeptBo deptBo = new SysDeptBo();
        deptBo.setParentId(100L); // 政府根部门
        deptBo.setDeptName("甘肃省住房和城乡建设厅");
        deptBo.setOrderNum(0);
        deptBo.setStatus("0");
        deptBo.setDeptType("GOV_PROVINCE");
        deptBo.setCreateBy(operUserId);

        if (StrUtil.isNotBlank(provinceCode)) {
            deptBo.setProvinceCode(provinceCode);
        }

        deptService.insertDept(deptBo);

        // 获取新创建的部门ID
        List<SysDept> newDepts = deptMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDeptName, "甘肃省住房和城乡建设厅")
                .eq(SysDept::getParentId, 100L)
        );

        if (!newDepts.isEmpty()) {
            return newDepts.get(0).getDeptId();
        } else {
            throw new ServiceException("创建省级部门失败");
        }
    }

    /**
     * 创建市级节点
     */
    private Long createCityDept(Long provinceDeptId, String cityCode) {
        // 根据城市编码获取城市名称
        String cityName = getCityNameByCode(cityCode);

        SysDeptBo deptBo = new SysDeptBo();
        deptBo.setParentId(provinceDeptId);
        deptBo.setDeptName(cityName + "市");
        deptBo.setOrderNum(0);
        deptBo.setStatus("0");
        deptBo.setCreateBy(operUserId);

        if (StrUtil.isNotBlank(cityCode)) {
            deptBo.setCityCode(cityCode);
        }

        deptService.insertDept(deptBo);

        // 获取新创建的部门ID
        List<SysDept> newDepts = deptMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDeptName, cityName + "市")
                .eq(SysDept::getParentId, provinceDeptId)
        );

        if (!newDepts.isEmpty()) {
            return newDepts.get(0).getDeptId();
        } else {
            throw new ServiceException("创建市级节点失败");
        }
    }

    /**
     * 创建市级住建部门
     */
    private Long createCityBuildDept(Long cityDeptId, String provinceCode, String cityCode) {
        // 根据城市编码获取城市名称
        String cityName = getCityNameByCode(cityCode);

        SysDeptBo deptBo = new SysDeptBo();
        deptBo.setParentId(cityDeptId);
        deptBo.setDeptName(cityName + "住房和城乡建设局");
        deptBo.setOrderNum(0);
        deptBo.setStatus("0");
        deptBo.setDeptType("GOV_CITY_DEPT");
        deptBo.setCreateBy(operUserId);

        if (StrUtil.isNotBlank(provinceCode)) {
            deptBo.setProvinceCode(provinceCode);
        }
        if (StrUtil.isNotBlank(cityCode)) {
            deptBo.setCityCode(cityCode);
        }

        deptService.insertDept(deptBo);

        // 获取新创建的部门ID
        List<SysDept> newDepts = deptMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDeptName, cityName + "住房和城乡建设局")
                .eq(SysDept::getParentId, cityDeptId)
        );

        if (!newDepts.isEmpty()) {
            return newDepts.get(0).getDeptId();
        } else {
            throw new ServiceException("创建市级住建部门失败");
        }
    }

    /**
     * 创建市级质监站
     */
    private Long createCityQsDept(Long cityBuildDeptId, String provinceCode, String cityCode) {
        // 根据城市编码获取城市名称
        String cityName = getCityNameByCode(cityCode);

        SysDeptBo deptBo = new SysDeptBo();
        deptBo.setParentId(cityBuildDeptId);
        deptBo.setDeptName(cityName + "市建设工程安全质量监督站");
        deptBo.setOrderNum(0);
        deptBo.setStatus("0");
        deptBo.setDeptType("GOV_CITY_QS");
        deptBo.setCreateBy(operUserId);

        if (StrUtil.isNotBlank(provinceCode)) {
            deptBo.setProvinceCode(provinceCode);
        }
        if (StrUtil.isNotBlank(cityCode)) {
            deptBo.setCityCode(cityCode);
        }

        deptService.insertDept(deptBo);

        // 获取新创建的部门ID
        List<SysDept> newDepts = deptMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDeptName, cityName + "市建设工程安全质量监督站")
                .eq(SysDept::getParentId, cityBuildDeptId)
        );

        if (!newDepts.isEmpty()) {
            return newDepts.get(0).getDeptId();
        } else {
            throw new ServiceException("创建市级质监站失败");
        }
    }

    /**
     * 根据城市编码获取城市名称
     */
    private String getCityNameByCode(String cityCode) {
        if (StrUtil.isBlank(cityCode)) {
            return "未知城市";
        }

        // 这里可以根据城市编码映射城市名称
        // 可以维护一个城市编码到名称的映射表，或者调用地区服务
        switch (cityCode) {
            case "620100":
                return "兰州";
            case "620200":
                return "嘉峪关";
            case "620300":
                return "金昌";
            case "620400":
                return "白银";
            case "620500":
                return "天水";
            case "620600":
                return "武威";
            case "620700":
                return "张掖";
            case "620800":
                return "平凉";
            case "620900":
                return "酒泉";
            case "621000":
                return "庆阳";
            case "621100":
                return "定西";
            case "621200":
                return "陇南";
            case "622900":
                return "临夏";
            case "623000":
                return "甘南";
            default:
                return "未知城市";
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 导入完成后的处理
    }

    @Override
    public ExcelResult<GovUserImportVo> getExcelResult() {
        return new ExcelResult<>() {

            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }

            @Override
            public List<GovUserImportVo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }
}

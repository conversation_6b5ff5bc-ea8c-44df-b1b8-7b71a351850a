<template>
  <div class="p-2">
    <!-- 统计卡片区域 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon device-icon">
              <el-icon size="32"><Monitor /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ deviceStats.total }}</div>
              <div class="stat-label">设备总数</div>
            </div>
          </div>
          <div class="stat-footer">
            <span class="stat-trend" :class="deviceStats.increase >= 0 ? 'up' : 'down'">
              <el-icon><ArrowUp v-if="deviceStats.increase >= 0" /><ArrowDown v-else /></el-icon>
              {{ Math.abs(deviceStats.increase) }}%
            </span>
            <span class="stat-desc">较上月{{ deviceStats.increase >= 0 ? '增长' : '下降' }}</span>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon measurement-icon">
              <el-icon size="32"><DataAnalysis /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ measurementStats.total }}</div>
              <div class="stat-label">测量记录</div>
            </div>
          </div>
          <div class="stat-footer">
            <span class="stat-trend" :class="measurementStats.increase >= 0 ? 'up' : 'down'">
              <el-icon><ArrowUp v-if="measurementStats.increase >= 0" /><ArrowDown v-else /></el-icon>
              {{ Math.abs(measurementStats.increase) }}%
            </span>
            <span class="stat-desc">较上月{{ measurementStats.increase >= 0 ? '增长' : '下降' }}</span>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon normal-icon">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ measurementStats.normal }}</div>
              <div class="stat-label">正常测量</div>
            </div>
          </div>
          <div class="stat-footer">
            <span class="stat-trend up">
              <el-icon><ArrowUp /></el-icon>
              {{ measurementStats.normalRate }}%
            </span>
            <span class="stat-desc">正常率</span>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon hazard-icon">
              <el-icon size="32"><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ measurementStats.hazard }}</div>
              <div class="stat-label">隐患记录</div>
            </div>
          </div>
          <div class="stat-footer">
            <span class="stat-trend down">
              <el-icon><ArrowDown /></el-icon>
              {{ measurementStats.hazardRate }}%
            </span>
            <span class="stat-desc">隐患率</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表和快捷操作区域 -->
    <el-row :gutter="20">
      <!-- 左侧图表区域 -->
      <el-col :xs="24" :lg="16">
        <el-card shadow="hover" class="mb-4">
          <template #header>
            <div class="card-header">
              <span class="card-title">测量趋势分析</span>
              <el-radio-group v-model="chartPeriod" size="small" @change="updateChart">
                <el-radio-button value="week">近一周</el-radio-button>
                <el-radio-button value="month">近一月</el-radio-button>
                <el-radio-button value="quarter">近三月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="chartRef" style="height: 350px" v-loading="chartLoading"></div>
        </el-card>

        <!-- 设备状态分布 -->
        <el-card shadow="hover">
          <template #header>
            <span class="card-title">设备状态分布</span>
          </template>
          <div ref="pieChartRef" style="height: 300px" v-loading="pieChartLoading"></div>
        </el-card>
      </el-col>

      <!-- 右侧快捷操作和最新记录 -->
      <el-col :xs="24" :lg="8">
        <!-- 快捷操作 -->
        <el-card shadow="hover" class="mb-4">
          <template #header>
            <span class="card-title">快捷操作</span>
          </template>
          <div class="quick-actions">
            <el-button type="primary" icon="Plus" @click="goToAddDevice" class="action-btn">
              新增设备
            </el-button>
            <el-button type="success" icon="DataAnalysis" @click="goToAddMeasurement" class="action-btn">
              新增测量
            </el-button>
          </div>
        </el-card>

        <!-- 最新测量记录 -->
        <el-card shadow="hover" class="mb-4">
          <template #header>
            <div class="card-header">
              <span class="card-title">最新测量记录</span>
              <el-link type="primary" @click="goToMeasurementList">查看更多</el-link>
            </div>
          </template>
          <div class="recent-records" v-loading="recordsLoading">
            <div v-if="recentMeasurements.length === 0" class="empty-data">
              <el-empty description="暂无测量记录" :image-size="80" />
            </div>
            <div v-else>
              <div v-for="record in recentMeasurements" :key="record.measurementId" class="record-item">
                <div class="record-info">
                  <div class="record-title">{{ record.deviceName }}</div>
                  <div class="record-desc">
                    {{ getMeasurementTypeText(record.measurementType) }} | {{ record.measurementValue }}{{ record.unit }}
                  </div>
                  <div class="record-time">{{ record.createTime }}</div>
                </div>
                <div class="record-status">
                  <el-tag v-if="record.isHazard" type="danger" size="small">隐患</el-tag>
                  <el-tag v-else-if="record.status === '1'" type="warning" size="small">异常</el-tag>
                  <el-tag v-else type="success" size="small">正常</el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 待处理事项 -->
        <el-card shadow="hover">
          <template #header>
            <span class="card-title">待处理事项</span>
          </template>
          <div class="todo-list">
            <div class="todo-item">
              <el-icon class="todo-icon"><Warning /></el-icon>
              <div class="todo-content">
                <div class="todo-title">{{ pendingTasks.hazardCount }} 个隐患待处理</div>
                <div class="todo-desc">需要及时关注和处理</div>
              </div>
              <el-button type="danger" size="small" text @click="goToHazardList">处理</el-button>
            </div>
            <div class="todo-item">
              <el-icon class="todo-icon"><Clock /></el-icon>
              <div class="todo-content">
                <div class="todo-title">{{ pendingTasks.retestCount }} 个待复测项目</div>
                <div class="todo-desc">需要重新进行测量</div>
              </div>
              <el-button type="warning" size="small" text @click="goToRetestList">查看</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="QualityIndex" lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, getCurrentInstance } from 'vue';
import { useRouter } from 'vue-router';
import * as echarts from 'echarts';
import {
  Monitor,
  DataAnalysis,
  CircleCheck,
  Warning,
  ArrowUp,
  ArrowDown,
  Clock,
  Document,
  Plus,
  Download
} from '@element-plus/icons-vue';
import { listMeasurement } from '@/api/quality/measurement';
import {
  getQualityOverview,
  getChartData,
  getDeviceStatusDistribution
} from '@/api/quality/overview';
import type { MeasurementVO, ChartDataQuery } from '@/api/quality/measurement/types';

const router = useRouter();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 加载状态
const chartLoading = ref(false);
const pieChartLoading = ref(false);
const recordsLoading = ref(false);

// 统计数据
const deviceStats = ref({
  total: 0,
  increase: 0
});

const measurementStats = ref({
  total: 0,
  normal: 0,
  hazard: 0,
  increase: 0,
  normalRate: 0,
  hazardRate: 0
});

const pendingTasks = ref({
  hazardCount: 0,
  retestCount: 0,
  reportCount: 0
});

// 最新测量记录
const recentMeasurements = ref<MeasurementVO[]>([]);

// 图表相关
const chartRef = ref<HTMLDivElement>();
const pieChartRef = ref<HTMLDivElement>();
const chartPeriod = ref('month');
let lineChart: echarts.ECharts | null = null;
let pieChart: echarts.ECharts | null = null;

/** 获取统计数据 */
const getStatistics = async () => {
  try {
    const res = await getQualityOverview();
    const data = res.data;

    // 设备统计
    deviceStats.value.total = data.deviceStats.total;
    deviceStats.value.increase = data.deviceStats.increase;

    // 测量统计
    measurementStats.value.total = data.measurementStats.total;
    measurementStats.value.normal = data.measurementStats.normal;
    measurementStats.value.hazard = data.measurementStats.hazard;
    measurementStats.value.increase = data.measurementStats.increase;
    measurementStats.value.normalRate = data.measurementStats.normalRate;
    measurementStats.value.hazardRate = data.measurementStats.hazardRate;

    // 待处理事项
    pendingTasks.value.hazardCount = data.pendingTasks.hazardCount;
    pendingTasks.value.retestCount = data.pendingTasks.retestCount;
    pendingTasks.value.reportCount = data.pendingTasks.reportCount;

  } catch (error) {
    console.error('获取统计数据失败:', error);
    proxy?.$modal.msgError('获取统计数据失败');
  }
};

/** 获取最新测量记录 */
const getRecentMeasurements = async () => {
  recordsLoading.value = true;
  try {
    const res = await listMeasurement({ pageNum: 1, pageSize: 5 });
    recentMeasurements.value = res.rows;
  } catch (error) {
    console.error('获取最新测量记录失败:', error);
    proxy?.$modal.msgError('获取最新测量记录失败');
  } finally {
    recordsLoading.value = false;
  }
};

/** 获取图表数据 */
const getChartDataFromAPI = async () => {
  chartLoading.value = true;
  try {
    const query: ChartDataQuery = {
      period: chartPeriod.value as 'week' | 'month' | 'quarter'
    };
    const res = await getChartData(query);
    return res.data;
  } catch (error) {
    console.error('获取图表数据失败:', error);
    proxy?.$modal.msgError('获取图表数据失败');
    return {
      dates: [],
      normalData: [],
      abnormalData: [],
      hazardData: []
    };
  } finally {
    chartLoading.value = false;
  }
};

/** 获取设备状态分布数据 */
const getDeviceStatusData = async () => {
  pieChartLoading.value = true;
  try {
    const res = await getDeviceStatusDistribution();
    return res.data;
  } catch (error) {
    console.error('获取设备状态数据失败:', error);
    proxy?.$modal.msgError('获取设备状态数据失败');
    return {
      normal: 0,
      disabled: 0
    };
  } finally {
    pieChartLoading.value = false;
  }
};

/** 初始化折线图 */
const initLineChart = async () => {
  if (!chartRef.value) return;

  lineChart = echarts.init(chartRef.value);

  const chartData = await getChartDataFromAPI();

  const option = {
    title: {
      text: '测量数据趋势',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['正常测量', '异常测量', '隐患记录'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.dates.length > 0 ? chartData.dates : ['暂无数据']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '正常测量',
        type: 'line',
        smooth: true,
        itemStyle: { color: '#67C23A' },
        data: chartData.normalData.length > 0 ? chartData.normalData : [0]
      },
      {
        name: '异常测量',
        type: 'line',
        smooth: true,
        itemStyle: { color: '#E6A23C' },
        data: chartData.abnormalData.length > 0 ? chartData.abnormalData : [0]
      },
      {
        name: '隐患记录',
        type: 'line',
        smooth: true,
        itemStyle: { color: '#F56C6C' },
        data: chartData.hazardData.length > 0 ? chartData.hazardData : [0]
      }
    ]
  };

  lineChart.setOption(option);
};

/** 初始化饼图 */
const initPieChart = async () => {
  if (!pieChartRef.value) return;

  pieChart = echarts.init(pieChartRef.value);

  const statusData = await getDeviceStatusData();

  const option = {
    title: {
      text: '设备状态分布',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['正常设备', '停用设备']
    },
    series: [
      {
        name: '设备状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: statusData.normal, name: '正常设备', itemStyle: { color: '#67C23A' } },
          { value: statusData.disabled, name: '停用设备', itemStyle: { color: '#F56C6C' } }
        ]
      }
    ]
  };

  pieChart.setOption(option);
};

/** 更新图表 */
const updateChart = async () => {
  if (lineChart) {
    const chartData = await getChartDataFromAPI();
    const option = lineChart.getOption();
    option.xAxis[0].data = chartData.dates.length > 0 ? chartData.dates : ['暂无数据'];
    option.series[0].data = chartData.normalData.length > 0 ? chartData.normalData : [0];
    option.series[1].data = chartData.abnormalData.length > 0 ? chartData.abnormalData : [0];
    option.series[2].data = chartData.hazardData.length > 0 ? chartData.hazardData : [0];
    lineChart.setOption(option);
  }
};

/** 获取测量类型文本 */
const getMeasurementTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    dimension: '尺寸测量',
    weight: '重量测量',
    temperature: '温度测量',
    pressure: '压力测量',
    other: '其他测量'
  };
  return typeMap[type] || type;
};

/** 快捷操作 */
const goToAddDevice = () => {
  router.push('/quality/device');
};

const goToAddMeasurement = () => {
  router.push('/quality/measurement');
};

const goToMeasurementList = () => {
  router.push('/quality/measurement');
};

const goToHazardList = () => {
  router.push('/quality/measurement?hazard=true');
};

const goToRetestList = () => {
  router.push('/quality/measurement?status=2');
};

const exportData = () => {
  proxy?.$modal.msgSuccess('数据导出功能开发中...');
};

const generateReport = () => {
  proxy?.$modal.msgSuccess('报告生成功能开发中...');
};

/** 窗口大小变化处理 */
const handleResize = () => {
  lineChart?.resize();
  pieChart?.resize();
};

onMounted(async () => {
  await getStatistics();
  await getRecentMeasurements();

  nextTick(() => {
    initLineChart();
    initPieChart();
  });

  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  lineChart?.dispose();
  pieChart?.dispose();
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.stat-card {
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.device-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.measurement-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.normal-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.hazard-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.stat-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
}

.stat-trend.up {
  color: #67c23a;
}

.stat-trend.down {
  color: #f56c6c;
}

.stat-desc {
  font-size: 12px;
  color: #909399;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.action-btn {
  width: 100%;
  height: 48px;
  border-radius: 8px;
  font-weight: 500;
}

.recent-records {
  max-height: 300px;
  overflow-y: auto;
}

.record-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-info {
  flex: 1;
}

.record-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.record-desc {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.record-time {
  font-size: 12px;
  color: #909399;
}

.todo-list {
  space-y: 16px;
}

.todo-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #909399;
}

.todo-content {
  flex: 1;
}

.todo-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.todo-desc {
  font-size: 12px;
  color: #909399;
}

.empty-data {
  padding: 20px;
  text-align: center;
}

@media (max-width: 768px) {
  .quick-actions {
    grid-template-columns: 1fr;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
}
</style>

{"doc": " 实测实量Service接口\n\n <AUTHOR>\n @date 2024-01-01\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询实测实量\n\n @param measurementId 测量ID\n @return 实测实量\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.quality.domain.bo.QualityMeasurementBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询实测实量列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 实测实量集合\n"}, {"name": "queryList", "paramTypes": ["org.dromara.quality.domain.bo.QualityMeasurementBo"], "doc": " 查询实测实量列表\n\n @param bo 查询条件\n @return 实测实量集合\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.quality.domain.bo.QualityMeasurementBo"], "doc": " 新增实测实量\n\n @param bo 实测实量\n @return 结果\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.quality.domain.bo.QualityMeasurementBo"], "doc": " 修改实测实量\n\n @param bo 实测实量\n @return 结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除实测实量信息\n\n @param ids     需要删除的实测实量主键集合\n @param isValid 是否校验,true-删除前校验,false-不校验\n @return 结果\n"}, {"name": "pushMeasurementInfo", "paramTypes": ["java.util.Collection"], "doc": " 推送测量信息\n\n @param measurementIds 测量ID集合\n @return 结果\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 标记隐患\n\n @param measurementId     测量ID\n @param hazardDescription 隐患描述\n @return 结果\n"}, {"name": "unmarkHazard", "paramTypes": ["java.lang.Long"], "doc": " 取消标记隐患\n\n @param measurementId 测量ID\n @return 结果\n"}, {"name": "fillDeviceInfo", "paramTypes": ["java.lang.Long", "org.dromara.quality.domain.bo.QualityMeasurementBo"], "doc": " 根据设备ID获取设备信息并填充到测量记录\n\n @param deviceId 设备ID\n @param bo       测量业务对象\n"}], "constructors": []}
export interface AttSnVO {
  /**
   * 设备id
   */
  snId: string | number;

  /**
   * 项目id
   */
  projectId: string | number;

  /**
   * 设备sn号
   */
  sn: string;

  /**
   * 设备名称
   */
  snName: string;

  /**
   * 方向。0-进，1-出
   */
  direction: number;

  /**
   * 设备状态。0-在线，1-掉线
   */
  status: number;

}

export interface AttSnForm extends BaseEntity {
  /**
   * 设备id
   */
  snId?: string | number;

  /**
   * 项目id
   */
  projectId?: string | number;

  /**
   * 设备sn号
   */
  sn?: string;

  /**
   * 设备名称
   */
  snName?: string;

  /**
   * 方向。0-进，1-出
   */
  direction?: number;

  /**
   * 设备状态。0-在线，1-掉线
   */
  status?: number;

}

export interface AttSnQuery extends PageQuery {

  /**
   * 项目id
   */
  projectId?: string | number;

  /**
   * 设备sn号
   */
  sn?: string;

  /**
   * 设备名称
   */
  snName?: string;

  /**
   * 方向。0-进，1-出
   */
  direction?: number;

  /**
   * 设备状态。0-在线，1-掉线
   */
  status?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}




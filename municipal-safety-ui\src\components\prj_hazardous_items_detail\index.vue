<template>
  <div v-if="prjDetailData?.projectId">
    <!-- 65vw -->
    <el-dialog :title="dialogDetail.title" v-model="dialogDetail.visible" append-to-body @close="handleClose"
      width="75vw">
      <el-tabs v-model="activeName" @tab-click="handleTabClick">
        <el-tab-pane label="基础信息" name="first">
          <el-descriptions border label-width="150px">
            <el-descriptions-item label="危大工程名称" align="center" :span="1" width="36%">{{ prjDetailData.itemName
            }}</el-descriptions-item>
            <el-descriptions-item label="状态" align="center" :span="2">
              <dict-tag :options="project_item_status" :value="prjDetailData.status" />
            </el-descriptions-item>
            <el-descriptions-item label="危大类型" align="center" :span="1">
              <dict-tag :options="danger_list_type" :value="prjDetailData.dangerListType" />
            </el-descriptions-item>
            <el-descriptions-item label="涉危工程" align="center" :span="2">{{ dangerTitle }}</el-descriptions-item>
            <el-descriptions-item label="涉危工程类型" label-align="center" min-width="130px" :span="3">
              <template v-for="(item, index) in dangerListList" :key="item.dangerId">
                <span>{{ index + 1 }}、</span><span>{{ item.name }}；</span><br />
              </template>
            </el-descriptions-item>
            <el-descriptions-item label="具体范围详情" :span="3" label-align="center">{{ prjDetailData.scopeDetails
            }}</el-descriptions-item>
            <el-descriptions-item label="计划开工日期" align="center" :span="1">{{ prjDetailData.startDate ?
              dayjs(prjDetailData.startDate).format('YYYY-MM-DD') : ''
            }}</el-descriptions-item>
            <el-descriptions-item label="计划开工日期" align="center" :span="2">{{ prjDetailData.plannedEndDate ?
              dayjs(prjDetailData.plannedEndDate).format('YYYY-MM-DD') : ''
            }}</el-descriptions-item>
            <el-descriptions-item label="实际开工日期" align="center" :span="1">{{ prjDetailData.actualStartDate ?
              dayjs(prjDetailData.actualStartDate).format('YYYY-MM-DD') : ''
            }}</el-descriptions-item>
            <el-descriptions-item label="实际竣工日期" align="center" :span="2">{{ prjDetailData.actualEndDate ?
              dayjs(prjDetailData.actualEndDate).format('YYYY-MM-DD') : '' }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="隐患清单" name="second" v-hasPermi="['projects:prj_hazardous_items:aiList']">
          <el-table class="expertTable3" v-loading="loading" :data="prjDetailData.aiListVOS" show-overflow-tooltip>
            <el-table-column label="序号" align="center" width="55" type="index" />
            <el-table-column label="隐患识别图片" align="center" prop="name">
              <template #default="scope">
                <HeaderPrewiew :src="scope.row.imgUrl" :width="50" :height="50" :preview-src-list="[scope.row.imgUrl]">
                </HeaderPrewiew>
              </template>
            </el-table-column>
            <el-table-column label="照片上传上传时间" align="center" prop="uploadTime" min-width="120px">
            </el-table-column>
            <el-table-column label="拍照位置文字描述" align="center" prop="locationDescription"
              min-width="150px"></el-table-column>
            <el-table-column label="补充说明" align="center" prop="expertManualInputDetails">
              <template #default="scope">
                <span v-if="scope.row.expertManualInputDetails">{{ scope.row.expertManualInputDetails }}</span>
                <span v-else>无</span>
              </template>
            </el-table-column>
            <el-table-column label="任务状态" align="center" prop="status">
              <template #default="scope">
                <dict-tag :options="ai_haz_analysis_tasks_status" :value="scope.row.status" />
              </template>
            </el-table-column>
            <el-table-column label="人工复检隐患" align="center" prop="recheckStatus">
              <template #default="scope">
                <dict-tag :options="ai_haz_analysis_tasks_recheck_status" :value="scope.row.recheckStatus" />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="250px">
              <template #default="scope">
                <div style="display: flex;justify-content: center;align-items: center;">
                  <el-button v-if="scope.row.recheckStatus == 'AUDIT_RECHECK'" plain type="primary" size="small"
                    @click="handleToExamine(scope.row)" v-hasPermi="['projects:prj_hazardous_items:detail_condition']"
                    style="width: 60px;">审核</el-button>
                  <div v-else style="margin-left: 12px;">
                    <el-button plain type="primary" size="small" @click="handleDetail(scope.row)"
                      v-hasPermi="['projects:prj_hazardous_items:detail_detail']" style="width: 60px;">详情</el-button>
                    <el-button plain type="primary" size="small" @click="handleManual(scope.row)"
                      v-hasPermi="['projects:prj_hazardous_items:detail_manual']" style="width: 70px;">人工复检</el-button>
                    <el-button plain type="primary" size="small" @click="handleDispatchReview(scope.row)"
                      v-hasPermi="['projects:prj_hazardous_items:detail_secondManual']"
                      style="width: 70px;">派遣审查</el-button>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="专项施工方案" name="three">
          <el-card shadow="never">
            <el-table v-loading="loading1" :data="plansList" show-overflow-tooltip class="expertTable3">
              <el-table-column type="index" label="序号" width="55" align="center" />
              <el-table-column label="施工方案名称" align="center" prop="planName" />
              <el-table-column label="施工方案" align="center" prop="planDocumentId">
                <template #default="scope">
                  <el-button size="small" type="text" icon="Download" @click="schemeDownload(scope.row)">下载</el-button>
                </template>
              </el-table-column>
              <el-table-column label="施工方案审批表" align="center" prop="approvalFormDocId">
                <template #default="scope">
                  <el-button size="small" type="text" icon="Download"
                    @click="approvalFormDownload(scope.row)">下载</el-button>
                </template>
              </el-table-column>
              <el-table-column label="AI校验状态" align="center" prop="reviewStatus">
                <template #default="scope">
                  <dict-tag :options="plan_ai_check_status" :value="scope.row.reviewStatus" />
                </template>
              </el-table-column>
              <el-table-column label="AI分析结果" align="center">
                <template #default="scope">
                  <el-button size="small" type="primary" plain @click="showAiResultDetail(scope.row)">查看详情</el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="plansTotal > 0" :total="plansTotal" v-model:page="plansQueryParams.pageNum"
              v-model:limit="plansQueryParams.pageSize" @pagination="getSchemeList" />
          </el-card>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogDetail.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 隐患清单的详情 -->
    <el-dialog :title="hiddenDangerDialog.title" v-model="hiddenDangerDialog.visible" append-to-body
      class="hiddenDialog" width="1200px">
      <el-row :gutter="10" style="padding:15px;box-shadow: 0px 0px 5px rgba(0, 0, 0, .12);height: 626px;">
        <el-col :span="7"
          style="display: flex;flex-direction: column; justify-content:center;align-items: flex-start;padding-left: 20px;">
          <div style="margin-bottom: 10px;">
            <p style="text-align: start;color: #409EFF;margin: 0 0 16px;">分析前</p>
            <HeaderPrewiew :src="aiDetailData?.photoDocumentUrl" :width="250" :height="250"
              :preview-src-list="[aiDetailData?.photoDocumentUrl]">
            </HeaderPrewiew>
          </div>
          <div>
            <p style="text-align: start;color: #67C23A;margin: 10px 0 16px;">分析后</p>
            <HeaderPrewiew :src="aiDetailData?.aiPhotoDocumentUrl" :width="250" :height="250"
              :preview-src-list="[aiDetailData?.aiPhotoDocumentUrl]">
            </HeaderPrewiew>
          </div>
        </el-col>
        <el-col :span="17" style="display: flex;flex-direction: column;padding-top: 29px;height: 100%;">
          <div style="width: 100%; height: 100%;overflow-y: auto;">
            <el-card style="width: 100%;margin-bottom: 20px;" v-for="(item, index) in aiDetailData?.violations"
              :key="index">
              <template #header>
                <div class="card-header">
                  <span>问题 {{ index + 1 }}</span>
                  <div style="display: flex;align-items: center;">
                    <span style="color: #409EFF;">危险级别：</span>
                    <dict-tag :options="hidden_danger_type" :value="item.level" />
                  </div>
                </div>
              </template>
              <div style="display: flex;">
                <span style="color: #666;display: block;width: 70px;">隐患描述：</span>
                <span style="display: block;flex: 1;">{{ item.violation }}</span>
              </div>
              <div style="display: flex;margin: 15px 0;">
                <span style="color: #666;display: block;width: 70px;">违反条款：</span>
                <span style="display: block;flex: 1;">{{ item.regulation }}</span>
              </div>
              <div style="display: flex;">
                <span style="color: #666;display: block;width: 70px;">整改意见：</span>
                <span style="display: block;flex: 1;">{{ item.measure
                }}</span>
              </div>
            </el-card>
          </div>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="hiddenDangerDialog.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- AI分析结果详情弹窗 -->
    <el-dialog :title="aiResultDialog.title" v-model="aiResultDialog.visible" append-to-body width="600px">
      <div class="ai-result-content">{{ currentAiResult }}</div>
      <div class="copy-btn-container">
        <el-button type="primary" @click="copyAiResult">复制内容</el-button>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="aiResultDialog.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 人工复检弹框 -->
    <el-dialog :title="manualDialog.title" v-model="manualDialog.visible" append-to-body width="100%"
      style="height: 100%;" class="manualDialog" @close="handleManualClose">
      <el-row :gutter="10" style="width: 100%;height: 100%;">
        <el-col :span="5" style="display: flex;flex-direction: column;align-items: flex-start;">
          <div style="margin-bottom: 10px;">
            <p style="text-align: start;color: #409EFF;margin: 0 0 16px;">分析前</p>
            <HeaderPrewiew :src="aiDetailData?.photoDocumentUrl" width="18.3vw" height="18.3vw"
              :preview-src-list="[aiDetailData?.photoDocumentUrl]">
            </HeaderPrewiew>
          </div>
          <div>
            <p style="text-align: start;color: #67C23A;margin: 10px 0 16px;">分析后</p>
            <HeaderPrewiew :src="aiDetailData?.aiPhotoDocumentUrl" width="18.3vw" height="18.3vw"
              :preview-src-list="[aiDetailData?.aiPhotoDocumentUrl]">
            </HeaderPrewiew>
          </div>
        </el-col>
        <el-col ref="ref1" :span="10" style="display: flex;flex-direction: column;height: 100%;">
          <div style="width: 100%; height: calc(90vh - 50px);overflow-y: auto;">
            <el-card v-for="(item, index) in aiDetailData?.violations" :key="index"
              @dblclick="hiddenDangerDBClick(index, item)"
              :class="[rgfjCheck[index] ? 'active' : '', 'hiddenDangerCard']">
              <template #header>
                <div class="card-header">
                  <div style="display: flex;align-items: center;">
                    <el-checkbox v-model="rgfjCheck[index]" @change="handleCheckChange(index, item)" />
                    <span style="display: block;padding-bottom: 3px;margin-left: 8px;">问题 {{ index + 1 }}</span>
                  </div>
                  <div style="display: flex;align-items: center;">
                    <span style="color: #409EFF;">危险级别：</span>
                    <dict-tag :options="hidden_danger_type" :value="item.level" />
                  </div>
                </div>
              </template>
              <div style="display: flex;">
                <span style="color: #666;display: block;width: 70px;">隐患描述：</span>
                <span style="display: block;flex: 1;">{{ item.violation }}</span>
              </div>
              <div style="display: flex;margin: 15px 0;">
                <span style="color: #666;display: block;width: 70px;">违反条款：</span>
                <span style="display: block;flex: 1;">{{ item.regulation }}</span>
              </div>
              <div style="display: flex;">
                <span style="color: #666;display: block;width: 70px;">整改意见：</span>
                <span style="display: block;flex: 1;">{{ item.measure
                }}</span>
              </div>
            </el-card>
          </div>
        </el-col>
        <el-col :span="9" style="padding-left: 20px;height: 100%;">
          <div style="width: 100%;height: calc(90vh - 50px);overflow-y: auto;padding-right: 20px;">
            <div>
              <div style="display: flex;align-items: center;">
                <label>整改复核</label>
                <span style="font-size: 15px;color: #F56C6C;margin-left: 10px;padding-bottom: 2px;">(注：请先选择左侧的问题)</span>
              </div>
              <el-checkbox-group ref="ref2" v-model="selectCheckVal" style="margin-top: 20px;">
                <el-checkbox label="限期整改" :value="1" />
                <el-checkbox label="停工通知" :value="2" />
                <el-checkbox label="行政处罚" :value="3" />
                <el-checkbox label="其他" :value="4" />
              </el-checkbox-group>
              <div>
                <div style="display: flex;align-items: center;margin-top: 20px;">
                  <span style="display: block;width: 70px;">整改时限</span>
                  <el-input v-model="formCorrectData.timeLimit" style="width: 12vw;" type="number" :min="1"
                    placeholder="请输入时间" @change="() => {
                      if (formCorrectData.timeLimit <= 0) {
                        formCorrectData.timeLimit = 1;
                      }
                    }">
                    <template #append>
                      <el-select v-model="formCorrectData.timeType" style="width: 80px;">
                        <el-option label="小时" :value="1" />
                        <el-option label="天" :value="2" />
                      </el-select>
                    </template>
                  </el-input>
                </div>
              </div>
            </div>
            <div style="margin-top: 30px;">
              <ul style="list-style: none;padding: 0;margin: 0;">
                <li v-if="selectCheckVal.includes(1)" style="margin-bottom: 20px;">
                  <div style="display: flex;">
                    <label>限期整改内容</label>
                    <el-button ref="ref4" type="primary" size="small" style="margin-left: 20px;margin-right: 15px;"
                      @click="handleDeadlineTemplate">下载模板</el-button>
                    <FileUpload @update:modelValue="handleDeadlineFile" :limit="1"
                      :modelValue="formCorrectData.correctionsFile" :isShowTip="false" :fileSize="20">
                      <el-button ref="ref5" size="small" type="primary">上传</el-button>
                    </FileUpload>
                    <span
                      style="font-size: 15px;color: #F56C6C;margin-left: 10px;padding-bottom: 2px;margin-left: 20px;">(注：请上传已盖章文件)</span>
                  </div>
                  <el-input ref="ref3" v-model="formCorrectData.correctionsContent" :rows="18" type="textarea"
                    style="margin-top: 10px;" />
                </li>
                <li v-if="selectCheckVal.includes(2)" style="margin-bottom: 20px;">
                  <div style="display: flex;">
                    <label>停工通知内容</label>
                    <el-button type="primary" size="small" style="margin-left: 20px;margin-right: 15px;"
                      @click="handleSuspensionTemplate">下载模板</el-button>
                    <FileUpload @update:modelValue="handleShutdownFile" :limit="1"
                      :modelValue="formCorrectData.suspensionFile" :isShowTip="false" :fileSize="20">
                      <el-button size="small" type="primary">上传</el-button>
                    </FileUpload>
                    <span
                      style="font-size: 15px;color: #F56C6C;margin-left: 10px;padding-bottom: 2px;margin-left: 20px;">(注：请上传已盖章文件)</span>
                  </div>
                  <el-input v-model="formCorrectData.suspensionContent" :rows="18" type="textarea"
                    style="margin-top: 10px;" />
                </li>
                <li v-if="selectCheckVal.includes(3)" style="margin-bottom: 20px;">
                  <div style="display: flex;">
                    <label>行政处罚内容</label>
                    <el-button type="primary" size="small" style="margin-left: 20px;margin-right: 15px;"
                      @click="handlePenaltyTemplate">下载模板</el-button>
                    <FileUpload @update:modelValue="handlePunishFile" :limit="1"
                      :modelValue="formCorrectData.penaltyFile" :isShowTip="false" :fileSize="20">
                      <el-button size="small" type="primary">上传</el-button>
                    </FileUpload>
                    <span
                      style="font-size: 15px;color: #F56C6C;margin-left: 10px;padding-bottom: 2px;margin-left: 20px;">(注：请上传已盖章文件)</span>
                  </div>
                  <el-input v-model="formCorrectData.penaltyContent" :rows="18" type="textarea"
                    style="margin-top: 10px;" />
                </li>
                <li v-if="selectCheckVal.includes(4)" style="margin-bottom: 20px;">
                  <div style="display: flex;">
                    <label>其他</label>
                    <FileUpload @update:modelValue="handleOtherFile" :isShowTip="false" :fileSize="20"
                      style="margin-left: 20px;">
                      <el-button ref="ref5" size="small" type="primary">上传</el-button>
                    </FileUpload>
                  </div>
                  <el-table :data="formCorrectData.elseFile" style="width: 100%;margin-top: 10px;">
                    <el-table-column type="index" label="序号" width="55" align="center" />
                    <el-table-column prop="name" label="名称" align="center">
                      <template #default="scope">
                        <el-input v-model="scope.row.name" style="width: 150px;" />
                      </template>
                    </el-table-column>
                    <el-table-column prop="fileName" label="文件" align="center">
                      <template #default="scope">
                        <el-link type="primary" href="javascript:;" style="color: #409EFF;"
                          @click="handleFileView(scope.row.url)">{{
                            scope.row.fileName
                          }}</el-link>
                      </template>
                    </el-table-column>
                  </el-table>
                </li>
              </ul>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-tour v-model="tourOpen" @change="tourChange" @close="handleTourClose" :z-index="3001" close-icon="CircleClose"
        class="tourClass">
        <el-tour-step :target="ref1?.$el" title="选择问题点" description="双击选择左侧问题点，可多选" placement="right" />
        <el-tour-step :target="ref2?.$el" title="选择整改措施" description="选择整改措施，可多选" />
        <el-tour-step :target="ref3?.$el" title="编辑内容" description="编辑问题点内容" />
        <el-tour-step :target="ref4?.$el" title="下载模板" description="下载需要上传的文件模板" />
        <el-tour-step :target="ref5?.$el" title="上传文件" description="上传盖章后的文件" />
        <el-tour-step :target="ref6?.$el" title="提交" description="一键提交所有内容" />
      </el-tour>
      <template #footer>
        <div class="dialog-footer">
          <el-button ref="ref6" type="primary" @click="handleManualSubmit">提交工单</el-button>
          <el-button @click="manualDialog.visible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 派遣审查弹框组件 -->
    <DispatchReview :dispatchRightData="dispatchRightData" :isDispatchModel="isDispatchModel"
      :dispatchDialogTitle="dispatchDialogTitle" :taskId="taskId" @dispatchClose="(val: boolean) => {
        isDispatchModel = val;
        dispatchExpertData = [];
      }">
      <!-- 具名插槽 -->
      <template #dispatchRight="{ data }">
        <div>
          <label style="font-size: 15px;">派遣专家</label>
          <div style="margin-top: 30px;">
            <el-button type="primary" size="small" @click="selectExpertClick">选择专家</el-button>
            <span
              style="font-size: 14px;color: #F56C6C;margin-left: 10px;padding-bottom: 2px;margin-left: 20px;">(注：必须选择专家)</span>
            <div style="margin-top: 25px;">
              <template v-for="item in dispatchExpertData" :key="item.expertId">
                <el-tag type="primary" style="margin-right: 12px;" closable @close="handleTagClose(item)">{{ item.name
                }}</el-tag>
              </template>
            </div>
          </div>
          <div style="margin-top: 40px;">
            <label style="font-size: 15px;">补充说明</label>
            <el-input v-model="dispatchRightData.instruction" :rows="15" type="textarea" style="margin-top: 15px;" />
          </div>
        </div>
      </template>
    </DispatchReview>
    <!-- 派遣审查右侧选择专家的列表弹框-->
    <SelectExpert :isShowModel="isShowExpertModel" @update:isShowModel="handleModelExpert"
      @selectEmit="selectEmitChange" />
    <!-- 隐患清单详情审核组件 -->
    <HazardListEdit :isShowModel="isShowHazardousListModel" :itemId="HazardousListItemId" :taskId="HazardousListTaskId"
      @update:isShowModel="handleModelHazardousList" @hazardSubmit="hazardSubmit" />
  </div>
</template>

<script setup lang="ts">
import { getPrj_hazardous_items_detail, getPrj_hazardous_items_ai_detail } from '@/api/projects/prj_hazardous_items/index'
import { DangerListVO } from '@/api/system/dangerList/types';
import { listDangerList } from '@/api/system/dangerList';
import { listByIds } from '@/api/system/oss/index'
import type { TabsPaneContext } from 'element-plus'
import { ConstructionPlansVO, ConstructionPlansQuery } from '@/api/projects/prj_construction_plans/types'
import { listConstructionPlans, startWorkOrder } from '@/api/projects/prj_construction_plans/api'
import dayjs from 'dayjs';
import HeaderPrewiew from '@/components/ImagePreview/index.vue';
import FileUpload from '@/components/FileUpload/index.vue'
import { manualSubmit } from '@/api/customFlow/api'
import type { ButtonInstance } from 'element-plus'
import { ExpertVO } from '@/api/expert/expert/types';
import { useStorage } from '@vueuse/core';
import DispatchReview from '@/components/DispatchReview/index.vue'
import SelectExpert from '@/components/SelectExpert/index.vue'
import _ from 'lodash'
import HazardListEdit from '@/components/HazardListEdit/index.vue'

const ref1 = ref<ButtonInstance>()
const ref2 = ref<ButtonInstance>()
const ref3 = ref<ButtonInstance>()
const ref4 = ref<ButtonInstance>()
const ref5 = ref<ButtonInstance>()
const ref6 = ref<ButtonInstance>()
const tourOpen = ref(false)
const tourStorage = useStorage<null | string>('Tour', null);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { danger_list_type, ai_haz_analysis_tasks_status, project_item_status, hidden_danger_type, plan_ai_check_status, ai_haz_analysis_tasks_recheck_status } = toRefs<any>(proxy?.useDict('danger_list_type', 'ai_haz_analysis_tasks_status', 'project_item_status', 'hidden_danger_type', 'plan_ai_check_status', 'ai_haz_analysis_tasks_recheck_status'));

const emit = defineEmits(['update:isShowModel']);
const props = defineProps({
  itemId: {
    type: String,
    default: ''
  },
  isShowModel: {
    type: Boolean,
    default: false
  }
});

const activeName = ref('first');
const dialogDetail = reactive({
  visible: false, // 控制对话框的显示隐藏
  title: '项目工程信息', // 对话框的标题
})
// 详情信息的变量数据
const prjDetailData = ref();
const dangerTitle = ref();
const dangerListList = ref();
const loading = ref(false)

// 隐患清单的详情的弹框显隐
const hiddenDangerDialog = reactive({
  visible: false, // 控制对话框的显示隐藏
  title: '', // 对话框的标题
})
// 存放隐患清单详情的数据
const aiDetailData = ref();
const loading1 = ref(false)
// 存放专项施工方案列表数据
const plansList = ref<ConstructionPlansVO[]>([])
// 施工方案列表总条数
const plansTotal = ref(0)
const plansQueryParams = ref<ConstructionPlansQuery>({
  itemId: undefined,
  pageNum: 1,
  pageSize: 10,
})

// AI分析结果弹窗
const aiResultDialog = reactive({
  visible: false,
  title: 'AI分析结果详情',
})
// 控制人工复检弹框显隐
const manualDialog = reactive({
  visible: false,
  title: '',
})
// 人工复检多选值
const rgfjCheck = ref([])
// 人工复检多选框的值
const selectCheckVal = ref([])
// 临时存放选中的问题
const tempCheck = ref([])
// 提交工单的表单参数
const formCorrectData = reactive({
  question: '', // 问题点id(多个根据顺序用,隔开拼接)
  timeLimit: 1, // 整改时限
  timeType: 1, // 时间单位
  correctionsFile: '', // 限期整改文件
  correctionsContent: '', // 限期整改内容
  suspensionFile: '', // 停工通知文件
  suspensionContent: '', // 停工通知内容
  penaltyFile: '', // 行政处罚文件
  penaltyContent: '', // 行政处罚内容
  elseFile: [],
  taskId: '', // 任务id
})
const taskId1 = ref('')
const currentAiResult = ref('');


// 派遣审查部分
// 控制派遣审查弹框的显隐
const isDispatchModel = ref(false);
// 派遣审查弹框的标题
const dispatchDialogTitle = ref('');
// 派遣审查任务id
const taskId = ref('');
// 派遣审查控制选择专家弹框的显隐
const isShowExpertModel = ref(false);
// 派遣审查的专家数据
const dispatchExpertData = ref<ExpertVO[]>([]);
// 派遣审查右侧的所有数据
const dispatchRightData = ref({
  specialist: '', // 专家id(多个根据顺序用,隔开拼接)
  instruction: '', // 补充说明
  aiHazAnalysisId: '', // 隐患id
});

// 审核事件的相关变量
const isShowHazardousListModel = ref(false); // 控制隐患清单详情审核的显隐
const HazardousListItemId = ref(''); // 隐患清单详情审核的id
const HazardousListTaskId = ref(''); // 隐患清单详情审核的任务id

watch(() => props.isShowModel, (val) => {
  if (props.itemId) {
    HazardousListItemId.value = props.itemId;
    dispatchRightData.value.aiHazAnalysisId = props.itemId;
    plansQueryParams.value.itemId = props.itemId;
    getItemsDetail(props.itemId);
  }
  dialogDetail.visible = val;
  getSchemeList()
})
/** 查询dangerList列表 */
const getDangerList = async (dangerListType: string | number) => {
  const res = await listDangerList();
  dangerListList.value = [];
  const data = proxy?.handleTree<DangerListVO>(res.data, 'dangerId', 'preId');
  if (data) {
    for (let i = 0; i < data.length; i++) {
      if (data[i].type == dangerListType) {
        for (let m = 0; m < data[i].children.length; m++) {
          for (let n = 0; n < data[i].children[m].children.length; n++) {
            if ((prjDetailData.value.dangerId as string).includes(data[i].children[m].children[n].dangerId)) {
              dangerTitle.value = data[i].children[m].name
              dangerListList.value.push(data[i].children[m].children[n]);
            }
          }
        }
      }
    }
  }
};
const getItemsDetail = async (itemId: string) => {
  const res = await getPrj_hazardous_items_detail(itemId);
  if (res.code === 200) {
    prjDetailData.value = res.data;
    getDangerList(prjDetailData.value.dangerListType);
    for (let index = 0; index < prjDetailData.value.aiListVOS?.length; index++) {
      if (prjDetailData.value.aiListVOS[index].aiPhotoDocumentId != null) {
        prjDetailData.value.aiListVOS[index].imgUrl = await getImageUrl(prjDetailData.value.aiListVOS[index].aiPhotoDocumentId);
      } else {
        prjDetailData.value.aiListVOS[index].imgUrl = ""
      }
    }
  }
}
// 使用ossId查询图片的url地址
const getImageUrl = async (ossId: string | number) => {
  const { data } = await listByIds(ossId);
  return data[0]?.url;
}
// 获取隐患详情数据
const getAiDetail = async (taskId: string) => {
  const res = await getPrj_hazardous_items_ai_detail(taskId);
  if (res.code === 200) {
    aiDetailData.value = res.data;
    aiDetailData.value.photoDocumentUrl = await getImageUrl(aiDetailData.value.photoDocumentId);
    aiDetailData.value.aiPhotoDocumentUrl = await getImageUrl(aiDetailData.value.aiPhotoDocumentId);
  }
}
// 审核
const handleToExamine = (row: any) => {
  HazardousListTaskId.value = row.taskId;
  isShowHazardousListModel.value = true;
}
// 详情
const handleDetail = (row: any) => {
  getAiDetail(row.taskId);
  hiddenDangerDialog.title = '隐患清单详情';
  hiddenDangerDialog.visible = true;
}
// 人工复检
const handleManual = (row: any) => {
  taskId1.value = row.taskId;
  formCorrectData.taskId = row.taskId;
  getAiDetail(row.taskId);
  manualDialog.title = '人工复检';
  manualDialog.visible = true;
  if (tourStorage.value == 'isTour') {
    return;
  }
  setTimeout(() => {
    tourOpen.value = true
    tourStorage.value = 'isTour'
  }, 500);
}
// 派遣审查按钮点击事件
const handleDispatchReview = async (row: any) => {
  taskId.value = row.taskId;
  dispatchDialogTitle.value = '派遣审查';
  isDispatchModel.value = true;
}
// tour引导下一步事件方法
const tourChange = (val: number) => {
  if (val === 1) {
    selectCheckVal.value[0] = 1
    hiddenDangerDBClick(0, aiDetailData.value.violations[0])
  }
}
// tour关闭事件
const handleTourClose = () => {
  handleManualClose()
}
// 双击人工复检里面的问题框事件
const hiddenDangerDBClick = (index: number, row: any) => {
  rgfjCheck.value[index] = !rgfjCheck.value[index];
  handleCheckChange(index, row)
}
// 限期整改模板下载
const handleDeadlineTemplate = async () => {
  proxy?.download('/system/hazardousItemsComments/down/xqzg',
    { projectId: prjDetailData.value.projectId, timeLimit: formCorrectData.timeLimit, timeType: formCorrectData.timeType, question: formCorrectData.correctionsContent },
    `corrections_${new Date().getTime()}.docx`)
}
// 停工通知模板下载
const handleSuspensionTemplate = async () => {
  proxy?.download('/system/hazardousItemsComments/down/tgzg',
    { projectId: prjDetailData.value.projectId, timeLimit: formCorrectData.timeLimit, timeType: formCorrectData.timeType, question: formCorrectData.suspensionContent },
    `suspension_${new Date().getTime()}.docx`)
}
// 行政处罚模板下载
const handlePenaltyTemplate = async () => {
  proxy?.download('/system/hazardousItemsComments/down/xzcfjds',
    { projectId: prjDetailData.value.projectId, timeLimit: formCorrectData.timeLimit, timeType: formCorrectData.timeType, question: formCorrectData.penaltyContent },
    `penalty_${new Date().getTime()}.docx`)
}
// 人工发起工单
const handleManualSubmit = () => {
  const isRgfjCheck = rgfjCheck.value.some((item: any) => item)
  if (!isRgfjCheck) {
    proxy?.$modal.msgError('请选择左侧问题！');
    return;
  }
  if (selectCheckVal.value.length === 0) {
    proxy?.$modal.msgError('请选择整改复核下方的内容！');
    return;
  }
  if (!formCorrectData.correctionsFile && selectCheckVal.value.includes(1)) {
    proxy?.$modal.msgError('请上传限期整改文件！');
    return;
  }
  if (!formCorrectData.suspensionFile && selectCheckVal.value.includes(2)) {
    proxy?.$modal.msgError('请上传停工通知文件！');
    return;
  }
  if (!formCorrectData.penaltyFile && selectCheckVal.value.includes(3)) {
    proxy?.$modal.msgError('请上传行政处罚文件！');
    return;
  }
  if (formCorrectData.elseFile.length <= 0 && selectCheckVal.value.includes(4)) {
    proxy?.$modal.msgError('请上传其他文件！');
    return;
  }
  if (!selectCheckVal.value.includes(1)) {
    formCorrectData.correctionsContent = '';
  }
  if (!selectCheckVal.value.includes(2)) {
    formCorrectData.suspensionContent = '';
  }
  if (!selectCheckVal.value.includes(3)) {
    formCorrectData.penaltyContent = '';
  }
  if (!selectCheckVal.value.includes(4)) {
    formCorrectData.elseFile = [];
  }
  proxy?.$modal.confirm('是否确认发起工单？').then(async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '提交中，请稍等...',
      background: 'rgba(255, 255, 255, 0.8)',
    })
    const res = await manualSubmit(formCorrectData);
    if (res.code === 200) {
      let busId = res.data
      const res1 = await startWorkOrder(taskId1.value, busId);
      if (res1.code === 200) {
        proxy?.$modal.msgSuccess('操作成功');
      }
    }
    loading.close()
    manualDialog.visible = false;
  }).catch(() => { });
}
// 问题的多选事件
const handleCheckChange = (index: number, item: any) => {
  if (rgfjCheck.value[index]) {
    if (formCorrectData.question) {
      formCorrectData.question = formCorrectData.question + ',' + item.resultId;
    } else {
      formCorrectData.question = item.resultId;
    }
    tempCheck.value[index] = `问题 ${index + 1}\n隐患描述：${item.violation}\n违反条款：${item.regulation}\n整改意见：${item.measure}\n`
  }
  if (rgfjCheck.value[index]) {
    formCorrectData.correctionsContent = tempCheck.value.join('');
    formCorrectData.suspensionContent = tempCheck.value.join('');
    formCorrectData.penaltyContent = tempCheck.value.join('');
  } else {
    tempCheck.value[index] = '';
    formCorrectData.correctionsContent = tempCheck.value.join('');
    formCorrectData.suspensionContent = tempCheck.value.join('');
    formCorrectData.penaltyContent = tempCheck.value.join('');
  }
}
// 上传限期整改文件的回调函数
const handleDeadlineFile = (fileOssId: string) => {
  formCorrectData.correctionsFile = fileOssId;
}
// 上传停工通知文件的回调函数
const handleShutdownFile = (fileOssId: string) => {
  formCorrectData.suspensionFile = fileOssId;
}
// 上传行政处罚文件的回调函数
const handlePunishFile = (fileOssId: string) => {
  formCorrectData.penaltyFile = fileOssId;
}
// 上传其他文件的回调函数
const handleOtherFile = (fileOssId: string) => {
  formCorrectData.elseFile = []
  const fileOssIdArr = fileOssId.split(',');
  fileOssIdArr.forEach((item: string) => {
    formCorrectData.elseFile.push({
      name: '',       //名称
      fileName: '',   //文件名称
      fileId: item,     //文件id
      taskId: taskId1.value,     //任务id,
      serviceType: 'quality_supervision_department'   //服务类型（取字典表flow_service_type）
    })
  })
  getOtherFileList(fileOssId);
}
// 获取其他文件列表
const getOtherFileList = async (fileOssId: string) => {
  if (!fileOssId) {
    formCorrectData.elseFile = [];
    return;
  }
  const res = await listByIds(fileOssId);
  if (res.code == 200) {
    formCorrectData.elseFile.forEach((item: any, index: number) => {
      item.name = res.data[index].originalName
      item.fileName = res.data[index].originalName
    });
  }
}
// 预览上传的其他文件
const handleFileView = (urls: any) => {
  const botaUrl = btoa(urls)
  const url = `${import.meta.env.VITE_APP_VIEW_URL}/onlinePreview?url=${botaUrl}`
  // 其他文件预览
  window.open(url, '_blank')
}
// 关闭人工复检弹框的回调函数
const handleManualClose = () => {
  rgfjCheck.value.forEach((item: any, index: number) => { // 重置多选框的值;
    rgfjCheck.value[index] = false;
  })
  formCorrectData.timeLimit = 1;
  selectCheckVal.value = [];
  formCorrectData.correctionsContent = '';
  formCorrectData.suspensionContent = '';
  formCorrectData.penaltyContent = '';
  formCorrectData.correctionsFile = '';
  formCorrectData.suspensionFile = '';
  formCorrectData.penaltyFile = '';
  formCorrectData.question = '';
  formCorrectData.timeType = 1;
  formCorrectData.elseFile = [];
  tempCheck.value = [];
}
// 查询专项施工方案列表数据
const getSchemeList = async () => {
  loading1.value = true;
  const res = await listConstructionPlans(plansQueryParams.value);
  plansList.value = res.rows;
  plansTotal.value = res.total;
  loading1.value = false;
}
// 关闭详情弹框
const handleClose = () => {
  emit('update:isShowModel', false);
}
// 下载施工方案
const schemeDownload = (row: ConstructionPlansVO) => {
  proxy?.$download.oss(row.planDocumentId);
}
// 下载施工方案审批表
const approvalFormDownload = (row: ConstructionPlansVO) => {
  proxy?.$download.oss(row.approvalFormDocId);
}
// 切换tab
const handleTabClick = (tab: TabsPaneContext) => {

}
// 显示AI分析结果详情
const showAiResultDetail = (row: ConstructionPlansVO) => {
  currentAiResult.value = row.aiDefectWarningDetails || '暂无分析结果';
  aiResultDialog.visible = true;
}
// 复制AI分析结果
const copyAiResult = () => {
  navigator.clipboard.writeText(currentAiResult.value).then(() => {
    proxy?.$modal.msgSuccess('复制成功');
  }).catch(() => {
    proxy?.$modal.msgError('复制失败，请手动复制');
  });
}
// 选择专家组件的自定义事件方法
const handleModelExpert = (val: boolean) => {
  isShowExpertModel.value = val;
}
// 接受子组件通过emit传递过来的数据
const selectEmitChange = (val: ExpertVO[]) => {
  const cloneVal = _.cloneDeep(val);
  if (cloneVal.length > 0) {
    dispatchRightData.value.specialist = cloneVal.map(item => item.idCard).join(',');
    dispatchExpertData.value = cloneVal;
  }
}
// 选择专家按钮点击事件
const selectExpertClick = (row: any) => {
  isShowExpertModel.value = true;
}
// 派遣审查右侧的标签关闭事件
const handleTagClose = (tag: ExpertVO) => {
  const index = dispatchExpertData.value.indexOf(tag);
  if (index !== -1) {
    dispatchExpertData.value.splice(index, 1);
    dispatchRightData.value.specialist = dispatchExpertData.value.map(item => item.idCard).join(',');
  }
}
// 审核组件的自定义事件
const handleModelHazardousList = (val: boolean) => {
  isShowHazardousListModel.value = val;
}
// 审核组件的提交自定义事件
const hazardSubmit = (val: string) => {
  setTimeout(() => {
    getItemsDetail(val);
  }, 2000);
}
</script>

<style lang="scss">
.expertTable3 {

  .el-popper,
  .is-dark {
    max-width: 300px;
  }
}

.el-overlay .el-overlay-dialog .hiddenDialog .el-dialog__body {
  padding: 20px 8px 10px !important;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-result-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
}

.copy-btn-container {
  margin-top: 15px;
  text-align: right;
}

.manualDialog {
  .el-dialog__body {
    overflow-y: hidden;
    max-height: calc(90vh - 50px) !important;
  }
}

.hiddenDangerCard {
  width: 100%;
  margin-bottom: 20px;
  cursor: pointer;

  &.active {
    background-color: #e8f2ff;
  }

  &:hover {
    background-color: #f2f7fd;
  }
}

.tourClass {
  .el-tour__close {
    font-size: 24px !important;
  }
}
</style>

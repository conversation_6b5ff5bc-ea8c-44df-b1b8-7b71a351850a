:root {
  --menuBg: #E8F0FD;
  --topMenuBg: #238DF7;
  --menuColor: #313136;
  --topMenuColor: #ffffff;
  --menuActiveText: #313136;
  --menuHover: #ffffff;
  --subMenuBg: #E8F0FD;
  --subMenuActiveText: #f4f4f5;
  --subMenuHover: #ffffff;
  --subMenuTitleHover: #ffffff;
  --fixedHeaderBg: #ffffff;
  --tableHeaderBg: #f8f8f9;
  --tableHeaderTextColor: #515a6e;
  --brder-color: #e8e8e8;
  --tags-view-active-bg: var(--el-color-primary);
  --tags-view-active-border-color: var(--el-color-primary);
}

html.dark {
  --menuBg: #1d1e1f;
  --menuColor: #bfcbd9;
  --menuActiveText: #f4f4f5;
  --menuHover: #171819;
  --subMenuBg: #1d1e1f;
  --subMenuActiveText: #1d1e1f;
  --subMenuHover: #171819;
  --subMenuTitleHover: #171819;
  --fixedHeaderBg: #171819;
  --tableHeaderBg: var(--el-bg-color);
  --tableHeaderTextColor: var(--el-text-color);
  --tags-view-active-bg: var(--el-color-primary-dark-6);
  --tags-view-active-border-color: var(--el-color-primary-light-2);
  --vxe-font-color: #98989e;
  --vxe-primary-color: #2c7ecf;
  --vxe-icon-background-color: #98989e;
  --vxe-table-font-color: #98989e;
  --vxe-table-resizable-color: #95969a;
  --vxe-table-header-background-color: #28282a;
  --vxe-table-body-background-color: #151518;
  --vxe-table-background-color: #4a5663;
  --vxe-table-border-width: 1px;
  --vxe-table-border-color: #37373a;
  --vxe-toolbar-background-color: #37373a;
  --brder-color: #37373a;
}

html.dark .el-tree-node__content {
  --el-color-primary-light-9: #262727;
}

html.dark .el-button--primary {
  --el-button-bg-color: var(--el-color-primary-dark-6);
  --el-button-border-color: var(--el-color-primary-light-2);
}

html.dark .el-switch {
  --el-switch-on-color: var(--el-color-primary-dark-6);
  --el-switch-border-color: var(--el-color-primary-light-2);
}

html.dark .el-tag--primary {
  --el-tag-bg-color: var(--el-color-primary-dark-6);
  --el-tag-border-color: var(--el-color-primary-light-2);
}

:export {
  menuColor: var(--menuColor);
  menuTopColor: var(--topMenuColor);
  menuLightColor: rgba(0, 0, 0, 0.7);
  menuColorActive: var(--menuActiveText);
  menuBackground: var(--menuBg);
  menuTopBackground: var(--topMenuBg);
  menuLightBackground: #ffffff;
  subMenuBackground: var(--subMenuBg);
  subMenuHover: var(--subMenuHover);
  sideBarWidth: 200px;
  logoTitleColor: #313136;
  logoLightTitleColor: #001529;
  primaryColor: #409eff;
  successColor: #67c23a;
  dangerColor: #f56c6c;
  infoColor: #909399;
  warningColor: #e6a23c;
}

package org.dromara.facility.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.facility.domain.JlTowerReal;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 塔机实时数据视图对象 jl_tower_real
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = JlTowerReal.class)
public class JlTowerRealVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String devNo;

    /**
     * 塔机编号
     */
    @ExcelProperty(value = "塔机编号")
    private String tcNo;

    /**
     * 时间
     */
    @ExcelProperty(value = "时间")
    private Date addTime;

    /**
     * 厂家及设备类型
     */
    @ExcelProperty(value = "厂家及设备类型")
    private String lockMachineStatus;

    /**
     * 高度
     */
    @ExcelProperty(value = "高度")
    private Long height;

    /**
     * 幅度
     */
    @ExcelProperty(value = "幅度")
    private Long realRange;

    /**
     * 回转
     */
    @ExcelProperty(value = "回转")
    private Long rotary;

    /**
     * 起始重量
     */
    @ExcelProperty(value = "起始重量")
    private Long startWeight;

    /**
     * 风速数据
     */
    @ExcelProperty(value = "风速数据")
    private Long windSpeed;

    /**
     * 倾角数据
     */
    @ExcelProperty(value = "倾角数据")
    private Long slant;

    /**
     * 重量百分比
     */
    @ExcelProperty(value = "重量百分比")
    private Long weightPct;

    /**
     * 力矩百分比
     */
    @ExcelProperty(value = "力矩百分比")
    private Long mofPct;

    /**
     * 风速百分比
     */
    @ExcelProperty(value = "风速百分比")
    private Long windSpeedPct;

    /**
     * 倾斜百分比
     */
    @ExcelProperty(value = "倾斜百分比")
    private Long slantPct;

    /**
     * 报警原因
     */
    @ExcelProperty(value = "报警原因")
    private String alarmCause;

    /**
     * 报警原因中文
     */
    @ExcelProperty(value = "报警原因中文")
    private String alarmCauseZh;

    /**
     * 制动状态上
     */
    @ExcelProperty(value = "制动状态上")
    private Long brakingStatusUp;

    /**
     * 制动状态下
     */
    @ExcelProperty(value = "制动状态下")
    private Long brakingStatusDown;

    /**
     * 制动状态前
     */
    @ExcelProperty(value = "制动状态前")
    private Long brakingStatusFront;

    /**
     * 制动状态后
     */
    @ExcelProperty(value = "制动状态后")
    private Long brakingStatusBack;

    /**
     * 制动状态左
     */
    @ExcelProperty(value = "制动状态左")
    private Long brakingStatusLeft;

    /**
     * 制动状态右
     */
    @ExcelProperty(value = "制动状态右")
    private Long brakingStatusRight;

    /**
     * 工作状态 1工作 2空闲
     */
    @ExcelProperty(value = "工作状态 1工作 2空闲")
    private Long workingStatus;
}

package org.dromara.projects.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjSafeUser;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjSafeUserVoToPrjSafeUserMapperImpl implements PrjSafeUserVoToPrjSafeUserMapper {

    @Override
    public PrjSafeUser convert(PrjSafeUserVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjSafeUser prjSafeUser = new PrjSafeUser();

        prjSafeUser.setSaleUserId( arg0.getSaleUserId() );
        prjSafeUser.setMobile( arg0.getMobile() );
        prjSafeUser.setUserName( arg0.getUserName() );
        prjSafeUser.setIdCard( arg0.getIdCard() );
        prjSafeUser.setPositionType( arg0.getPositionType() );
        prjSafeUser.setOpenTaskId( arg0.getOpenTaskId() );

        return prjSafeUser;
    }

    @Override
    public PrjSafeUser convert(PrjSafeUserVo arg0, PrjSafeUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSaleUserId( arg0.getSaleUserId() );
        arg1.setMobile( arg0.getMobile() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setPositionType( arg0.getPositionType() );
        arg1.setOpenTaskId( arg0.getOpenTaskId() );

        return arg1;
    }
}

{"doc": " <AUTHOR>\n @date 2025/5/7 10:45\n @Description TODO\n @Version 1.0\n", "fields": [], "enumConstants": [], "methods": [{"name": "getSnap", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 通过rtsp等流地址截取图片\n\n @param url\n @return 流数组\n @throws IOException\n"}, {"name": "free", "paramTypes": ["java.lang.String", "org.bytedeco.ffmpeg.avcodec.AVCodecContext", "org.bytedeco.ffmpeg.avcodec.AVCodecContext", "org.bytedeco.ffmpeg.avformat.AVFormatContext", "org.bytedeco.ffmpeg.avformat.AVFormatContext", "org.bytedeco.ffmpeg.avutil.AVFrame", "org.bytedeco.ffmpeg.avcodec.AVPacket", "org.bytedeco.ffmpeg.avcodec.AVPacket"], "doc": " 释放资源\n\n @param deCodecCtx\n @param enCodecCtx\n @param iFmtCtx\n @param oFmtCtx\n @param frame\n @param srcPacket\n @param packet\n"}], "constructors": []}
package org.dromara.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.constraints.NotNull;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.TableDataInfo;
import org.dromara.system.domain.SysEnterpriseInfo;
import org.dromara.system.domain.vo.EnterpriseNameAndId;

import java.util.List;

public interface ISysEnterpriseInfoService extends IService<SysEnterpriseInfo> {


    /**
     *  分页查询
     */
    List<SysEnterpriseInfo> pageList(SysEnterpriseInfo bo);

    /**
     *  新增 企业信息
     */
    R   ins(SysEnterpriseInfo bo);

    R audit(SysEnterpriseInfo bo);

    SysEnterpriseInfo getOneInfo(@NotNull(message = "主键不能为空") Long enterpriseId);

    R<List<EnterpriseNameAndId>> getSearchData();
}

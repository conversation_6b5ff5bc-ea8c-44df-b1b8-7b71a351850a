<template>
  <div class="register">
    <div class="register-form">
      <el-form :inline="true" ref="registerRef" :model="form" :rules="rules" label-width="180px">
        <el-form-item label="企业名称" prop="enterpriseName">
          <el-input v-model="form.enterpriseName" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="统一社会信用代码" prop="unifiedSocialCreditCode">
          <el-input v-model="form.unifiedSocialCreditCode" placeholder="请输入统一社会信用代码" />
        </el-form-item>
        <el-form-item label="企业类型" prop="enterpriseType">
          <el-select v-model="form.enterpriseType" placeholder="请选择企业类型">
            <el-option v-for="dict in enterprise_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="企业地址" prop="businessAddress">
          <el-input v-model="form.businessAddress" placeholder="请输入企业地址" />
        </el-form-item>
        <el-form-item label="法定代表人" prop="legalRepresentative">
          <el-input v-model="form.legalRepresentative" placeholder="请输入法定代表人" />
        </el-form-item>
        <el-form-item label="法人联系电话" prop="officePhone">
          <el-input v-model="form.officePhone"  placeholder="请输入法人联系电话" />
        </el-form-item>
        <el-form-item label="注册日期" style="width: 100%" prop="registrationDate">
          <el-date-picker
            clearable
            style="width: 240px"
            v-model="form.registrationDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择注册日期"
          >
          </el-date-picker>
        </el-form-item>
        <div style="width: 100%; display: flex">
          <div style="width: 180px; text-align: right; font-size: 14px; color: #606266; font-weight: 600">
            <span style="color: #f56c6c; margin-right: 5px">*</span>选择企业所在地区：
          </div>
          <el-form-item label="" label-width="0px" style="width: 150px" prop="registrationRegionProvince">
            <el-select v-model="form.registrationRegionProvince" placeholder="请选择省份" @change="selectChange($event, 'province')">
              <el-option v-for="(item, index) in provinceOptions" :key="index" :label="item.divisionName" :value="item.divisionCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" label-width="0px" style="width: 150px" prop="registrationRegionCity">
            <el-select v-model="form.registrationRegionCity" placeholder="请选择市" @change="selectChange($event, 'city')">
              <el-option v-for="(item, index) in cityOptions" :key="index" :label="item.divisionName" :value="item.divisionCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" label-width="0px" style="width: 150px" prop="registrationRegionArea">
            <el-select v-model="form.registrationRegionArea" placeholder="请选择区">
              <el-option v-for="(item, index) in areaOptions" :key="index" :label="item.divisionName" :value="item.divisionCode"></el-option>
            </el-select>
          </el-form-item>
        </div>

        <el-form-item label="营业执照" prop="businessLicensePath">
          <image-upload-register v-model="form.businessLicensePath" :limit="1" />
        </el-form-item>
        <el-form-item label="法人代表身份证扫描件" prop="legalIdCardPath">
          <image-upload-register v-model="form.legalIdCardPath" :limit="1" />
        </el-form-item>
        <el-form-item label="资质证书文件" prop="qualificationCertificatePath">
          <image-upload-register v-model="form.qualificationCertificatePath" :limit="1" />
        </el-form-item>
        <el-form-item label="安全生产许可证" prop="safetyProductionLicensePath">
          <image-upload-register v-model="form.safetyProductionLicensePath" :limit="1" />
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <el-input type="text" v-model="form.code" placeholder="验证码" />
          <div style="width: 100px; height: 40px; border: 1px solid #ccc; margin-left: 10px; cursor: pointer">
            <img :src="codeUrl" alt="" style="width: 100%; height: 100%" @click="getCode" />
          </div>
        </el-form-item>
      </el-form>

      <div style="display: flex; justify-content: flex-end; margin-top: 40px; height: 60px; justify-content: center">
        <el-button type="primary" @click="submitForm">注册</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getCodeImg, register, getTenantList } from '@/api/login';
import { RegisterForm, TenantVO } from '@/api/types';
import { to } from 'await-to-js';
import { useI18n } from 'vue-i18n';
import { ExpertVO } from '@/api/expert/expert/types';
import { getProvinceList, getCityList, getAreaList } from '@/api/expert/expert';
import { EnterpriseInfoQuery, EnterpriseInfoForm } from '@/api/system/enterpriseInfo/types';
import { registerCode, companyregister } from '@/api/login';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { enterprise_type } = toRefs<any>(proxy?.useDict('enterprise_type'));
const codeUrl = ref('');
const emit = defineEmits(['closedialog']);

const title = import.meta.env.VITE_APP_TITLE;
const router = useRouter();

const { t } = useI18n();

onMounted(() => {
  getProvince();
  getCode();
});

const initFormData: EnterpriseInfoForm = {
  enterpriseId: undefined,
  enterpriseName: undefined,
  unifiedSocialCreditCode: undefined,
  enterpriseType: undefined,
  businessAddress: undefined,
  legalRepresentative: undefined,
  registrationRegionProvince: undefined,
  registrationRegionCity: undefined,
  registrationRegionArea: undefined,
  registrationDate: undefined,
  officePhone: undefined,
  businessLicensePath: undefined,
  legalIdCardPath: undefined,
  qualificationCertificatePath: undefined,
  safetyProductionLicensePath: undefined,
  userId: undefined,
  deptId: undefined,
  uuid: undefined,
  code: undefined
};
const data = reactive<PageData<EnterpriseInfoForm, EnterpriseInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    enterpriseName: undefined,
    unifiedSocialCreditCode: undefined,
    enterpriseType: undefined,
    businessAddress: undefined,
    legalRepresentative: undefined,
    registrationRegionProvince: undefined,
    registrationRegionCity: undefined,
    registrationRegionArea: undefined,
    registrationDate: undefined,
    officePhone: undefined,
    businessLicensePath: undefined,
    legalIdCardPath: undefined,
    qualificationCertificatePath: undefined,
    safetyProductionLicensePath: undefined,
    userId: undefined,
    deptId: undefined,
    params: {}
  },
  rules: {
    enterpriseName: [{ required: true, message: '企业名称不能为空', trigger: 'blur' }],
    unifiedSocialCreditCode: [{ required: true, message: '统一社会信用代码不能为空', trigger: 'blur' }],
    enterpriseType: [{ required: true, message: '企业类型不能为空', trigger: 'change' }],
    legalRepresentative: [{ required: true, message: '法定代表人不能为空', trigger: 'blur' }],
    registrationRegionProvince: [{ required: true, message: '省不能为空', trigger: 'blur' }],
    registrationRegionCity: [{ required: true, message: '市不能为空', trigger: 'blur' }],
    registrationRegionArea: [{ required: true, message: '区不能为空', trigger: 'blur' }],
    registrationDate: [{ required: true, message: '注册日期不能为空', trigger: 'blur' }],
    businessAddress: [{ required: true, message: '企业地址不能为空', trigger: 'blur' }],

    code: [{ required: true, message: '验证码不能为空', trigger: 'blur' }],
    officePhone: [
      {
        required: true,
        pattern: /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[1589]))\d{8}$/,
        message: '请填写正确的手机号',
        trigger: 'blur'
      }
    ],
    businessLicensePath: [{ required: true, message: '营业执照不能为空', trigger: 'blur' }],
    legalIdCardPath: [{ required: true, message: '法定代表人身份证不能为空', trigger: 'blur' }],
    qualificationCertificatePath: [{ required: true, message: '资质证书不能为空', trigger: 'blur' }],
    safetyProductionLicensePath: [{ required: true, message: '安全生产许可证不能为空', trigger: 'blur' }]
  }
});
const provinceOptions = ref<ExpertVO[]>([]); // 省份选项
const cityOptions = ref<ExpertVO[]>([]); // 城市选项
const areaOptions = ref<ExpertVO[]>([]); // 区选项

const { form, rules } = toRefs(data);

// 获取省份列表信息
const getProvince = async () => {
  const res = await getProvinceList();
  if (res.code === 200) {
    provinceOptions.value = res.data;
  }
};
// 获取市列表信息
const getCity = async (divisionCode: string) => {
  const res = await getCityList(divisionCode);
  if (res.code === 200) {
    // 处理返回的数据，例如更新城市选项等
    cityOptions.value = res.data;
  }
};
// 获取区列表信息
const getArea = async (divisionCode: string) => {
  const res = await getAreaList(divisionCode);
  if (res.code === 200) {
    // 处理返回的数据，例如更新区选项等
    areaOptions.value = res.data;
  }
};
// 选择省份
const selectChange = (divisionCode: string, flag: string) => {
  if (flag === 'province') {
    getCity(divisionCode);
  } else if (flag === 'city') {
    getArea(divisionCode);
  }
};

const registerRef = ref<ElFormInstance>();
const submitForm = () => {
  registerRef.value?.validate(async (valid: boolean, fields: any) => {
    if (valid) {
      // 确认注册
      companyregister(form.value).then((res) => {
        if (res.code === 200) {
          proxy?.$modal.msgSuccess('注册成功');
          // 重置注册表单
          registerRef.value?.resetFields();
          emit('closedialog');
        } else {
          // 提示错误信息
          proxy?.$modal.msgError(res.msg);
        }
      });
    }
  });
};

// 取消注册
const resetForm = () => {
  emit('closedialog');
  registerRef.value?.resetFields();
  form.value = { ...initFormData };
};

/**
 * 获取验证码
 */
const getCode = async () => {
  const res = await registerCode();
  const { data } = res;
  codeUrl.value = 'data:image/gif;base64,' + data.img;
  form.value.uuid = data.uuid;
};
</script>

<style lang="scss" scoped>
.register {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url('../assets/images/login-background.jpg');
  background-size: cover;

  .register-form {
    background: #fff;
    height: 70vh;
    width: 100%;
    border-radius: 10px;
  }
}

.title-box {
  display: flex;

  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #707070;
  }

  :deep(.lang-select--style) {
    line-height: 0;
    color: #7483a3;
  }
}

.register-form {
  border-radius: 6px;
  background: #ffffff;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 40px;

    input {
      height: 40px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0;
  }
}

.register-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.register-code {
  width: 33%;
  height: 40px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.register-code-img {
  height: 40px;
  padding-left: 12px;
}
</style>

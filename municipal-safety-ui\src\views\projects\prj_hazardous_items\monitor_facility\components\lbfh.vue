<template>
  <div class="app-container">

    <el-table v-loading="loading" :data="edgeGuardList">
      <el-table-column label="编号" align="center" prop="dumpnumber" />
      <el-table-column label="开关状态" align="center" prop="checkSensor" />
      <el-table-column label="经度" align="center" prop="longitude" />
      <el-table-column label="纬度" align="center" prop="latitude" />
      <el-table-column label="电池电压" align="center" prop="batvolt">
        <template v-slot="scope">
          {{ scope.row.batvolt }}v
        </template>
      </el-table-column>
      <el-table-column label="电池电量" align="center" prop="batPercent">
        <template v-slot="scope">
          {{ scope.row.batPercent }}%
        </template>
      </el-table-column>
      <el-table-column label="报警信息" align="center" prop="alarmInfo">
        <template v-slot="scope">
          <span v-if="scope.row.alarmInfo == '0'">未触发</span>
          <span v-if="scope.row.alarmInfo == '1'">触发</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

  </div>
</template>

<script>
import { listEdgeGuard } from "@/api/projects/facility/index";

export default {
  name: "EdgeGuard",
  props: {
    devNo: {
      type: String,
      default: ""
    }
  },
  data () {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 绿能临边防护表格数据
      edgeGuardList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        devNo: null
      },
    };
  },
  created () {
    this.getList();
  },
  methods: {
    /** 查询绿能临边防护列表 */
    async getList () {
      this.edgeGuardList = []
      this.loading = true;
      this.queryParams.devNo = this.devNo
      const res = await listEdgeGuard(this.queryParams)
      this.edgeGuardList = res.rows;
      this.total = res.total;
      this.loading = false;
    },
  }
};
</script>

package org.dromara.person.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.person.domain.QualificationDict;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class QualificationDictBoToQualificationDictMapperImpl implements QualificationDictBoToQualificationDictMapper {

    @Override
    public QualificationDict convert(QualificationDictBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QualificationDict qualificationDict = new QualificationDict();

        qualificationDict.setSearchValue( arg0.getSearchValue() );
        qualificationDict.setCreateDept( arg0.getCreateDept() );
        qualificationDict.setCreateBy( arg0.getCreateBy() );
        qualificationDict.setCreateTime( arg0.getCreateTime() );
        qualificationDict.setUpdateBy( arg0.getUpdateBy() );
        qualificationDict.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            qualificationDict.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        qualificationDict.setId( arg0.getId() );
        qualificationDict.setName( arg0.getName() );
        qualificationDict.setPreId( arg0.getPreId() );

        return qualificationDict;
    }

    @Override
    public QualificationDict convert(QualificationDictBo arg0, QualificationDict arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setPreId( arg0.getPreId() );

        return arg1;
    }
}

export interface EnterpriseInfoVO {
  /**
   * 企业ID
   */
  enterpriseId: string | number;

  /**
   * 企业名称
   */
  enterpriseName: string;

  /**
   * 统一社会信用代码
   */
  unifiedSocialCreditCode: string;

  /**
   * 企业类型（下拉选择）
   */
  enterpriseType: string;

  /**
   * 企业地址
   */
  businessAddress: string;

  /**
   * 法定代表人
   */
  legalRepresentative: string;

  /**
   * 省
   */
  registrationRegionProvince: string;

  /**
   * 市
   */
  registrationRegionCity: string;

  /**
   * 区
   */
  registrationRegionArea: string;

  /**
   * 注册日期
   */
  registrationDate: string;

  /**
   * 办公电话
   */
  officePhone: string;

  /**
   * 营业执照路径
   */
  businessLicensePath: number;

  /**
   * 法人代表身份证扫描件路径
   */
  legalIdCardPath: string | number;

  /**
   * 资质证书文件路径
   */
  qualificationCertificatePath: number;

  /**
   * 安全生产许可证路径
   */
  safetyProductionLicensePath: number;

  /**
   * 用户ID 
   */
  userId: string | number;

  /**
   * 部门ID
   */
  deptId: string | number;

}

export interface EnterpriseInfoForm extends BaseEntity {
  /**
   * 企业ID
   */
  enterpriseId?: string | number;

  /**
   * 企业名称
   */
  enterpriseName?: string;

  /**
   * 统一社会信用代码
   */
  unifiedSocialCreditCode?: string;

  /**
   * 企业类型（下拉选择）
   */
  enterpriseType?: string | string[];

  /**
   * 企业地址
   */
  businessAddress?: string;

  /**
   * 法定代表人
   */
  legalRepresentative?: string;

  /**
   * 省
   */
  registrationRegionProvince?: string;

  /**
   * 市
   */
  registrationRegionCity?: string;

  /**
   * 区
   */
  registrationRegionArea?: string;

  /**
   * 注册日期
   */
  registrationDate?: string;

  /**
   * 办公电话
   */
  officePhone?: string;

  /**
   * 营业执照路径
   */
  businessLicensePath?: number;

  /**
   * 法人代表身份证扫描件路径
   */
  legalIdCardPath?: string | number;

  /**
   * 资质证书文件路径
   */
  qualificationCertificatePath?: number;

  /**
   * 安全生产许可证路径
   */
  safetyProductionLicensePath?: number;

  /**
   * 用户ID 
   */
  userId?: string | number;

  /**
   * 部门ID
   */
  deptId?: string | number;
  // 注册验证码id
  uuid?: string;
  // 注册验证码
  code?: string;
}

export interface EnterpriseInfoQuery extends PageQuery {

  /**
   * 企业名称
   */
  enterpriseName?: string;

  /**
   * 统一社会信用代码
   */
  unifiedSocialCreditCode?: string;

  /**
   * 企业类型（下拉选择）
   */
  enterpriseType?: string;

  /**
   * 企业地址
   */
  businessAddress?: string;

  /**
   * 法定代表人
   */
  legalRepresentative?: string;

  /**
   * 省
   */
  registrationRegionProvince?: string;

  /**
   * 市
   */
  registrationRegionCity?: string;

  /**
   * 区
   */
  registrationRegionArea?: string;

  /**
   * 注册日期
   */
  registrationDate?: string;

  /**
   * 办公电话
   */
  officePhone?: string;

  /**
   * 营业执照路径
   */
  businessLicensePath?: number;

  /**
   * 法人代表身份证扫描件路径
   */
  legalIdCardPath?: string | number;

  /**
   * 资质证书文件路径
   */
  qualificationCertificatePath?: number;

  /**
   * 安全生产许可证路径
   */
  safetyProductionLicensePath?: number;

  /**
   * 用户ID 
   */
  userId?: string | number;

  /**
   * 部门ID
   */
  deptId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
    // 企业信息状态
    enterpriseStatus?:string | number;
}




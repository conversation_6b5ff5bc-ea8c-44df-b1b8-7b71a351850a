<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="父级id" prop="preId">
              <el-input v-model="queryParams.preId" placeholder="请输入父级id" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd()" v-hasPermi="['person:qualificationDict:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="handleToggleExpandAll">展开/折叠</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>
      <el-table
        ref="qualificationDictTableRef"
        v-loading="loading"
        :data="qualificationDictList"
        row-key="id"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column label="名称" prop="name" />
        <el-table-column label="父级id" align="center" prop="preId" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['person:qualificationDict:edit']" />
            </el-tooltip>
            <el-tooltip content="新增" placement="top">
              <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['person:qualificationDict:add']" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['person:qualificationDict:remove']" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 添加或修改人员证书属性类型对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="qualificationDictFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="父级id" prop="preId">
          <el-tree-select
            v-model="form.preId"
            :data="qualificationDictOptions"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="id"
            placeholder="请选择父级id"
            check-strictly
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="QualificationDict" lang="ts">
import { listQualificationDict, getQualificationDict, delQualificationDict, addQualificationDict, updateQualificationDict } from "@/api/person/qualificationDict/api";
import { QualificationDictVO, QualificationDictQuery, QualificationDictForm } from '@/api/person/qualificationDict/types';

type QualificationDictOption = {
  id: number;
  name: string;
  children?: QualificationDictOption[];
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance;;


const qualificationDictList = ref<QualificationDictVO[]>([]);
const qualificationDictOptions = ref<QualificationDictOption[]>([]);
const buttonLoading = ref(false);
const showSearch = ref(true);
const isExpandAll = ref(true);
const loading = ref(false);

const queryFormRef = ref<ElFormInstance>();
const qualificationDictFormRef = ref<ElFormInstance>();
const qualificationDictTableRef = ref<ElTableInstance>()

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});


const initFormData: QualificationDictForm = {
  id: undefined,
  name: undefined,
  preId: undefined,
}

const data = reactive<PageData<QualificationDictForm, QualificationDictQuery>>({
  form: {...initFormData},
  queryParams: {
    name: undefined,
    preId: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "主键不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询人员证书属性类型列表 */
const getList = async () => {
  loading.value = true;
  const res = await listQualificationDict(queryParams.value);
  const data = proxy?.handleTree<QualificationDictVO>(res.data, "id", "preId");
  if (data) {
    qualificationDictList.value = data;
    loading.value = false;
  }
}

/** 查询人员证书属性类型下拉树结构 */
const getTreeselect = async () => {
  const res = await listQualificationDict();
  qualificationDictOptions.value = [];
  const data: QualificationDictOption = { id: 0, name: '顶级节点', children: [] };
  data.children = proxy?.handleTree<QualificationDictOption>(res.data, "id", "preId");
  qualificationDictOptions.value.push(data);
}

// 取消按钮
const cancel = () => {
  reset();
  dialog.visible = false;
}

// 表单重置
const reset = () => {
  form.value = {...initFormData}
  qualificationDictFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 新增按钮操作 */
const handleAdd = (row?: QualificationDictVO) => {
  reset();
  getTreeselect();
  if (row != null && row.id) {
    form.value.preId = row.id;
  } else {
    form.value.preId = 0;
  }
  dialog.visible = true;
  dialog.title = "添加人员证书属性类型";
}

/** 展开/折叠操作 */
const handleToggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value;
  toggleExpandAll(qualificationDictList.value, isExpandAll.value)
}

/** 展开/折叠操作 */
const toggleExpandAll = (data: QualificationDictVO[], status: boolean) => {
  data.forEach((item) => {
    qualificationDictTableRef.value?.toggleRowExpansion(item, status)
    if (item.children && item.children.length > 0) toggleExpandAll(item.children, status)
  })
}

/** 修改按钮操作 */
const handleUpdate = async (row: QualificationDictVO) => {
  reset();
  await getTreeselect();
  if (row != null) {
    form.value.preId = row.preId;
  }
  const res = await getQualificationDict(row.id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改人员证书属性类型";
}

/** 提交按钮 */
const submitForm = () => {
  qualificationDictFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateQualificationDict(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addQualificationDict(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row: QualificationDictVO) => {
  await proxy?.$modal.confirm('是否确认删除人员证书属性类型编号为"' + row.id + '"的数据项？');
  loading.value = true;
  await delQualificationDict(row.id).finally(() => loading.value = false);
  await getList();
  proxy?.$modal.msgSuccess("删除成功");
}

onMounted(() => {
  getList();
});
</script>

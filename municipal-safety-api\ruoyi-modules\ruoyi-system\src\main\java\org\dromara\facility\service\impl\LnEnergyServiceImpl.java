package org.dromara.facility.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.facility.domain.LnEnergy;
import org.dromara.facility.domain.bo.LnEnergyBo;
import org.dromara.facility.domain.vo.LnEnergyVo;
import org.dromara.facility.mapper.LnEnergyMapper;
import org.dromara.facility.service.ILnEnergyService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 绿能用电监测Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@RequiredArgsConstructor
@Service
public class LnEnergyServiceImpl implements ILnEnergyService {

    private final LnEnergyMapper baseMapper;

    /**
     * 查询绿能用电监测
     *
     * @param id 主键
     * @return 绿能用电监测
     */
    @Override
    public LnEnergyVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询绿能用电监测列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能用电监测分页列表
     */
    @Override
    public TableDataInfo<LnEnergyVo> queryPageList(LnEnergyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LnEnergy> lqw = buildQueryWrapper(bo);
        Page<LnEnergyVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的绿能用电监测列表
     *
     * @param bo 查询条件
     * @return 绿能用电监测列表
     */
    @Override
    public List<LnEnergyVo> queryList(LnEnergyBo bo) {
        LambdaQueryWrapper<LnEnergy> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LnEnergy> buildQueryWrapper(LnEnergyBo bo) {
        LambdaQueryWrapper<LnEnergy> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(LnEnergy::getCreateTime);
        lqw.eq(LnEnergy::getDevNo, bo.getDevNo());
        return lqw;
    }

    /**
     * 新增绿能用电监测
     *
     * @param bo 绿能用电监测
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LnEnergyBo bo) {
        LnEnergy add = MapstructUtils.convert(bo, LnEnergy.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public void insertByJson(String jsonString) {
        LnEnergy add = MapstructUtils.convert(JSON.parseObject(jsonString, LnEnergyBo.class), LnEnergy.class);
        assert add != null;
        add.setCreateTime(new Date());
        baseMapper.insert(add);
    }

    /**
     * 修改绿能用电监测
     *
     * @param bo 绿能用电监测
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LnEnergyBo bo) {
        LnEnergy update = MapstructUtils.convert(bo, LnEnergy.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LnEnergy entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除绿能用电监测信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


}

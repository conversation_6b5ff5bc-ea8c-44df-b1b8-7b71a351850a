package org.dromara.facility.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.LnEnergy;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnEnergyVoToLnEnergyMapperImpl implements LnEnergyVoToLnEnergyMapper {

    @Override
    public LnEnergy convert(LnEnergyVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnEnergy lnEnergy = new LnEnergy();

        lnEnergy.setId( arg0.getId() );
        lnEnergy.setUa( arg0.getUa() );
        lnEnergy.setUb( arg0.getUb() );
        lnEnergy.setUc( arg0.getUc() );
        lnEnergy.setIa( arg0.getIa() );
        lnEnergy.setIb( arg0.getIb() );
        lnEnergy.setIc( arg0.getIc() );
        lnEnergy.setIl( arg0.getIl() );
        lnEnergy.setTa( arg0.getTa() );
        lnEnergy.setTb( arg0.getTb() );
        lnEnergy.setTc( arg0.getTc() );
        lnEnergy.setTn( arg0.getTn() );
        lnEnergy.setRecordTime( arg0.getRecordTime() );
        lnEnergy.setDeviceNo( arg0.getDeviceNo() );
        lnEnergy.setDevNo( arg0.getDevNo() );
        lnEnergy.setCreateTime( arg0.getCreateTime() );

        return lnEnergy;
    }

    @Override
    public LnEnergy convert(LnEnergyVo arg0, LnEnergy arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUa( arg0.getUa() );
        arg1.setUb( arg0.getUb() );
        arg1.setUc( arg0.getUc() );
        arg1.setIa( arg0.getIa() );
        arg1.setIb( arg0.getIb() );
        arg1.setIc( arg0.getIc() );
        arg1.setIl( arg0.getIl() );
        arg1.setTa( arg0.getTa() );
        arg1.setTb( arg0.getTb() );
        arg1.setTc( arg0.getTc() );
        arg1.setTn( arg0.getTn() );
        arg1.setRecordTime( arg0.getRecordTime() );
        arg1.setDeviceNo( arg0.getDeviceNo() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}

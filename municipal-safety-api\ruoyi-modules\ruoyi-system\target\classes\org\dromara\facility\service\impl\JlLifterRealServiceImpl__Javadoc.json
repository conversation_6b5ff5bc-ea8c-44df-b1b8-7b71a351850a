{"doc": " 升降机实时数据Service业务层处理\n\n <AUTHOR>\n @date 2025-07-24\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询升降机实时数据\n\n @param id 主键\n @return 升降机实时数据\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.facility.domain.bo.JlLifterRealBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询升降机实时数据列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 升降机实时数据分页列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.facility.domain.bo.JlLifterRealBo"], "doc": " 新增升降机实时数据\n\n @param bo 升降机实时数据\n @return 是否新增成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.facility.domain.JlLifterReal"], "doc": " 保存前的数据校验\n"}], "constructors": []}
package org.dromara.attendance.domain.so;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 访客系统人员名单对象 base_visitor
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public class BaseVisitor {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    private String snIds;

    /**
     * 人员ID
     */
    private String code;

    /**
     * 人员姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 注册人脸
     */
    private String registerFace;

    /**
     * 人员角色固定传1。 0->普通人员。1->白名单人员。2->黑名单人员。-1->所有人员。
     */
    private Integer role;

    /**
     * 门禁卡号生成方式。0-共有卡号，1-自动生成，2-手动输入
     */
    private Integer accessCard;

    /**
     * 门禁卡号
     */
    private String accessCardNumber;

    /**
     * 名单类型。0-永久名单，1-临时名单
     */
    private Integer rollType;

    /**
     * 是否下发。0-是，1-否
     */
    private Integer isUpload;

    /**
     * 调度级别
     */
    private Integer scheduling;

    /**
     * 起始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 是否过期
     */
    private Integer isExpired;

    /**
     * 自定义内容
     */
    private String content;

    /**
     * 是否删除。0-否，1-是
     */
    private Integer deleted;

    private String snList;

    /**
     * 部门id
     */
    private Long deptId;

    public String getSnIds() {
        return snIds;
    }

    public void setSnIds(String snIds) {
        this.snIds = snIds;
    }

    public String getSnList() {
        return snList;
    }

    public void setSnList(String snList) {
        this.snList = snList;
    }

    public Integer getIsExpired() {
        return isExpired;
    }

    public void setIsExpired(Integer isExpired) {
        this.isExpired = isExpired;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setRegisterFace(String registerFace) {
        this.registerFace = registerFace;
    }

    public String getRegisterFace() {
        return registerFace;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    public Integer getRole() {
        return role;
    }

    public void setAccessCard(Integer accessCard) {
        this.accessCard = accessCard;
    }

    public Integer getAccessCard() {
        return accessCard;
    }

    public void setAccessCardNumber(String accessCardNumber) {
        this.accessCardNumber = accessCardNumber;
    }

    public String getAccessCardNumber() {
        return accessCardNumber;
    }

    public void setRollType(Integer rollType) {
        this.rollType = rollType;
    }

    public Integer getRollType() {
        return rollType;
    }

    public void setIsUpload(Integer isUpload) {
        this.isUpload = isUpload;
    }

    public Integer getIsUpload() {
        return isUpload;
    }

    public void setScheduling(Integer scheduling) {
        this.scheduling = scheduling;
    }

    public Integer getScheduling() {
        return scheduling;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("code", getCode())
                .append("name", getName())
                .append("mobile", getMobile())
                .append("registerFace", getRegisterFace())
                .append("role", getRole())
                .append("accessCard", getAccessCard())
                .append("accessCardNumber", getAccessCardNumber())
                .append("rollType", getRollType())
                .append("isUpload", getIsUpload())
                .append("scheduling", getScheduling())
                .append("startTime", getStartTime())
                .append("endTime", getEndTime())
                .append("content", getContent())
                .append("deleted", getDeleted())
                .toString();
    }
}

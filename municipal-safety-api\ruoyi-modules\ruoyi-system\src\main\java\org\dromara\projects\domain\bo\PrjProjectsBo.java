package org.dromara.projects.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.projects.domain.PrjProjects;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 项目录入业务对象 prj_projects
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjProjects.class, reverseConvertGenerate = false)
public class PrjProjectsBo extends BaseEntity {

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空", groups = { EditGroup.class })
    private Long projectId;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectName;

    /**
     * 项目编码/标识
     */
    private String projectCode;

    /**
     * 工程概况 (对应附件一.1)
     */
    private String projectOverview;

    /**
     * 施工许可证编号 (对应附件一.2)
     */
    private String constructionPermitNo;

    /**
     * 施工许可证扫描件文档ID (逻辑外键至 sys_documents.document_id)
     */
    private Long constructionPermitDocId;

    /**
     * 项目位置（经纬度）
     */
    private String lola;

    /**
     * 省/直辖市编码
     */
    private String provinceCode;

    /**
     * 省/直辖市名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区/县编码
     */
    private String districtCode;

    /**
     * 区/县名称
     */
    private String districtName;

    /**
     * 乡镇/街道编码 (可选)
     */
    private String countyCode;

    /**
     * 乡镇/街道名称 (可选)
     */
    private String countyName;

    /**
     * 详细地址
     */
    private String locationDetail;

    /**
     * 占地面积(平方米)
     */
    private BigDecimal siteArea;

    /**
     * 预算投资总额(万元)
     */
    private BigDecimal budgetTotal;

    /**
     * 项目所属质监站机构ID
     */
    private Long supervisingQsOrgId;

    /**
     * 项目状态
     */
    private String status;

    /**
     * 计划开工日期
     */
    private Date startDate;

    /**
     * 计划竣工日期
     */
    private Date plannedEndDate;

    /**
     * 实际开工日期
     */
    private Date actualStartDate;

    /**
     * 实际竣工日期
     */
    private Date actualEndDate;

    /**
     * 建设单位ID (逻辑外键至 sys_dept.dept_id)
     */
    private Long clientOrgId;

    /**
     * 施工总包单位ID (逻辑外键至 sys_dept.dept_id)
     */
    private Long constructionOrgId;

    /**
     * 监理单位ID (逻辑外键至 sys_dept.dept_id)
     */
    private Long supervisionOrgId;

    /**
     * 设计单位ID (逻辑外键至 sys_dept.dept_id)
     */
    private Long designOrgId;

    /**
     * 勘察单位ID (逻辑外键至 sys_dept.dept_id)
     */
    private Long surveyOrgId;

    /**
     * 安拆单位ID (逻辑外键至 sys_dept.dept_id)
     */
    private Long installationDismantlingOrgId;

    /**
     * 维保单位ID (逻辑外键至 sys_dept.dept_id)
     */
    private Long maintenanceOrgId;

    /**
     * 专业分包单位ID列表 (JSON数组，元素为 sys_organizations.org_id)
     */
    private String subcontractorOrgIds;

    /**
     * 施工单位项目负责人ID (逻辑外键至 sys_users.user_id)
     */
    private Long projectManagerUserId;

    /**
     * 监理单位总监ID (逻辑外键至 sys_users.user_id)
     */
    private Long supervisionChiefEngUserId;

    /**
     * 危大工程安全防护文明施工措施费财务凭证文档ID (对应附件三.1)
     */
    private Long safetyMeasuresFeeDocId;

    /**
     * 勾选的项目人员列表，逗号拼接
     */
    private String personIds;

    /**
     *  type  移动端进入 分类
     */
    private String type;

    /**
     *  projectIdList
     */
    private List<String> projectIdList;

    /**
     *  type  判断专家是否存在项目
     */
    private String projectType;

    /**
     * 参建单位名称（模糊）
     */
    private String participateOrgName;


}

package org.dromara.system.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * dangerList对象 t_danger_list
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_danger_list")
public class DangerList extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "danger_id")
    private Long dangerId;

    /**
     * 名称
     */
    private String name;

    /**
     * 父级id
     */
    private Long preId;

    /**
     * 危大类型
     */
    private Integer type;

    /**
     * 备注
     */
    private String remark;

}

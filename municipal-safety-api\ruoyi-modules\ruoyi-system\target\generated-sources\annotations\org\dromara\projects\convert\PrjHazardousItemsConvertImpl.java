package org.dromara.projects.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjHazardousItems;
import org.dromara.projects.domain.vo.PrjHazardousItemsVo;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-11T14:50:33+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
public class PrjHazardousItemsConvertImpl implements PrjHazardousItemsConvert {

    @Override
    public Page<PrjHazardousItemsVo> page2voPage(Page<PrjHazardousItems> itemsPage) {
        if ( itemsPage == null ) {
            return null;
        }

        Page<PrjHazardousItemsVo> page = new Page<PrjHazardousItemsVo>();

        page.setPages( itemsPage.getPages() );
        page.setRecords( listItems2voList( itemsPage.getRecords() ) );
        page.setTotal( itemsPage.getTotal() );
        page.setSize( itemsPage.getSize() );
        page.setCurrent( itemsPage.getCurrent() );

        return page;
    }

    @Override
    public PrjHazardousItemsVo items2vo(PrjHazardousItems items) {
        if ( items == null ) {
            return null;
        }

        PrjHazardousItemsVo prjHazardousItemsVo = new PrjHazardousItemsVo();

        prjHazardousItemsVo.setItemId( items.getItemId() );
        prjHazardousItemsVo.setProjectId( items.getProjectId() );
        prjHazardousItemsVo.setDangerId( items.getDangerId() );
        prjHazardousItemsVo.setItemName( items.getItemName() );
        prjHazardousItemsVo.setScopeDetails( items.getScopeDetails() );
        prjHazardousItemsVo.setDangerListType( items.getDangerListType() );
        prjHazardousItemsVo.setStartDate( items.getStartDate() );
        prjHazardousItemsVo.setPlannedEndDate( items.getPlannedEndDate() );
        prjHazardousItemsVo.setActualEndDate( items.getActualEndDate() );
        prjHazardousItemsVo.setActualStartDate( items.getActualStartDate() );
        prjHazardousItemsVo.setStatus( items.getStatus() );

        return prjHazardousItemsVo;
    }

    @Override
    public List<PrjHazardousItemsVo> listItems2voList(List<PrjHazardousItems> itemsList) {
        if ( itemsList == null ) {
            return null;
        }

        List<PrjHazardousItemsVo> list = new ArrayList<PrjHazardousItemsVo>( itemsList.size() );
        for ( PrjHazardousItems prjHazardousItems : itemsList ) {
            list.add( items2vo( prjHazardousItems ) );
        }

        return list;
    }
}

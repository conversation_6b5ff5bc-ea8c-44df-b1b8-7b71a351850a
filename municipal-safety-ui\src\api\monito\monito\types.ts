export interface MonitoVO {
  /**
   * 监控ID
   */
  monitoId: string | number;
  channelNo: string | number;
  /**
   * 项目ID
   */
  projectId: string | number;

  /**
   * 项目工程ID
   */
  itemId: string | number;

  itemName: string;
  /**
   * 设备名称
   */
  deviceName: string;

  /**
   * 设备类型(001:萤石云)
   */
  deviceType: string;

  /**
   * 设备编码
   */
  deviceCode: string;

  /**
   * 设备在线状态(在线、不在线)
   */
  deviceStatus: string;

  /**
   * 是否启用抓拍
   */
  enableSnapshot: string;

  /**
   * 抓拍时间间隔(秒)
   */
  snapshotTime: number;

  projectName: string;
  /**
   * 备注
   */
  remarks: string;

}

export interface MonitoForm extends BaseEntity {
  /**
   * 监控ID
   */
  monitoId?: string | number;
  channelNo: number;
  /**
   * 项目ID
   */
  projectId?: string | number;
  /**
   * 项目工程名称
   */
  projectName?: string;
  itemName?: string;
  /**
   * 项目工程ID
   */
  itemId?: string | number;

  /**
   * 设备名称
   */
  deviceName?: string;

  /**
   * 设备类型(001:萤石云)
   */
  deviceType?: string;

  /**
   * 设备编码
   */
  deviceCode?: string;

  /**
   * 设备在线状态(在线、不在线)
   */
  deviceStatus?: string;

  /**
   * 是否启用抓拍
   */
  enableSnapshot?: string;

  /**
   * 抓拍时间间隔(秒)
   */
  snapshotTime?: number;

  /**
   * 备注
   */
  remarks?: string;

}

export interface MonitoQuery extends PageQuery {

  /**
   * 项目ID
   */
  projectId?: string | number;
  /**
    * 项目工程名称
    */
  projectName?: string;
  itemName?: string;
  channelNo: number,
  /**
   * 项目工程ID
   */
  itemId?: string | number;

  /**
   * 设备名称
   */
  deviceName?: string;

  /**
   * 设备类型(001:萤石云)
   */
  deviceType?: string;

  /**
   * 设备编码
   */
  deviceCode?: string;

  /**
   * 设备在线状态(在线、不在线)
   */
  deviceStatus?: string;

  /**
   * 是否启用抓拍
   */
  enableSnapshot?: string;

  /**
   * 抓拍时间间隔(秒)
   */
  snapshotTime?: number;

  /**
   * 备注
   */
  remarks?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}




<template>
  <div class="p-2">
    <el-card shadow="never">
      <div style="display: flex; justify-content: space-between">
        <div>
          <el-button v-if="submitButtonShow" :loading="buttonLoading" type="info"
            @click="submitForm('draft')">暂存</el-button>
          <el-button v-if="submitButtonShow" :loading="buttonLoading" type="primary" @click="submitForm('submit')">提
            交</el-button>
          <el-button v-if="approvalButtonShow" :loading="buttonLoading" type="primary"
            @click="approvalVerifyOpen">审批</el-button>
          <el-button v-if="form && form.id" type="primary" @click="handleApprovalRecord">流程进度</el-button>
        </div>
        <div>
          <el-button style="float: right" @click="goBack()">返回</el-button>
        </div>
      </div>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px;">
      <el-table v-loading="loading" :data="tableData" :span-method="objectSpanMethod">
        <el-table-column prop="name" label="名称" align="center" />
        <el-table-column prop="time" label="整改时限" align="center">
          <template #default="scope">
            <el-countdown format="D [天] HH:mm:ss" :value="dayjs(scope.row.time)" />
            <span>{{ dayjs(scope.row.time).format('YYYY-MM-DD HH:mm:ss') }}</span><br />
          </template>
        </el-table-column>
        <el-table-column label="监督站文件" align="center">
          <template #default="scope">
            <el-button size="small" type="primary" @click="handleFileView(scope.row.manualFile.url)">预览</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <div v-if="scope.row.flag == 'corrections'"> <!-- 限期整改 -->
              <FileUpload v-if="approvalButtonShow" @update:modelValue="handleDeadlinePassback($event, scope.$index)"
                :limit="1" :modelValue="formCorrectData.correctionsBackFile" :isShowTip="false" :fileSize="20">
                <el-button size="small" type="primary">上传报告</el-button>
              </FileUpload>
              <el-button v-else size="small" type="primary"
                @click="handleFileView(scope.row.manualBackFile.url)">预览</el-button>
              <!-- <el-link v-else type="primary" href="javascript:;" style="color: #409EFF;"
                @click="handleFileView(scope.row.manualBackFile.url)">{{
                scope.row.manualBackFile.name
                }}</el-link> -->
            </div>
            <div v-if="scope.row.flag == 'suspension'"> <!-- 停工整改 -->
              <FileUpload v-if="approvalButtonShow" @update:modelValue="handleShutdownPassback($event, scope.$index)"
                :limit="1" :modelValue="formCorrectData.suspensionBackFile" :isShowTip="false" :fileSize="20">
                <el-button size="small" type="primary">上传报告</el-button>
              </FileUpload>
              <el-button v-else size="small" type="primary"
                @click="handleFileView(scope.row.manualBackFile.url)">预览</el-button>
              <!-- <el-link v-else type="primary" href="javascript:;" style="color: #409EFF;"
                @click="handleFileView(scope.row.manualBackFile.url)">{{
                scope.row.manualBackFile.name
                }}</el-link> -->
            </div>
            <div v-if="scope.row.flag == 'penalty'"> <!-- 行政处罚 -->
              <FileUpload v-if="approvalButtonShow" @update:modelValue="handlePunishPassback($event, scope.$index)"
                :limit="1" :modelValue="formCorrectData.penaltyBackFile" :isShowTip="false" :fileSize="20">
                <el-button size="small" type="primary">上传报告</el-button>
              </FileUpload>
              <el-button v-else size="small" type="primary"
                @click="handleFileView(scope.row.manualBackFile.url)">预览</el-button>
              <!-- <el-link v-else type="primary" href="javascript:;" style="color: #409EFF;"
                @click="handleFileView(scope.row.manualBackFile.url)">{{
                scope.row.manualBackFile.name
                }}</el-link> -->
            </div>
            <div v-if="scope.row.flag == 'elseFile'"> <!-- 其他 -->
              <FileUpload v-if="approvalButtonShow"
                @update:modelValue="handleOtherPassback($event, scope.$index, scope.row.itemFileId)"
                :modelValue="scope.row.manualBackFile" :isShowTip="false" :fileSize="20">
                <el-button size="small" type="primary">上传报告</el-button>
              </FileUpload>
              <div class="elseFileBtn" v-else v-for="(item, index) in scope.row.manualBackFile" :key="index">
                <el-button size="small" type="primary" @click="handleFileView(item.url)">预览</el-button><br />
                <!-- <el-link type="primary" href="javascript:;" style="color: #409EFF;" @click="handleFileView(item.url)">{{
                  item.name
                  }}</el-link><br /> -->
              </div>
            </div>
            <div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 提交组件 -->
    <submitVerify ref="submitVerifyRef" :task-variables="taskVariables" :isReadyFile="false" :isSelectBtnDisabled="true"
      @submit-callback="submitCallback" @beforeSubmit="beforeSubmit" />
    <!-- 审批记录 -->
    <approvalRecord ref="approvalRecordRef" />
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" :before-close="handleClose" width="500">
      <el-select v-model="flowCode" placeholder="Select" style="width: 240px">
        <el-option v-for="item in flowCodeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="submitFlow()"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Leave" lang="ts">
import { addLeave, updateLeave, saveSupervisionReport, saveSupervisionReportOther } from '@/api/workflow/leave';
import { listByIds } from '@/api/system/oss';
import { getManualDetail } from '@/api/customFlow/api'
import { LeaveForm, LeaveQuery, LeaveVO } from '@/api/workflow/leave/types';
import { startWorkFlow, getTaskVariables } from '@/api/workflow/task';
import SubmitVerify from '@/components/Process/submitVerify.vue';
import ApprovalRecord from '@/components/Process/approvalRecord.vue';
import { AxiosResponse } from 'axios';
import { StartProcessBo } from '@/api/workflow/workflowCommon/types';
import FileUpload from '@/components/FileUpload/index.vue'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
import dayjs from 'dayjs'
const { hidden_danger_type } = toRefs<any>(proxy?.useDict('hidden_danger_type'));

const buttonLoading = ref(false);
const loading = ref(true);
const leaveTime = ref<Array<string>>([]);
// 上传报告的表单参数
const formCorrectData = reactive({
  id: '', //唯一id
  penaltyBackFile: '',   //行政处罚报告
  suspensionBackFile: '', //停工整改报告
  correctionsBackFile: ''  //限期整改报告
})
// 其他部分提交参数
const otherFileData = reactive([])
// 判断是否上传了报告
const isUploaded = ref([]);
// 存放上传报告的名字
const isFileNames = ref([]);
// 存放限期整改、停工整改、行政处罚的数组的长度
const correctLength = ref(0);
//路由参数
const routeParams = ref<Record<string, any>>({});
const tableData = ref([])
const flowCodeOptions = [
  {
    value: 'leave1',
    label: '请假申请-普通'
  },
  {
    value: 'leave2',
    label: '请假申请-排他网关'
  },
  {
    value: 'leave3',
    label: '请假申请-并行网关'
  },
  {
    value: 'leave4',
    label: '请假申请-会签'
  },
  {
    value: 'leave5',
    label: '请假申请-并行会签网关'
  },
  {
    value: 'leave6',
    label: '请假申请-排他并行会签'
  }
];

const flowCode = ref<string>('');

const dialogVisible = reactive<DialogOption>({
  visible: false,
  title: '流程定义'
});
//提交组件
const submitVerifyRef = ref<InstanceType<typeof SubmitVerify>>();
//审批记录组件
const approvalRecordRef = ref<InstanceType<typeof ApprovalRecord>>();

const leaveFormRef = ref<ElFormInstance>();

const submitFormData = ref<StartProcessBo>({
  businessId: '',
  flowCode: '',
  variables: {}
});
const taskVariables = ref<Record<string, any>>({});

const initFormData: LeaveForm = {
  id: undefined,
  leaveType: undefined,
  startDate: undefined,
  endDate: undefined,
  leaveDays: undefined,
  remark: undefined,
  status: 'waiting'
};
const data = reactive<PageData<LeaveForm, LeaveQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    startLeaveDays: undefined,
    endLeaveDays: undefined
  },
  rules: {
    id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
    leaveType: [{ required: true, message: '请假类型不能为空', trigger: 'blur' }],
    leaveTime: [{ required: true, message: '请假时间不能为空', trigger: 'blur' }],
    leaveDays: [{ required: true, message: '请假天数不能为空', trigger: 'blur' }]
  }
});

const handleClose = () => {
  dialogVisible.visible = false;
  flowCode.value = '';
  buttonLoading.value = false;
};
const { form, rules } = toRefs(data);

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  leaveTime.value = [];
  leaveFormRef.value?.resetFields();
};
// 获取监督站文件列表
const getSupervisionFileList = async (val: string) => {
  const res = await listByIds(val);
  if (res.code == 200) {
    return res.data.map((oss) => {
      return {
        name: oss.originalName,
        url: oss.url,
        ossId: oss.ossId
      };
    });
  }
}
// 获取施工方需要整改的数据
const getConstructionData = async () => {
  loading.value = true;
  const res = await getManualDetail(routeParams.value.id)
  if (res.code === 200) {
    formCorrectData.id = res.data.id;
    if (res.data.correctionsFile) {
      // 限期整改文件列表
      const correctionsFileList = await getSupervisionFileList(res.data.correctionsFile);
      tableData.value[0] = {
        id: 1,
        name: '限期整改',
        time: res.data.futureTime,
        manualFile: correctionsFileList[0],
        manualBackFile: '',
        flag: 'corrections'
      }
      if (res.data.correctionsBackFile) {
        const correctionsBackFileList = await getSupervisionFileList(res.data.correctionsBackFile);
        tableData.value[0].manualBackFile = correctionsBackFileList[0];
      }
    }
    if (res.data.suspensionFile) {
      if (!tableData.value[0]) {
        const suspensionFileList = await getSupervisionFileList(res.data.suspensionFile);
        tableData.value[0] = {
          id: 2,
          name: '停工整改',
          time: res.data.futureTime,
          manualFile: suspensionFileList[0],
          manualBackFile: '',
          flag: 'suspension'
        }
        if (res.data.suspensionBackFile) {
          const suspensionBackFileList = await getSupervisionFileList(res.data.suspensionBackFile);
          tableData.value[0].manualBackFile = suspensionBackFileList[0];
        }
      } else if (!tableData.value[1]) {
        const suspensionFileList = await getSupervisionFileList(res.data.suspensionFile);
        tableData.value[1] = {
          id: 2,
          name: '停工整改',
          time: res.data.futureTime,
          manualFile: suspensionFileList[0],
          manualBackFile: '',
          flag: 'suspension'
        }
        if (res.data.suspensionBackFile) {
          const suspensionBackFileList = await getSupervisionFileList(res.data.suspensionBackFile);
          tableData.value[1].manualBackFile = suspensionBackFileList[0];
        }
      } else {
        const suspensionFileList = await getSupervisionFileList(res.data.suspensionFile);
        tableData.value[2] = {
          id: 2,
          name: '停工整改',
          time: res.data.futureTime,
          manualFile: suspensionFileList[0],
          manualBackFile: '',
          flag: 'suspension'
        }
        if (res.data.suspensionBackFile) {
          const suspensionBackFileList = await getSupervisionFileList(res.data.suspensionBackFile);
          tableData.value[2].manualBackFile = suspensionBackFileList[0];
        }
      }
    }
    if (res.data.penaltyFile) {
      if (!tableData.value[0]) {
        const penaltyFileList = await getSupervisionFileList(res.data.penaltyFile);
        tableData.value[0] = {
          id: 3,
          name: '行政处罚',
          time: res.data.futureTime,
          manualFile: penaltyFileList[0],
          manualBackFile: '',
          flag: 'penalty'
        }
        if (res.data.penaltyBackFile) {
          const penaltyBackFileList = await getSupervisionFileList(res.data.penaltyBackFile);
          tableData.value[0].manualBackFile = penaltyBackFileList[0];
        }
      } else if (!tableData.value[1]) {
        const penaltyFileList = await getSupervisionFileList(res.data.penaltyFile);
        tableData.value[1] = {
          id: 3,
          name: '行政处罚',
          time: res.data.futureTime,
          manualFile: penaltyFileList[0],
          manualBackFile: '',
          flag: 'penalty'
        }
        if (res.data.penaltyBackFile) {
          const penaltyBackFileList = await getSupervisionFileList(res.data.penaltyBackFile);
          tableData.value[1].manualBackFile = penaltyBackFileList[0];
        }
      } else {
        const penaltyFileList = await getSupervisionFileList(res.data.penaltyFile);
        tableData.value[2] = {
          id: 3,
          name: '行政处罚',
          time: res.data.futureTime,
          manualFile: penaltyFileList[0],
          manualBackFile: '',
          flag: 'penalty'
        }
        if (res.data.penaltyBackFile) {
          const penaltyBackFileList = await getSupervisionFileList(res.data.penaltyBackFile);
          tableData.value[2].manualBackFile = penaltyBackFileList[0];
        }
      }
    }
    correctLength.value = tableData.value.length;
    if (res.data.elseFile.length > 0) {
      let elseBackFileList = []
      for (let i = 0; i < res.data.elseFile.length; i++) {
        const elseFileList = await getSupervisionFileList(res.data.elseFile[i].fileId);
        if (res.data.elseFile[i].callFileId) {
          elseBackFileList = await getSupervisionFileList(res.data.elseFile[i].callFileId);
        }
        tableData.value.push({
          id: i + 4,
          itemFileId: res.data.elseFile[i].itemFileId,
          name: res.data.elseFile[i].name,
          time: res.data.futureTime,
          manualFile: elseFileList[0],
          manualBackFile: elseBackFileList,
          flag: 'elseFile'
        })
      }
    }
    tableData.value.forEach((item, index) => {
      isUploaded.value[index] = false;
      if (item.flag == 'elseFile') {
        isFileNames.value[index] = '其他'
        return
      }
      isFileNames.value[index] = item.name;
    })
  }
  // 退回时的回显数据
  tableData.value.forEach((item, index) => {
    switch (item.flag) {
      case 'corrections':
        if (item.manualBackFile) {
          formCorrectData.correctionsBackFile = item.manualBackFile.ossId;
          isUploaded.value[index] = true;
        }
        break;
      case 'suspension':
        if (item.manualBackFile) {
          formCorrectData.suspensionBackFile = item.manualBackFile.ossId;
          isUploaded.value[index] = true;
        }
        break;
      case 'penalty':
        if (item.manualBackFile) {
          formCorrectData.penaltyBackFile = item.manualBackFile.ossId;
          isUploaded.value[index] = true;
        }
        break;
      case 'elseFile':
        if (item.manualBackFile.length > 0) {
          isUploaded.value[index] = true;
        }
        break;

      default:
        break;
    }
  })
  loading.value = false;
}
// 上传限期整改文件的回调函数
const handleDeadlinePassback = (fileOssId: string, index: number) => {
  formCorrectData.correctionsBackFile = fileOssId;
  if (fileOssId) {
    isUploaded.value[index] = true;
  } else {
    isUploaded.value[index] = false;
  }
}
// 上传停工通知文件的回调函数
const handleShutdownPassback = (fileOssId: string, index: number) => {
  formCorrectData.suspensionBackFile = fileOssId;
  if (fileOssId) {
    isUploaded.value[index] = true;
  } else {
    isUploaded.value[index] = false;
  }
}
// 上传行政处罚文件的回调函数
const handlePunishPassback = (fileOssId: string, index: number) => {
  formCorrectData.penaltyBackFile = fileOssId;
  if (fileOssId) {
    isUploaded.value[index] = true;
  } else {
    isUploaded.value[index] = false;
  }
}
// 上传其他文件的回调函数
const handleOtherPassback = (fileOssId: string, index: number, itemFileId: string) => {
  if (fileOssId) {
    otherFileData[index - correctLength.value] = {
      itemFileId: itemFileId, //其他每一行的id
      callFileId: fileOssId   //其他文件回显的ossid
    };
    isUploaded.value[index] = true;
  } else {
    otherFileData.splice(index - correctLength.value, 1); //删除;
    isUploaded.value[index] = false;
  }
}
// 获取任务变量数据
const getTaskVariablesData = async () => {
  const res = await getTaskVariables(routeParams.value.id);
  if (res.code === 200) {
    taskVariables.value = res.data;
  }
}
/** 暂存提交按钮 */
const submitForm = (status: string) => {
  if (leaveTime.value.length === 0) {
    proxy?.$modal.msgError('请假时间不能为空');
    return;
  }
  try {
    leaveFormRef.value?.validate(async (valid: boolean) => {
      form.value.startDate = leaveTime.value[0];
      form.value.endDate = leaveTime.value[1];
      if (valid) {
        buttonLoading.value = true;
        let res: AxiosResponse<LeaveVO>;
        if (form.value.id) {
          res = await updateLeave(form.value);
        } else {
          res = await addLeave(form.value);
        }
        form.value = res.data;
        if (status === 'draft') {
          buttonLoading.value = false;
          proxy?.$modal.msgSuccess('暂存成功');
          proxy.$tab.closePage(proxy.$route);
          proxy.$router.go(-1);
        } else {
          if ((form.value.status === 'draft' && (flowCode.value === '' || flowCode.value === null)) || routeParams.value.type === 'add') {
            flowCode.value = flowCodeOptions[0].value;
            dialogVisible.visible = true;
            return;
          }
          //说明启动过先随意穿个参数
          if (flowCode.value === '' || flowCode.value === null) {
            flowCode.value = 'xx';
          }
          await handleStartWorkFlow(res.data);
        }
      }
    });
  } finally {
    buttonLoading.value = false;
  }
};

const submitFlow = async () => {
  handleStartWorkFlow(form.value);
  dialogVisible.visible = false;
};
const objectSpanMethod = ({ row, column, rowIndex, columnIndex, }) => {
  if (columnIndex === 1) {
    if (rowIndex % 5 === 0) {
      return {
        rowspan: 5,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
}
//提交申请
const handleStartWorkFlow = async (data: LeaveForm) => {
  try {
    submitFormData.value.flowCode = flowCode.value;
    submitFormData.value.businessId = data.id;

    //流程变量
    taskVariables.value = {
      leaveDays: data.leaveDays,
      userList: ['1', '3', '4']
    };
    submitFormData.value.variables = taskVariables.value;
    const resp = await startWorkFlow(submitFormData.value);
    if (submitVerifyRef.value) {
      buttonLoading.value = false;
      submitVerifyRef.value.openDialog(resp.data.taskId);
    }
  } finally {
    buttonLoading.value = false;
  }
};
//头部流程进度
const handleApprovalRecord = () => {
  approvalRecordRef.value.init(form.value.id);
};

//提交组件回调
const submitCallback = async () => {
  await proxy.$tab.closePage(proxy.$route);
  proxy.$router.go(-1);
};
// 提交前的通用回调函数
const beforeSubmit = async (fun) => {
  // 提交前的逻辑处理
  fun(true)
};
//头部返回
const goBack = () => {
  proxy.$tab.closePage(proxy.$route);
  proxy.$router.go(-1);
};
//头部审批
const approvalVerifyOpen = async () => {
  const isBoolenLength = isUploaded.value.every((item) => item === true);
  if (isBoolenLength) {
    const res = await saveSupervisionReport(formCorrectData)
    if (res.code == 200) {
      if (otherFileData.length > 0) {
        const res1 = await saveSupervisionReportOther(otherFileData)
        if (res1.code == 200) {
          submitVerifyRef.value.openDialog(routeParams.value.taskId);
        }
        return;
      }
      submitVerifyRef.value.openDialog(routeParams.value.taskId);
    }
  } else {
    for (let i = 0; i < isUploaded.value.length; i++) {
      if (!isUploaded.value[i]) {
        proxy?.$modal.msgError(`请上传${isFileNames.value[i]}报告`);
        break;
      }
    }
  }
};
//校验提交按钮是否显示
const submitButtonShow = computed(() => {
  return (
    routeParams.value.type === 'add' ||
    (routeParams.value.type === 'update' &&
      form.value.status &&
      (form.value.status === 'draft' || form.value.status === 'cancel' || form.value.status === 'back'))
  );
});

//校验审批按钮是否显示
const approvalButtonShow = computed(() => {
  return routeParams.value.type === 'approval' && form.value.status && form.value.status === 'waiting';
});
const handleFileView = (urls: any) => {
  const botaUrl = btoa(urls)
  const url = `${import.meta.env.VITE_APP_VIEW_URL}/onlinePreview?url=${botaUrl}`
  // 证书预览
  window.open(url, '_blank')
}
onMounted(() => {
  nextTick(async () => {
    routeParams.value = proxy.$route.query;
    reset();
    form.value.id = routeParams.value.id;
    await getTaskVariablesData();
    getConstructionData();
  });
});
</script>
<style lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.elseFileBtn {
  margin-bottom: 7px;
}

.elseFileBtn:last-child {
  margin-bottom: 0;
}
</style>

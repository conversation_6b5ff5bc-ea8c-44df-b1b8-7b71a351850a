{"doc": " 人员证书属性类型\n\n <AUTHOR>\n @date 2025-05-10\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.person.domain.bo.QualificationDictBo"], "doc": " 查询人员证书属性类型列表\n"}, {"name": "export", "paramTypes": ["org.dromara.person.domain.bo.QualificationDictBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出人员证书属性类型列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取人员证书属性类型详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.person.domain.bo.QualificationDictBo"], "doc": " 新增人员证书属性类型\n"}, {"name": "edit", "paramTypes": ["org.dromara.person.domain.bo.QualificationDictBo"], "doc": " 修改人员证书属性类型\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除人员证书属性类型\n\n @param ids 主键串\n"}], "constructors": []}
package org.dromara.flow.domain.bo;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.flow.domain.PrjHazardousItemsSpecialWarning;

/**
 * 特殊预警业务对象 prj_hazardous_items_special_warning
 *
 * <AUTHOR> Li
 * @date 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjHazardousItemsSpecialWarning.class, reverseConvertGenerate = false)
public class PrjHazardousItemsSpecialWarningBo extends BaseEntity {

    /**
     * 特殊预警id
     */
    @NotNull(message = "特殊预警id不能为空", groups = {EditGroup.class})
    private Long warningId;

    /**
     * 流程业务id
     */
    private String taskId;

    /**
     * 预警原因（质监站可补充）
     */
    private String reason;

    /**
     * 预警类型(字典special_warning_type)
     */
    private String reasonType;
}

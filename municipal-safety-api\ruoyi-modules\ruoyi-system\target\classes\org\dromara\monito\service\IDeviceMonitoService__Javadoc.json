{"doc": " 监控管理Service接口\n\n <AUTHOR>\n @date 2025-05-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询监控管理\n\n @param monitoId 主键\n @return 监控管理\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.monito.domain.bo.DeviceMonitoBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询监控管理列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 监控管理分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.monito.domain.bo.DeviceMonitoBo"], "doc": " 查询符合条件的监控管理列表\n\n @param bo 查询条件\n @return 监控管理列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.monito.domain.bo.DeviceMonitoBo"], "doc": " 新增监控管理\n\n @param bo 监控管理\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.monito.domain.bo.DeviceMonitoBo"], "doc": " 修改监控管理\n\n @param bo 监控管理\n @return 是否修改成功\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除监控管理信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}, {"name": "getItemNum", "paramTypes": ["org.dromara.dp.domain.bo.DataViewBo"], "doc": " 根据危大类型查询工程数量\n"}, {"name": "getDangerNum", "paramTypes": ["org.dromara.dp.domain.bo.DataViewBo"], "doc": " 根据危大类型查询父级工程清单，清单下面的工程数量，隐患(已处理和未处理的数量)\n"}, {"name": "getProItemList", "paramTypes": ["org.dromara.dp.domain.bo.DataViewBo"], "doc": " 根据危大类型和清单父级名称查询工程列表\n"}, {"name": "getYearItemNum", "paramTypes": ["org.dromara.dp.domain.bo.DataViewBo"], "doc": " 根据年份查询危大工程数量（危大、超危大）\n"}, {"name": "getAreaItemNum", "paramTypes": ["org.dromara.dp.domain.bo.DataViewBo"], "doc": " 根据区域查询危大工程数量（危大、超危大）\n"}], "constructors": []}
{"doc": " 智能隐患分析任务\n\n <AUTHOR>\n @date 2025-05-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询智能隐患分析任务列表\n"}, {"name": "export", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出智能隐患分析任务列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取智能隐患分析任务详细信息\n\n @param taskId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksBo"], "doc": " 新增智能隐患分析任务\n"}, {"name": "edit", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksBo"], "doc": " 修改智能隐患分析任务\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除智能隐患分析任务\n\n @param taskIds 主键串\n"}, {"name": "appList", "paramTypes": ["org.dromara.ai.domain.dto.AiHazAnalysisTasksDto", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " APP查询隐患列表\n 支持通过项目名称和工程名称搜索\n"}, {"name": "getProjectHazards", "paramTypes": ["java.lang.Long", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据项目ID查询隐患列表\n 从工程管理页面点击查看该项目的隐患列表\n"}, {"name": "getAppInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取APP端智能隐患分析任务详细信息\n\n @param taskId 主键\n"}, {"name": "uploadHazRecord", "paramTypes": ["org.dromara.ai.domain.dto.HazRecordUploadDto"], "doc": " 专家上传隐患记录和图片URL\n\n @param dto 隐患记录信息，包含图片URL\n @return 上传结果\n"}, {"name": "aiCallback", "paramTypes": ["org.dromara.ai.domain.dto.AiAnalysisResultDto"], "doc": " AI回调接口 - 接收外部AI系统分析结果\n\n @param aiResult AI分析结果\n"}], "constructors": []}
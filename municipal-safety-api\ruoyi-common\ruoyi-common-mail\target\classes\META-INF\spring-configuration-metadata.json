{"groups": [{"name": "mail", "type": "org.dromara.common.mail.config.properties.MailProperties", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}], "properties": [{"name": "mail.auth", "type": "java.lang.Bo<PERSON>an", "description": "是否需要用户名密码验证", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.connection-timeout", "type": "java.lang.Long", "description": "Socket连接超时值，单位毫秒，缺省值不超时", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.enabled", "type": "java.lang.Bo<PERSON>an", "description": "过滤开关", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.from", "type": "java.lang.String", "description": "发送方，遵循RFC-822标准<br> 发件人可以是以下形式： <pre> 1. <EMAIL> 2. name &lt;<EMAIL>&gt; </pre>", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.host", "type": "java.lang.String", "description": "SMTP服务器域名", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.pass", "type": "java.lang.String", "description": "密码", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.port", "type": "java.lang.Integer", "description": "SMTP服务端口", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.ssl-enable", "type": "java.lang.Bo<PERSON>an", "description": "使用 SSL安全连接", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.starttls-enable", "type": "java.lang.Bo<PERSON>an", "description": "使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。它将纯文本连接升级为加密连接（TLS或SSL）， 而不是使用一个单独的加密通信端口。", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.timeout", "type": "java.lang.Long", "description": "SMTP超时时长，单位毫秒，缺省值不超时", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.user", "type": "java.lang.String", "description": "用户名", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}], "hints": []}
package org.dromara.projects.service;

import org.dromara.projects.domain.PrjSafeTask;
import org.dromara.projects.domain.vo.PrjSafeTaskVo;
import org.dromara.projects.domain.bo.PrjSafeTaskBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 【项目管理】安拆任务Service接口
 *
 * <AUTHOR> Li
 * @date 2025-08-08
 */
public interface IPrjSafeTaskService {

    /**
     * 查询【项目管理】安拆任务
     *
     * @param openTaskId 主键
     * @return 【项目管理】安拆任务
     */
    PrjSafeTaskVo queryById(Long openTaskId);

    /**
     * 分页查询【项目管理】安拆任务列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 【项目管理】安拆任务分页列表
     */
    TableDataInfo<PrjSafeTaskVo> queryPageList(PrjSafeTaskBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的【项目管理】安拆任务列表
     *
     * @param bo 查询条件
     * @return 【项目管理】安拆任务列表
     */
    List<PrjSafeTaskVo> queryList(PrjSafeTaskBo bo);

    /**
     * 新增【项目管理】安拆任务
     *
     * @param bo 【项目管理】安拆任务
     * @return 是否新增成功
     */
    Boolean insertByBo(PrjSafeTaskBo bo);

    /**
     * 修改【项目管理】安拆任务
     *
     * @param bo 【项目管理】安拆任务
     * @return 是否修改成功
     */
    Boolean updateByBo(PrjSafeTaskBo bo);

    /**
     * 校验并批量删除【项目管理】安拆任务信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 通过projectId转换安拆任务
     *
     * @param projectId
     * @return
     */
    PrjSafeTask getToProjectId(Long projectId);


}

package org.dromara.plan.service;


import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.plan.domain.bo.PrjPatrolPlanBo;
import org.dromara.plan.domain.vo.PrjPatrolPlanVo;

import java.util.Collection;
import java.util.List;

/**
 * 巡检计划Service接口
 *
 * <AUTHOR> Li
 * @date 2025-06-18
 */
public interface IPrjPatrolPlanService {

    /**
     * 查询巡检计划
     *
     * @param planId 主键
     * @return 巡检计划
     */
    PrjPatrolPlanVo queryById(Long planId);

    /**
     * 分页查询巡检计划列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 巡检计划分页列表
     */
    TableDataInfo<PrjPatrolPlanVo> queryPageList(PrjPatrolPlanBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的巡检计划列表
     *
     * @param bo 查询条件
     * @return 巡检计划列表
     */
    List<PrjPatrolPlanVo> queryList(PrjPatrolPlanBo bo);

    /**
     * 新增巡检计划
     *
     * @param bo 巡检计划
     * @return 是否新增成功
     */
    Boolean insertByBo(PrjPatrolPlanBo bo);

    /**
     * 修改巡检计划
     *
     * @param bo 巡检计划
     * @return 是否修改成功
     */
    Boolean updateByBo(PrjPatrolPlanBo bo);

    /**
     * 校验并批量删除巡检计划信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


}

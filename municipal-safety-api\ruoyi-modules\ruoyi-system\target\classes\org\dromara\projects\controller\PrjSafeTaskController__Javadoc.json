{"doc": " 【项目管理】安拆任务\n\n <AUTHOR>\n @date 2025-08-08\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.projects.domain.bo.PrjSafeTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询【项目管理】安拆任务列表\n"}, {"name": "export", "paramTypes": ["org.dromara.projects.domain.bo.PrjSafeTaskBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出【项目管理】安拆任务列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取【项目管理】安拆任务详细信息\n\n @param openTaskId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.projects.domain.bo.PrjSafeTaskBo"], "doc": " 新增【项目管理】安拆任务\n"}, {"name": "edit", "paramTypes": ["org.dromara.projects.domain.bo.PrjSafeTaskBo"], "doc": " 修改【项目管理】安拆任务\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除【项目管理】安拆任务\n\n @param openTaskIds 主键串\n"}], "constructors": []}
package org.dromara.expert.controller;

import java.util.List;
import java.util.Objects;

import cn.dev33.satoken.secure.BCrypt;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import org.dromara.common.mybatis.helper.DataPermissionHelper;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.expert.service.IFieldService;
import org.dromara.expert.service.IProjectService;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.service.ISysUserService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.expert.domain.vo.ExpertVo;
import org.dromara.expert.domain.bo.ExpertBo;
import org.dromara.expert.service.IExpertService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 *  专家主
 * @date 2025-05-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/expert/expert")
public class ExpertController extends BaseController {

    private final IExpertService expertService;

    private final IFieldService fieldService;

    private final IProjectService projectService;
    private final ISysUserService userService;

    /**
     * 查询 专家主列表
     */
    @GetMapping("/list")
    public TableDataInfo<ExpertVo> list(ExpertBo bo, PageQuery pageQuery) {
        return expertService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询 专家主列表
     */
    @GetMapping("/expertList")
    public R expertList(ExpertBo bo) {
        List<ExpertVo> expertVos = expertService.queryList(bo);
        return R.ok(expertVos);
    }

    /**
     * 导出 专家列表
     */
    @Log(title = " 专家主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ExpertBo bo, HttpServletResponse response) {
        List<ExpertVo> list = expertService.queryList(bo);
        ExcelUtil.exportExcel(list, " 专家主", ExpertVo.class, response);
    }

    /**
     * 获取 专家详细信息
     *
     * @param expertId 主键
     */
    @GetMapping("/{expertId}")
    public R<ExpertVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long expertId) {
        return R.ok(expertService.queryById(expertId));
    }

    /**
     * 新增 专家
     */
    @Log(title = " 专家", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ExpertBo bo) {
        ExpertBo query = new ExpertBo();
        query.setIdCard(bo.getIdCard());
        List<ExpertVo> expertVos = expertService.queryList(query);
        if (!expertVos.isEmpty()){
            return R.fail("该证件编号已存在!");
        }
        return expertService.insertByBo(bo);
    }

    /**
     * 修改 专家
     */
    @Log(title = " 专家", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ExpertBo bo) {
        return toAjax(expertService.updateByBo(bo));
    }

    /**
     * 删除 专家主
     *
     * @param expertIds 主键串
     */
    @Log(title = " 专家", businessType = BusinessType.DELETE)
    @DeleteMapping("/{expertIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] expertIds) {
        return toAjax(expertService.deleteWithValidByIds(List.of(expertIds), true));
    }

    /**
     *  删除 专家领域
     */
    @DeleteMapping("/field/{fieldId}")
    public R<Void> removeField(@PathVariable Long fieldId) {
        return toAjax(fieldService.removeById(fieldId));
    }

    /**
     *  删除 专家项目
     */
    @DeleteMapping("/project/{projectId}")
    public R<Void> removeProject(@PathVariable Long projectId) {
        return toAjax(projectService.removeById(projectId));
    }

    /**
     * 重置密码
     */
    @GetMapping("/resetPwd")
    public R<Void> resetPwd(ExpertBo bo) {
        SysUserVo user = userService.selectUserByUserName(bo.getIdCard());
        if (Objects.isNull(user)) {
            return R.fail("账号不存在");
        }
        int rows = DataPermissionHelper.ignore(() -> userService.resetUserPwd(user.getUserId(), BCrypt.hashpw("123456")));
        if (rows > 0) {
            return R.ok("密码已重置，初始密码为123456");
        }
        return R.fail("密码重置，请联系管理员");
    }

}

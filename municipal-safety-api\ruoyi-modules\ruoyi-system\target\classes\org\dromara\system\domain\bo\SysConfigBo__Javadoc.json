{"doc": " 参数配置业务对象 sys_config\n\n <AUTHOR>\n", "fields": [{"name": "configId", "doc": " 参数主键\n"}, {"name": "config<PERSON><PERSON>", "doc": " 参数名称\n"}, {"name": "config<PERSON><PERSON>", "doc": " 参数键名\n"}, {"name": "config<PERSON><PERSON><PERSON>", "doc": " 参数键值\n"}, {"name": "configType", "doc": " 系统内置（Y是 N否）\n"}, {"name": "remark", "doc": " 备注\n"}], "enumConstants": [], "methods": [], "constructors": []}
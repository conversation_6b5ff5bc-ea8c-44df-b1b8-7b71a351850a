import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PatrolPlanVO, PatrolPlanForm, PatrolPlanQuery } from '@/api/plan/patrolPlan/types';

/**
 * 查询巡检计划列表
 * @param query
 * @returns {*}
 */

export const listPatrolPlan = (query?: PatrolPlanQuery): AxiosPromise<PatrolPlanVO[]> => {
  return request({
    url: '/plan/patrolPlan/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询巡检计划详细
 * @param planId
 */
export const getPatrolPlan = (planId: string | number): AxiosPromise<PatrolPlanVO> => {
  return request({
    url: '/plan/patrolPlan/' + planId,
    method: 'get'
  });
};

/**
 * 新增巡检计划
 * @param data
 */
export const addPatrolPlan = (data: PatrolPlanForm) => {
  return request({
    url: '/plan/patrolPlan',
    method: 'post',
    data: data
  });
};

/**
 * 修改巡检计划
 * @param data
 */
export const updatePatrolPlan = (data: PatrolPlanForm) => {
  return request({
    url: '/plan/patrolPlan',
    method: 'put',
    data: data
  });
};

/**
 * 删除巡检计划
 * @param planId
 */
export const delPatrolPlan = (planId: string | number | Array<string | number>) => {
  return request({
    url: '/plan/patrolPlan/' + planId,
    method: 'delete'
  });
};
// 部门
export const deptList = (query) => {
  return request({
    url: '/system/dept/deptList',
    method: 'get',
    params: query
  });
};
// 专家列表
export const expertList = () => {
  return request({
    url: '/expert/expert/expertList',
    method: 'get',
  });
};
// 部门 含分页
export const deptPageList = (query) => {
  return request({
    url: '/system/dept/deptPageList',
    method: 'get',
    params: query
  });
};
{"doc": " <AUTHOR>\n @date 2025/5/28 15:02\n @Description TODO\n @Version 1.0\n", "fields": [], "enumConstants": [], "methods": [{"name": "getVariable", "paramTypes": ["java.lang.String"], "doc": " 获取任务变量\n\n @param busId\n @return\n"}, {"name": "aiDetailVOR", "paramTypes": ["java.lang.Long"], "doc": " ai隐患获取详情信息\n\n @param id\n @return\n"}, {"name": "pageByTaskWait", "paramTypes": ["org.dromara.workflow.domain.bo.FlowTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询当前用户的待办任务\n\n @param flowTaskBo 参数\n @param pageQuery  分页\n"}, {"name": "deleteByInstanceIds", "paramTypes": ["java.util.List"], "doc": " 按照实例id删除流程实例\n\n @param instanceIds 实例id\n"}, {"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取用户列表\n"}, {"name": "makeCopyUserPage", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取用户列表\n"}], "constructors": []}
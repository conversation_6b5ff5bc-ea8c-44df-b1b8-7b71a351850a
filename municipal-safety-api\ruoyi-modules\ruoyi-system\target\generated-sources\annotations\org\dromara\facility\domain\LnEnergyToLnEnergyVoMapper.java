package org.dromara.facility.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.bo.LnEnergyBoToLnEnergyMapper;
import org.dromara.facility.domain.vo.LnEnergyVo;
import org.dromara.facility.domain.vo.LnEnergyVoToLnEnergyMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {LnEnergyVoToLnEnergyMapper.class,LnEnergyBoToLnEnergyMapper.class},
    imports = {}
)
public interface LnEnergyToLnEnergyVoMapper extends BaseMapper<LnEnergy, LnEnergyVo> {
}

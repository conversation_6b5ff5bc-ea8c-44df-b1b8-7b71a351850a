package org.dromara.facility.domain.bo;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.LnEdgeGuard;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnEdgeGuardBoToLnEdgeGuardMapperImpl implements LnEdgeGuardBoToLnEdgeGuardMapper {

    @Override
    public LnEdgeGuard convert(LnEdgeGuardBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnEdgeGuard lnEdgeGuard = new LnEdgeGuard();

        lnEdgeGuard.setId( arg0.getId() );
        lnEdgeGuard.setDumpnumber( arg0.getDumpnumber() );
        lnEdgeGuard.setCheckSensor( arg0.getCheckSensor() );
        lnEdgeGuard.setLongitude( arg0.getLongitude() );
        lnEdgeGuard.setLatitude( arg0.getLatitude() );
        lnEdgeGuard.setBatvolt( arg0.getBatvolt() );
        lnEdgeGuard.setBatPercent( arg0.getBatPercent() );
        lnEdgeGuard.setAlarmInfo( arg0.getAlarmInfo() );
        lnEdgeGuard.setDevNo( arg0.getDevNo() );
        lnEdgeGuard.setCreateTime( arg0.getCreateTime() );

        return lnEdgeGuard;
    }

    @Override
    public LnEdgeGuard convert(LnEdgeGuardBo arg0, LnEdgeGuard arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setDumpnumber( arg0.getDumpnumber() );
        arg1.setCheckSensor( arg0.getCheckSensor() );
        arg1.setLongitude( arg0.getLongitude() );
        arg1.setLatitude( arg0.getLatitude() );
        arg1.setBatvolt( arg0.getBatvolt() );
        arg1.setBatPercent( arg0.getBatPercent() );
        arg1.setAlarmInfo( arg0.getAlarmInfo() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}

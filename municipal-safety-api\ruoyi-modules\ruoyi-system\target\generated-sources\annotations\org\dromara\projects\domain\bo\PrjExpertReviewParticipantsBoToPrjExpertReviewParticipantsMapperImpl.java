package org.dromara.projects.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjExpertReviewParticipants;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjExpertReviewParticipantsBoToPrjExpertReviewParticipantsMapperImpl implements PrjExpertReviewParticipantsBoToPrjExpertReviewParticipantsMapper {

    @Override
    public PrjExpertReviewParticipants convert(PrjExpertReviewParticipantsBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjExpertReviewParticipants prjExpertReviewParticipants = new PrjExpertReviewParticipants();

        prjExpertReviewParticipants.setSearchValue( arg0.getSearchValue() );
        prjExpertReviewParticipants.setCreateDept( arg0.getCreateDept() );
        prjExpertReviewParticipants.setCreateBy( arg0.getCreateBy() );
        prjExpertReviewParticipants.setCreateTime( arg0.getCreateTime() );
        prjExpertReviewParticipants.setUpdateBy( arg0.getUpdateBy() );
        prjExpertReviewParticipants.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            prjExpertReviewParticipants.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        prjExpertReviewParticipants.setReviewId( arg0.getReviewId() );
        prjExpertReviewParticipants.setUserId( arg0.getUserId() );
        prjExpertReviewParticipants.setRoleInMeeting( arg0.getRoleInMeeting() );
        prjExpertReviewParticipants.setIsAttendingExpert( arg0.getIsAttendingExpert() );

        return prjExpertReviewParticipants;
    }

    @Override
    public PrjExpertReviewParticipants convert(PrjExpertReviewParticipantsBo arg0, PrjExpertReviewParticipants arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setReviewId( arg0.getReviewId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setRoleInMeeting( arg0.getRoleInMeeting() );
        arg1.setIsAttendingExpert( arg0.getIsAttendingExpert() );

        return arg1;
    }
}

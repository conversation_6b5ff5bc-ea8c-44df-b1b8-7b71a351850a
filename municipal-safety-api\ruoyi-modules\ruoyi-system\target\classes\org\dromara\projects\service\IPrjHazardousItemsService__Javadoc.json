{"doc": " [项目管理] 列出项目内具体的危险性较大的分部分项工程Service接口\n\n <AUTHOR>\n @date 2025-05-07\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询[项目管理] 列出项目内具体的危险性较大的分部分项工程\n\n @param itemId 主键\n @return [项目管理] 列出项目内具体的危险性较大的分部分项工程\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return [项目管理] 列出项目内具体的危险性较大的分部分项工程分页列表\n"}, {"name": "queryAdminPageList", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 管理员分页查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return [项目管理] 列出项目内具体的危险性较大的分部分项工程分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsBo"], "doc": " 查询符合条件的[项目管理] 列出项目内具体的危险性较大的分部分项工程列表\n\n @param bo 查询条件\n @return [项目管理] 列出项目内具体的危险性较大的分部分项工程列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsBo"], "doc": " 新增[项目管理] 列出项目内具体的危险性较大的分部分项工程\n\n @param bo [项目管理] 列出项目内具体的危险性较大的分部分项工程\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsBo"], "doc": " 修改[项目管理] 列出项目内具体的危险性较大的分部分项工程\n\n @param bo [项目管理] 列出项目内具体的危险性较大的分部分项工程\n @return 是否修改成功\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除[项目管理] 列出项目内具体的危险性较大的分部分项工程信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}, {"name": "queryDetailById", "paramTypes": ["java.lang.Long"], "doc": " 分部分项获取详细信息\n @param itemId\n @return\n"}, {"name": "getAiHazAnalysisTaskDetail", "paramTypes": ["java.lang.Long"], "doc": " ai隐患清单详情\n @param taskId\n @return\n"}], "constructors": []}
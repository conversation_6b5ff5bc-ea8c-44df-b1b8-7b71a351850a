2025-05-07 08:39:31 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-07 08:39:31 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.15 with PID 17520 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-snailjob-server\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-07 08:39:31 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-05-07 08:39:32 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-05-07 08:39:32 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-05-07 08:39:32 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-07 08:39:32 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-05-07 08:39:32 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-05-07 08:39:32 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1574 ms
2025-05-07 08:39:35 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-05-07 08:39:35 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-07 08:39:35 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-07 08:39:35 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-07 08:39:35 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-07 08:39:36 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-05-07 08:39:36 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-05-07 08:39:36 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-05-07 08:39:36 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.4.0
2025-05-07 08:39:36 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-05-07 08:39:36 [main] INFO  c.a.s.s.common.rpc.server.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.GrpcServer, port = 17888
2025-05-07 08:39:36 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-05-07 08:39:36 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-05-07 08:39:36 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-05-07 08:39:36 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-05-07 08:39:36 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-05-07 08:39:36 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-05-07 08:39:36 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-05-07 08:39:36 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-05-07 08:39:36 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-05-07 08:39:36 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-05-07 08:39:36 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-05-07 08:39:36 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-05-07 08:39:36 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.4.0
2025-05-07 08:39:36 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 5.87 seconds (process running for 6.347)
2025-05-07 08:39:36 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-07 08:39:36 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as cc20f5b8d840
2025-05-07 08:39:36 [http-nio-8800-exec-1] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-07 08:39:36 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-07 08:39:36 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-07 08:39:37 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3e60e9c4
2025-05-07 08:39:37 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-07 08:39:37 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1919914971417636864]
2025-05-07 08:39:46 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[1] remoteNodeSize:[3]
2025-05-07 08:39:46 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-05-07 08:39:46 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42]]
2025-05-07 08:39:58 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-11] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1919915021204029440]
2025-05-07 08:43:10 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-70] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919728084518305792]
2025-05-07 09:02:37 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919715216260509696]
2025-05-07 09:12:17 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919920678654398464]
2025-05-07 09:35:29 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-1030] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919923103595405312]
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.4.0
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-05-07 09:56:26 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1919914971417636864]
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.4.0
2025-05-07 09:56:26 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-05-07 09:56:26 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-07 09:56:26 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-07 10:19:35 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-07 10:19:35 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.15 with PID 6344 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-snailjob-server\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-07 10:19:35 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-05-07 10:19:37 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-05-07 10:19:37 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-05-07 10:19:37 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-07 10:19:37 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-05-07 10:19:37 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-05-07 10:19:37 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1564 ms
2025-05-07 10:19:38 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-05-07 10:19:39 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-07 10:19:39 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-07 10:19:39 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-07 10:19:39 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-07 10:19:39 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-05-07 10:19:40 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-05-07 10:19:40 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-05-07 10:19:40 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.4.0
2025-05-07 10:19:40 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-05-07 10:19:40 [main] INFO  c.a.s.s.common.rpc.server.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.GrpcServer, port = 17888
2025-05-07 10:19:40 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-05-07 10:19:40 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-05-07 10:19:40 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-05-07 10:19:40 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-05-07 10:19:40 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-05-07 10:19:40 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-05-07 10:19:40 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-05-07 10:19:40 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-05-07 10:19:40 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-05-07 10:19:40 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-05-07 10:19:40 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-05-07 10:19:40 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-05-07 10:19:40 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.4.0
2025-05-07 10:19:40 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 5.061 seconds (process running for 5.552)
2025-05-07 10:19:40 [snail-job-scheduled-thread-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-07 10:19:40 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as cc20f5b8d840
2025-05-07 10:19:40 [http-nio-8800-exec-1] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-07 10:19:40 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-07 10:19:40 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-05-07 10:19:40 [snail-job-scheduled-thread-1] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7647ae0f
2025-05-07 10:19:40 [snail-job-scheduled-thread-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-07 10:19:41 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1919940154006253568]
2025-05-07 10:19:50 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[1] remoteNodeSize:[3]
2025-05-07 10:19:50 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-05-07 10:19:50 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42]]
2025-05-07 10:20:11 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-13] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1919940232548773888]
2025-05-07 10:47:30 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-523] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919946607468380160]
2025-05-07 10:53:20 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919947196654850048]
2025-05-07 10:54:40 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919948465939374080]
2025-05-07 10:55:15 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-662] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919940232548773888]
2025-05-07 10:59:59 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-744] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919948982550630400]
2025-05-07 11:02:50 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-797] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919948465939374080]
2025-05-07 11:07:13 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-873] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919950225603919872]
2025-05-07 11:08:31 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-891] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919950891421470720]
2025-05-07 11:08:31 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-891] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919952036834394112]
2025-05-07 11:21:18 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[3] remoteNodeSize:[2]
2025-05-07 11:21:18 [server-node-balance] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[DEFAULT_SERVER] hostId:[1919276579861458944]
2025-05-07 11:21:18 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-05-07 11:21:18 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63]]
2025-05-07 11:22:08 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-1144] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919952740161617920]
2025-05-07 15:57:40 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919952352166371328]
2025-05-07 16:49:32 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-10] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[824] 任务调度成功.
2025-05-07 16:49:36 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-13] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[825] 任务调度成功.
2025-05-07 16:49:39 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-15] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[826] 任务调度成功.
2025-05-07 17:17:44 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[2] remoteNodeSize:[1]
2025-05-07 17:17:44 [server-node-balance] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[DEFAULT_SERVER] hostId:[1917496843421327360]
2025-05-07 17:17:44 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-05-07 17:17:44 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-05-07 17:18:55 [snail-job-scheduled-thread-2] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1919915801470529536]
2025-05-07 17:36:18 [snail-job-scheduled-thread-4] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920025414073155584]
2025-05-07 20:51:20 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920049955176529920]
2025-05-07 20:51:23 [snail-job-scheduled-thread-4] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920049955176529920]

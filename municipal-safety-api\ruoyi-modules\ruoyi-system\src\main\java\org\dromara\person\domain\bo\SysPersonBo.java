package org.dromara.person.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.person.domain.SysPerson;

/**
 * 人员基本信息业务对象 sys_person
 *
 * <AUTHOR> zu da
 * @date 2025-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysPerson.class, reverseConvertGenerate = false)
public class SysPersonBo extends BaseEntity {

    /**
     * 人员ID
     */
    private Long personId;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 身份证号码
     */
    @NotBlank(message = "身份证号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idCard;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phone;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gender;

    /**
     * 政治面貌（如群众、党员）
     */
    private String politicalStatus;

    /**
     * 文化程度
     */
    @NotBlank(message = "文化程度不能为空", groups = { AddGroup.class, EditGroup.class })
    private String education;

    /**
     * 头像图片id
     */
    private Long headImgId;

    /**
     * 头像压缩图片id
     */
    private Long headImgMini;

    /**
     * 证书种类
     */
    private String certificateType;

    /**
     * 证书名称
     */
    private String certificateName;

    private Long projectId;

    private Long deptId;
}

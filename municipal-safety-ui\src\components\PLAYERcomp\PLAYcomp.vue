<template>
  <div>
    <div id="mse" ref="video"></div>
    <div class="danmu-input-container" v-if="needDanmuComp">
      <input v-model="danmuText" type="text" class="danmu-input" placeholder="发条弹幕吧~" @keyup.enter="sendDanmu('1')" />
      <button class="danmu-send-btn" @click="sendDanmu('1')">内嵌发送</button>
    </div>
  </div>
</template>
<script>
/**
 * *** 基本配置项 ***
 * playType // 播放类型 video || hls || flv
 * propDefinition // 清晰度地址(数组)
 * propHeight // 播放器高度
 * propWidth // 播放器宽度
 * needDanmuComp // 是否自带需要弹幕组件
 *
 * *** 附加配置项 ***
 * propIsLive // 是否直播
 * playUrlList // 播放列表(数组)
 * propControls // 控制条
 * propVolume // 预设音量 0.5
 * propAutoplay // 自动播放
 * propAutoplayMuted // 自动播放时静音
 * propLoop // 循环播放
 * propDefinitionActive // 清晰度切换方式 click || hover
 * propPip // 开启画中画
 * propCssFullscreen // 网页全屏
 * propPoster // 视频封面
 * propMuted // 静音
 * propControlsList // 选择控制条关闭选项 但是只是针对原生video标签管用 控件使用ignore
 * propIgnores // 关闭内置控件项 'time', 'definition', 'error', 'fullscreen', 'i18n', 'loading','mobile', 'pc', 'play', 'poster', 'progress','replay', 'volume'
 * propFluid // 流式布局 ，可使播放器宽度跟随父元素的宽度大小变化，且高度按照配置项中的高度和宽度的比例来变化
 * propDownload // 是否下载
 * propRotateFullscreen // 样式横屏全屏旋转
 * propErrorTips // 错误提示
 * propKeyShortcut // 快捷键
 * *** 配置型 ***
 * propRotate // 视频旋转角度配置
 *      例：{
 *          innerRotate: true,
 *          clockwise: false
 *      }
 * propKeyShortcutStep // 快捷键步长
 *     例： {
 *          currentTime: 15,
 *          volume: 0.2,
 *      }
 * propProgressDot // 进度条标记
 *      例：[{
 *         time : 5 // 标记时间
 *         text : '我是标记文字' // 标记文字
 *         duration : 10 // 标记持续时间
 *         style:{
 *           color: '#fff', // 标记文字颜色
 *           background: '#000', // 标记背景颜色
 *         }
 *       }, ....]
 *
 * *** 未配置项 ***
 * screenShot // 截图 原因：直接可使用默认配置
 * lastPlayTime // 视频起搏时间（单位 / 秒） 原因：直接可使用默认配置
 * lastPlayTimeHideDelay // 视频提示文字显示时长（单位 / 秒） 原因：直接可使用默认配置
 *
 * *** 暴露方法 ***
 * setSec // 设置视频播放时间
 * sendDanmu // 发送弹幕
 * startRecording // 开始录屏
 * stopRecording // 停止录屏
 *
 */
import Player from 'xgplayer';
// import RecordRTC from "recordrtc"
import flvMixins from './flv';
import hlsMixins from './hls';

export default {
  name: 'VideoPlayer',
  mixins: [flvMixins, hlsMixins],
  data() {
    return {
      player: null,
      hlsplayer: null,
      flvplayer: null,
      mediaRecorder: null,
      danmuList: [],
      danmuText: '',
      definitionList: [
        {
          name: '标清',
          url: null,
          default: true
        },
        {
          name: '高清',
          url: null
        },
        {
          name: '超清',
          url: null
        }
      ]
    };
  },
  props: {
    needDanmuComp: {
      type: Boolean,
      default: true
    },
    playType: {
      type: String,
      default: 'video'
    },
    propDefinition: {
      type: Array,
      default: () => []
    },
    propHeight: {
      type: String,
      default: '60vh'
    },
    propWidth: {
      type: String,
      default: '100vw'
    },
    propIsLive: {
      type: Boolean,
      default: false
    },
    propControls: {
      type: Boolean,
      default: true
    },
    playUrlList: {
      type: Array,
      default: () => []
    },
    propVolume: {
      type: Number,
      default: 0.5
    },
    propAutoplay: {
      type: Boolean,
      default: false
    },
    propAutoplayMuted: {
      type: Boolean,
      default: true
    },
    propLoop: {
      type: Boolean,
      default: false
    },
    propDefinitionActive: {
      type: String,
      default: 'click'
    },
    propPip: {
      type: Boolean,
      default: true
    },
    propCssFullscreen: {
      type: Boolean,
      default: true
    },
    propPoster: {
      type: String,
      default: ''
    },
    propMuted: {
      type: Boolean,
      default: true
    },
    propControlsList: {
      type: Array,
      default: () => []
    },
    propIgnores: {
      type: Array,
      default: () => ['replay']
    },
    propFluid: {
      type: Boolean,
      default: false
    },
    propDownload: {
      type: Boolean,
      default: true
    },
    propRotateFullscreen: {
      type: Boolean,
      default: false
    },
    propErrorTips: {
      type: String,
      default: `视频加载出错了！请刷新重试`
    },
    propRotate: {
      type: Object,
      default: () => ({
        innerRotate: true,
        clockwise: false
      })
    },
    propKeyShortcut: {
      type: String,
      default: 'on'
    },
    propKeyShortcutStep: {
      type: Object,
      default: () => ({
        currentTime: 15,
        volume: 0.2
      })
    },
    propProgressDot: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
    this.setDefinition();
    // this.setProgressDot()
    this.getVideoInfo();
  },
  methods: {
    choosePlayer() {
      switch (this.playType) {
        case 'video':
          this.initVideo();
          break;
        case 'hls':
          this.initHls();
          break;
        case 'flv':
          this.initFlv();
          break;
        default:
          break;
      }
    },
    setDefinition() {
      this.definitionList = this.definitionList.filter((_, index) => index < this.propDefinition.length);
      this.propDefinition.forEach((item, index) => {
        if (this.definitionList[index]) {
          this.definitionList[index].url = item;
        }
      });
      this.choosePlayer();
    },
    initVideo() {
      // 将弹幕数据转换为西瓜播放器需要的格式
      let formattedDanmus = this.formatDanmus(this.danmuList);
      this.player = new Player({
        id: 'mse',
        url: this.definitionList[0].url,
        height: this.propHeight,
        width: this.propWidth,
        controls: this.propControls, // 控制条
        muted: this.propMuted, // 静音
        // controlsList: ['nodownload','nofullscreen','noremoteplayback'] ,  // 选择控制条关闭选项 但是只是针对原生video标签管用 控件使用ignore
        ignores: this.propIgnores, // 关闭内置控件项 'time', 'definition', 'error', 'fullscreen', 'i18n', 'loading','mobile', 'pc', 'play', 'poster', 'progress', 'replay', 'volume'
        fluid: this.propFluid, // 流式布局 ，可使播放器宽度跟随父元素的宽度大小变化，且高度按照配置项中的高度和宽度的比例来变化
        volume: this.propVolume, // 音量预设
        autoplay: this.propAutoplay, // 自动播放
        autoplayMuted: this.propAutoplayMuted, // 自动播放时静音
        loop: this.propLoop, // 循环播放
        definitionActive: this.propDefinitionActive, // 清晰度切换方式 click || hover
        pip: this.propPip, // 开启画中画
        cssFullscreen: this.propCssFullscreen, // 网页全屏
        poster: this.propPoster, // 封面图
        lastPlayTime: 0, //视频起播时间（单位：秒）
        lastPlayTimeHideDelay: 5, //提示文字展示时长（单位：秒）
        download: this.propDownload, // 是否下载
        rotateFullscreen: this.propRotateFullscreen, // 样式横屏全屏
        keyShortcut: this.propKeyShortcut, // 能否键盘操控视频进度 音量
        keyShortcutStep: this.propKeyShortcutStep,
        errorTips: this.propErrorTips, // 错误提示
        rotate: this.propRotate, // 视频旋转角度配置 ,
        playNext: {
          // 视频列表 可用于下一集
          urlList: this.playUrlList
        },
        screenShot: {
          // 截图
          saveImg: false,
          quality: 0.92, // 截图质量 默认0.92
          type: 'image/png', // 格式
          format: '.png' // 后缀名
        },
        progressDot: this.propProgressDot, // 进度条标记,
        controlPlugins: [],
        danmu: {
          comments: formattedDanmus, // 弹幕数据
          area: {
            //弹幕显示区域
            start: 0, //区域顶部到播放器顶部所占播放器高度的比例
            end: 1 //区域底部到播放器顶部所占播放器高度的比例
          },
          closeDefaultBtn: false, //开启此项后不使用默认提供的弹幕开关，默认使用西瓜播放器提供的开关
          defaultOff: false, //开启此项后弹幕不会初始化，默认初始化弹幕
          panel: true //开启弹幕面板
        },
        execBeforePluginsCall: [
          () => {
            // 插件失效前执行
            console.log('Execute before plugins call');
          }
        ],
        playbackRate: [0.5, 0.75, 1, 1.5, 2], // 倍速列表
        'x5-video-player-type': 'h5', // 微信同层播放
        'x5-video-player-fullscreen': false, // 微信全屏播放
        playsinline: false, // 内联模式 适用于ios和微信
        airplay: false, // 适用ios设备上视频传送到支持airplay设备播放
        referrerPolicy: 'strict-origin-when-cross-origin' // 设置更严格的 referrer 策略
      });
      if (this.player) {
        console.log('播放器实例：', this.player);
        // 监听视频开始播放 播放传给父组件的是true
        this.player.on('play', () => {
          console.log('play:');
        });
        // 监听视频已经暂停 暂停传给父组件的是true
        this.player.on('pause', () => {
          console.log('pause:');
        });
        // 监听截图事件
        this.player.on(Events.SCREEN_SHOT, (data) => {
          const screenshotBase64 = data.base64; // 获取截图的 base64 数据
          console.log('Screenshot:', screenshotBase64);
        });
      }

      this.player.emit('resourceReady', this.definitionList); // 清晰度列表
    },
    formatDanmus(danmus) {
      return danmus.map((item) => {
        return {
          id: item.id || new Date().getTime(), // 弹幕ID
          txt: item.content || item.text, // 弹幕内容
          duration: 15000, // 弹幕显示时间(毫秒)
          start: item.time || 0, // 弹幕出现的时间点(毫秒)
          color: true, // 是否是彩色弹幕
          style: {
            // 弹幕样式
            color: '#ff9500',
            fontSize: '20px',
            border: 'solid 1px #ff9500',
            borderRadius: '50px',
            padding: '5px 11px',
            backgroundColor: 'rgba(255, 255, 255, 0.1)'
          }
        };
      });
    },
    sendDanmu(type, danmuText = '') {
      if (type == '0') {
        this.danmuText = danmuText;
      }
      if (!this.danmuText.trim()) {
        return;
      }
      // 获取当前播放时间（毫秒）
      let player = this.playType == 'video' ? this.player : this.playType == 'hls' ? this.hlsplayer : this.flvplayer;
      const currentTime = player.currentTime * 1000;

      // 在本地先显示弹幕
      player.danmu.sendComment({
        duration: 15000,
        id: Date.now(), // 使用时间戳作为ID
        start: currentTime,
        prior: true, // 优先显示
        color: true, // 彩色弹幕
        txt: this.danmuText,
        style: {
          color: '#ff9500',
          fontSize: '20px',
          border: 'solid 1px #ff9500',
          borderRadius: '50px',
          padding: '5px 11px',
          backgroundColor: 'rgba(255, 255, 255, 0.1)'
        }
      });

      // // 发送到服务器
      // axios.post('/api/danmu/send', {
      //   token: this.userToken,
      //   content: this.danmuText,
      //   video_id: this.videoId,
      //   time: currentTime
      // })
      // .then(response => {
      //   console.log('弹幕发送成功', response)
      // })
      // .catch(error => {
      //   console.error('弹幕发送失败', error)
      // })

      // 清空输入框
      this.danmuText = '';
    },
    canPlayType() {
      // console.log( this.player.canPlayType('video/mp4; codecs="avc1.64001E, mp4a.40.5"') , 'istrue')
    },
    getVideoInfo() {
      // this.player.on('canplay',  function(e){
      //     console.log('Video duration:', e.video.duration);
      // });
      // this.player.on('play', function(e) {
      //   console.log('play',e);
      // })
      // let pausedOnce = false;
      // this.player.on('timeupdate', function(e) {
      //     console.log(e , '222');
      // console.log('timeupdate',e.video.currentTime);
      // let time = e.video.currentTime;
      // if(time > 8 && !pausedOnce){
      //   e.pause();
      //   pausedOnce = true; // Set the flag to true after pausing
      //   setTimeout(() => {
      //     e.play();
      //   }, 3000);
      // }
      // });
      this.flvplayer.on('definitionChange', function (e) {
        console.log(e, 'cghange');
      });
    },
    setSec(sec) {
      this.player.video.currentTime = sec;
    },
    setProgressDot() {
      // 设置标记
      let setData = {
        time: 6,
        text: '我是披得',
        duration: 4,
        style: {
          color: 'red',
          background: 'white'
        }
      };
      // this.player.addProgressDot(setData.time, setData.text, setData.duration, setData.style) //添加标记（参数含义同上，后3项可选传入）
      // this.player.removeProgressDot(7) //删除标记
      // this.player.removeAllProgressDot() //删除所有标记
    },
    startRecording() {
      console.log(this.$refs.video.querySelector('video').captureStream());
      this.mediaRecorder = null;
      let stream = null;
      if (this.$refs.video.querySelector('video').captureStream) {
        //适用于chrome浏览器
        stream = this.$refs.video.querySelector('video').captureStream();
      } else if (this.$refs.video.querySelector('video').mozCaptureStream) {
        //适用于firefox内核
        stream = this.$refs.video.querySelector('video').mozCaptureStream();
      } else {
        console.log('不支持captureStream，无法录制！');
      }
      // this.mediaRecorder = new RecordRTC(stream, { type: 'video', mimeType: 'video/mp4' });
      this.mediaRecorder.startRecording();
    },
    stopRecording() {
      let that = this;
      that.mediaRecorder.stopRecording(function (e) {
        console.log(e, 'bbbolb');
        let recordedBlobs = that.mediaRecorder.getBlob();
        that.downloadVideo(recordedBlobs);
      });
    },
    downloadVideo(recordedBlobs) {
      const url = URL.createObjectURL(recordedBlobs);
      const downloadLink = document.createElement('a');
      downloadLink.href = url;
      downloadLink.download = 'recording.mp4';
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    }
  },
  beforeDestroy() {}
};
</script>

<style scoped lang="scss">
::v-deep .xgplayer-skin-default .xgplayer-progress-played {
  background-image: linear-gradient(90deg, #3983c5, #0e5591);
}

.video-container {
  width: 100%;
  position: relative;
}

.player-wrapper {
  width: 100%;
  height: 500px;
  background-color: #000;
}
.danmu-input-container {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #1d1d1d;
  position: relative;
}

.danmu-input {
  flex: 1;
  height: 40px;
  box-sizing: border-box;
  border-radius: 18px;
  background-color: #1d1d1d;
  border: solid 1px rgb(88, 88, 89);
  caret-color: #e7e7e7;
  font-size: 15px;
  color: #fff;
  padding: 3px 15px;
}

.danmu-send-btn {
  position: absolute;
  right: 20px;
  width: 60px;
  height: 30px;
  line-height: 30px;
  border-radius: 15px;
  background-color: #d42801;
  color: #fff;
  font-size: 12px;
  border: none;
  cursor: pointer;
}

/* 音量图标颜色 */
.xgplayer-volume-icon {
  filter: brightness(1.5); /* 调节图标亮度 */
}
</style>

<style lang="scss">
.xgplayer-skin-default .xgplayer-drag {
  background: #409eff !important;
}
</style>

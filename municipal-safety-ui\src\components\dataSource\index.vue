<template>
  <div>
    <el-form ref="queryForm" size="small" :inline="true">
      <el-form-item label="数据源" prop="listenTableName">
        <el-select clearable v-model="tn" placeholder="请选择数据源">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.title"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="getList"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { listSource } from "@/api/sbsource.js";
export default {
  data() {
    return {
      options: null,
      tn: null,
    };
  },
  props: ["type"],
  watch: {
    stype() {
      this.getList();
    },
  },
  created() {
    listSource(this.type).then((Res) => {
      this.options = Res.data;
    });
  },
  methods: {
    getList() {
      this.$emit("SearchList", this.tn);
    },
    resetQuery() {
      // 清空查询条件
      this.tn = null;
      // 重新搜索
      this.$emit("SearchList", this.tn);
    },
  },
};
</script>

<style>

</style>

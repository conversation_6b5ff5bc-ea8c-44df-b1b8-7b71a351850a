package org.dromara.projects.domain.bo;

import org.dromara.projects.domain.PrjHazardousItems;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * [项目管理] 列出项目内具体的危险性较大的分部分项工程业务对象 prj_hazardous_items
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjHazardousItems.class, reverseConvertGenerate = false)
public class PrjHazardousItemsBo extends BaseEntity {

    /**
     * 危大工程项ID
     */
    @NotNull(message = "危大工程项ID不能为空", groups = { EditGroup.class })
    private Long itemId;

    /**
     * 所属项目ID (逻辑外键至 prj_projects.project_id)
     */
    @NotNull(message = "所属项目ID (逻辑外键至 prj_projects.project_id)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectId;

    /**
     * 涉危工程清单ID（支持多选逗号隔开）
     */
    private String dangerId;

    /**
     * 危大工程名称/描述
     */
    @NotBlank(message = "危大工程名称/描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String itemName;

    /**
     * 具体范围详情
     */
    private String scopeDetails;

    /**
     * 危大类型 (1:危大, 2:超危大)
     */
    @NotNull(message = "危大类型 (1:危大, 2:超危大)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long dangerListType;

    /**
     * 计划开工日期
     */
    private Date startDate;

    /**
     * 计划竣工日期
     */
    private Date plannedEndDate;

    /**
     * 实际竣工日期
     */
    private Date actualEndDate;

    /**
     * 实际开工日期
     */
    private Date actualStartDate;

    /**
     * 状态
     */
    private String status;

    /**
     * 企业id
     */
    private Long enterpriseId;
}

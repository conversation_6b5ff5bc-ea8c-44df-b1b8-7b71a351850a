{"doc": " 流程分类Mapper接口\n\n <AUTHOR>\n @date 2023-06-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "countCategoryById", "paramTypes": ["java.lang.Long"], "doc": " 统计指定流程分类ID的分类数量\n\n @param categoryId 流程分类ID\n @return 该流程分类ID的分类数量\n"}, {"name": "selectListByParentId", "paramTypes": ["java.lang.Long"], "doc": " 根据父流程分类ID查询其所有子流程分类的列表\n\n @param parentId 父流程分类ID\n @return 包含子流程分类的列表\n"}, {"name": "selectCategoryIdsByParentId", "paramTypes": ["java.lang.Long"], "doc": " 根据父流程分类ID查询包括父ID及其所有子流程分类ID的列表\n\n @param parentId 父流程分类ID\n @return 包含父ID和子流程分类ID的列表\n"}], "constructors": []}
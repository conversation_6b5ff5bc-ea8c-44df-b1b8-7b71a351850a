<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.attendance.mapper.MAttPersonMapper">

    <select id="selectMAttPersonByPersonId" resultType="org.dromara.attendance.domain.vo.MAttPersonVo">
        select * from m_att_person
        where person_id = #{projectPersonnelId} and del_flag = 0
    </select>

    <select id="selectMAttPersonBySnId" resultType="org.dromara.attendance.domain.vo.MAttPersonVo">
        select * from m_att_person
        where sn_id = #{snId} and del_flag = 0
    </select>

    <select id="selectMAttPersonByPersonIdAndSnId" resultType="org.dromara.attendance.domain.vo.MAttPersonVo">
        select * from m_att_person
        where sn_id = #{snId} and person_id = #{personId} and del_flag = 0
    </select>
</mapper>

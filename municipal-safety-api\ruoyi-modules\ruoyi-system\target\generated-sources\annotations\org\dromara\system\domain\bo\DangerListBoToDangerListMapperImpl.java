package org.dromara.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.DangerList;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class DangerListBoToDangerListMapperImpl implements DangerListBoToDangerListMapper {

    @Override
    public DangerList convert(DangerListBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        DangerList dangerList = new DangerList();

        dangerList.setSearchValue( arg0.getSearchValue() );
        dangerList.setCreateDept( arg0.getCreateDept() );
        dangerList.setCreateBy( arg0.getCreateBy() );
        dangerList.setCreateTime( arg0.getCreateTime() );
        dangerList.setUpdateBy( arg0.getUpdateBy() );
        dangerList.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            dangerList.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        dangerList.setDangerId( arg0.getDangerId() );
        dangerList.setName( arg0.getName() );
        dangerList.setPreId( arg0.getPreId() );
        dangerList.setType( arg0.getType() );
        dangerList.setRemark( arg0.getRemark() );

        return dangerList;
    }

    @Override
    public DangerList convert(DangerListBo arg0, DangerList arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setDangerId( arg0.getDangerId() );
        arg1.setName( arg0.getName() );
        arg1.setPreId( arg0.getPreId() );
        arg1.setType( arg0.getType() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}

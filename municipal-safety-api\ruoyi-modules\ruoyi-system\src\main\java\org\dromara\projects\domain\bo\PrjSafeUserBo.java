package org.dromara.projects.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.projects.domain.PrjSafeUser;

/**
 * 【安拆任务】项目人员业务对象 prj_safe_user
 *
 * <AUTHOR> Li
 * @date 2025-08-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjSafeUser.class, reverseConvertGenerate = false)
public class PrjSafeUserBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long saleUserId;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码 不能为空", groups = {AddGroup.class, EditGroup.class})
    private String mobile;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String userName;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String idCard;

    /**
     * 岗位类型
     * 项目经
     * 理:ProjectManager
     * 安全总监:SafetyDirector
     * 安全经
     * 理:SafetyManager
     * 安全员:SafetyOfficer
     * 机管员:MachineKeeper
     */
    @NotBlank(message = "岗位类型 项目经 理:ProjectManager 安全总监:SafetyDirector 安全经 理:SafetyManager 安全员:SafetyOfficer 机管员:MachineKeeper不能为空", groups = {AddGroup.class, EditGroup.class})
    private String positionType;

    /**
     * 关联安拆prj_safe_task.open_task_id
     */
    @NotNull(message = "关联安拆prj_safe_task.open_task_id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long openTaskId;
}

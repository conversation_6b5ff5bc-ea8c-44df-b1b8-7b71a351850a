package org.dromara.attendance.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.attendance.domain.MAttRecord;
import org.dromara.attendance.domain.MAttRecordToMAttRecordVoMapper;
import org.dromara.attendance.domain.MAttRuleToMAttRuleVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {MAttRuleVoToMAttRuleMapper.class,MAttRuleToMAttRuleVoMapper.class,MAttRecordToMAttRecordVoMapper.class},
    imports = {}
)
public interface MAttRecordVoToMAttRecordMapper extends BaseMapper<MAttRecordVo, MAttRecord> {
}

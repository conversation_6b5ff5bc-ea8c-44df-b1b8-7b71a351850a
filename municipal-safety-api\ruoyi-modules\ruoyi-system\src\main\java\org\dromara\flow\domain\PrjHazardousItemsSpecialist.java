package org.dromara.flow.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 省厅自动工单对象 prj_hazardous_items_specialist
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prj_hazardous_items_specialist")
public class PrjHazardousItemsSpecialist extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 专家id（多个用,隔开）
     */
    private String specialist;

    /**
     * 厅局补充说明
     */
    private String instruction;

    /**
     * 厅局下发文件（多个用,隔开）
     */
    private String downPushFile;

    /**
     * 业务id
     */
    private String taskId;

    /**
     * 厅局决策备注
     */
    private String remark;

    /**
     * 关联质监站工单业务id（flow_instance.business_id）
     */
    private String qualityTaskId;

    /**
     * 删除标志 (字典: 0[存在], 1[删除])
     */
    @TableLogic
    private String delFlag;

}

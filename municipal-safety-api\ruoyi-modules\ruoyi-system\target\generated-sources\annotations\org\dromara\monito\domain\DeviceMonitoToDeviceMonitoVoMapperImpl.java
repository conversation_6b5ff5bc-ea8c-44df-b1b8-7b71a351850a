package org.dromara.monito.domain;

import javax.annotation.processing.Generated;
import org.dromara.monito.domain.vo.DeviceMonitoVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class DeviceMonitoToDeviceMonitoVoMapperImpl implements DeviceMonitoToDeviceMonitoVoMapper {

    @Override
    public DeviceMonitoVo convert(DeviceMonito arg0) {
        if ( arg0 == null ) {
            return null;
        }

        DeviceMonitoVo deviceMonitoVo = new DeviceMonitoVo();

        deviceMonitoVo.setMonitoId( arg0.getMonitoId() );
        deviceMonitoVo.setProjectId( arg0.getProjectId() );
        deviceMonitoVo.setItemId( arg0.getItemId() );
        deviceMonitoVo.setDeviceName( arg0.getDeviceName() );
        deviceMonitoVo.setDeviceType( arg0.getDeviceType() );
        deviceMonitoVo.setDeviceCode( arg0.getDeviceCode() );
        deviceMonitoVo.setDeviceStatus( arg0.getDeviceStatus() );
        deviceMonitoVo.setEnableSnapshot( arg0.getEnableSnapshot() );
        deviceMonitoVo.setSnapshotTime( arg0.getSnapshotTime() );
        deviceMonitoVo.setRemarks( arg0.getRemarks() );
        deviceMonitoVo.setChannelNo( arg0.getChannelNo() );

        return deviceMonitoVo;
    }

    @Override
    public DeviceMonitoVo convert(DeviceMonito arg0, DeviceMonitoVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setMonitoId( arg0.getMonitoId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setDeviceName( arg0.getDeviceName() );
        arg1.setDeviceType( arg0.getDeviceType() );
        arg1.setDeviceCode( arg0.getDeviceCode() );
        arg1.setDeviceStatus( arg0.getDeviceStatus() );
        arg1.setEnableSnapshot( arg0.getEnableSnapshot() );
        arg1.setSnapshotTime( arg0.getSnapshotTime() );
        arg1.setRemarks( arg0.getRemarks() );
        arg1.setChannelNo( arg0.getChannelNo() );

        return arg1;
    }
}

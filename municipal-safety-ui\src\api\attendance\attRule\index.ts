import request from '@/utils/request';
import {AxiosPromise} from 'axios';
import {AttRuleForm, AttRuleQuery, AttRuleVO} from '@/api/';

/**
 * 查询考勤规则列表
 * @param query
 * @returns {*}
 */

export const listAttRule = (query?: AttRuleQuery): AxiosPromise<AttRuleVO[]> => {
    return request({
        url: '/attendance/attRule/list',
        method: 'get',
        params: query
    });
};

/**
 * 查询考勤规则详细
 * @param id
 */
export const getAttRule = (id: string | number): AxiosPromise<AttRuleVO> => {
    return request({
        url: '/attendance/attRule/' + id,
        method: 'get'
    });
};

/**
 * 新增考勤规则
 * @param data
 */
export const addAttRule = (data: AttRuleForm) => {
    return request({
        url: '/attendance/attRule',
        method: 'post',
        data: data
    });
};

/**
 * 修改考勤规则
 * @param data
 */
export const updateAttRule = (data: AttRuleForm) => {
    return request({
        url: '/attendance/attRule',
        method: 'put',
        data: data
    });
};

/**
 * 删除考勤规则
 * @param id
 */
export const delAttRule = (id: string | number | Array<string | number>) => {
    return request({
        url: '/attendance/attRule/' + id,
        method: 'delete'
    });
};

export const getProjectList = () => {
  return request({
    url: '/projects/prj_projects/selectAll',
    method: 'get',
  });
};

export const listAttRules = (query?: AttRuleQuery): AxiosPromise<AttRuleVO[]> => {
  return request({
    url: '/attendance/attRule/selectAll',
    method: 'get',
    params: query
  });
};

export const personTypeList = (id: string | number): AxiosPromise<AttRuleVO> => {
  return request({
    url: '/attendance/attRule/roleList/'+id,
    method: 'get',
  });
};

package org.dromara.facility.domain.bo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonAlias;
import org.dromara.facility.domain.JlDustReal;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 扬尘数据业务对象 jl_dust_real
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = JlDustReal.class, reverseConvertGenerate = false)
public class JlDustRealBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 扬尘设备唯一编号
     */
    @JsonAlias("devNo")
    private String mn;

    /**
     * 设备时间
     */
    @JSONField(format = DatePattern.NORM_DATETIME_PATTERN)
    private Date datatime;

    /**
     * 噪音平均值
     */
    private Long b03Avg;

    /**
     * 噪音传感器状态
     */
    private String b03Flag;

    /**
     * pm2.5
     */
    private Long pm25Avg;

    /**
     * pm2.5传感器状态
     */
    private String pm25Flag;

    /**
     * pm10
     */
    private Long pm10Avg;

    /**
     * pm10传感器状态
     */
    private String pm10Flag;

    /**
     * 风速
     */
    private Long w02Avg;

    /**
     * 风速传感器状态
     */
    private String w02Flag;

    /**
     * 风向
     */
    private Long w01Avg;

    /**
     * 风向传感器状态
     */
    private String w01Flag;

    /**
     * 温度
     */
    private Long t01Avg;

    /**
     * 温度传感器状态
     */
    private String t01Flag;

    /**
     * 湿度
     */
    private Long h01Avg;

    /**
     * 湿度传感器状态
     */
    private String h01Flag;

    /**
     * 总悬浮微粒
     */
    private Long tspAvg;

    /**
     * 总悬浮微粒传感器状态
     */
    private String tspFlag;

    /**
     * 臭氧
     */
    private Long o3Avg;

    /**
     * 臭氧状态
     */
    private String o3Flag;

    /**
     * 一氧化碳
     */
    private Long coAvg;

    /**
     * 一氧化碳传感器状态
     */
    private String coFlag;

    /**
     * 二氧化碳
     */
    private Long so2Avg;

    /**
     * 二氧化碳传感器状态
     */
    private String so2Flag;

    /**
     * 二氧化氮
     */
    private Long no2Avg;

    /**
     * 二氧化氮传感器状态
     */
    private String no2Flag;

    /**
     * 大气压
     */
    private Long a01006Rtd;

    /**
     * 大气压传感器状态
     */
    private String a01006Flag;
}

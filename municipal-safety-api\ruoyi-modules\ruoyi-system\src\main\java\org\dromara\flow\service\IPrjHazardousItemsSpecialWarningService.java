package org.dromara.flow.service;

import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialWarningBo;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialWarningVo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 特殊预警Service接口
 *
 * <AUTHOR> Li
 * @date 2025-06-19
 */
public interface IPrjHazardousItemsSpecialWarningService {

    /**
     * 查询特殊预警
     *
     * @param warningId 主键
     * @return 特殊预警
     */
    PrjHazardousItemsSpecialWarningVo queryById(Long warningId);

    /**
     * 分页查询特殊预警列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 特殊预警分页列表
     */
    TableDataInfo<PrjHazardousItemsSpecialWarningVo> queryPageList(PrjHazardousItemsSpecialWarningBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的特殊预警列表
     *
     * @param bo 查询条件
     * @return 特殊预警列表
     */
    List<PrjHazardousItemsSpecialWarningVo> queryList(PrjHazardousItemsSpecialWarningBo bo);

    /**
     * 新增特殊预警
     *
     * @param bo 特殊预警
     * @return 是否新增成功
     */
    Boolean insertByBo(PrjHazardousItemsSpecialWarningBo bo);

    /**
     * 修改特殊预警
     *
     * @param bo 特殊预警
     * @return 是否修改成功
     */
    Boolean updateByBo(PrjHazardousItemsSpecialWarningBo bo);

    /**
     * 校验并批量删除特殊预警信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

package org.dromara.expert.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 专家领域对象 z_field
 * @date 2025-05-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("z_field")
public class Field extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 领域id
     */
    @TableId(value = "field_id")
    private Long fieldId;

    /**
     * 领域名称
     */
    private String name;

    /**
     * 领域简介
     */
    private String description;

    /**
     * 0 正常 1 删除
     */
    @TableLogic(value = "0", delval = "1")
    private String delFlag;

    /**
     * 专家id
     */
    private Long expertId;


}

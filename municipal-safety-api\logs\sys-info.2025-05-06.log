2025-05-06 09:03:00 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-06 09:03:00 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 22668 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-06 09:03:00 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-06 09:03:06 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-06 09:03:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-06 09:03:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-06 09:03:07 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@771ede0d
2025-05-06 09:03:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-06 09:03:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-06 09:03:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-06 09:03:09 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-06 09:03:09 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-06 09:03:09 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-06 09:03:09 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-06 09:03:10 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-06 17:26:29 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-06 17:26:33 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-06 17:26:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-06 17:26:34 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-06 17:26:35 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1fd73dcb
2025-05-06 17:26:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-06 17:26:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-06 17:26:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-06 17:26:37 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-06 17:26:37 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-06 17:26:37 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-06 17:26:37 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-06 17:26:38 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-06 17:35:16 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-05-06 17:35:23 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-06 17:35:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-06 17:35:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-06 17:35:24 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@45775a15
2025-05-06 17:35:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-06 17:35:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-06 17:35:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-06 17:35:26 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-06 17:35:26 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-06 17:35:26 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-06 17:35:26 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-06 17:35:28 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>
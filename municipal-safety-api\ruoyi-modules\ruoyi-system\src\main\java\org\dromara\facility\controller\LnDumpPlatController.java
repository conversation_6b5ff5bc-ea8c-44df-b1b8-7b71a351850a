package org.dromara.facility.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.facility.domain.vo.LnDumpPlatVo;
import org.dromara.facility.domain.bo.LnDumpPlatBo;
import org.dromara.facility.service.ILnDumpPlatService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 绿能卸料平台
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/lnDumpPlat")
public class LnDumpPlatController extends BaseController {

    private final ILnDumpPlatService lnDumpPlatService;

    /**
     * 查询绿能卸料平台列表
     */
    @GetMapping("/list")
    public TableDataInfo<LnDumpPlatVo> list(LnDumpPlatBo bo, PageQuery pageQuery) {
        return lnDumpPlatService.queryPageList(bo, pageQuery);
    }
}

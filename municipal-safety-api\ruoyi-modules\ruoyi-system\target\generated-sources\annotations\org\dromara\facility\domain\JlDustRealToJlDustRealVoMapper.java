package org.dromara.facility.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.bo.JlDustRealBoToJlDustRealMapper;
import org.dromara.facility.domain.vo.JlDustRealVo;
import org.dromara.facility.domain.vo.JlDustRealVoToJlDustRealMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {JlDustRealBoToJlDustRealMapper.class,JlDustRealVoToJlDustRealMapper.class},
    imports = {}
)
public interface JlDustRealToJlDustRealVoMapper extends BaseMapper<JlDustReal, JlDustRealVo> {
}

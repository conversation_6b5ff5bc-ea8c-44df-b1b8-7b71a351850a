<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.ai.mapper.AiHazAnalysisTasksMapper">

    <!-- 定义resultMap -->
    <resultMap id="AiHazAnalysisTasksResult" type="org.dromara.ai.domain.vo.AiHazAnalysisTasksVo">
        <id property="taskId" column="task_id"/>
        <result property="projectId" column="project_id"/>
        <result property="sourceType" column="source_type"/>
        <result property="expertUserId" column="expert_user_id"/>
        <result property="uploadTime" column="upload_time"/>
        <result property="photoDocumentId" column="photo_document_id"/>
        <result property="aiPhotoDocumentId" column="ai_photo_document_id"/>
        <result property="gpsLocation" column="gps_location"/>
        <result property="locationDescription" column="location_description"/>
        <result property="aiRecognitionRawResult" column="ai_recognition_raw_result"/>
        <result property="itemId" column="item_id"/>
        <result property="status" column="status"/>
        <result property="projectName" column="project_name"/>
        <result property="dangerId" column="danger_id"/>
        <result property="dangerListType" column="danger_list_type"/>
        <result property="itemName" column="item_name"/>
    </resultMap>

    <!-- 多表联查分页查询 -->
    <select id="selectAppPageList" resultMap="AiHazAnalysisTasksResult">
        SELECT
            t.task_id,
            t.location_description,
            t.upload_time,
            t.status,
            t.source_type,
            t.project_id,
            p.project_name,
            i.danger_id,
            i.danger_list_type,
            i.item_name
        FROM ai_haz_analysis_tasks t
        LEFT JOIN prj_projects p ON t.project_id = p.project_id
        LEFT JOIN prj_hazardous_items i ON t.item_id = i.item_id
        <where>
            <if test="dto.type != null and dto.type == 'project' and dto.projectName != null and dto.projectName != ''">
                AND p.project_name LIKE CONCAT('%', #{dto.projectName}, '%')
            </if>
            <if test="dto.type != null and dto.type == 'item' and dto.itemName != null and dto.itemName != ''">
                AND i.item_name LIKE CONCAT('%', #{dto.itemName}, '%')
            </if>
            <if test="dto.itemId != null">
                AND t.item_id = #{dto.itemId}
            </if>
            <if test="dto.status != null and dto.status != ''">
                AND t.status = #{dto.status}
            </if>
            and t.del_flag = 0 and p.del_flag = 0 and i.del_flag = 0
        </where>
        ORDER BY t.create_time DESC
    </select>

    <select id="selectAIPageList" resultMap="AiHazAnalysisTasksResult">
        SELECT
        t.task_id,
        t.location_description,
        t.upload_time,
        t.status,
        t.source_type,
        p.project_name,
        i.danger_id,
        i.danger_list_type,
        i.item_name
        FROM ai_haz_analysis_tasks t
        LEFT JOIN prj_projects p ON t.project_id = p.project_id
        LEFT JOIN prj_hazardous_items i ON t.item_id = i.item_id
        <where>
            <if test="dto.type != null and dto.type == 'project' and dto.projectName != null and dto.projectName != ''">
                AND p.project_name LIKE CONCAT('%', #{dto.projectName}, '%')
            </if>
            <if test="dto.type != null and dto.type == 'item' and dto.itemName != null and dto.itemName != ''">
                AND i.item_name LIKE CONCAT('%', #{dto.itemName}, '%')
            </if>
            <if test="dto.itemId != null">
                AND t.item_id = #{dto.itemId}
            </if>
            <if test="dto.createPeopleList != null and dto.createPeopleList.size() > 0">
                AND t.create_by IN
                <foreach item="item" collection="dto.createPeopleList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            and t.del_flag = 0 and p.del_flag = 0 and i.del_flag = 0
        </where>
        ORDER BY t.create_time DESC
    </select>

</mapper>

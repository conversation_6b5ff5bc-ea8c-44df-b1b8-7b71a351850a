{"doc": " 省厅自动工单对象 prj_hazardous_items_specialist\n\n <AUTHOR>\n @date 2025-06-20\n", "fields": [{"name": "id", "doc": " 主键\n"}, {"name": "specialist", "doc": " 专家id（多个用,隔开）\n"}, {"name": "instruction", "doc": " 厅局补充说明\n"}, {"name": "downPushFile", "doc": " 厅局下发文件（多个用,隔开）\n"}, {"name": "taskId", "doc": " 业务id\n"}, {"name": "remark", "doc": " 厅局决策备注\n"}, {"name": "qualityTaskId", "doc": " 关联质监站工单业务id（flow_instance.business_id）\n"}, {"name": "delFlag", "doc": " 删除标志 (字典: 0[存在], 1[删除])\n"}], "enumConstants": [], "methods": [], "constructors": []}
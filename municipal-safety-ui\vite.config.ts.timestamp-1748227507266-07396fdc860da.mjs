// vite.config.ts
import { defineConfig, loadEnv } from "file:///G:/WORK2/municipal-safety-ui/node_modules/vite/dist/node/index.js";

// vite/plugins/index.ts
import vue from "file:///G:/WORK2/municipal-safety-ui/node_modules/@vitejs/plugin-vue/dist/index.mjs";

// vite/plugins/unocss.ts
import UnoCss from "file:///G:/WORK2/municipal-safety-ui/node_modules/unocss/dist/vite.mjs";
var unocss_default = () => {
  return UnoCss({
    hmrTopLevelAwait: false
    // unocss默认是true，低版本浏览器是不支持的，启动后会报错
  });
};

// vite/plugins/auto-import.ts
import AutoImport from "file:///G:/WORK2/municipal-safety-ui/node_modules/unplugin-auto-import/dist/vite.js";
import { ElementPlusResolver } from "file:///G:/WORK2/municipal-safety-ui/node_modules/unplugin-vue-components/dist/resolvers.js";
var __vite_injected_original_dirname = "G:\\WORK2\\municipal-safety-ui\\vite\\plugins";
var auto_import_default = (path3) => {
  return AutoImport({
    // 自动导入 Vue 相关函数
    imports: ["vue", "vue-router", "@vueuse/core", "pinia"],
    eslintrc: {
      enabled: true,
      filepath: "./.eslintrc-auto-import.json",
      globalsPropValue: true
    },
    resolvers: [
      // 自动导入 Element Plus 相关函数ElMessage, ElMessageBox... (带样式)
      ElementPlusResolver()
    ],
    vueTemplate: true,
    // 是否在 vue 模板中自动导入
    dts: path3.resolve(path3.resolve(__vite_injected_original_dirname, "../../src"), "types", "auto-imports.d.ts")
  });
};

// vite/plugins/components.ts
import Components from "file:///G:/WORK2/municipal-safety-ui/node_modules/unplugin-vue-components/dist/vite.js";
import { ElementPlusResolver as ElementPlusResolver2 } from "file:///G:/WORK2/municipal-safety-ui/node_modules/unplugin-vue-components/dist/resolvers.js";
import IconsResolver from "file:///G:/WORK2/municipal-safety-ui/node_modules/unplugin-icons/dist/resolver.js";
var __vite_injected_original_dirname2 = "G:\\WORK2\\municipal-safety-ui\\vite\\plugins";
var components_default = (path3) => {
  return Components({
    resolvers: [
      // 自动导入 Element Plus 组件
      ElementPlusResolver2(),
      // 自动注册图标组件
      IconsResolver({
        enabledCollections: ["ep"]
      })
    ],
    dts: path3.resolve(path3.resolve(__vite_injected_original_dirname2, "../../src"), "types", "components.d.ts")
  });
};

// vite/plugins/icons.ts
import Icons from "file:///G:/WORK2/municipal-safety-ui/node_modules/unplugin-icons/dist/vite.js";
var icons_default = () => {
  return Icons({
    // 自动安装图标库
    autoInstall: true
  });
};

// vite/plugins/svg-icon.ts
import { createSvgIconsPlugin } from "file:///G:/WORK2/municipal-safety-ui/node_modules/vite-plugin-svg-icons-ng/dist/index.mjs";
var __vite_injected_original_dirname3 = "G:\\WORK2\\municipal-safety-ui\\vite\\plugins";
var svg_icon_default = (path3) => {
  return createSvgIconsPlugin({
    // 指定需要缓存的图标文件夹
    iconDirs: [path3.resolve(path3.resolve(__vite_injected_original_dirname3, "../../src"), "assets/icons/svg")],
    // 指定symbolId格式
    symbolId: "icon-[dir]-[name]"
  });
};

// vite/plugins/compression.ts
import compression from "file:///G:/WORK2/municipal-safety-ui/node_modules/vite-plugin-compression/dist/index.mjs";
var compression_default = (env) => {
  const { VITE_BUILD_COMPRESS } = env;
  const plugin = [];
  if (VITE_BUILD_COMPRESS) {
    const compressList = VITE_BUILD_COMPRESS.split(",");
    if (compressList.includes("gzip")) {
      plugin.push(
        compression({
          ext: ".gz",
          deleteOriginFile: false
        })
      );
    }
    if (compressList.includes("brotli")) {
      plugin.push(
        compression({
          ext: ".br",
          algorithm: "brotliCompress",
          deleteOriginFile: false
        })
      );
    }
  }
  return plugin;
};

// vite/plugins/setup-extend.ts
import setupExtend from "file:///G:/WORK2/municipal-safety-ui/node_modules/unplugin-vue-setup-extend-plus/dist/vite.js";
var setup_extend_default = () => {
  return setupExtend({});
};

// vite/plugins/index.ts
import path from "path";
var plugins_default = (viteEnv, isBuild = false) => {
  const vitePlugins = [];
  vitePlugins.push(vue());
  vitePlugins.push(unocss_default());
  vitePlugins.push(auto_import_default(path));
  vitePlugins.push(components_default(path));
  vitePlugins.push(compression_default(viteEnv));
  vitePlugins.push(icons_default());
  vitePlugins.push(svg_icon_default(path));
  vitePlugins.push(setup_extend_default());
  return vitePlugins;
};

// vite.config.ts
import autoprefixer from "file:///G:/WORK2/municipal-safety-ui/node_modules/autoprefixer/lib/autoprefixer.js";
import path2 from "path";
var __vite_injected_original_dirname4 = "G:\\WORK2\\municipal-safety-ui";
var vite_config_default = defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: env.VITE_APP_CONTEXT_PATH,
    resolve: {
      alias: {
        "@": path2.resolve(__vite_injected_original_dirname4, "./src")
      },
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"]
    },
    // https://cn.vitejs.dev/config/#resolve-extensions
    plugins: plugins_default(env, command === "build"),
    server: {
      host: "0.0.0.0",
      port: Number(env.VITE_APP_PORT),
      open: true,
      proxy: {
        [env.VITE_APP_BASE_API]: {
          target: "http://***************:8080",
          changeOrigin: true,
          ws: true,
          rewrite: (path3) => path3.replace(new RegExp("^" + env.VITE_APP_BASE_API), "")
        }
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          // additionalData: '@use "@/assets/styles/variables.module.scss as *";'
          // javascriptEnabled: true
          api: "modern-compiler"
        }
      },
      postcss: {
        plugins: [
          // 浏览器兼容性
          autoprefixer(),
          {
            postcssPlugin: "internal:charset-removal",
            AtRule: {
              charset: (atRule) => {
                atRule.remove();
              }
            }
          }
        ]
      }
    },
    // 预编译
    optimizeDeps: {
      include: [
        "vue",
        "vue-router",
        "pinia",
        "axios",
        "@vueuse/core",
        "echarts",
        "vue-i18n",
        "@vueup/vue-quill",
        "image-conversion",
        "element-plus/es/components/**/css"
      ]
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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

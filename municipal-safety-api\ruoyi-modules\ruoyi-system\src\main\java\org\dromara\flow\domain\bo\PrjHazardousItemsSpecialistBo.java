package org.dromara.flow.domain.bo;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.flow.domain.PrjHazardousItemsSpecialist;

/**
 * 省厅自动工单业务对象 prj_hazardous_items_specialist
 *
 * <AUTHOR> Li
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjHazardousItemsSpecialist.class, reverseConvertGenerate = false)
public class PrjHazardousItemsSpecialistBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 专家id（多个用,隔开）
     */
    private String specialist;

    /**
     * 厅局补充说明
     */
    private String instruction;

    /**
     * 厅局下发文件（多个用,隔开）
     */
    private String downPushFile;

    /**
     * 业务id
     */
    private String taskId;

    /**
     * 隐患id
     */
    private String aiHazAnalysisId;
}

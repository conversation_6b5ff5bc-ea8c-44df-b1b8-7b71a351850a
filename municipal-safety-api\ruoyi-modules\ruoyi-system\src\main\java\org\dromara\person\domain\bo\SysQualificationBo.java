package org.dromara.person.domain.bo;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.Date;

import org.dromara.person.domain.SysQualification;

/**
 * 人员资格证书业务对象 sys_qualification
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysQualification.class, reverseConvertGenerate = false)
public class SysQualificationBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long qualificationId;

    /**
     * 关联人员ID
     */
    @NotNull(message = "关联人员ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long personId;

    /**
     * 证书种类
     */
    @NotBlank(message = "证书种类不能为空", groups = {AddGroup.class, EditGroup.class})
    private String certificateType;

    /**
     * 证书名称
     */
    @NotBlank(message = "证书名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String certificateName;

    /**
     * 证书编号
     */
    @NotBlank(message = "证书编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String certificateNumber;

    /**
     * 获取时间
     */
    @NotNull(message = "获取时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date acquisitionTime;

    /**
     * 发证机关
     */
    @NotBlank(message = "发证机关不能为空", groups = {AddGroup.class, EditGroup.class})
    private String issuingAuthority;

    /**
     * 证书等级
     */
    @NotBlank(message = "证书等级不能为空", groups = {AddGroup.class, EditGroup.class})
    private String certificateLevel;

    /**
     * 资质对应岗位
     */
    @NotBlank(message = "资质对应岗位不能为空", groups = {AddGroup.class, EditGroup.class})
    private String correspondingPosition;

    /**
     * 有效期起始
     */
    @NotNull(message = "有效期起始不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date validFrom;

    /**
     * 有效期结束
     */
    @NotNull(message = "有效期结束不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date validTo;

    /**
     * 证件照片路径
     */
    private Long uploadPhoto;
}

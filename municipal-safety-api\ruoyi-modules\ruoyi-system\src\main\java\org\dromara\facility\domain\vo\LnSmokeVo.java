package org.dromara.facility.domain.vo;

import java.util.Date;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.facility.domain.LnSmoke;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 绿能烟感视图对象 ln_smoke
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LnSmoke.class)
public class LnSmokeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 编号
     */
    @ExcelProperty(value = "编号")
    private String deviceNo;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 事件类型
     */
    @ExcelProperty(value = "事件类型")
    private String eventCode;

    /**
     * 上报时间
     */
    @ExcelProperty(value = "上报时间")
    private String eventContent;

    /**
     * 上报时间
     */
    @ExcelProperty(value = "上报时间")
    private Date eventTime;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String devNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

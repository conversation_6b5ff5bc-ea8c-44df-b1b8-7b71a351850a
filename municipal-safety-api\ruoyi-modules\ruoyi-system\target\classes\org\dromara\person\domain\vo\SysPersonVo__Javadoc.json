{"doc": " 人员基本信息视图对象 sys_person\n\n <AUTHOR> zu <PERSON>\n @date 2025-05-09\n", "fields": [{"name": "personId", "doc": " 人员ID\n"}, {"name": "enterpriseId", "doc": " 企业ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "name", "doc": " 姓名\n"}, {"name": "idCard", "doc": " 身份证号码\n"}, {"name": "phone", "doc": " 手机号码\n"}, {"name": "nativePlace", "doc": " 籍贯\n"}, {"name": "gender", "doc": " 性别\n"}, {"name": "politicalStatus", "doc": " 政治面貌（如群众、党员）\n"}, {"name": "education", "doc": " 文化程度\n"}, {"name": "headImgId", "doc": " 头像图片id\n"}, {"name": "headImgUrl", "doc": "  头像地址\n"}, {"name": "enterpriseName", "doc": " 企业名称\n"}, {"name": "unifiedSocialCreditCode", "doc": " 企业信用代码\n"}, {"name": "qualificationDetail", "doc": " 证书类型\n"}, {"name": "qualificationDictVos", "doc": " 证书列表\n"}], "enumConstants": [], "methods": [], "constructors": []}
package org.dromara.person.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.person.domain.SysQualification;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class SysQualificationVoToSysQualificationMapperImpl implements SysQualificationVoToSysQualificationMapper {

    @Override
    public SysQualification convert(SysQualificationVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysQualification sysQualification = new SysQualification();

        sysQualification.setQualificationId( arg0.getQualificationId() );
        sysQualification.setPersonId( arg0.getPersonId() );
        sysQualification.setCertificateType( arg0.getCertificateType() );
        sysQualification.setCertificateName( arg0.getCertificateName() );
        sysQualification.setCertificateNumber( arg0.getCertificateNumber() );
        sysQualification.setAcquisitionTime( arg0.getAcquisitionTime() );
        sysQualification.setIssuingAuthority( arg0.getIssuingAuthority() );
        sysQualification.setCertificateLevel( arg0.getCertificateLevel() );
        sysQualification.setCorrespondingPosition( arg0.getCorrespondingPosition() );
        sysQualification.setValidFrom( arg0.getValidFrom() );
        sysQualification.setValidTo( arg0.getValidTo() );
        sysQualification.setUploadPhoto( arg0.getUploadPhoto() );

        return sysQualification;
    }

    @Override
    public SysQualification convert(SysQualificationVo arg0, SysQualification arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setQualificationId( arg0.getQualificationId() );
        arg1.setPersonId( arg0.getPersonId() );
        arg1.setCertificateType( arg0.getCertificateType() );
        arg1.setCertificateName( arg0.getCertificateName() );
        arg1.setCertificateNumber( arg0.getCertificateNumber() );
        arg1.setAcquisitionTime( arg0.getAcquisitionTime() );
        arg1.setIssuingAuthority( arg0.getIssuingAuthority() );
        arg1.setCertificateLevel( arg0.getCertificateLevel() );
        arg1.setCorrespondingPosition( arg0.getCorrespondingPosition() );
        arg1.setValidFrom( arg0.getValidFrom() );
        arg1.setValidTo( arg0.getValidTo() );
        arg1.setUploadPhoto( arg0.getUploadPhoto() );

        return arg1;
    }
}

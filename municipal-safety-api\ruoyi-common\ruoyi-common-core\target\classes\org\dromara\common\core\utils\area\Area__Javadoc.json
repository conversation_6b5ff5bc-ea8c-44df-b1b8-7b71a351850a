{"doc": " 区域节点，包括国家、省份、城市、地区等信息\n\n 数据可见 resources/area.csv 文件\n\n <AUTHOR>\n", "fields": [{"name": "ID_GLOBAL", "doc": " 编号 - 全球，即根目录\n"}, {"name": "ID_CHINA", "doc": " 编号 - 中国\n"}, {"name": "id", "doc": " 编号\n"}, {"name": "name", "doc": " 名字\n"}, {"name": "type", "doc": " 类型\n\n 枚举 {@link AreaTypeEnum}\n"}, {"name": "parent", "doc": " 父节点\n"}, {"name": "children", "doc": " 子节点\n"}], "enumConstants": [], "methods": [], "constructors": []}
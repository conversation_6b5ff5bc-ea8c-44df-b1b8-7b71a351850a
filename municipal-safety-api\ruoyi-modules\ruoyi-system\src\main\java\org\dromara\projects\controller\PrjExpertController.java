package org.dromara.projects.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.projects.domain.bo.ExpertFullBo;
import org.dromara.projects.domain.vo.PrjExpertDetailVo;
import org.dromara.projects.service.IPrjExpertReviewsService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/6/5 14:28
 * @Description TODO
 * @Version 1.0
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/expertReview")
public class PrjExpertController extends BaseController {

    private final IPrjExpertReviewsService prjExpertReviewsService;

    /**
     * 保存专家施工方案
     */
    @PostMapping("/save")
    public R<Boolean> save(@RequestBody ExpertFullBo expertBo) {
        return R.ok(prjExpertReviewsService.save(expertBo));
    }
    /**
     * 获取专家施工方案详细信息
     *
     * @param planId 施工方案id
     * @return R<PrjExpertDetailVo>
     */
    @GetMapping("/detail/{planId}")
    public R<PrjExpertDetailVo> detail(@PathVariable Long planId) {
        return R.ok(prjExpertReviewsService.getDetail(planId));
    }
}

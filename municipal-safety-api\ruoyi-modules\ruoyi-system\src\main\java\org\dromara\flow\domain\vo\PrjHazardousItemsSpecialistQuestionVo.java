package org.dromara.flow.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.flow.domain.PrjHazardousItemsSpecialistQuestion;

import java.io.Serial;
import java.io.Serializable;


/**
 * 省厅自动工单专家建议视图对象 prj_hazardous_items_specialist_question
 *
 * <AUTHOR> Li
 * @date 2025-06-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjHazardousItemsSpecialistQuestion.class)
public class PrjHazardousItemsSpecialistQuestionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 专家名称
     */
    @ExcelProperty(value = "专家名称")
    private String name;

    /**
     * 专家意见
     */
    @ExcelProperty(value = "专家意见")
    private String detail;

    /**
     * 问题id(ai_haz_analysis_tasks_result.id)
     */
    @ExcelProperty(value = "问题id(ai_haz_analysis_tasks_result.id)")
    private Long resultId;

    /**
     * 省厅工单数据id(prj_hazardous_items_specialist.id)
     */
    @ExcelProperty(value = "省厅工单数据id(prj_hazardous_items_specialist.id)")
    private Long specialist;
}

package org.dromara.special.domain.sync;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NonNull;

@Data
public class SyncEntity {

    /**  1 同步人员信息 2 同步特种设备信息*/
    @NotNull(message = "同步类型不能为空")
    private String type;

    /** 施工许可证件号 */
    @NotNull(message = "施工许可证件号不能为空")
    private String constructionPermitNo;

    /** 项目id */
    @NotNull(message = "项目id不能为空")
    private Long projectId;
}

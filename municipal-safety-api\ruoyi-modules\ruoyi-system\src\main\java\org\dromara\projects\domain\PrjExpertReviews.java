package org.dromara.projects.domain;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import java.io.Serial;

/**
 * [项目管理] 记录专项施工方案专家论证会议，包含利害关系预警对象 prj_expert_reviews
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prj_expert_reviews")
public class PrjExpertReviews extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 专家论证ID
     */
    @TableId(value = "review_id")
    private Long reviewId;

    /**
     * 施工方案ID (逻辑外键至 prj_construction_plans.plan_id)
     */
    private Long planId;

    /**
     * 论证会议日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date reviewDate;

    /**
     * 会议地点
     */
    private String meetingLocation;

    /**
     * 论证结论 (通过, 修改后通过, 不通过)
     */
    private String conclusion;

    /**
     * 专家意见摘要/修改要求
     */
    private String expertOpinionSummary;

    /**
     * 专家利害关系预警信息 (对应附件四.(二).1)
     */
    private String conflictOfInterestWarning;

    /**
     * 论证报告文档ID (逻辑外键至 sys_documents.document_id)
     */
    private Long reportDocumentId;

    /**
     * 会议组织者ID (逻辑外键至 sys_users.user_id)
     */
    private Long convenorUserId;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;
}

package org.dromara.person.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.person.domain.vo.QualificationDictVo;
import org.dromara.person.domain.bo.QualificationDictBo;
import org.dromara.person.service.IQualificationDictService;

/**
 * 人员证书属性类型
 *
 * <AUTHOR> Li
 * @date 2025-05-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/person/qualificationDict")
public class QualificationDictController extends BaseController {

    private final IQualificationDictService qualificationDictService;

    /**
     * 查询人员证书属性类型列表
     */
//    @SaCheckPermission("person:qualificationDict:list")
    @GetMapping("/list")
    public R<List<QualificationDictVo>> list(QualificationDictBo bo) {
        List<QualificationDictVo> list = qualificationDictService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 导出人员证书属性类型列表
     */
    @SaCheckPermission("person:qualificationDict:export")
    @Log(title = "人员证书属性类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QualificationDictBo bo, HttpServletResponse response) {
        List<QualificationDictVo> list = qualificationDictService.queryList(bo);
        ExcelUtil.exportExcel(list, "人员证书属性类型", QualificationDictVo.class, response);
    }

    /**
     * 获取人员证书属性类型详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("person:qualificationDict:query")
    @GetMapping("/{id}")
    public R<QualificationDictVo> getInfo(@NotNull(message = "主键不能为空")
                                          @PathVariable Long id) {
        return R.ok(qualificationDictService.queryById(id));
    }

    /**
     * 新增人员证书属性类型
     */
    @SaCheckPermission("person:qualificationDict:add")
    @Log(title = "人员证书属性类型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QualificationDictBo bo) {
        return toAjax(qualificationDictService.insertByBo(bo));
    }

    /**
     * 修改人员证书属性类型
     */
    @SaCheckPermission("person:qualificationDict:edit")
    @Log(title = "人员证书属性类型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QualificationDictBo bo) {
        return toAjax(qualificationDictService.updateByBo(bo));
    }

    /**
     * 删除人员证书属性类型
     *
     * @param ids 主键串
     */
    @SaCheckPermission("person:qualificationDict:remove")
    @Log(title = "人员证书属性类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(qualificationDictService.deleteWithValidByIds(List.of(ids), true));
    }
}

package org.dromara.quality.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.quality.domain.vo.*;
import org.dromara.quality.service.IQualityOverviewService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 质量管理概览Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/quality/overview")
public class QualityOverviewController extends BaseController {

    private final IQualityOverviewService qualityOverviewService;

    /**
     * 获取质量管理概览数据
     */
    @SaCheckPermission("quality:overview:view")
    @GetMapping()
    public R<QualityOverviewVo> getOverviewData() {
        QualityOverviewVo data = qualityOverviewService.getOverviewData();
        return R.ok(data);
    }

    /**
     * 获取设备统计数据
     */
    @SaCheckPermission("quality:overview:view")
    @GetMapping("/device-statistics")
    public R<DeviceStatisticsVo> getDeviceStatistics() {
        DeviceStatisticsVo data = qualityOverviewService.getDeviceStatistics();
        return R.ok(data);
    }

    /**
     * 获取测量统计数据
     */
    @SaCheckPermission("quality:overview:view")
    @GetMapping("/measurement-statistics")
    public R<MeasurementStatisticsVo> getMeasurementStatistics() {
        MeasurementStatisticsVo data = qualityOverviewService.getMeasurementStatistics();
        return R.ok(data);
    }

    /**
     * 获取测量趋势图表数据
     *
     * @param period    时间周期：week/month/quarter
     * @param startDate 开始日期 (YYYY-MM-DD)
     * @param endDate   结束日期 (YYYY-MM-DD)
     */
    @SaCheckPermission("quality:overview:view")
    @GetMapping("/chart-data")
    public R<ChartDataVo> getChartData(@RequestParam String period,
                                       @RequestParam(required = false) String startDate,
                                       @RequestParam(required = false) String endDate) {
        ChartDataVo data = qualityOverviewService.getChartData(period, startDate, endDate);
        return R.ok(data);
    }

    /**
     * 获取设备状态分布数据
     */
    @SaCheckPermission("quality:overview:view")
    @GetMapping("/device-status-distribution")
    public R<DeviceStatusDistributionVo> getDeviceStatusDistribution() {
        DeviceStatusDistributionVo data = qualityOverviewService.getDeviceStatusDistribution();
        return R.ok(data);
    }
}

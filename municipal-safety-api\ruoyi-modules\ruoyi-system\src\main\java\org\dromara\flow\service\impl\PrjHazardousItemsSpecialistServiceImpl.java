package org.dromara.flow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.expert.domain.Expert;
import org.dromara.expert.domain.vo.ExpertVo;
import org.dromara.expert.mapper.ExpertMapper;
import org.dromara.flow.domain.PrjHazardousItemsFile;
import org.dromara.flow.domain.PrjHazardousItemsSpecialist;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistBo;
import org.dromara.flow.domain.dto.SpecialistThreeDTO;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistVo;
import org.dromara.flow.domain.vo.SpecialistThreeVO;
import org.dromara.flow.domain.vo.SpecialistUserVO;
import org.dromara.flow.mapper.PrjHazardousItemsFileMapper;
import org.dromara.flow.mapper.PrjHazardousItemsSpecialistMapper;
import org.dromara.flow.service.IPrjHazardousItemsSpecialistService;
import org.dromara.system.domain.SysUser;
import org.dromara.system.mapper.SysUserMapper;
import org.dromara.warm.flow.core.enums.NodeType;
import org.dromara.warm.flow.orm.entity.FlowInstance;
import org.dromara.warm.flow.orm.mapper.FlowInstanceMapper;
import org.dromara.workflow.domain.bo.FlowTaskBo;
import org.dromara.workflow.domain.vo.FlowHisTaskVo;
import org.dromara.workflow.domain.vo.FlowTaskVo;
import org.dromara.workflow.mapper.FlwTaskMapper;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 省厅自动工单Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-06-20
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PrjHazardousItemsSpecialistServiceImpl implements IPrjHazardousItemsSpecialistService {

    private final PrjHazardousItemsSpecialistMapper baseMapper;
    private final ExpertMapper expertMapper;
    private final SysUserMapper sysUserMapper;
    private final PrjHazardousItemsFileMapper prjHazardousItemsFileMapper;
    private final FlowInstanceMapper flowInstanceMapper;
    private final FlwTaskMapper flwTaskMapper;

    /**
     * 查询省厅自动工单
     *
     * @param id 主键
     * @return 省厅自动工单
     */
    @Override
    public PrjHazardousItemsSpecialistVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询省厅自动工单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 省厅自动工单分页列表
     */
    @Override
    public TableDataInfo<PrjHazardousItemsSpecialistVo> queryPageList(PrjHazardousItemsSpecialistBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PrjHazardousItemsSpecialist> lqw = buildQueryWrapper(bo);
        Page<PrjHazardousItemsSpecialistVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的省厅自动工单列表
     *
     * @param bo 查询条件
     * @return 省厅自动工单列表
     */
    @Override
    public List<PrjHazardousItemsSpecialistVo> queryList(PrjHazardousItemsSpecialistBo bo) {
        LambdaQueryWrapper<PrjHazardousItemsSpecialist> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PrjHazardousItemsSpecialist> buildQueryWrapper(PrjHazardousItemsSpecialistBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PrjHazardousItemsSpecialist> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PrjHazardousItemsSpecialist::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getSpecialist()), PrjHazardousItemsSpecialist::getSpecialist, bo.getSpecialist());
        lqw.eq(StringUtils.isNotBlank(bo.getInstruction()), PrjHazardousItemsSpecialist::getInstruction, bo.getInstruction());
        lqw.eq(StringUtils.isNotBlank(bo.getDownPushFile()), PrjHazardousItemsSpecialist::getDownPushFile, bo.getDownPushFile());
        lqw.eq(StringUtils.isNotBlank(bo.getTaskId()), PrjHazardousItemsSpecialist::getTaskId, bo.getTaskId());
        return lqw;
    }

    /**
     * 新增省厅自动工单
     *
     * @param bo 省厅自动工单
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PrjHazardousItemsSpecialistBo bo) {
        PrjHazardousItemsSpecialist add = MapstructUtils.convert(bo, PrjHazardousItemsSpecialist.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改省厅自动工单
     *
     * @param bo 省厅自动工单
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PrjHazardousItemsSpecialistBo bo) {
        PrjHazardousItemsSpecialist update = MapstructUtils.convert(bo, PrjHazardousItemsSpecialist.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PrjHazardousItemsSpecialist entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除省厅自动工单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 获取详情信息
     *
     * @param taskId
     * @return
     */
    @Override
    public PrjHazardousItemsSpecialistVo getDetail(String taskId) {

        LambdaQueryWrapper<PrjHazardousItemsSpecialist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PrjHazardousItemsSpecialist::getTaskId, taskId);

        PrjHazardousItemsSpecialistVo prjHazardousItemsSpecialistVo = baseMapper.selectVoOne(wrapper);

        if (prjHazardousItemsSpecialistVo != null) {

            List<Long> userIds = Arrays.stream(prjHazardousItemsSpecialistVo.getSpecialist().split(",")).map(Long::valueOf).toList();

            List<SysUser> sysUsers = sysUserMapper.selectByIds(userIds);

            List<String> idcardList = sysUsers.stream().map(SysUser::getUserName).toList();

            LambdaQueryWrapper<Expert> expertWrapper = Wrappers.lambdaQuery();
            expertWrapper.in(Expert::getIdCard, idcardList)
                .select(Expert::getName);

            List<ExpertVo> expertVos = expertMapper.selectVoList(expertWrapper);

            prjHazardousItemsSpecialistVo.setSpecialistNames(expertVos.stream().map(ExpertVo::getName).toList());
        }

        return prjHazardousItemsSpecialistVo;
    }

    @Override
    public Boolean saveThree(SpecialistThreeDTO dto) {

        LambdaQueryWrapper<PrjHazardousItemsSpecialist> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PrjHazardousItemsSpecialist::getTaskId, dto.getTaskId());

        PrjHazardousItemsSpecialist specialist = baseMapper.selectOne(wrapper);

        specialist.setRemark(dto.getRemark());

        if (CollectionUtil.isNotEmpty(dto.getFiles())) {
            List<PrjHazardousItemsFile> files = dto.getFiles();

            for (PrjHazardousItemsFile file : files) {
                file.setServiceType("specialist_flow");
            }

            prjHazardousItemsFileMapper.insertBatch(files);

            List<Long> list = files.stream().map(PrjHazardousItemsFile::getItemFileId).toList();

            specialist.setDownPushFile(CollectionUtil.join(list, ","));
        }

        return baseMapper.updateById(specialist) > 0;
    }

    @Override
    public SpecialistThreeVO threeDetail(String taskId) {

        SpecialistThreeVO vo = new SpecialistThreeVO();

        try {
            LambdaQueryWrapper<PrjHazardousItemsSpecialist> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(PrjHazardousItemsSpecialist::getTaskId, taskId);

            PrjHazardousItemsSpecialist specialist = baseMapper.selectOne(wrapper);

            if (StringUtils.isNotBlank(specialist.getQualityTaskId())) {
                FlowInstance instance = flowInstanceMapper.selectById(specialist.getQualityTaskId());
                vo.setChildrenTaskId(instance.getBusinessId());
            }
            vo.setQualityTaskId(specialist.getQualityTaskId());
            vo.setRemark(specialist.getRemark());

            if (StringUtils.isNotBlank(specialist.getDownPushFile())) {
                LambdaQueryWrapper<PrjHazardousItemsFile> fileWrapper = Wrappers.lambdaQuery();
                fileWrapper.eq(PrjHazardousItemsFile::getTaskId, taskId)
                    .in(PrjHazardousItemsFile::getItemFileId, Arrays.stream(specialist.getDownPushFile().split(",")).map(Long::valueOf).toList());

                List<PrjHazardousItemsFile> files = prjHazardousItemsFileMapper.selectList(fileWrapper);

                vo.setFiles(files);
            }


            //专家查询
            LambdaQueryWrapper<FlowInstance> flowWrapper = Wrappers.lambdaQuery();
            flowWrapper.eq(FlowInstance::getBusinessId, specialist.getTaskId());

            FlowInstance flowInstance = flowInstanceMapper.selectOne(flowWrapper);

            JSONObject array = JSON.parseObject(flowInstance.getVariable());

            String qualityPerson = array.getString("quality_person");

            List<Long> userIds = Arrays.stream(qualityPerson.split(",")).map(Long::valueOf).toList();

            List<SysUser> sysUsers = sysUserMapper.selectByIds(userIds);

            Map<String, Long> userMap = sysUsers.stream().collect(Collectors.toMap(SysUser::getUserName, SysUser::getUserId));

            List<Expert> experts = expertMapper.selectListByUserIds(userIds);

            List<SpecialistUserVO> userVOS = new ArrayList<>();

            for (Expert expert : experts) {
                SpecialistUserVO userVO = new SpecialistUserVO();
                userVO.setName(expert.getName());
                userVO.setUserId(userMap.get(expert.getIdCard()));
                userVOS.add(userVO);
            }

            vo.setCheckUsers(userVOS);
        } catch (Exception e) {
            log.info("数据错误！");
        }

        return vo;
    }

    /**
     * @param specialist
     * @return
     */
    @Override
    public boolean saveThree2(PrjHazardousItemsSpecialist specialist) {

        LambdaQueryWrapper<PrjHazardousItemsSpecialist> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PrjHazardousItemsSpecialist::getTaskId, specialist.getTaskId());

        PrjHazardousItemsSpecialist specialist1 = baseMapper.selectOne(wrapper);

        PrjHazardousItemsSpecialist update = new PrjHazardousItemsSpecialist();
        update.setId(specialist1.getId());
        update.setTaskId(specialist.getTaskId());
        update.setQualityTaskId(specialist.getQualityTaskId());

        return baseMapper.updateById(update) > 0;
    }

    @Override
    public FlowHisTaskVo getInstance(String flowInstanceId) {

        FlowInstance instance = flowInstanceMapper.selectById(flowInstanceId);

        Page<FlowTaskVo> page = new Page<>(1, 10);
        QueryWrapper<FlowTaskBo> queryWrapper = Wrappers.query();
        queryWrapper.eq("t.node_type", NodeType.BETWEEN.getKey());
        queryWrapper.in("t.approver", LoginHelper.getUserIdStr());
        queryWrapper.in("t.business_id", instance.getBusinessId());
        queryWrapper.orderByDesc("t.create_time").orderByDesc("t.update_time");

        Page<FlowHisTaskVo> page2 = flwTaskMapper.getListFinishTask(page, queryWrapper);

        List<FlowHisTaskVo> records = page2.getRecords();
        FlowHisTaskVo flowHisTaskVo = records.get(0);

        return flowHisTaskVo;
    }
}

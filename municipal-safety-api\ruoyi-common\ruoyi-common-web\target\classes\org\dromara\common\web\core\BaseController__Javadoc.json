{"doc": " web层通用数据处理\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "toAjax", "paramTypes": ["int"], "doc": " 响应返回结果\n\n @param rows 影响行数\n @return 操作结果\n"}, {"name": "toAjax", "paramTypes": ["boolean"], "doc": " 响应返回结果\n @param result 结果\n @return 操作结果\n"}, {"name": "redirect", "paramTypes": ["java.lang.String"], "doc": " 页面跳转\n"}, {"name": "initBinder", "paramTypes": ["org.springframework.web.bind.WebDataBinder"], "doc": " 将前台传递过来的日期格式的字符串，自动转化为Date类型\n"}, {"name": "startPage", "paramTypes": [], "doc": " 设置请求分页数据\n"}, {"name": "startOrderBy", "paramTypes": [], "doc": " 设置请求排序数据\n"}, {"name": "clearPage", "paramTypes": [], "doc": " 清理分页的线程变量\n"}, {"name": "getDataTable", "paramTypes": ["java.util.List"], "doc": " 响应请求分页数据\n"}], "constructors": []}
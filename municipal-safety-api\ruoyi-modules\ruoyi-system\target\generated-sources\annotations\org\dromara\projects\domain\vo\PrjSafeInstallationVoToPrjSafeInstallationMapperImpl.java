package org.dromara.projects.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjSafeInstallation;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjSafeInstallationVoToPrjSafeInstallationMapperImpl implements PrjSafeInstallationVoToPrjSafeInstallationMapper {

    @Override
    public PrjSafeInstallation convert(PrjSafeInstallationVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjSafeInstallation prjSafeInstallation = new PrjSafeInstallation();

        prjSafeInstallation.setInstallationId( arg0.getInstallationId() );
        prjSafeInstallation.setUserName( arg0.getUserName() );
        prjSafeInstallation.setUserPositionName( arg0.getUserPositionName() );
        prjSafeInstallation.setFace( arg0.getFace() );
        prjSafeInstallation.setCertificate( arg0.getCertificate() );
        prjSafeInstallation.setOpenTaskId( arg0.getOpenTaskId() );

        return prjSafeInstallation;
    }

    @Override
    public PrjSafeInstallation convert(PrjSafeInstallationVo arg0, PrjSafeInstallation arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setInstallationId( arg0.getInstallationId() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setUserPositionName( arg0.getUserPositionName() );
        arg1.setFace( arg0.getFace() );
        arg1.setCertificate( arg0.getCertificate() );
        arg1.setOpenTaskId( arg0.getOpenTaskId() );

        return arg1;
    }
}

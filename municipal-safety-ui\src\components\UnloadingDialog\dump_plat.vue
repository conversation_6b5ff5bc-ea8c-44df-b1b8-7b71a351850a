<template>
  <div class="app-container">

    <el-table v-loading="loading" :data="dumpPlatList">
      <el-table-column :width="commonWitch" label="编号" align="center" prop="dumpnumber" />
      <el-table-column :width="commonWitch" label="创建时间" align="center" prop="createTime" width="160px">
        <template v-slot="scope">
          {{ scope.row.createTime }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWitch" label="最大载重" align="center" prop="weightMax">
        <template v-slot="scope">
          {{ scope.row.weightMax }}吨
        </template>
      </el-table-column>
      <el-table-column :width="commonWitch" label="重量" align="center" prop="weight">
        <template v-slot="scope">
          {{ scope.row.weight }}吨
        </template>
      </el-table-column>
      <el-table-column :width="commonWitch" label="倾角" align="center" prop="tilt">
        <template v-slot="scope">
          {{ scope.row.tilt }}度
        </template>
      </el-table-column>
      <el-table-column :width="commonWitch" label="电池电压" align="center" prop="batvolt">
        <template v-slot="scope">
          {{ scope.row.batvolt }}v
        </template>
      </el-table-column>
      <el-table-column :width="commonWitch" label="吊重比例" align="center" prop="wightPercent">
        <template v-slot="scope">
          {{ scope.row.wightPercent }}%
        </template>
      </el-table-column>
      <el-table-column :width="commonWitch" label="报警信息" align="center" prop="alarmInfo">
        <template v-slot="scope">
          {{ scope.row.alarmInfo }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWitch" label="设备状态" align="center" prop="status">
        <template v-slot="scope">
          {{ scope.row.status }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWitch" label="载重预警百分比" align="center" prop="weightWarning">
        <template v-slot="scope">
          {{ scope.row.weightWarning }}%
        </template>
      </el-table-column>
      <el-table-column :width="commonWitch" label="载重报警百分比" align="center" prop="weightAlarm">
        <template v-slot="scope">
          {{ scope.row.weightAlarm }}%
        </template>
      </el-table-column>
      <el-table-column :width="commonWitch" label="倾斜预警值" align="center" prop="tiltWarning">
        <template v-slot="scope">
          {{ scope.row.tiltWarning }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWitch" label="倾斜报警值" align="center" prop="tiltAlarm">
        <template v-slot="scope">
          {{ scope.row.tiltAlarm }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWitch" label="实时倾斜度X" align="center" prop="realTiltX">
        <template v-slot="scope">
          {{ scope.row.realTiltX }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWitch" label="实时倾斜度Y" align="center" prop="realTiltY">
        <template v-slot="scope">
          {{ scope.row.realTiltY }}
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script>
import { listDumpPlat } from "@/api/special/equipment/index";

export default {
  name: "DumpPlat",
  props: {
    devNo: {
      type: String,
      default: ""
    }
  },
  data () {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 绿能卸料平台数据表表格数据
      dumpPlatList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        devNo: null
      },
      commonWitch: "120"
    };
  },
  created () {
    this.getList();
  },
  methods: {
    /** 查询绿能卸料平台数据表列表 */
    async getList () {
      this.dumpPlatList = []
      this.loading = true;
      this.queryParams.devNo = this.devNo
      const res = await listDumpPlat(this.queryParams)
      this.dumpPlatList = res.rows;
      this.total = res.total;
      this.loading = false;
    },
  }
};
</script>

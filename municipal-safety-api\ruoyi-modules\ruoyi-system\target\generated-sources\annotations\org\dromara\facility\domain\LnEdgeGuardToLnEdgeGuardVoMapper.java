package org.dromara.facility.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.bo.LnEdgeGuardBoToLnEdgeGuardMapper;
import org.dromara.facility.domain.vo.LnEdgeGuardVo;
import org.dromara.facility.domain.vo.LnEdgeGuardVoToLnEdgeGuardMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {LnEdgeGuardBoToLnEdgeGuardMapper.class,LnEdgeGuardVoToLnEdgeGuardMapper.class},
    imports = {}
)
public interface LnEdgeGuardToLnEdgeGuardVoMapper extends BaseMapper<LnEdgeGuard, LnEdgeGuardVo> {
}

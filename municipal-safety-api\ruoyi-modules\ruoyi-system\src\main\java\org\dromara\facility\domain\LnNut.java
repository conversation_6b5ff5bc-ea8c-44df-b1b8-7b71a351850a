package org.dromara.facility.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 绿能螺母对象 ln_nut
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ln_nut")
public class LnNut extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 参数
     */
    private String para;

    /**
     * 值类型
     */
    private String vt;

    /**
     * 值状态
     */
    private String qds;

    /**
     * 数据时间
     */
    private Date time;

    /**
     * 数值
     */
    private String value;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}

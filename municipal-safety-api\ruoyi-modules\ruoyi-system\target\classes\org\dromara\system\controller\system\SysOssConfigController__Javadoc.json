{"doc": " 对象存储配置\n\n <AUTHOR>\n <AUTHOR>\n @date 2021-08-13\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询对象存储配置列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取对象存储配置详细信息\n\n @param ossConfigId OSS配置ID\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo"], "doc": " 新增对象存储配置\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo"], "doc": " 修改对象存储配置\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除对象存储配置\n\n @param ossConfigIds OSS配置ID串\n"}, {"name": "changeStatus", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo"], "doc": " 状态修改\n"}], "constructors": []}
package org.dromara.ai.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.ai.domain.bo.AiHazAnalysisTasksBoToAiHazAnalysisTasksMapper;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksVo;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksVoToAiHazAnalysisTasksMapper;
import org.dromara.projects.domain.PrjHazardousItemsToPrjHazardousItemsVoMapper;
import org.dromara.projects.domain.PrjProjectsToPrjProjectsVoMapper;
import org.dromara.projects.domain.vo.PrjHazardousItemsVoToPrjHazardousItemsMapper;
import org.dromara.projects.domain.vo.PrjProjectsVoToPrjProjectsMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjProjectsVoToPrjProjectsMapper.class,PrjProjectsToPrjProjectsVoMapper.class,PrjHazardousItemsVoToPrjHazardousItemsMapper.class,PrjHazardousItemsToPrjHazardousItemsVoMapper.class,AiHazAnalysisTasksBoToAiHazAnalysisTasksMapper.class,AiHazAnalysisTasksVoToAiHazAnalysisTasksMapper.class},
    imports = {}
)
public interface AiHazAnalysisTasksToAiHazAnalysisTasksVoMapper extends BaseMapper<AiHazAnalysisTasks, AiHazAnalysisTasksVo> {
}

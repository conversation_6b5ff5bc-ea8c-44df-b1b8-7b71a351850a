package org.dromara.person.domain;

import javax.annotation.processing.Generated;
import org.dromara.person.domain.vo.SysQualificationVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class SysQualificationToSysQualificationVoMapperImpl implements SysQualificationToSysQualificationVoMapper {

    @Override
    public SysQualificationVo convert(SysQualification arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysQualificationVo sysQualificationVo = new SysQualificationVo();

        sysQualificationVo.setQualificationId( arg0.getQualificationId() );
        sysQualificationVo.setPersonId( arg0.getPersonId() );
        sysQualificationVo.setCertificateType( arg0.getCertificateType() );
        sysQualificationVo.setCertificateName( arg0.getCertificateName() );
        sysQualificationVo.setCertificateNumber( arg0.getCertificateNumber() );
        sysQualificationVo.setAcquisitionTime( arg0.getAcquisitionTime() );
        sysQualificationVo.setIssuingAuthority( arg0.getIssuingAuthority() );
        sysQualificationVo.setCertificateLevel( arg0.getCertificateLevel() );
        sysQualificationVo.setCorrespondingPosition( arg0.getCorrespondingPosition() );
        sysQualificationVo.setValidFrom( arg0.getValidFrom() );
        sysQualificationVo.setValidTo( arg0.getValidTo() );
        sysQualificationVo.setUploadPhoto( arg0.getUploadPhoto() );

        return sysQualificationVo;
    }

    @Override
    public SysQualificationVo convert(SysQualification arg0, SysQualificationVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setQualificationId( arg0.getQualificationId() );
        arg1.setPersonId( arg0.getPersonId() );
        arg1.setCertificateType( arg0.getCertificateType() );
        arg1.setCertificateName( arg0.getCertificateName() );
        arg1.setCertificateNumber( arg0.getCertificateNumber() );
        arg1.setAcquisitionTime( arg0.getAcquisitionTime() );
        arg1.setIssuingAuthority( arg0.getIssuingAuthority() );
        arg1.setCertificateLevel( arg0.getCertificateLevel() );
        arg1.setCorrespondingPosition( arg0.getCorrespondingPosition() );
        arg1.setValidFrom( arg0.getValidFrom() );
        arg1.setValidTo( arg0.getValidTo() );
        arg1.setUploadPhoto( arg0.getUploadPhoto() );

        return arg1;
    }
}

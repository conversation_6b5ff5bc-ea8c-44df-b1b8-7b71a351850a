package org.dromara.flow.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.flow.domain.PrjHazardousItemsSpecialistQuestion;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsSpecialistQuestionVoToPrjHazardousItemsSpecialistQuestionMapperImpl implements PrjHazardousItemsSpecialistQuestionVoToPrjHazardousItemsSpecialistQuestionMapper {

    @Override
    public PrjHazardousItemsSpecialistQuestion convert(PrjHazardousItemsSpecialistQuestionVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsSpecialistQuestion prjHazardousItemsSpecialistQuestion = new PrjHazardousItemsSpecialistQuestion();

        prjHazardousItemsSpecialistQuestion.setId( arg0.getId() );
        prjHazardousItemsSpecialistQuestion.setName( arg0.getName() );
        prjHazardousItemsSpecialistQuestion.setDetail( arg0.getDetail() );
        prjHazardousItemsSpecialistQuestion.setResultId( arg0.getResultId() );
        prjHazardousItemsSpecialistQuestion.setSpecialist( arg0.getSpecialist() );

        return prjHazardousItemsSpecialistQuestion;
    }

    @Override
    public PrjHazardousItemsSpecialistQuestion convert(PrjHazardousItemsSpecialistQuestionVo arg0, PrjHazardousItemsSpecialistQuestion arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setDetail( arg0.getDetail() );
        arg1.setResultId( arg0.getResultId() );
        arg1.setSpecialist( arg0.getSpecialist() );

        return arg1;
    }
}

package org.dromara.plan.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.expert.domain.Expert;
import org.dromara.expert.domain.vo.ExpertVo;
import org.dromara.plan.domain.PrjPatrolPlan;
import org.dromara.projects.domain.PrjProjects;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.system.domain.vo.SysDeptVo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 巡检计划视图对象 prj_patrol_plan
 *
 * <AUTHOR> <PERSON>
 * @date 2025-06-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjPatrolPlan.class)
public class PrjPatrolPlanVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long planId;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 计划开始时间
     */
    @ExcelProperty(value = "计划开始时间")
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ExcelProperty(value = "计划结束时间")
    private Date endTime;

    /**
     * 监督机构id
     */
    @ExcelProperty(value = "监督机构id")
    private String deptIds;

    /**
     * 检查项目id
     */
    @ExcelProperty(value = "检查项目id")
    private String projectIds;

    /**
     * 专家ids
     */
    @ExcelProperty(value = "专家ids")
    private String expertIds;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 主办部门
     */
    private Long mainDeptId;

    /**
     *  主办部门名称
     */
    private String mainDeptName;

    /**
     *  项目信息列表
     */
    private List<PrjProjectsVo> projectList;

    /**
     *  专家信息列表
     */
    private List<ExpertVo> expertList;

    /**
     * 部门信息列表
     */
    private List<SysDeptVo> deptList;
}

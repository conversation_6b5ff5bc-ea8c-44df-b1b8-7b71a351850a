<template>
  <div class="RealTimeData">
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" append-to-body @close="handleClose"
      width="80%">
      <div>
        <tower_real v-if="dialogVisible.visible" ref="towerReal" :dev-no="devNo" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import tower_real from '@/components/RealTimeDialog/tower_real.vue';

const props = defineProps({
  isShowRealTimeModel: {
    type: Boolean,
    default: false
  },
  devNo: {
    type: String,
    default: ''
  }
});
const emit = defineEmits(['update:isShowRealTimeModel']);
const dialogVisible = reactive<DialogOption>({
  visible: false,
  title: '实时数据'
});
watch(() => props.isShowRealTimeModel, (newVal) => {
  dialogVisible.visible = newVal;
}
);
const handleClose = () => {
  emit('update:isShowRealTimeModel', false);
};
</script>

<style lang="scss" scoped></style>
{"doc": " 全局任务办理监听\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "create", "paramTypes": ["org.dromara.warm.flow.core.listener.ListenerVariable"], "doc": " 创建监听器，任务创建时执行\n\n @param listenerVariable 监听器变量\n"}, {"name": "start", "paramTypes": ["org.dromara.warm.flow.core.listener.ListenerVariable"], "doc": " 开始监听器，任务开始办理时执行\n\n @param listenerVariable 监听器变量\n"}, {"name": "assignment", "paramTypes": ["org.dromara.warm.flow.core.listener.ListenerVariable"], "doc": " 分派监听器，动态修改代办任务信息\n\n @param listenerVariable 监听器变量\n"}, {"name": "finish", "paramTypes": ["org.dromara.warm.flow.core.listener.ListenerVariable"], "doc": " 完成监听器，当前任务完成后执行\n\n @param listenerVariable 监听器变量\n"}, {"name": "determineFlowStatus", "paramTypes": ["org.dromara.warm.flow.core.entity.Instance", "java.lang.String"], "doc": " 根据流程实例和当前流程状态确定最终状态\n\n @param instance   流程实例\n @param flowStatus 流程实例当前状态\n @return 流程最终状态\n"}], "constructors": []}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.QZJXMapper">

    <resultMap id="QZJXResultMap" type="org.dromara.system.domain.QZJX">
        <id property="id" column="ID"/>
        <result property="constructionPermitNum" column="CONSTRUCTIONPERMITNUM"/>
        <result property="sbbabh" column="SBBABH"/>
        <result property="sblx" column="SBLX"/>
        <result property="sbmc" column="SBMC"/>
        <result property="ggxh" column="GGXH"/>
        <result property="sccs" column="SCCS"/>
        <result property="sccstyshxydm" column="SCCSTYSHXYDM"/>
        <result property="ccbh" column="CCBH"/>
        <result property="ccrq" column="CCRQ"/>
        <result property="zzxkzh" column="ZZXKZH"/>
        <result property="synx" column="SYNX"/>
        <result property="cqdw" column="CQDW"/>
        <result property="cqdwtyshxydm" column="CQDWTYSHXYDM"/>
        <result property="cqdwdz" column="CQDWDZ"/>
        <result property="qyfrdb" column="QYFRDB"/>
        <result property="frsfzh" column="FRSFZH"/>
        <result property="lxr" column="LXR"/>
        <result property="lxdh" column="LXDH"/>
        <result property="gzjg" column="GZJG"/>
        <result property="gzny" column="GZNY"/>
        <result property="sbbajg" column="SBBAJG"/>
        <result property="fzjgtryshdm" column="FZJGTYSHDM"/>
        <result property="jxszds" column="JXSZDS"/>
        <result property="jxszqx" column="JXSZQX"/>
        <result property="jxqy" column="JXQY"/>
        <result property="edqzl" column="EDQZL"/>
        <result property="edqzlj" column="EDQZLJ"/>
        <result property="qzcb" column="TSQZJQZCB"/>
        <result property="zdgzfd" column="ZDGZFD"/>
        <result property="zdfdedqzl" column="TSQZJZDFDQZL"/>
        <result property="zddlqsgd" column="ZDDLQSGD"/>
        <result property="zdqsgd" column="ZDQSGD"/>
        <result property="zyjgjwybh" column="ZYJGJWYBH"/>
        <result property="nazzdgd" column="TSQZJNAZZDGD"/>
        <result property="zyjgjgg" column="ZYJGJGG"/>
        <result property="jqjcs" column="TAQZJJQJCS"/>
        <result property="bzjcs" column="TSQZJBZJCS"/>
        <result property="sgsjjytlx" column="SGSJJYTLX"/>
        <result property="ddjzgl" column="DDJZGL"/>
        <result property="edtssd" column="EDTSSD"/>
        <result property="fzaqqxh" column="FZAQQXH"/>
        <result property="jkcc" column="JKCC"/>
        <result property="sblb" column="SBLB"/>
        <result property="msqzjkd" column="MSQZJKD"/>
        <result property="delFlag" column="DEL_FLAG"/>
    </resultMap>

    <sql id="selectQZJXVo">
        SELECT ID,
               CONSTRUCTIONPERMITNUM,
               SBBABH,
               SBLX,
               SBMC,
               GGXH,
               SCCS,
               SCCSTYSHXYDM,
               CCBH,
               CCRQ,
               ZZXKZH,
               SYNX,
               CQDW,
               CQDWTYSHXYDM,
               CQDWDZ,
               QYFRDB,
               FRSFZH,
               LXR,
               LXDH,
               GZJG,
               GZNY,
               SBBAJG,
               FZJGTYSHDM,
               JXSZDS,
               JXSZQX,
               JXQY,
               EDQZL,
               EDQZLJ,
               TSQZJQZCB,
               ZDGZFD,
               TSQZJZDFDQZL,
               ZDDLQSGD,
               ZDQSGD,
               ZYJGJWYBH,
               TSQZJNAZZDGD,
               ZYJGJGG,
               TAQZJJQJCS,
               TSQZJBZJCS,
               SGSJJYTLX,
               DDJZGL,
               EDTSSD,
               FZAQQXH,
               JKCC,
               SBLB,
               MSQZJKD,
               DEL_FLAG
        FROM QZJX
    </sql>

    <select id="selectQZJXList" parameterType="org.dromara.system.domain.QZJX" resultMap="QZJXResultMap">
        <include refid="selectQZJXVo"/>
        <where>
            DEL_FLAG = '0'
            <if test="constructionPermitNum != null and constructionPermitNum != ''">
                AND CONSTRUCTIONPERMITNUM = #{constructionPermitNum}
            </if>
            <if test="sbbabh != null and sbbabh != ''">
                AND SBBABH = #{sbbabh}
            </if>
            <if test="sblx != null and sblx != ''">
                AND SBLX = #{sblx}
            </if>
            <if test="sbmc != null and sbmc != ''">
                AND SBMC = #{sbmc}
            </if>
            <if test="ggxh != null and ggxh != ''">
                AND GGXH = #{ggxh}
            </if>
        </where>
    </select>

    <select id="selectQZJXById" parameterType="Integer" resultMap="QZJXResultMap">
        <include refid="selectQZJXVo"/>
        WHERE ID = #{id} AND DEL_FLAG = '0'
    </select>

    <insert id="insertQzjx" parameterType="org.dromara.system.domain.QZJX" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO QZJX (CONSTRUCTIONPERMITNUM,
                          SBBABH,
                          SBLX,
                          SBMC,
                          GGXH,
                          SCCS,
                          SCCSTYSHXYDM,
                          CCBH,
                          CCRQ,
                          ZZXKZH,
                          SYNX,
                          CQDW,
                          CQDWTYSHXYDM,
                          CQDWDZ,
                          QYFRDB,
                          FRSFZH,
                          LXR,
                          LXDH,
                          GZJG,
                          GZNY,
                          SBBAJG,
                          FZJGTYSHDM,
                          JXSZDS,
                          JXSZQX,
                          JXQY,
                          EDQZL,
                          EDQZLJ,
                          TSQZJQZCB,
                          ZDGZFD,
                          TSQZJZDFDQZL,
                          ZDDLQSGD,
                          ZDQSGD,
                          ZYJGJWYBH,
                          TSQZJNAZZDGD,
                          ZYJGJGG,
                          TAQZJJQJCS,
                          TSQZJBZJCS,
                          SGSJJYTLX,
                          DDJZGL,
                          EDTSSD,
                          FZAQQXH,
                          JKCC,
                          SBLB,
                          MSQZJKD,
                          DEL_FLAG)
        VALUES (#{constructionPermitNum},
                #{sbbabh},
                #{sblx},
                #{sbmc},
                #{ggxh},
                #{sccs},
                #{sccstyshxydm},
                #{ccbh},
                #{ccrq},
                #{zzxkzh},
                #{synx},
                #{cqdw},
                #{cqdwtyshxydm},
                #{cqdwdz},
                #{qyfrdb},
                #{frsfzh},
                #{lxr},
                #{lxdh},
                #{gzjg},
                #{gzny},
                #{sbbajg},
                #{fzjgtryshdm},
                #{jxszds},
                #{jxszqx},
                #{jxqy},
                #{edqzl},
                #{edqzlj},
                #{qzcb},
                #{zdgzfd},
                #{zdfdedqzl},
                #{zddlqsgd},
                #{zdqsgd},
                #{zyjgjwybh},
                #{nazzdgd},
                #{zyjgjgg},
                #{jqjcs},
                #{bzjcs},
                #{sgsjjytlx},
                #{ddjzgl},
                #{edtssd},
                #{fzaqqxh},
                #{jkcc},
                #{sblb},
                #{msqzjkd},
                '0')
    </insert>

    <!-- 批量插入QZJX操作（动态字段） -->
    <insert id="batchInsertQzjx" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO QZJX (
        CONSTRUCTIONPERMITNUM,
        SBBABH,
        SBLX,
        SBMC,
        GGXH,
        SCCS,
        SCCSTYSHXYDM,
        CCBH,
        CCRQ,
        ZZXKZH,
        SYNX,
        CQDW,
        CQDWTYSHXYDM,
        CQDWDZ,
        QYFRDB,
        FRSFZH,
        LXR,
        LXDH,
        GZJG,
        GZNY,
        SBBAJG,
        FZJGTYSHDM,
        JXSZDS,
        JXSZQX,
        JXQY,
        EDQZL,
        EDQZLJ,
        TSQZJQZCB,
        ZDGZFD,
        TSQZJZDFDQZL,
        ZDDLQSGD,
        ZDQSGD,
        ZYJGJWYBH,
        TSQZJNAZZDGD,
        ZYJGJGG,
        TAQZJJQJCS,
        TSQZJBZJCS,
        SGSJJYTLX,
        DDJZGL,
        EDTSSD,
        FZAQQXH,
        JKCC,
        SBLB,
        MSQZJKD,
        DEL_FLAG
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.constructionPermitNum},
            #{item.sbbabh},
            #{item.sblx},
            #{item.sbmc},
            #{item.ggxh},
            #{item.sccs},
            #{item.sccstyshxydm},
            #{item.ccbh},
            #{item.ccrq},
            #{item.zzxkzh},
            #{item.synx},
            #{item.cqdw},
            #{item.cqdwtyshxydm},
            #{item.cqdwdz},
            #{item.qyfrdb},
            #{item.frsfzh},
            #{item.lxr},
            #{item.lxdh},
            #{item.gzjg},
            #{item.gzny},
            #{item.sbbajg},
            #{item.fzjgtryshdm},
            #{item.jxszds},
            #{item.jxszqx},
            #{item.jxqy},
            #{item.edqzl},
            #{item.edqzlj},
            #{item.qzcb},
            #{item.zdgzfd},
            #{item.zdfdedqzl},
            #{item.zddlqsgd},
            #{item.zdqsgd},
            #{item.zyjgjwybh},
            #{item.nazzdgd},
            #{item.zyjgjgg},
            #{item.jqjcs},
            #{item.bzjcs},
            #{item.sgsjjytlx},
            #{item.ddjzgl},
            #{item.edtssd},
            #{item.fzaqqxh},
            #{item.jkcc},
            #{item.sblb},
            #{item.msqzjkd},
            '0'
            )
        </foreach>
    </insert>

    <update id="updateQZJX" parameterType="org.dromara.system.domain.QZJX">
        UPDATE QZJX
        <set>
            <if test="constructionPermitNum != null and constructionPermitNum != ''">CONSTRUCTIONPERMITNUM =
                #{constructionPermitNum},
            </if>
            <if test="sbbabh != null and sbbabh != ''">SBBABH = #{sbbabh},</if>
            <if test="sblx != null and sblx != ''">SBLX = #{sblx},</if>
            <if test="sbmc != null and sbmc != ''">SBMC = #{sbmc},</if>
            <if test="ggxh != null and ggxh != ''">GGXH = #{ggxh},</if>
            <if test="sccs != null and sccs != ''">SCCS = #{sccs},</if>
            <if test="sccstyshxydm != null and sccstyshxydm != ''">SCCSTYSHXYDM = #{sccstyshxydm},</if>
            <if test="ccbh != null and ccbh != ''">CCBH = #{ccbh},</if>
            <if test="ccrq != null">CCRQ = #{ccrq},</if>
            <if test="zzxkzh != null and zzxkzh != ''">ZZXKZH = #{zzxkzh},</if>
            <if test="synx != null and synx != ''">SYNX = #{synx},</if>
            <if test="cqdw != null and cqdw != ''">CQDW = #{cqdw},</if>
            <if test="cqdwtyshxydm != null and cqdwtyshxydm != ''">CQDWTYSHXYDM = #{cqdwtyshxydm},</if>
            <if test="cqdwdz != null and cqdwdz != ''">CQDWDZ = #{cqdwdz},</if>
            <if test="qyfrdb != null and qyfrdb != ''">QYFRDB = #{qyfrdb},</if>
            <if test="frsfzh != null and frsfzh != ''">FRSFZH = #{frsfzh},</if>
            <if test="lxr != null and lxr != ''">LXR = #{lxr},</if>
            <if test="lxdh != null and lxdh != ''">LXDH = #{lxdh},</if>
            <if test="gzjg != null">GZJG = #{gzjg},</if>
            <if test="gzny != null">GZNY = #{gzny},</if>
            <if test="sbbajg != null and sbbajg != ''">SBBAJG = #{sbbajg},</if>
            <if test="fzjgtryshdm != null and fzjgtryshdm != ''">FZJGTYSHDM = #{fzjgtryshdm},</if>
            <if test="jxszds != null and jxszds != ''">JXSZDS = #{jxszds},</if>
            <if test="jxszqx != null and jxszqx != ''">JXSZQX = #{jxszqx},</if>
            <if test="jxqy != null and jxqy != ''">JXQY = #{jxqy},</if>
            <if test="edqzl != null">EDQZL = #{edqzl},</if>
            <if test="edqzlj != null">EDQZLJ = #{edqzlj},</if>
            <if test="qzcb != null">TSQZJQZCB = #{qzcb},</if>
            <if test="zdgzfd != null">ZDGZFD = #{zdgzfd},</if>
            <if test="zdfdedqzl != null">TSQZJZDFDQZL = #{zdfdedqzl},</if>
            <if test="zddlqsgd != null">ZDDLQSGD = #{zddlqsgd},</if>
            <if test="zdqsgd != null">ZDQSGD = #{zdqsgd},</if>
            <if test="zyjgjwybh != null and zyjgjwybh != ''">ZYJGJWYBH = #{zyjgjwybh},</if>
            <if test="nazzdgd != null">TSQZJNAZZDGD = #{nazzdgd},</if>
            <if test="zyjgjgg != null and zyjgjgg != ''">ZYJGJGG = #{zyjgjgg},</if>
            <if test="jqjcs != null and jqjcs != ''">TAQZJJQJCS = #{jqjcs},</if>
            <if test="bzjcs != null and bzjcs != ''">TSQZJBZJCS = #{bzjcs},</if>
            <if test="sgsjjytlx != null and sgsjjytlx != ''">SGSJJYTLX = #{sgsjjytlx},</if>
            <if test="ddjzgl != null and ddjzgl != ''">DDJZGL = #{ddjzgl},</if>
            <if test="edtssd != null">EDTSSD = #{edtssd},</if>
            <if test="fzaqqxh != null and fzaqqxh != ''">FZAQQXH = #{fzaqqxh},</if>
            <if test="jkcc != null and jkcc != ''">JKCC = #{jkcc},</if>
            <if test="sblb != null and sblb != ''">SBLB = #{sblb},</if>
            <if test="msqzjkd != null and msqzjkd != ''">MSQZJKD = #{msqzjkd},</if>
        </set>
        WHERE ID = #{id}
    </update>

    <update id="deleteQZJXById" parameterType="Integer">
        UPDATE QZJX
        SET DEL_FLAG = '1'
        WHERE ID = #{id}
    </update>

    <delete id="deleteQZJXByIds" parameterType="String">
        UPDATE QZJX
        SET DEL_FLAG = '1'
        WHERE ID IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteAllPhysically">
        DELETE FROM QZJX
    </delete>
</mapper>

{"doc": " 测试分布式限流样例\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "test", "paramTypes": ["java.lang.String"], "doc": " 测试全局限流\n 全局影响\n"}, {"name": "testip", "paramTypes": ["java.lang.String"], "doc": " 测试请求IP限流\n 同一IP请求受影响\n"}, {"name": "testcluster", "paramTypes": ["java.lang.String"], "doc": " 测试集群实例限流\n 启动两个后端服务互不影响\n"}, {"name": "testObj", "paramTypes": ["java.lang.String"], "doc": " 测试请求IP限流(key基于参数获取)\n 同一IP请求受影响\n <p>\n 简单变量获取 #变量 复杂表达式 #{#变量 != 1 ? 1 : 0}\n"}], "constructors": []}
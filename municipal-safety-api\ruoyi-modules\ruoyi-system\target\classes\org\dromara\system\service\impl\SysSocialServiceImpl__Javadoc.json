{"doc": " 社会化关系Service业务层处理\n\n <AUTHOR>\n @date 2023-06-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.String"], "doc": " 查询社会化关系\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": " 授权列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": " 新增社会化关系\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": " 更新社会化关系\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.system.domain.SysSocial"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidById", "paramTypes": ["java.lang.Long"], "doc": " 删除社会化关系\n"}, {"name": "selectByAuthId", "paramTypes": ["java.lang.String"], "doc": " 根据 authId 查询用户信息\n\n @param authId 认证id\n @return 授权信息\n"}], "constructors": []}
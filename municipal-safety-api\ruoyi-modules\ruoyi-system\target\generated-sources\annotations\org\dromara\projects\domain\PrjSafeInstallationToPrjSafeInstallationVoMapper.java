package org.dromara.projects.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.bo.PrjSafeInstallationBoToPrjSafeInstallationMapper;
import org.dromara.projects.domain.vo.PrjSafeInstallationVo;
import org.dromara.projects.domain.vo.PrjSafeInstallationVoToPrjSafeInstallationMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjSafeInstallationBoToPrjSafeInstallationMapper.class,PrjSafeInstallationVoToPrjSafeInstallationMapper.class},
    imports = {}
)
public interface PrjSafeInstallationToPrjSafeInstallationVoMapper extends BaseMapper<PrjSafeInstallation, PrjSafeInstallationVo> {
}

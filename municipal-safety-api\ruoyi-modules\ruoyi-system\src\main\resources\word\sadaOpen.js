if (typeof window === "undefined") {
    var window = globalThis;
}

var sadaOpen=function(t){var e={};function r(i){if(e[i])return e[i].exports;var o=e[i]={i:i,l:!1,exports:{}};return t[i].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=t,r.c=e,r.d=function(t,e,i){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)r.d(i,o,function(e){return t[e]}.bind(null,o));return i},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=0)}([function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getSignatureUrl=void 0;var i=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var r=[],i=!0,o=!1,n=void 0;try{for(var s,h=t[Symbol.iterator]();!(i=(s=h.next()).done)&&(r.push(s.value),!e||r.length!==e);i=!0);}catch(t){o=!0,n=t}finally{try{!i&&h.return&&h.return()}finally{if(o)throw n}}return r}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},o=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t},n=r(1);function s(t){for(var e="",r=0;r<Math.floor(Math.random()*t+1);r++)e+=Math.floor(Math.random()*"123456789".length+1);return Number(e)}e.getSignatureUrl=function(t,e){var r=o({},t,{signTimestamp:(new Date).getTime(),nonce:s(9)}),h=Object.keys(r).sort()||[],a=h.join(";")||"",u=h.reduce((function(t,e){return t+r[e]}),"");u+=a;var c=n.sha256.hmac(e.AppSecret,u),f="",l=!0,_=!1,p=void 0;try{for(var H,d=Object.entries(r)[Symbol.iterator]();!(l=(H=d.next()).done);l=!0){var y=i(H.value,2);f+="&"+y[0]+"="+y[1]}}catch(t){_=!0,p=t}finally{try{!l&&d.return&&d.return()}finally{if(_)throw p}}return e.url+"?sign="+c+f+"&signedField="+a}},function(module,exports,__webpack_require__){"use strict";(function(process,global,module){var __WEBPACK_AMD_DEFINE_RESULT__,_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};
/**
 * [js-sha256]{@link https://github.com/emn178/js-sha256}
 *
 * @version 0.9.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2017
 * @license MIT
 */
!function(){var ERROR="input is invalid type",WINDOW="object"===("undefined"==typeof window?"undefined":_typeof(window)),root=WINDOW?window:{};root.JS_SHA256_NO_WINDOW&&(WINDOW=!1);var WEB_WORKER=!WINDOW&&"object"===("undefined"==typeof self?"undefined":_typeof(self)),NODE_JS=!root.JS_SHA256_NO_NODE_JS&&"object"===(void 0===process?"undefined":_typeof(process))&&process.versions&&process.versions.node;NODE_JS?root=global:WEB_WORKER&&(root=self);var COMMON_JS=!root.JS_SHA256_NO_COMMON_JS&&"object"===_typeof(module)&&module.exports,AMD=__webpack_require__(5),ARRAY_BUFFER=!root.JS_SHA256_NO_ARRAY_BUFFER&&"undefined"!=typeof ArrayBuffer,HEX_CHARS="0123456789abcdef".split(""),EXTRA=[-**********,8388608,32768,128],SHIFT=[24,16,8,0],K=[**********,**********,**********,**********,961987163,**********,**********,**********,**********,310598401,607225278,**********,**********,**********,**********,**********,**********,**********,264347078,604807628,770255983,**********,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],OUTPUT_TYPES=["hex","array","digest","arrayBuffer"],blocks=[];!root.JS_SHA256_NO_NODE_JS&&Array.isArray||(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)}),!ARRAY_BUFFER||!root.JS_SHA256_NO_ARRAY_BUFFER_IS_VIEW&&ArrayBuffer.isView||(ArrayBuffer.isView=function(t){return"object"===(void 0===t?"undefined":_typeof(t))&&t.buffer&&t.buffer.constructor===ArrayBuffer});var createOutputMethod=function(t,e){return function(r){return new Sha256(e,!0).update(r)[t]()}},createMethod=function(t){var e=createOutputMethod("hex",t);NODE_JS&&(e=nodeWrap(e,t)),e.create=function(){return new Sha256(t)},e.update=function(t){return e.create().update(t)};for(var r=0;r<OUTPUT_TYPES.length;++r){var i=OUTPUT_TYPES[r];e[i]=createOutputMethod(i,t)}return e},nodeWrap=function nodeWrap(method,is224){var crypto=eval("require('crypto')"),Buffer=eval("require('buffer').Buffer"),algorithm=is224?"sha224":"sha256",nodeMethod=function(t){if("string"==typeof t)return crypto.createHash(algorithm).update(t,"utf8").digest("hex");if(null==t)throw new Error(ERROR);return t.constructor===ArrayBuffer&&(t=new Uint8Array(t)),Array.isArray(t)||ArrayBuffer.isView(t)||t.constructor===Buffer?crypto.createHash(algorithm).update(new Buffer(t)).digest("hex"):method(t)};return nodeMethod},createHmacOutputMethod=function(t,e){return function(r,i){return new HmacSha256(r,e,!0).update(i)[t]()}},createHmacMethod=function(t){var e=createHmacOutputMethod("hex",t);e.create=function(e){return new HmacSha256(e,t)},e.update=function(t,r){return e.create(t).update(r)};for(var r=0;r<OUTPUT_TYPES.length;++r){var i=OUTPUT_TYPES[r];e[i]=createHmacOutputMethod(i,t)}return e};function Sha256(t,e){e?(blocks[0]=blocks[16]=blocks[1]=blocks[2]=blocks[3]=blocks[4]=blocks[5]=blocks[6]=blocks[7]=blocks[8]=blocks[9]=blocks[10]=blocks[11]=blocks[12]=blocks[13]=blocks[14]=blocks[15]=0,this.blocks=blocks):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t?(this.h0=3238371032,this.h1=914150663,this.h2=812702999,this.h3=4144912697,this.h4=4290775857,this.h5=1750603025,this.h6=1694076839,this.h7=3204075428):(this.h0=1779033703,this.h1=3144134277,this.h2=1013904242,this.h3=2773480762,this.h4=1359893119,this.h5=2600822924,this.h6=528734635,this.h7=1541459225),this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0,this.is224=t}function HmacSha256(t,e,r){var i,o=void 0===t?"undefined":_typeof(t);if("string"===o){var n,s=[],h=t.length,a=0;for(i=0;i<h;++i)(n=t.charCodeAt(i))<128?s[a++]=n:n<2048?(s[a++]=192|n>>6,s[a++]=128|63&n):n<55296||n>=57344?(s[a++]=224|n>>12,s[a++]=128|n>>6&63,s[a++]=128|63&n):(n=65536+((1023&n)<<10|1023&t.charCodeAt(++i)),s[a++]=240|n>>18,s[a++]=128|n>>12&63,s[a++]=128|n>>6&63,s[a++]=128|63&n);t=s}else{if("object"!==o)throw new Error(ERROR);if(null===t)throw new Error(ERROR);if(ARRAY_BUFFER&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!(Array.isArray(t)||ARRAY_BUFFER&&ArrayBuffer.isView(t)))throw new Error(ERROR)}t.length>64&&(t=new Sha256(e,!0).update(t).array());var u=[],c=[];for(i=0;i<64;++i){var f=t[i]||0;u[i]=92^f,c[i]=54^f}Sha256.call(this,e,r),this.update(c),this.oKeyPad=u,this.inner=!0,this.sharedMemory=r}Sha256.prototype.update=function(t){if(!this.finalized){var e,r=void 0===t?"undefined":_typeof(t);if("string"!==r){if("object"!==r)throw new Error(ERROR);if(null===t)throw new Error(ERROR);if(ARRAY_BUFFER&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!(Array.isArray(t)||ARRAY_BUFFER&&ArrayBuffer.isView(t)))throw new Error(ERROR);e=!0}for(var i,o,n=0,s=t.length,h=this.blocks;n<s;){if(this.hashed&&(this.hashed=!1,h[0]=this.block,h[16]=h[1]=h[2]=h[3]=h[4]=h[5]=h[6]=h[7]=h[8]=h[9]=h[10]=h[11]=h[12]=h[13]=h[14]=h[15]=0),e)for(o=this.start;n<s&&o<64;++n)h[o>>2]|=t[n]<<SHIFT[3&o++];else for(o=this.start;n<s&&o<64;++n)(i=t.charCodeAt(n))<128?h[o>>2]|=i<<SHIFT[3&o++]:i<2048?(h[o>>2]|=(192|i>>6)<<SHIFT[3&o++],h[o>>2]|=(128|63&i)<<SHIFT[3&o++]):i<55296||i>=57344?(h[o>>2]|=(224|i>>12)<<SHIFT[3&o++],h[o>>2]|=(128|i>>6&63)<<SHIFT[3&o++],h[o>>2]|=(128|63&i)<<SHIFT[3&o++]):(i=65536+((1023&i)<<10|1023&t.charCodeAt(++n)),h[o>>2]|=(240|i>>18)<<SHIFT[3&o++],h[o>>2]|=(128|i>>12&63)<<SHIFT[3&o++],h[o>>2]|=(128|i>>6&63)<<SHIFT[3&o++],h[o>>2]|=(128|63&i)<<SHIFT[3&o++]);this.lastByteIndex=o,this.bytes+=o-this.start,o>=64?(this.block=h[16],this.start=o-64,this.hash(),this.hashed=!0):this.start=o}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}},Sha256.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var t=this.blocks,e=this.lastByteIndex;t[16]=this.block,t[e>>2]|=EXTRA[3&e],this.block=t[16],e>=56&&(this.hashed||this.hash(),t[0]=this.block,t[16]=t[1]=t[2]=t[3]=t[4]=t[5]=t[6]=t[7]=t[8]=t[9]=t[10]=t[11]=t[12]=t[13]=t[14]=t[15]=0),t[14]=this.hBytes<<3|this.bytes>>>29,t[15]=this.bytes<<3,this.hash()}},Sha256.prototype.hash=function(){var t,e,r,i,o,n,s,h,a,u=this.h0,c=this.h1,f=this.h2,l=this.h3,_=this.h4,p=this.h5,H=this.h6,d=this.h7,y=this.blocks;for(t=16;t<64;++t)e=((o=y[t-15])>>>7|o<<25)^(o>>>18|o<<14)^o>>>3,r=((o=y[t-2])>>>17|o<<15)^(o>>>19|o<<13)^o>>>10,y[t]=y[t-16]+e+y[t-7]+r<<0;for(a=c&f,t=0;t<64;t+=4)this.first?(this.is224?(n=300032,d=(o=y[0]-1413257819)-150054599<<0,l=o+24177077<<0):(n=704751109,d=(o=y[0]-210244248)-1521486534<<0,l=o+143694565<<0),this.first=!1):(e=(u>>>2|u<<30)^(u>>>13|u<<19)^(u>>>22|u<<10),i=(n=u&c)^u&f^a,d=l+(o=d+(r=(_>>>6|_<<26)^(_>>>11|_<<21)^(_>>>25|_<<7))+(_&p^~_&H)+K[t]+y[t])<<0,l=o+(e+i)<<0),e=(l>>>2|l<<30)^(l>>>13|l<<19)^(l>>>22|l<<10),i=(s=l&u)^l&c^n,H=f+(o=H+(r=(d>>>6|d<<26)^(d>>>11|d<<21)^(d>>>25|d<<7))+(d&_^~d&p)+K[t+1]+y[t+1])<<0,e=((f=o+(e+i)<<0)>>>2|f<<30)^(f>>>13|f<<19)^(f>>>22|f<<10),i=(h=f&l)^f&u^s,p=c+(o=p+(r=(H>>>6|H<<26)^(H>>>11|H<<21)^(H>>>25|H<<7))+(H&d^~H&_)+K[t+2]+y[t+2])<<0,e=((c=o+(e+i)<<0)>>>2|c<<30)^(c>>>13|c<<19)^(c>>>22|c<<10),i=(a=c&f)^c&l^h,_=u+(o=_+(r=(p>>>6|p<<26)^(p>>>11|p<<21)^(p>>>25|p<<7))+(p&H^~p&d)+K[t+3]+y[t+3])<<0,u=o+(e+i)<<0;this.h0=this.h0+u<<0,this.h1=this.h1+c<<0,this.h2=this.h2+f<<0,this.h3=this.h3+l<<0,this.h4=this.h4+_<<0,this.h5=this.h5+p<<0,this.h6=this.h6+H<<0,this.h7=this.h7+d<<0},Sha256.prototype.hex=function(){this.finalize();var t=this.h0,e=this.h1,r=this.h2,i=this.h3,o=this.h4,n=this.h5,s=this.h6,h=this.h7,a=HEX_CHARS[t>>28&15]+HEX_CHARS[t>>24&15]+HEX_CHARS[t>>20&15]+HEX_CHARS[t>>16&15]+HEX_CHARS[t>>12&15]+HEX_CHARS[t>>8&15]+HEX_CHARS[t>>4&15]+HEX_CHARS[15&t]+HEX_CHARS[e>>28&15]+HEX_CHARS[e>>24&15]+HEX_CHARS[e>>20&15]+HEX_CHARS[e>>16&15]+HEX_CHARS[e>>12&15]+HEX_CHARS[e>>8&15]+HEX_CHARS[e>>4&15]+HEX_CHARS[15&e]+HEX_CHARS[r>>28&15]+HEX_CHARS[r>>24&15]+HEX_CHARS[r>>20&15]+HEX_CHARS[r>>16&15]+HEX_CHARS[r>>12&15]+HEX_CHARS[r>>8&15]+HEX_CHARS[r>>4&15]+HEX_CHARS[15&r]+HEX_CHARS[i>>28&15]+HEX_CHARS[i>>24&15]+HEX_CHARS[i>>20&15]+HEX_CHARS[i>>16&15]+HEX_CHARS[i>>12&15]+HEX_CHARS[i>>8&15]+HEX_CHARS[i>>4&15]+HEX_CHARS[15&i]+HEX_CHARS[o>>28&15]+HEX_CHARS[o>>24&15]+HEX_CHARS[o>>20&15]+HEX_CHARS[o>>16&15]+HEX_CHARS[o>>12&15]+HEX_CHARS[o>>8&15]+HEX_CHARS[o>>4&15]+HEX_CHARS[15&o]+HEX_CHARS[n>>28&15]+HEX_CHARS[n>>24&15]+HEX_CHARS[n>>20&15]+HEX_CHARS[n>>16&15]+HEX_CHARS[n>>12&15]+HEX_CHARS[n>>8&15]+HEX_CHARS[n>>4&15]+HEX_CHARS[15&n]+HEX_CHARS[s>>28&15]+HEX_CHARS[s>>24&15]+HEX_CHARS[s>>20&15]+HEX_CHARS[s>>16&15]+HEX_CHARS[s>>12&15]+HEX_CHARS[s>>8&15]+HEX_CHARS[s>>4&15]+HEX_CHARS[15&s];return this.is224||(a+=HEX_CHARS[h>>28&15]+HEX_CHARS[h>>24&15]+HEX_CHARS[h>>20&15]+HEX_CHARS[h>>16&15]+HEX_CHARS[h>>12&15]+HEX_CHARS[h>>8&15]+HEX_CHARS[h>>4&15]+HEX_CHARS[15&h]),a},Sha256.prototype.toString=Sha256.prototype.hex,Sha256.prototype.digest=function(){this.finalize();var t=this.h0,e=this.h1,r=this.h2,i=this.h3,o=this.h4,n=this.h5,s=this.h6,h=this.h7,a=[t>>24&255,t>>16&255,t>>8&255,255&t,e>>24&255,e>>16&255,e>>8&255,255&e,r>>24&255,r>>16&255,r>>8&255,255&r,i>>24&255,i>>16&255,i>>8&255,255&i,o>>24&255,o>>16&255,o>>8&255,255&o,n>>24&255,n>>16&255,n>>8&255,255&n,s>>24&255,s>>16&255,s>>8&255,255&s];return this.is224||a.push(h>>24&255,h>>16&255,h>>8&255,255&h),a},Sha256.prototype.array=Sha256.prototype.digest,Sha256.prototype.arrayBuffer=function(){this.finalize();var t=new ArrayBuffer(this.is224?28:32),e=new DataView(t);return e.setUint32(0,this.h0),e.setUint32(4,this.h1),e.setUint32(8,this.h2),e.setUint32(12,this.h3),e.setUint32(16,this.h4),e.setUint32(20,this.h5),e.setUint32(24,this.h6),this.is224||e.setUint32(28,this.h7),t},HmacSha256.prototype=new Sha256,HmacSha256.prototype.finalize=function(){if(Sha256.prototype.finalize.call(this),this.inner){this.inner=!1;var t=this.array();Sha256.call(this,this.is224,this.sharedMemory),this.update(this.oKeyPad),this.update(t),Sha256.prototype.finalize.call(this)}};var exports=createMethod();exports.sha256=exports,exports.sha224=createMethod(!0),exports.sha256.hmac=createHmacMethod(),exports.sha224.hmac=createHmacMethod(!0),COMMON_JS?module.exports=exports:(root.sha256=exports.sha256,root.sha224=exports.sha224,AMD&&(__WEBPACK_AMD_DEFINE_RESULT__=function(){return exports}.call(exports,__webpack_require__,exports,module),void 0===__WEBPACK_AMD_DEFINE_RESULT__||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)))}()}).call(this,__webpack_require__(2),__webpack_require__(3),__webpack_require__(4)(module))},function(t,e,r){"use strict";var i,o,n=t.exports={};function s(){throw new Error("setTimeout has not been defined")}function h(){throw new Error("clearTimeout has not been defined")}function a(t){if(i===setTimeout)return setTimeout(t,0);if((i===s||!i)&&setTimeout)return i=setTimeout,setTimeout(t,0);try{return i(t,0)}catch(e){try{return i.call(null,t,0)}catch(e){return i.call(this,t,0)}}}!function(){try{i="function"==typeof setTimeout?setTimeout:s}catch(t){i=s}try{o="function"==typeof clearTimeout?clearTimeout:h}catch(t){o=h}}();var u,c=[],f=!1,l=-1;function _(){f&&u&&(f=!1,u.length?c=u.concat(c):l=-1,c.length&&p())}function p(){if(!f){var t=a(_);f=!0;for(var e=c.length;e;){for(u=c,c=[];++l<e;)u&&u[l].run();l=-1,e=c.length}u=null,f=!1,function(t){if(o===clearTimeout)return clearTimeout(t);if((o===h||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(t);try{o(t)}catch(e){try{return o.call(null,t)}catch(e){return o.call(this,t)}}}(t)}}function H(t,e){this.fun=t,this.array=e}function d(){}n.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];c.push(new H(t,e)),1!==c.length||f||a(p)},H.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=d,n.addListener=d,n.once=d,n.off=d,n.removeListener=d,n.removeAllListeners=d,n.emit=d,n.prependListener=d,n.prependOnceListener=d,n.listeners=function(t){return[]},n.binding=function(t){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(t){throw new Error("process.chdir is not supported")},n.umask=function(){return 0}},function(t,e,r){"use strict";var i,o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};i=function(){return this}();try{i=i||new Function("return this")()}catch(t){"object"===("undefined"==typeof window?"undefined":o(window))&&(i=window)}t.exports=i},function(t,e,r){"use strict";t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}},function(t,e){(function(e){t.exports=e}).call(this,{})}]);


function signatureUrl(uniqueId){
	let params = {
		openParty: 'Gsjtsz',
		uniqueId: uniqueId,
		appKey: 'acli_oDjdjFgfzQeiVYqGmdTHENQTyaeDy6LBxamqD',
		path: '/',
	  }
	  let extraParams = {
		url: 'https://partner.ixiaosa.com/gs_uid',
		AppSecret: 'gajDJtkqPRucyPKyjRutbGKII7UUW9XMIiCt6KRbvZE'
	  }
	return sadaOpen.getSignatureUrl(params, extraParams)
}
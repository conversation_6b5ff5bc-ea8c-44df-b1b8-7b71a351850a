2025-05-01 08:49:47 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.15 with PID 5084 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-monitor-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-01 08:49:47 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-05-01 08:49:48 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-05-01 08:49:48 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-01 08:49:48 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 940 ms
2025-05-01 08:49:48 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-05-01 08:49:49 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-05-01 08:49:49 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-05-01 08:49:49 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-01 08:49:49 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-01 08:49:49 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-01 08:49:50 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-01 08:49:50 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-05-01 08:49:50 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 2.994 seconds (process running for 3.432)
2025-05-01 08:49:50 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-01 08:49:50 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-01 08:49:50 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-05-01 08:49:50 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 5b2b5aa6be02
2025-05-01 08:49:50 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【5b2b5aa6be02】, 状态【UP】, 服务URL【http://**********:9090/】
2025-05-01 08:49:51 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【56c94c188ac7】, 状态【UP】, 服务URL【http://**********:8800/snail-job】
2025-05-01 08:50:12 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【8143852f6ba3】, 状态【UP】, 服务URL【http://**********:8080/】
2025-05-01 11:36:50 [reactor-http-nio-1] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【cfaedb4e44dd】, 状态【UP】, 服务URL【http://192.168.168.132:9090/】
2025-05-01 11:36:50 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【cc20f5b8d840】, 状态【UP】, 服务URL【http://192.168.168.132:8800/snail-job】
2025-05-01 11:36:52 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【3f35cdd037b2】, 状态【UP】, 服务URL【http://192.168.168.132:8080/】
2025-05-01 11:36:58 [parallel-8] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=8143852f6ba3, version=2, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://**********:8080/actuator, healthUrl=http://**********:8080/actuator/health, serviceUrl=http://**********:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, db={status=UP, details={database=MySQL, validationQuery=isValid()}}, diskSpace={status=UP, details={total=355626323968, free=310878343168, threshold=10485760, path=D:\Work\job\municipal-safety\municipal-safety-api\., exists=true}}, ping={status=UP}, redis={status=UP, details={version=********}}}), statusTimestamp=2025-05-01T00:50:12.875587100Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://**********:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://**********:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://**********:8080/actuator/logfile), health=Endpoint(id=health, url=http://**********:8080/actuator/health), env=Endpoint(id=env, url=http://**********:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://**********:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://**********:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://**********:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://**********:8080/actuator/startup), beans=Endpoint(id=beans, url=http://**********:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://**********:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://**********:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://**********:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://**********:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://**********:8080/actuator/conditions), info=Endpoint(id=info, url=http://**********:8080/actuator/info)}), buildVersion=null, tags=Tags(values={}))
java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 9000ms in 'log' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281)
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270)
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-01 11:36:58 [parallel-12] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=56c94c188ac7, version=3, registration=Registration(name=ruoyi-snailjob-server, managementUrl=http://**********:8800/snail-job/actuator, healthUrl=http://**********:8800/snail-job/actuator/health, serviceUrl=http://**********:8800/snail-job, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, db={status=UP, details={database=MySQL, validationQuery=isValid()}}, diskSpace={status=UP, details={total=355626323968, free=310878363648, threshold=10485760, path=D:\Work\job\municipal-safety\municipal-safety-api\., exists=true}}, ping={status=UP}}), statusTimestamp=2025-05-01T00:49:51.823188800Z, info=Info(values={build={artifact=snail-job-server-starter, name=snail-job-server-starter, time=2025-03-23T03:56:29.326Z, version=1.4.0, group=com.aizuda}}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://**********:8800/snail-job/actuator/caches), loggers=Endpoint(id=loggers, url=http://**********:8800/snail-job/actuator/loggers), logfile=Endpoint(id=logfile, url=http://**********:8800/snail-job/actuator/logfile), health=Endpoint(id=health, url=http://**********:8800/snail-job/actuator/health), env=Endpoint(id=env, url=http://**********:8800/snail-job/actuator/env), heapdump=Endpoint(id=heapdump, url=http://**********:8800/snail-job/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://**********:8800/snail-job/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://**********:8800/snail-job/actuator/mappings), beans=Endpoint(id=beans, url=http://**********:8800/snail-job/actuator/beans), configprops=Endpoint(id=configprops, url=http://**********:8800/snail-job/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://**********:8800/snail-job/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://**********:8800/snail-job/actuator/sbom), metrics=Endpoint(id=metrics, url=http://**********:8800/snail-job/actuator/metrics), conditions=Endpoint(id=conditions, url=http://**********:8800/snail-job/actuator/conditions), info=Endpoint(id=info, url=http://**********:8800/snail-job/actuator/info)}), buildVersion=1.4.0, tags=Tags(values={}))
java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 9000ms in 'log' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281)
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270)
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-01 11:36:58 [parallel-10] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=5b2b5aa6be02, version=2, registration=Registration(name=ruoyi-monitor-admin, managementUrl=http://**********:9090/actuator, healthUrl=http://**********:9090/actuator/health, serviceUrl=http://**********:9090/, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, diskSpace={status=UP, details={total=355626323968, free=310878363648, threshold=10485760, path=D:\Work\job\municipal-safety\municipal-safety-api\., exists=true}}, ping={status=UP}}), statusTimestamp=2025-05-01T00:49:50.953271700Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://**********:9090/actuator/caches), loggers=Endpoint(id=loggers, url=http://**********:9090/actuator/loggers), logfile=Endpoint(id=logfile, url=http://**********:9090/actuator/logfile), health=Endpoint(id=health, url=http://**********:9090/actuator/health), env=Endpoint(id=env, url=http://**********:9090/actuator/env), heapdump=Endpoint(id=heapdump, url=http://**********:9090/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://**********:9090/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://**********:9090/actuator/mappings), beans=Endpoint(id=beans, url=http://**********:9090/actuator/beans), configprops=Endpoint(id=configprops, url=http://**********:9090/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://**********:9090/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://**********:9090/actuator/sbom), metrics=Endpoint(id=metrics, url=http://**********:9090/actuator/metrics), conditions=Endpoint(id=conditions, url=http://**********:9090/actuator/conditions), info=Endpoint(id=info, url=http://**********:9090/actuator/info)}), buildVersion=null, tags=Tags(values={}))
java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 9000ms in 'log' (and no fallback has been configured)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.handleTimeout(FluxTimeout.java:296)
	at reactor.core.publisher.FluxTimeout$TimeoutMainSubscriber.doTimeout(FluxTimeout.java:281)
	at reactor.core.publisher.FluxTimeout$TimeoutTimeoutSubscriber.onNext(FluxTimeout.java:420)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.propagateDelay(MonoDelay.java:270)
	at reactor.core.publisher.MonoDelay$MonoDelayRunnable.run(MonoDelay.java:285)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-01 11:36:58 [parallel-10] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【ruoyi-monitor-admin】, 实例ID【5b2b5aa6be02】, 状态【OFFLINE】, 服务URL【http://**********:9090/】
2025-05-01 11:36:58 [parallel-8] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【8143852f6ba3】, 状态【OFFLINE】, 服务URL【http://**********:8080/】
2025-05-01 11:36:58 [parallel-12] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【ruoyi-snailjob-server】, 实例ID【56c94c188ac7】, 状态【OFFLINE】, 服务URL【http://**********:8800/snail-job】
2025-05-01 12:04:37 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-05-01 12:04:37 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-05-01 12:04:37 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-01 12:04:37 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'

package org.dromara.flow.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepoove.poi.XWPFTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.flow.domain.dto.DownTemplateDTO;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.mapper.PrjProjectsMapper;
import org.dromara.system.domain.SysDept;
import org.dromara.system.domain.SysEnterpriseInfo;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.mapper.SysDeptMapper;
import org.dromara.system.mapper.SysEnterpriseInfoMapper;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Zu Da
 * @date 2025/6/12 16:18
 * @Description TODO
 * @Version 1.0
 */
@Validated
@Controller
@RequestMapping("/system/hazardousItemsComments/down")
public class PrjCommentsDownController {

    @Resource
    private PrjProjectsMapper prjProjectsMapper;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private SysEnterpriseInfoMapper sysEnterpriseInfoMapper;

    /**
     * 限期整改
     *
     * @param dto
     * @param response
     * @throws IOException
     */
    @PostMapping("/xqzg")
    public void xqzg(DownTemplateDTO dto, HttpServletResponse response) throws IOException {

        //查询施工方
        PrjProjectsVo prjProjectsVo = prjProjectsMapper.selectVoById(dto.getProjectId());

        Long constructionOrgId = prjProjectsVo.getConstructionOrgId();

        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SysDept::getDeptId, constructionOrgId);

        SysDeptVo sysDeptVo = sysDeptMapper.selectVoOne(wrapper);

        InputStream resource = getClass().getResourceAsStream("/word/xqzg.docx");

        HashMap<String, Object> model = new HashMap<>();

        model.put("construction", sysDeptVo.getDeptName());

        //计算延期时间
        Date date = new Date();

        DateTime offset;
        if (dto.getTimeType() == 1) {
            offset = DateUtil.offsetHour(date, dto.getTimeLimit());
        } else {
            offset = DateUtil.offsetDay(date, dto.getTimeLimit());
        }
        model.put("year", DateUtil.year(date));
        model.put("month", DateUtil.month(date) + 1);
        model.put("day", DateUtil.dayOfMonth(date));
        model.put("updYear", DateUtil.year(offset));
        model.put("updMonth", DateUtil.month(offset) + 1);
        model.put("updDay", DateUtil.dayOfMonth(offset));
        model.put("questionArr", dto.getQuestion());

        XWPFTemplate template = XWPFTemplate.compile(resource).render(model);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        template.write(outputStream);

        byte[] bytes = outputStream.toByteArray();
        outputStream.close();

        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=123.docx");

        OutputStream out = response.getOutputStream();
        out.write(bytes);
        out.flush();
        out.close();
    }

    /**
     * 停工通知
     *
     * @param dto
     * @param response
     * @throws IOException
     */
    @PostMapping("/tgzg")
    public void tgzg(DownTemplateDTO dto, HttpServletResponse response) throws IOException {

        //查询施工方
        PrjProjectsVo prjProjectsVo = prjProjectsMapper.selectVoById(dto.getProjectId());

        List<Long> ids = Arrays.asList(prjProjectsVo.getConstructionOrgId(), prjProjectsVo.getSupervisionOrgId()
            , prjProjectsVo.getClientOrgId());

        List<SysDeptVo> sysDeptVos = sysDeptMapper.selectVoByIds(ids);

        List<String> strings = sysDeptVos.stream().map(SysDeptVo::getDeptName).toList();

        String sfdw = ArrayUtil.join(strings.toArray(), "、");

        InputStream resource = getClass().getResourceAsStream("/word/tgzg.docx");

        HashMap<String, Object> model = new HashMap<>();

        //计算延期时间
        Date date = new Date();

        DateTime offset;
        if (dto.getTimeType() == 1) {
            offset = DateUtil.offsetHour(date, dto.getTimeLimit());
        } else {
            offset = DateUtil.offsetDay(date, dto.getTimeLimit());
        }

        model.put("sfdw", sfdw);
        model.put("updYear", DateUtil.year(offset));
        model.put("updMonth", DateUtil.month(offset) + 1);
        model.put("updDay", DateUtil.dayOfMonth(offset));
        model.put("questionArr", dto.getQuestion());

        XWPFTemplate template = XWPFTemplate.compile(resource).render(model);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        template.write(outputStream);

        byte[] bytes = outputStream.toByteArray();
        outputStream.close();

        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=123.docx");

        OutputStream out = response.getOutputStream();
        out.write(bytes);
        out.flush();
        out.close();
    }

    /**
     * 行政处罚决定书
     *
     * @param dto
     * @param response
     * @throws IOException
     */
    @PostMapping("/xzcfjds")
    public void xzcfjds(DownTemplateDTO dto, HttpServletResponse response) throws IOException {

        //查询施工方
        PrjProjectsVo prjProjectsVo = prjProjectsMapper.selectVoById(dto.getProjectId());

        Long constructionOrgId = prjProjectsVo.getConstructionOrgId();

        LambdaQueryWrapper<SysEnterpriseInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysEnterpriseInfo::getDeptId, constructionOrgId);

        SysEnterpriseInfo enterpriseInfo = sysEnterpriseInfoMapper.selectOne(wrapper);


        InputStream resource = getClass().getResourceAsStream("/word/xzcfjds.docx");

        HashMap<String, Object> model = new HashMap<>();

        model.put("sgfqy", enterpriseInfo.getEnterpriseName());
        model.put("tyshxydm", enterpriseInfo.getUnifiedSocialCreditCode());
        model.put("zs", enterpriseInfo.getBusinessAddress());
        model.put("fr", enterpriseInfo.getLegalRepresentative());
        model.put("questionArr", dto.getQuestion());

        XWPFTemplate template = XWPFTemplate.compile(resource).render(model);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        template.write(outputStream);

        byte[] bytes = outputStream.toByteArray();
        outputStream.close();

        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=123.docx");

        OutputStream out = response.getOutputStream();
        out.write(bytes);
        out.flush();
        out.close();
    }
}

package org.dromara.projects.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目录入对象 prj_projects
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prj_projects")
public class PrjProjects extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @TableId(value = "project_id")
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编码/标识
     */
    private String projectCode;

    /**
     * 工程概况 (对应附件一.1)
     */
    private String projectOverview;

    /**
     * 施工许可证编号 (对应附件一.2)
     */
    private String constructionPermitNo;

    /**
     * 施工许可证扫描件文档ID (逻辑外键至 sys_documents.document_id)
     */
    private Long constructionPermitDocId;

    /**
     * 项目位置（经纬度）
     */
    private String lola;

    /**
     * 省/直辖市编码
     */
    private String provinceCode;

    /**
     * 省/直辖市名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区/县编码
     */
    private String districtCode;

    /**
     * 区/县名称
     */
    private String districtName;

    /**
     * 乡镇/街道编码 (可选)
     */
    private String countyCode;

    /**
     * 乡镇/街道名称 (可选)
     */
    private String countyName;

    /**
     * 详细地址
     */
    private String locationDetail;

    /**
     * 占地面积(平方米)
     */
    private BigDecimal siteArea;

    /**
     * 预算投资总额(万元)
     */
    private BigDecimal budgetTotal;

    /**
     * 项目所属质监站机构ID
     */
    private Long supervisingQsOrgId;

    /**
     * 项目状态
     */
    private String status;

    /**
     * 计划开工日期
     */
    private Date startDate;

    /**
     * 计划竣工日期
     */
    private Date plannedEndDate;

    /**
     * 实际开工日期
     */
    private Date actualStartDate;

    /**
     * 实际竣工日期
     */
    private Date actualEndDate;

    /**
     * 建设单位ID (逻辑外键至 sys_dept.dept_id)
     */
    private Long clientOrgId;

    /**
     * 施工总包单位ID (逻辑外键至 sys_dept.dept_id)
     */
    private Long constructionOrgId;

    /**
     * 监理单位ID (逻辑外键至 sys_dept.dept_id)
     */
    private Long supervisionOrgId;

    /**
     * 设计单位ID (逻辑外键至 sys_dept.dept_id)
     */
    private Long designOrgId;

    /**
     * 勘察单位ID (逻辑外键至 sys_dept.dept_id)
     */
    private Long surveyOrgId;

    /**
     * 安拆单位ID (逻辑外键至 sys_dept.dept_id)
     */
    private Long installationDismantlingOrgId;

    /**
     * 维保单位ID (逻辑外键至 sys_dept.dept_id)
     */
    private Long maintenanceOrgId;

    /**
     * 专业分包单位ID列表 (JSON数组，元素为 sys_organizations.org_id)
     */
    private String subcontractorOrgIds;

    /**
     * 施工单位项目负责人ID (逻辑外键至 sys_users.user_id)
     */
    private Long projectManagerUserId;

    /**
     * 监理单位总监ID (逻辑外键至 sys_users.user_id)
     */
    private Long supervisionChiefEngUserId;

    /**
     * 危大工程安全防护文明施工措施费财务凭证文档ID (对应附件三.1)
     */
    private Long safetyMeasuresFeeDocId;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;


}

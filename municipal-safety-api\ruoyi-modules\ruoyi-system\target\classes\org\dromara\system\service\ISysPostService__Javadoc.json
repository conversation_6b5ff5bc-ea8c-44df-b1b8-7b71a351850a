{"doc": " 岗位信息 服务层\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPostList", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo"], "doc": " 查询岗位信息集合\n\n @param post 岗位信息\n @return 岗位列表\n"}, {"name": "selectPostsByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户所属岗位组\n\n @param userId 用户ID\n @return 岗位ID\n"}, {"name": "selectPostAll", "paramTypes": [], "doc": " 查询所有岗位\n\n @return 岗位列表\n"}, {"name": "selectPostById", "paramTypes": ["java.lang.Long"], "doc": " 通过岗位ID查询岗位信息\n\n @param postId 岗位ID\n @return 角色对象信息\n"}, {"name": "selectPostListByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID获取岗位选择框列表\n\n @param userId 用户ID\n @return 选中岗位ID列表\n"}, {"name": "selectPostByIds", "paramTypes": ["java.util.List"], "doc": " 通过岗位ID串查询岗位\n\n @param postIds 岗位id串\n @return 岗位列表信息\n"}, {"name": "checkPostNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo"], "doc": " 校验岗位名称\n\n @param post 岗位信息\n @return 结果\n"}, {"name": "checkPostCodeUnique", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo"], "doc": " 校验岗位编码\n\n @param post 岗位信息\n @return 结果\n"}, {"name": "countUserPostById", "paramTypes": ["java.lang.Long"], "doc": " 通过岗位ID查询岗位使用数量\n\n @param postId 岗位ID\n @return 结果\n"}, {"name": "countPostByDeptId", "paramTypes": ["java.lang.Long"], "doc": " 通过部门ID查询岗位使用数量\n\n @param deptId 部门id\n @return 结果\n"}, {"name": "deletePostById", "paramTypes": ["java.lang.Long"], "doc": " 删除岗位信息\n\n @param postId 岗位ID\n @return 结果\n"}, {"name": "deletePostByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除岗位信息\n\n @param postIds 需要删除的岗位ID\n @return 结果\n"}, {"name": "insertPost", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo"], "doc": " 新增保存岗位信息\n\n @param bo 岗位信息\n @return 结果\n"}, {"name": "updatePost", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo"], "doc": " 修改保存岗位信息\n\n @param bo 岗位信息\n @return 结果\n"}], "constructors": []}
{"doc": " 租户对象 sys_tenant\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " id\n"}, {"name": "tenantId", "doc": " 租户编号\n"}, {"name": "contactUserName", "doc": " 联系人\n"}, {"name": "contactPhone", "doc": " 联系电话\n"}, {"name": "companyName", "doc": " 企业名称\n"}, {"name": "licenseNumber", "doc": " 统一社会信用代码\n"}, {"name": "address", "doc": " 地址\n"}, {"name": "domain", "doc": " 域名\n"}, {"name": "intro", "doc": " 企业简介\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "packageId", "doc": " 租户套餐编号\n"}, {"name": "expireTime", "doc": " 过期时间\n"}, {"name": "accountCount", "doc": " 用户数量（-1不限制）\n"}, {"name": "status", "doc": " 租户状态（0正常 1停用）\n"}, {"name": "delFlag", "doc": " 删除标志（0代表存在 1代表删除）\n"}], "enumConstants": [], "methods": [], "constructors": []}
package org.dromara.facility.domain;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 绿能卸料平台对象 ln_dump_plat
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ln_dump_plat")
public class LnDumpPlat{

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 编号
     */
    private Long dumpnumber;

    /**
     * 最大载重,吨
     */
    private Long weightMax;

    /**
     * 重量,吨
     */
    private Long weight;

    /**
     * 倾角,度
     */
    private Long tilt;

    /**
     * 电池电压,v
     */
    private Long batvolt;

    /**
     * 吊重比例 %
     */
    private Long wightPercent;

    /**
     * 倾斜比例X %
     */
    private Long tiltPercentX;

    /**
     * 倾斜比例Y %
     */
    private Long tiltPercentY;

    /**
     * 报警信息
     */
    private Long alarmInfo;

    /**
     * 设备状态
     */
    private String status;

    /**
     * 重量空载实际值 kg
     */
    private Long idleWeightReal;

    /**
     * 重量负载实际值 kg
     */
    private Long loadWeightReal;

    /**
     * 载重预警百分比 %
     */
    private Long weightWarning;

    /**
     * 载重报警百分比 %
     */
    private Long weightAlarm;

    /**
     * 倾斜预警值
     */
    private Long tiltWarning;

    /**
     * 倾斜报警值
     */
    private Long tiltAlarm;

    /**
     * 设备ip
     */
    private String deviceIp;

    /**
     * 实时倾斜度X
     */
    private Long realTiltX;

    /**
     * 实时倾斜度Y
     */
    private Long realTiltY;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

package org.dromara.quality.service;

import org.dromara.quality.domain.vo.*;

/**
 * 质量管理概览Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IQualityOverviewService {

    /**
     * 获取质量管理概览数据
     *
     * @return 概览数据
     */
    QualityOverviewVo getOverviewData();

    /**
     * 获取设备统计数据
     *
     * @return 设备统计数据
     */
    DeviceStatisticsVo getDeviceStatistics();

    /**
     * 获取测量统计数据
     *
     * @return 测量统计数据
     */
    MeasurementStatisticsVo getMeasurementStatistics();

    /**
     * 获取测量趋势图表数据
     *
     * @param period    时间周期：week/month/quarter
     * @param startDate 开始日期 (YYYY-MM-DD)
     * @param endDate   结束日期 (YYYY-MM-DD)
     * @return 图表数据
     */
    ChartDataVo getChartData(String period, String startDate, String endDate);

    /**
     * 获取设备状态分布数据
     *
     * @return 设备状态分布数据
     */
    DeviceStatusDistributionVo getDeviceStatusDistribution();
} 
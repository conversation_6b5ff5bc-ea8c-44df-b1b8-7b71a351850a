package org.dromara.projects.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.projects.domain.PrjSafeTask;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 【项目管理】安拆任务视图对象 prj_safe_task
 *
 * <AUTHOR> Li
 * @date 2025-08-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjSafeTask.class)
public class PrjSafeTaskVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long openTaskId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目地址
     */
    @ExcelProperty(value = "项目地址")
    private String projectAddress;

    /**
     * 项目经度
     */
    @ExcelProperty(value = "项目经度")
    private String projectLongitude;

    /**
     * 项目维度
     */
    @ExcelProperty(value = "项目维度")
    private String projectLatitude;

    /**
     * 塔机现场编号
     */
    @ExcelProperty(value = "塔机现场编号")
    private String projectCraneNum;

    /**
     * 塔机类型
     * 平臂:FlatArm
     * 动臂:SwingArm
     * 塔头:Tower
     * 缺少该字段默认传：
     * FlatArm
     */
    @ExcelProperty(value = "塔机类型 平臂:FlatArm 动臂:SwingArm 塔头:Tower 缺少该字段默认传：FlatArm")
    private String craneType;

    /**
     * 塔机规格型号
     */
    @ExcelProperty(value = "塔机规格型号")
    private String craneModel;

    /**
     * 塔机出厂编号
     */
    @ExcelProperty(value = "塔机出厂编号")
    private String craneSn;

    /**
     * 塔机出厂日期
     */
    @ExcelProperty(value = "塔机出厂日期")
    private Date craneProductionDate;

    /**
     * 产权单位名称
     */
    @ExcelProperty(value = "产权单位名称")
    private String propertyCompanyName;

    /**
     * 塔机生产厂商名称
     */
    @ExcelProperty(value = "塔机生产厂商名称")
    private String factoryName;

    /**
     * 顶升降节作业类型
     * 顶升:JackingUp
     * 降节:Disassembly
     */
    @ExcelProperty(value = "顶升降节作业类型 顶升:JackingUp 降节:Disassembly")
    private String jackingType;

    /**
     * 执行日期
     */
    @ExcelProperty(value = "执行日期")
    private Date executioinDate;

    /**
     * 升/降节数
     */
    @ExcelProperty(value = "升/降节数")
    private Long sectionNum;

    /**
     * 加降节后
     * 塔机高度
     */
    @ExcelProperty(value = "加降节后 塔机高度")
    private Double modifiedCraneHeight;

    /**
     * 加降节前
     * 塔机高度
     */
    @ExcelProperty(value = "加降节前 塔机高度")
    private Double initialCraneHeight;

    /**
     * 安拆单位名称
     */
    @ExcelProperty(value = "安拆单位名称")
    private String installationUnitName;

    /**
     * 安拆单位
     * 资质地址
     */
    @ExcelProperty(value = "安拆单位 资质地址")
    private String installationUnitQualification;

    /**
     * 安全生产
     * 许可证地
     * 址
     */
    @ExcelProperty(value = "安全生产 许可证地 址")
    private String safetyProductionPermit;

    /**
     * 安拆任务状态
     */
    @ExcelProperty(value = "安拆任务状态")
    private String status;
}

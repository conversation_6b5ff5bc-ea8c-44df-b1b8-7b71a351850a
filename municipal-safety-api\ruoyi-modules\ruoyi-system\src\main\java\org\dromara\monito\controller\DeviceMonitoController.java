package org.dromara.monito.controller;


import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.web.core.TableDataInfo;
import org.dromara.monito.domain.bo.CaptureBo;
import org.dromara.system.domain.vo.DivisionVo;
import org.dromara.util.YsyClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.monito.domain.vo.DeviceMonitoVo;
import org.dromara.monito.domain.bo.DeviceMonitoBo;
import org.dromara.monito.service.IDeviceMonitoService;

import java.util.List;
import java.util.Map;

/**
 * 监控管理
 *
 * <AUTHOR> Li
 * @date 2025-05-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/monito/monito")
public class DeviceMonitoController extends BaseController {

    private final IDeviceMonitoService deviceMonitoService;
    private final YsyClient ysyClient;

    /**
     * 查询监控管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DeviceMonitoBo bo) {
        startPage();
        List<DeviceMonitoVo> list = deviceMonitoService.pageList(bo);
        return getDataTable(list);
    }

    /**
     * 导出监控管理列表
     */
    @Log(title = "监控管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(DeviceMonitoBo bo, HttpServletResponse response) {
        List<DeviceMonitoVo> list = deviceMonitoService.queryList(bo);
        ExcelUtil.exportExcel(list, "监控管理", DeviceMonitoVo.class, response);
    }

    /**
     * 获取监控管理详细信息
     *
     * @param monitoId 主键
     */
    @GetMapping("/{monitoId}")
    public R<DeviceMonitoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long monitoId) {
        return R.ok(deviceMonitoService.selectById(monitoId));
    }

    /**
     * 新增监控管理
     */
    @Log(title = "监控管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DeviceMonitoBo bo) {
        return toAjax(deviceMonitoService.insertByBo(bo));
    }

    /**
     * 修改监控管理
     */
    @Log(title = "监控管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DeviceMonitoBo bo) {
        return toAjax(deviceMonitoService.updateByBo(bo));
    }

    /**
     * 删除监控管理
     *
     * @param monitoIds 主键串
     */
    @Log(title = "监控管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{monitoIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] monitoIds) {
        return toAjax(deviceMonitoService.deleteWithValidByIds(List.of(monitoIds), true));
    }

    /**
     * 监控列表  左边
     */
    @GetMapping("/showList")
    public R<List<Map<String, Object>>> showList(DeviceMonitoBo bo) {
        return R.ok(deviceMonitoService.getDeviceMonitoTreeInfo(bo));
    }

    /**
     * 获取播放地址
     */
    @GetMapping("/playUrl/{deviceCode}/{channelNo}")
    public R<String> playUrl(@NotBlank(message = "设备编号不能为空")
                             @PathVariable String deviceCode, @PathVariable Integer channelNo) {
        String ezopen = null;
        try {
            ezopen = ysyClient.getEzopen(deviceCode, channelNo);
        } catch (Exception e) {
            throw new ServiceException("设备异常！播放失败！");
        }
        return R.ok(ezopen);
    }

    /**
     * 获取token
     */
    @GetMapping("/getToken")
    public R<String> getToken() {
        return R.ok(ysyClient.getAccessToken());
    }

    /**
     * 截图并送ai隐患
     *
     * @param captureBo
     * @return
     */
    @PostMapping("/capture")
    public R<Boolean> capture(@RequestBody CaptureBo captureBo) {
        return R.ok(deviceMonitoService.capture(captureBo));
    }

//    /**
//     * 无人机截图并送ai隐患
//     *
//     * @param captureBo
//     * @return
//     */
//    @PostMapping("/capturePlane")
//    public R<Boolean> captureplane(@RequestBody CaptureBo captureBo) {
//        return R.ok(deviceMonitoService.capturePlane(captureBo));
//    }
}

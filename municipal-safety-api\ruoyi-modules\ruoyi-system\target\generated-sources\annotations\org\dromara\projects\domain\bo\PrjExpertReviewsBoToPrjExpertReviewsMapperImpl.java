package org.dromara.projects.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjExpertReviews;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjExpertReviewsBoToPrjExpertReviewsMapperImpl implements PrjExpertReviewsBoToPrjExpertReviewsMapper {

    @Override
    public PrjExpertReviews convert(PrjExpertReviewsBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjExpertReviews prjExpertReviews = new PrjExpertReviews();

        prjExpertReviews.setSearchValue( arg0.getSearchValue() );
        prjExpertReviews.setCreateDept( arg0.getCreateDept() );
        prjExpertReviews.setCreateBy( arg0.getCreateBy() );
        prjExpertReviews.setCreateTime( arg0.getCreateTime() );
        prjExpertReviews.setUpdateBy( arg0.getUpdateBy() );
        prjExpertReviews.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            prjExpertReviews.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        prjExpertReviews.setReviewId( arg0.getReviewId() );
        prjExpertReviews.setPlanId( arg0.getPlanId() );
        prjExpertReviews.setReviewDate( arg0.getReviewDate() );
        prjExpertReviews.setMeetingLocation( arg0.getMeetingLocation() );
        prjExpertReviews.setConclusion( arg0.getConclusion() );
        prjExpertReviews.setExpertOpinionSummary( arg0.getExpertOpinionSummary() );
        prjExpertReviews.setConflictOfInterestWarning( arg0.getConflictOfInterestWarning() );
        prjExpertReviews.setReportDocumentId( arg0.getReportDocumentId() );
        prjExpertReviews.setConvenorUserId( arg0.getConvenorUserId() );

        return prjExpertReviews;
    }

    @Override
    public PrjExpertReviews convert(PrjExpertReviewsBo arg0, PrjExpertReviews arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setReviewId( arg0.getReviewId() );
        arg1.setPlanId( arg0.getPlanId() );
        arg1.setReviewDate( arg0.getReviewDate() );
        arg1.setMeetingLocation( arg0.getMeetingLocation() );
        arg1.setConclusion( arg0.getConclusion() );
        arg1.setExpertOpinionSummary( arg0.getExpertOpinionSummary() );
        arg1.setConflictOfInterestWarning( arg0.getConflictOfInterestWarning() );
        arg1.setReportDocumentId( arg0.getReportDocumentId() );
        arg1.setConvenorUserId( arg0.getConvenorUserId() );

        return arg1;
    }
}

package org.dromara.flow.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.PrjHazardousItemsSpecialWarning;
import org.dromara.flow.domain.PrjHazardousItemsSpecialWarningToPrjHazardousItemsSpecialWarningVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjHazardousItemsSpecialWarningToPrjHazardousItemsSpecialWarningVoMapper.class},
    imports = {}
)
public interface PrjHazardousItemsSpecialWarningVoToPrjHazardousItemsSpecialWarningMapper extends BaseMapper<PrjHazardousItemsSpecialWarningVo, PrjHazardousItemsSpecialWarning> {
}

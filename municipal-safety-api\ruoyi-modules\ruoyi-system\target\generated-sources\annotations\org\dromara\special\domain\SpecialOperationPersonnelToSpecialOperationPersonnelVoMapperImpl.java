package org.dromara.special.domain;

import javax.annotation.processing.Generated;
import org.dromara.special.domain.vo.SpecialOperationPersonnelVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class SpecialOperationPersonnelToSpecialOperationPersonnelVoMapperImpl implements SpecialOperationPersonnelToSpecialOperationPersonnelVoMapper {

    @Override
    public SpecialOperationPersonnelVo convert(SpecialOperationPersonnel arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SpecialOperationPersonnelVo specialOperationPersonnelVo = new SpecialOperationPersonnelVo();

        specialOperationPersonnelVo.setSopId( arg0.getSopId() );
        specialOperationPersonnelVo.setCertificateNumber( arg0.getCertificateNumber() );
        specialOperationPersonnelVo.setName( arg0.getName() );
        specialOperationPersonnelVo.setIdCard( arg0.getIdCard() );
        specialOperationPersonnelVo.setGender( arg0.getGender() );
        specialOperationPersonnelVo.setBirthdate( arg0.getBirthdate() );
        specialOperationPersonnelVo.setOperationCategory( arg0.getOperationCategory() );
        specialOperationPersonnelVo.setIssuer( arg0.getIssuer() );
        specialOperationPersonnelVo.setFirstIssueDate( arg0.getFirstIssueDate() );
        specialOperationPersonnelVo.setLastIssueDate( arg0.getLastIssueDate() );
        specialOperationPersonnelVo.setValidityStart( arg0.getValidityStart() );
        specialOperationPersonnelVo.setValidityEnd( arg0.getValidityEnd() );
        specialOperationPersonnelVo.setStatus( arg0.getStatus() );
        specialOperationPersonnelVo.setElectronicLicenseUrl( arg0.getElectronicLicenseUrl() );
        specialOperationPersonnelVo.setElectronicLicenseId( arg0.getElectronicLicenseId() );
        specialOperationPersonnelVo.setProjectId( arg0.getProjectId() );
        specialOperationPersonnelVo.setDelFlag( arg0.getDelFlag() );
        specialOperationPersonnelVo.setCompanyName( arg0.getCompanyName() );
        specialOperationPersonnelVo.setCompanyCode( arg0.getCompanyCode() );
        specialOperationPersonnelVo.setCompanyType( arg0.getCompanyType() );
        specialOperationPersonnelVo.setPersonType( arg0.getPersonType() );

        return specialOperationPersonnelVo;
    }

    @Override
    public SpecialOperationPersonnelVo convert(SpecialOperationPersonnel arg0, SpecialOperationPersonnelVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSopId( arg0.getSopId() );
        arg1.setCertificateNumber( arg0.getCertificateNumber() );
        arg1.setName( arg0.getName() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setGender( arg0.getGender() );
        arg1.setBirthdate( arg0.getBirthdate() );
        arg1.setOperationCategory( arg0.getOperationCategory() );
        arg1.setIssuer( arg0.getIssuer() );
        arg1.setFirstIssueDate( arg0.getFirstIssueDate() );
        arg1.setLastIssueDate( arg0.getLastIssueDate() );
        arg1.setValidityStart( arg0.getValidityStart() );
        arg1.setValidityEnd( arg0.getValidityEnd() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setElectronicLicenseUrl( arg0.getElectronicLicenseUrl() );
        arg1.setElectronicLicenseId( arg0.getElectronicLicenseId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setDelFlag( arg0.getDelFlag() );
        arg1.setCompanyName( arg0.getCompanyName() );
        arg1.setCompanyCode( arg0.getCompanyCode() );
        arg1.setCompanyType( arg0.getCompanyType() );
        arg1.setPersonType( arg0.getPersonType() );

        return arg1;
    }
}

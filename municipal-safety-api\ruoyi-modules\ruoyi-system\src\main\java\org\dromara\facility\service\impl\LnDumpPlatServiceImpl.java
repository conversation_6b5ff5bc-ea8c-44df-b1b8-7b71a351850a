package org.dromara.facility.service.impl;

import com.alibaba.fastjson2.JSON;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.facility.domain.bo.LnDumpPlatBo;
import org.dromara.facility.domain.vo.LnDumpPlatVo;
import org.dromara.facility.domain.LnDumpPlat;
import org.dromara.facility.mapper.LnDumpPlatMapper;
import org.dromara.facility.service.ILnDumpPlatService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 绿能卸料平台Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@RequiredArgsConstructor
@Service
public class LnDumpPlatServiceImpl implements ILnDumpPlatService {

    private final LnDumpPlatMapper baseMapper;

    /**
     * 查询绿能卸料平台
     *
     * @param id 主键
     * @return 绿能卸料平台
     */
    @Override
    public LnDumpPlatVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询绿能卸料平台列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能卸料平台分页列表
     */
    @Override
    public TableDataInfo<LnDumpPlatVo> queryPageList(LnDumpPlatBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LnDumpPlat> lqw = buildQueryWrapper(bo);
        Page<LnDumpPlatVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的绿能卸料平台列表
     *
     * @param bo 查询条件
     * @return 绿能卸料平台列表
     */
    @Override
    public List<LnDumpPlatVo> queryList(LnDumpPlatBo bo) {
        LambdaQueryWrapper<LnDumpPlat> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LnDumpPlat> buildQueryWrapper(LnDumpPlatBo bo) {
        LambdaQueryWrapper<LnDumpPlat> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(LnDumpPlat::getCreateTime);
        lqw.eq(LnDumpPlat::getDevNo, bo.getDevNo());
        return lqw;
    }

    /**
     * 新增绿能卸料平台
     *
     * @param bo 绿能卸料平台
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LnDumpPlatBo bo) {
        LnDumpPlat add = MapstructUtils.convert(bo, LnDumpPlat.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }


    @Override
    public void insertByJson(String jsonString) {

        LnDumpPlatBo bo = JSON.parseObject(jsonString, LnDumpPlatBo.class);

        LnDumpPlat add = MapstructUtils.convert(bo, LnDumpPlat.class);

        add.setCreateTime(new Date());

        baseMapper.insert(add);
    }

    /**
     * 修改绿能卸料平台
     *
     * @param bo 绿能卸料平台
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LnDumpPlatBo bo) {
        LnDumpPlat update = MapstructUtils.convert(bo, LnDumpPlat.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LnDumpPlat entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除绿能卸料平台信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


}

package org.dromara.facility.service;

import org.dromara.facility.domain.vo.LnEdgeGuardVo;
import org.dromara.facility.domain.bo.LnEdgeGuardBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 绿能临边防护Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
public interface ILnEdgeGuardService extends BaseFacilityHandle {

    /**
     * 查询绿能临边防护
     *
     * @param id 主键
     * @return 绿能临边防护
     */
    LnEdgeGuardVo queryById(Long id);

    /**
     * 分页查询绿能临边防护列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能临边防护分页列表
     */
    TableDataInfo<LnEdgeGuardVo> queryPageList(LnEdgeGuardBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的绿能临边防护列表
     *
     * @param bo 查询条件
     * @return 绿能临边防护列表
     */
    List<LnEdgeGuardVo> queryList(LnEdgeGuardBo bo);

    /**
     * 新增绿能临边防护
     *
     * @param bo 绿能临边防护
     * @return 是否新增成功
     */
    Boolean insertByBo(LnEdgeGuardBo bo);

    /**
     * 修改绿能临边防护
     *
     * @param bo 绿能临边防护
     * @return 是否修改成功
     */
    Boolean updateByBo(LnEdgeGuardBo bo);

    /**
     * 校验并批量删除绿能临边防护信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

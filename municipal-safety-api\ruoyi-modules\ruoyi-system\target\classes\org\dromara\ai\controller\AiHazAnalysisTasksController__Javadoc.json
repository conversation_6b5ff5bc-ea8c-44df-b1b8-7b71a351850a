{"doc": " 智能隐患分析任务\n\n <AUTHOR>\n @date 2025-05-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.ai.domain.dto.AiHazAnalysisTasksDto", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询智能隐患分析任务列表\n"}, {"name": "export", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出智能隐患分析任务列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取智能隐患分析任务详细信息\n\n @param taskId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksBo"], "doc": " 新增智能隐患分析任务\n"}, {"name": "edit", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksBo"], "doc": " 修改智能隐患分析任务\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除智能隐患分析任务\n\n @param taskIds 主键串\n"}, {"name": "testConnection", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 推送工单\n\n @param taskId\n @return\n"}, {"name": "testConnection2", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistBo"], "doc": " 推送工单\n\n @return\n"}], "constructors": []}
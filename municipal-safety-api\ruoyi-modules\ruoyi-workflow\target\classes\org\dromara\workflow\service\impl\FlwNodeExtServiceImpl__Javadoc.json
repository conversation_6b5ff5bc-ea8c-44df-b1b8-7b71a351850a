{"doc": " 流程设计器-节点扩展属性\n\n <AUTHOR>\n", "fields": [{"name": "PERMISSION_TAB", "doc": " 权限页code\n"}, {"name": "PERMISSION_TAB_NAME", "doc": " 权限页名称\n"}, {"name": "TYPE_BASE_SETTING", "doc": " 基础设置\n"}, {"name": "TYPE_NEW_TAB", "doc": " 新页签\n"}, {"name": "CHILD_NODE_MAP", "doc": " 存储不同 dictType 对应的配置信息\n"}], "enumConstants": [], "methods": [{"name": "getNodeExt", "paramTypes": [], "doc": " 获取节点扩展属性\n\n @return 节点扩展属性列表\n"}, {"name": "buildNodeExt", "paramTypes": ["java.lang.String", "java.lang.String", "int", "java.util.List"], "doc": " 构建一个 `NodeExt` 对象\n\n @param code    唯一编码\n @param name    名称（新页签时，作为页签名称）\n @param type    节点类型（1: 基础设置，2: 新页签）\n @param sources 数据来源（枚举类或字典类型）\n @return 构建的 `NodeExt` 对象\n"}, {"name": "buildChildNode", "paramTypes": ["java.lang.Class"], "doc": " 根据枚举类型构建一个 `ChildNode` 对象\n\n @param enumClass 枚举类，必须实现 `NodeExtEnum` 接口\n @return 构建的 `ChildNode` 对象\n"}, {"name": "buildChildNode", "paramTypes": ["java.lang.String"], "doc": " 根据字典类型构建 `ChildNode` 对象\n\n @param dictType 字典类型\n @return 构建的 `ChildNode` 对象\n"}, {"name": "buildChildNodeMap", "paramTypes": ["java.lang.String"], "doc": " 根据 CHILD_NODE_MAP 中的配置信息，构建一个基本的 ChildNode 对象\n 该方法用于设置 ChildNode 的常规属性，例如 label、type、是否必填、是否多选等\n\n @param key CHILD_NODE_MAP 的 key\n @return 返回构建好的 ChildNode 对象\n"}], "constructors": []}
package org.dromara.projects.domain.bo;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.projects.domain.PrjConstructionPlans;

/**
 * [项目管理] 存储危大工程专项施工方案信息及其审批状态业务对象 prj_construction_plans
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjConstructionPlans.class, reverseConvertGenerate = false)
public class PrjConstructionPlansBo extends BaseEntity {

    /**
     * 方案ID (主键)
     */
    @NotNull(message = "方案ID (主键)不能为空", groups = { EditGroup.class })
    private Long planId;

    /**
     * 危大工程项ID
     */
    @NotNull(message = "危大工程项ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long itemId;

    /**
     * 专项施工方案名称
     */
    private String planName;

    /**
     * 方案版本号
     */
    private String planVersion;

    /**
     * 专项施工方案电子版文件ID
     */
    @NotNull(message = "专项施工方案电子版文件ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long planDocumentId;

    /**
     * 专项施工方案审批表文件ID
     */
    private Long approvalFormDocId;

    /**
     * AI校验状态
     */
    private String reviewStatus;

    /**
     * AI对比方案缺陷预警提示内容
     */
    private String aiDefectWarningDetails;

}

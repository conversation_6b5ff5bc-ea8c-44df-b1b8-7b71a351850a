package org.dromara.special.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.special.domain.bo.SpecialEquipmentBoToSpecialEquipmentMapper;
import org.dromara.special.domain.vo.SpecialEquipmentVo;
import org.dromara.special.domain.vo.SpecialEquipmentVoToSpecialEquipmentMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {SpecialEquipmentBoToSpecialEquipmentMapper.class,SpecialEquipmentVoToSpecialEquipmentMapper.class},
    imports = {}
)
public interface SpecialEquipmentToSpecialEquipmentVoMapper extends BaseMapper<SpecialEquipment, SpecialEquipmentVo> {
}

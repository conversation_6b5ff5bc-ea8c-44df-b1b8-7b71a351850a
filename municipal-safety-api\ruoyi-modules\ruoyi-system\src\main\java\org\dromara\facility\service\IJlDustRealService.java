package org.dromara.facility.service;

import org.dromara.facility.domain.vo.JlDustRealVo;
import org.dromara.facility.domain.bo.JlDustRealBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 扬尘数据Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
public interface IJlDustRealService extends BaseFacilityHandle {

    /**
     * 查询扬尘数据
     *
     * @param id 主键
     * @return 扬尘数据
     */
    JlDustRealVo queryById(Long id);

    /**
     * 分页查询扬尘数据列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 扬尘数据分页列表
     */
    TableDataInfo<JlDustRealVo> queryPageList(JlDustRealBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的扬尘数据列表
     *
     * @param bo 查询条件
     * @return 扬尘数据列表
     */
    List<JlDustRealVo> queryList(JlDustRealBo bo);

    /**
     * 新增扬尘数据
     *
     * @param bo 扬尘数据
     * @return 是否新增成功
     */
    Boolean insertByBo(JlDustRealBo bo);

    /**
     * 修改扬尘数据
     *
     * @param bo 扬尘数据
     * @return 是否修改成功
     */
    Boolean updateByBo(JlDustRealBo bo);

    /**
     * 校验并批量删除扬尘数据信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

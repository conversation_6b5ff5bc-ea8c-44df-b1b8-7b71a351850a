package org.dromara.common.translation.core.impl;

import lombok.AllArgsConstructor;
import org.dromara.common.core.service.DeptService;
import org.dromara.common.core.service.UserService;
import org.dromara.common.translation.annotation.TranslationType;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.common.translation.core.TranslationInterface;

import java.util.Collections;

/**
 * 部门名称通过用户id查询 翻译实现
 *
 * <AUTHOR> Li
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.USER_ID_TO_DEPT_NAME)
public class DeptNameByUserIdTranslationImpl implements TranslationInterface<String> {

    private final UserService userService;
    private final DeptService deptService;

    @Override
    public String translation(Object key, String other) {
        if (key instanceof String id) {
            Long deptId = userService.selectDeptIdByUserId(Long.valueOf(id));
            if (deptId != null) {
                //查询部门名称
                return deptService.selectDeptNameByIds(deptId.toString());
            }
        }
        return null;
    }
}

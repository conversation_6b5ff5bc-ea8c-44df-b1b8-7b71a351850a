package org.dromara.expert.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.expert.domain.bo.ExpertBoToExpertMapper;
import org.dromara.expert.domain.vo.ExpertVo;
import org.dromara.expert.domain.vo.ExpertVoToExpertMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {ExpertBoToExpertMapper.class,ExpertVoToExpertMapper.class},
    imports = {}
)
public interface ExpertToExpertVoMapper extends BaseMapper<Expert, ExpertVo> {
}

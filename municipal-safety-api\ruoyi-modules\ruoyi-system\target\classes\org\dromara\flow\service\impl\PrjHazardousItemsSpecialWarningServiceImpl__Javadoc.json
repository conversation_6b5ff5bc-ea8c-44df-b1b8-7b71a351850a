{"doc": " 特殊预警Service业务层处理\n\n <AUTHOR>\n @date 2025-06-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询特殊预警\n\n @param warningId 主键\n @return 特殊预警\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialWarningBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询特殊预警列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 特殊预警分页列表\n"}, {"name": "getProjectNameByIds", "paramTypes": ["java.util.List"], "doc": " 通过项目id集合获取项目名称\n\n @param projectIds\n @return\n"}, {"name": "getItemNameByIds", "paramTypes": ["java.util.List"], "doc": " 通过工程项目id获取工程名称\n\n @param itemIds\n @return\n"}, {"name": "queryList", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialWarningBo"], "doc": " 查询符合条件的特殊预警列表\n\n @param bo 查询条件\n @return 特殊预警列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialWarningBo"], "doc": " 新增特殊预警\n\n @param bo 特殊预警\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialWarningBo"], "doc": " 修改特殊预警\n\n @param bo 特殊预警\n @return 是否修改成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.flow.domain.PrjHazardousItemsSpecialWarning"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除特殊预警信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
package org.dromara.facility.domain.bo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.facility.domain.LnDumpPlat;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.Date;

/**
 * 绿能卸料平台业务对象 ln_dump_plat
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LnDumpPlat.class, reverseConvertGenerate = false)
public class LnDumpPlatBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 编号
     */
    private Long dumpnumber;

    /**
     * 最大载重,吨
     */
    private Long weightMax;

    /**
     * 重量,吨
     */
    private Long weight;

    /**
     * 倾角,度
     */
    private Long tilt;

    /**
     * 电池电压,v
     */
    private Long batvolt;

    /**
     * 吊重比例 %
     */
    private Long wightPercent;

    /**
     * 倾斜比例X %
     */
    private Long tiltPercentX;

    /**
     * 倾斜比例Y %
     */
    private Long tiltPercentY;

    /**
     * 报警信息
     */
    private Long alarmInfo;

    /**
     * 设备状态
     */
    private String status;

    /**
     * 重量空载实际值 kg
     */
    private Long idleWeightReal;

    /**
     * 重量负载实际值 kg
     */
    private Long loadWeightReal;

    /**
     * 载重预警百分比 %
     */
    private Long weightWarning;

    /**
     * 载重报警百分比 %
     */
    private Long weightAlarm;

    /**
     * 倾斜预警值
     */
    private Long tiltWarning;

    /**
     * 倾斜报警值
     */
    private Long tiltAlarm;

    /**
     * 设备ip
     */
    private String deviceIp;

    /**
     * 实时倾斜度X
     */
    private Long realTiltX;

    /**
     * 实时倾斜度Y
     */
    private Long realTiltY;

    /**
     * 设备编号
     */
    private String devNo;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

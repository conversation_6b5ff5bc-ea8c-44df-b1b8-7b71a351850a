<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.person.mapper.SysPersonMapper">

    <select id="selectPersonIdByIdCard" resultType="java.lang.Long">
        select person_id from sys_person where id_card = #{idCard}
    </select>

    <select id="queryByIdCard" resultType="org.dromara.person.domain.vo.SysPersonVo">
        select * from sys_person where id_card = #{idCard}
    </select>
</mapper>

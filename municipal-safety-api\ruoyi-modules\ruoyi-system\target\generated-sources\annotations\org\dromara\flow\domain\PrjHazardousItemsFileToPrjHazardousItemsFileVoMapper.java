package org.dromara.flow.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.bo.PrjHazardousItemsFileBoToPrjHazardousItemsFileMapper;
import org.dromara.flow.domain.vo.PrjHazardousItemsFileVo;
import org.dromara.flow.domain.vo.PrjHazardousItemsFileVoToPrjHazardousItemsFileMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjHazardousItemsFileBoToPrjHazardousItemsFileMapper.class,PrjHazardousItemsFileVoToPrjHazardousItemsFileMapper.class},
    imports = {}
)
public interface PrjHazardousItemsFileToPrjHazardousItemsFileVoMapper extends BaseMapper<PrjHazardousItemsFile, PrjHazardousItemsFileVo> {
}

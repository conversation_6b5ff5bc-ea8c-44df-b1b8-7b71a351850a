import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { EquipmentVO, EquipmentForm, EquipmentQuery } from '@/api/special/equipment/types';

/**
 * 查询特种设备列表
 * @param query
 * @returns {*}
 */

export const listEquipment = (query?: EquipmentQuery): AxiosPromise<EquipmentVO[]> => {
  return request({
    url: '/special/equipment/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询特种设备详细
 * @param equipmentId
 */
export const getEquipment = (equipmentId: string | number): AxiosPromise<EquipmentVO> => {
  return request({
    url: '/special/equipment/' + equipmentId,
    method: 'get'
  });
};

/**
 * 新增特种设备
 * @param data
 */
export const addEquipment = (data: EquipmentForm) => {
  return request({
    url: '/special/equipment',
    method: 'post',
    data: data
  });
};

/**
 * 修改特种设备
 * @param data
 */
export const updateEquipment = (data: EquipmentForm) => {
  return request({
    url: '/special/equipment',
    method: 'put',
    data: data
  });
};

/**
 * 删除特种设备
 * @param equipmentId
 */
export const delEquipment = (equipmentId: string | number | Array<string | number>) => {
  return request({
    url: '/special/equipment/' + equipmentId,
    method: 'delete'
  });
};
// 塔吊的实时数据
export const getTowerRealTimeData = (params: { pageNum: number; pageSize: number; devNo: string }) => {
  return request({
    url: '/system/towerReal/list',
    method: 'get',
    params
  });
};
// 升降机的实时数据
export const getLiftRealTimeData = (params: { pageNum: number; pageSize: number; devNo: string }) => {
  return request({
    url: '/system/lifterReal/list',
    method: 'get',
    params
  });
};
// 卸料平台的实时数据
export const listDumpPlat = (params: { pageNum: number; pageSize: number; devNo: string }) => {
  return request({
    url: '/system/lnDumpPlat/list',
    method: 'get',
    params
  });
};
// 发证机关
export const getAuthorityData = () => {
  return request({
    url: '/special/equipment/getAuthorityData',
    method: 'get',
  });
};

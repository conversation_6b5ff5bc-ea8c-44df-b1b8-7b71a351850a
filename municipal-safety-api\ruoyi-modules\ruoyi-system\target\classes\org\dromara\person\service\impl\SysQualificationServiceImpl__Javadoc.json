{"doc": " 人员资格证书Service业务层处理\n\n <AUTHOR>\n @date 2025-05-10\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询人员资格证书\n\n @param qualificationId 主键\n @return 人员资格证书\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.person.domain.bo.SysQualificationBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询人员资格证书列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 人员资格证书分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.person.domain.bo.SysQualificationBo"], "doc": " 查询符合条件的人员资格证书列表\n\n @param bo 查询条件\n @return 人员资格证书列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.person.domain.bo.SysQualificationBo"], "doc": " 新增人员资格证书\n\n @param bo 人员资格证书\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.person.domain.bo.SysQualificationBo"], "doc": " 修改人员资格证书\n\n @param bo 人员资格证书\n @return 是否修改成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.person.domain.SysQualification"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除人员资格证书信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
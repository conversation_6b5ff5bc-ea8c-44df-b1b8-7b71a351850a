package org.dromara.projects.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.attendance.domain.bo.MAttPersonBo;
import org.dromara.attendance.domain.so.BaseVisitor;
import org.dromara.attendance.domain.so.ClearFace;
import org.dromara.attendance.domain.vo.MAttPersonVo;
import org.dromara.attendance.service.IMAttPersonService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.projects.domain.bo.PrjPersonnelBo;
import org.dromara.projects.domain.vo.PrjPersonnelVo;
import org.dromara.projects.service.IPrjPersonnelService;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目人员关联表
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/projects/personnel")
public class PrjPersonnelController extends BaseController {

    private final IPrjPersonnelService prjPersonnelService;

    @Resource
    private IMAttPersonService imAttPersonService;

    @Value("${face.apiUrl}")
    private String apiUrl;

    /**
     * 查询项目人员关联表列表
     */
    @SaCheckPermission("projects:personnel:list")
    @GetMapping("/list")
    public TableDataInfo<PrjPersonnelVo> list(PrjPersonnelBo bo, PageQuery pageQuery) {
        return prjPersonnelService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出项目人员关联表列表
     */
    @SaCheckPermission("projects:personnel:export")
    @Log(title = "项目人员关联表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PrjPersonnelBo bo, HttpServletResponse response) {
        List<PrjPersonnelVo> list = prjPersonnelService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目人员关联表", PrjPersonnelVo.class, response);
    }

    /**
     * 获取项目人员关联表详细信息
     *
     * @param projectPersonnelId 主键
     */
    @SaCheckPermission("projects:personnel:query")
    @GetMapping("/{projectPersonnelId}")
    public R<PrjPersonnelVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long projectPersonnelId) {
        return R.ok(prjPersonnelService.queryById(projectPersonnelId));
    }

    /**
     * 新增项目人员关联表
     */
    @SaCheckPermission("projects:personnel:add")
    @Log(title = "项目人员关联表", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PrjPersonnelBo bo) {
        return toAjax(prjPersonnelService.insertByBo(bo));
    }

    /**
     * 修改项目人员关联表
     */
    @SaCheckPermission("projects:personnel:edit")
    @Log(title = "项目人员关联表", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PrjPersonnelBo bo) {
        return toAjax(prjPersonnelService.updateByBo(bo));
    }

    /**
     * 删除项目人员关联表
     *
     * @param projectPersonnelIds 主键串
     */
//    @SaCheckPermission("projects:personnel:remove")
    @Log(title = "项目人员关联表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{projectPersonnelIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] projectPersonnelIds) {
        return toAjax(prjPersonnelService.deleteWithValidByIds(List.of(projectPersonnelIds), true));
    }

    /**
     * 获取项目人员列表
     *
     * @param projectId 项目ID
     */
    @GetMapping("/listByProjectId/{projectId}")
    public R<List<PrjPersonnelVo>> listByProjectId(@NotNull(message = "项目ID不能为空")
                                                   @PathVariable Long projectId) {
        List<PrjPersonnelVo> list = prjPersonnelService.queryPersonnelListByProjectId(projectId);
        return R.ok(list);
    }

    /**
     * 批量下发人脸到考勤机(第三方接口)
     * CompletableFuture<Void>
     * @param baseVisitor
     * @return
     */
    @PostMapping("/batch/distribute")
    public R<Void> batchDistribution(@Validated(AddGroup.class) @RequestBody List<BaseVisitor> baseVisitor) {
        try {
//            ExecutorService executor = Executors.newFixedThreadPool(5);
//            executor.submit(() -> {
//                // 异步任务
//                System.out.println("使用线程池执行异步任务");
//                // 执行耗时操作...
//
//            });
//            executor.shutdown(); // 不再接受新任务，等待已提交任务完成
            String responseBody = sendPostRequest(baseVisitor, apiUrl + "/modules/visitor/batch");
            JSONObject jsonObject = new JSONObject(responseBody);
            int statusCode = jsonObject.getInt("code");
            String statusMsg = jsonObject.getString("msg");
            if (statusCode == HttpURLConnection.HTTP_OK) {
                for (BaseVisitor visitor : baseVisitor) {
                    String[] split = visitor.getSnIds().split(",");
                    for (String s : split) {
                        MAttPersonVo mAttPersonVo = imAttPersonService.selectMAttPersonByPersonIdAndSnId(s, visitor.getId());
                        MAttPersonBo mAttPersonBo = new MAttPersonBo();
                        mAttPersonBo.setSnId(Long.valueOf(s));
                        mAttPersonBo.setPersonId(visitor.getId());
                        if (ObjectUtils.isEmpty(mAttPersonVo)) {
                            imAttPersonService.insertByBo(mAttPersonBo);
                        }
                    }
                }
                log.info("请求处理成功");
                return R.ok();
            } else {
                log.error("请求发送失败: " + statusMsg);
                throw new ServiceException("响应码: " + statusCode + "，响应结果：" + statusMsg);
            }
        } catch (Exception e) {
            log.error("请求发送失败: " + e.getMessage());
            throw new ServiceException("请求发送失败: " + e.getMessage());
        }

//        CompletableFuture.supplyAsync(() -> {
//            try {
//                return sendPostRequest(baseVisitor, apiUrl + "/modules/visitor/batch");
//            } catch (Exception e) {
//                throw new CompletionException(e);
//            }
//        }).thenApplyAsync(responseBody -> {
//            JSONObject jsonObject = new JSONObject(responseBody);
//            int statusCode = jsonObject.getInt("code");
//            String statusMsg = jsonObject.getString("msg");
//
//            if (statusCode != HttpURLConnection.HTTP_OK) {
//                throw new CompletionException(new ServiceException(statusMsg + statusCode));
//            }
//            return baseVisitor;
//        }).thenAcceptAsync(visitors -> {
//            for (BaseVisitor visitor : visitors) {
//                String[] split = visitor.getSnIds().split(",");
//                for (String s : split) {
//                    MAttPersonVo mAttPersonVo = imAttPersonService.selectMAttPersonByPersonIdAndSnId(s, visitor.getId());
//                    MAttPersonBo mAttPersonBo = new MAttPersonBo();
//                    mAttPersonBo.setSnId(Long.valueOf(s));
//                    mAttPersonBo.setPersonId(visitor.getId());
//                    if (ObjectUtils.isEmpty(mAttPersonVo)) {
//                        imAttPersonService.insertByBo(mAttPersonBo);
//                    }
//                }
//            }
//            log.info("请求处理成功");
//        }).exceptionally(ex -> {
//            Throwable cause = ex instanceof CompletionException ? ex.getCause() : ex;
//            if (cause instanceof ServiceException) {
//                log.error("API返回错误: " + cause.getMessage());
//            } else {
//                log.error("请求处理失败: " + cause.getMessage());
//            }
//            throw new CompletionException(cause);
//        });

    }

    /**
     * 删除人脸信息
     * @param clearFace 人脸参数
     * @return 返回值
     * @throws IOException 异常处理
     */
    @PostMapping("/removeSns")
    public R<Void> removeSns(@RequestBody ClearFace clearFace) {
        try {
            String responseBody = sendDeleteRequest(apiUrl + "/modules/visitor/removeSns/" + clearFace.getSns() + "/" + clearFace.getProjectId() + "/" + clearFace.getIdCard());
            JSONObject jsonObject = new JSONObject(responseBody);
            int statusCode = jsonObject.getInt("code");
            String statusMsg = jsonObject.getString("msg");
            if (statusCode == HttpURLConnection.HTTP_OK) {
                return toAjax(imAttPersonService.deleteWithValidByIds(List.of(clearFace.getPersonSnIds()), true));
            } else {
                log.error("请求发送失败: " + statusMsg);
                throw new ServiceException("响应码: " + statusCode + "，响应结果：" + statusMsg);
            }
        } catch (Exception e) {
            log.error("请求发送失败: " + e.getMessage());
            throw new ServiceException("请求发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送POST请求到第三方接口
     *
     * @param data   要发送的数据列表
     * @param apiUrl 完整的API地址
     * @return HTTP状态码
     * @throws Exception 如果发生网络或IO错误
     */
    public static String sendPostRequest(List<BaseVisitor> data, String apiUrl) throws Exception {
        HttpURLConnection connection = null;
        try {
            // 1. 创建连接
            URL url = new URL(apiUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setDoOutput(true);
            connection.setConnectTimeout(50000); // 5秒连接超时
            connection.setReadTimeout(50000);  // 30秒读取超时

            // 2. 序列化数据为JSON
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonInput = objectMapper.writeValueAsString(data);

            // 3. 发送请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonInput.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            // 4. 获取响应状态码
            int statusCode = connection.getResponseCode();

            // 5. 读取响应内容（用于调试）
            String responseBody = readResponse(connection);
            log.debug("API响应: {} - {}", statusCode, responseBody);

            return responseBody;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 读取响应内容
     */
    private static String readResponse(HttpURLConnection conn) throws Exception {
        try (InputStream is = conn.getResponseCode() >= 400 ?
            conn.getErrorStream() : conn.getInputStream()) {
            if (is == null) return "";

            BufferedReader reader = new BufferedReader(
                new InputStreamReader(is, StandardCharsets.UTF_8));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            return response.toString();
        }
    }

    /**
     * 发送 DELETE 请求
     *
     * @return HTTP状态码 (如 200, 404, 500等)
     * @throws IOException 如果请求失败
     */
    public static String sendDeleteRequest(String url) throws IOException {
        HttpURLConnection connection = null;
        try {
            URL apiUrl = new URL(url);
            connection = (HttpURLConnection) apiUrl.openConnection();

            // 设置DELETE方法
            connection.setRequestMethod("DELETE");

            // 设置请求头（可选）
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");

            // 获取响应状态码
            int statusCode = connection.getResponseCode();

            // 读取响应体
            String responseBody = readResponseBody(connection, statusCode);
            log.debug("API响应: {} - {}", statusCode, responseBody);

            return responseBody;
        } finally {
            if (connection != null) {
                connection.disconnect(); // 关闭连接
            }
        }
    }

    /**
     * 读取响应内容
     */
    private static String readResponseBody(HttpURLConnection connection, int statusCode) throws IOException {
        try (InputStream inputStream = statusCode < HttpURLConnection.HTTP_BAD_REQUEST
            ? connection.getInputStream()
            : connection.getErrorStream()) {

            if (inputStream == null) {
                return null;
            }

            return new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                .lines()
                .collect(Collectors.joining("\n"));
        }
    }
}

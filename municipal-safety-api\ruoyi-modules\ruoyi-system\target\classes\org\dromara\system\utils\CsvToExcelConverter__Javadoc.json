{"doc": " CSV到Excel转换工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "convertCsvToGovUsers", "paramTypes": ["java.lang.String"], "doc": " 将CSV数据转换为政府用户导入VO列表\n\n @param csvContent CSV内容\n @return 政府用户导入VO列表\n"}, {"name": "parseCsvLine", "paramTypes": ["java.lang.String"], "doc": " 解析CSV行数据\n\n @param line CSV行数据\n @return 政府用户导入VO\n"}, {"name": "convertCsvToExcel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 将CSV文件转换为Excel文件\n\n @param csvFilePath   CSV文件路径\n @param excelFilePath Excel文件路径\n"}, {"name": "convertCsvContentToExcelBytes", "paramTypes": ["java.lang.String"], "doc": " 将CSV内容转换为Excel字节数组\n\n @param csvContent CSV内容\n @return Excel字节数组\n"}], "constructors": []}
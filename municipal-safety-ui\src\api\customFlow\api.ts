import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { HazardousItemsCommentsVO, HazardousItemsCommentsQuery, HazardousItemsComments } from '@/api/customFlow/types';

/**
 * 查询质监站隐患清单整改列表
 * @param query
 * @returns {*}
 */

export const listHazardousItemsComments = (query?: HazardousItemsCommentsQuery): AxiosPromise<HazardousItemsCommentsVO[]> => {
  return request({
    url: '/system/hazardousItemsComments/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询质监站隐患清单整改详细
 * @param id
 */
export const getHazardousItemsComments = (id: string | number): AxiosPromise<HazardousItemsCommentsVO> => {
  return request({
    url: '/system/hazardousItemsComments/' + id,
    method: 'get'
  });
};

/**
 * 删除质监站隐患清单整改
 * @param id
 */
export const delHazardousItemsComments = (id: string | number | Array<string | number>) => {
  return request({
    url: '/system/hazardousItemsComments/' + id,
    method: 'delete'
  });
};

/**
 * 批量保存质监站隐患清单整改
 * @param data
 */
export const batSave = (data: Array<HazardousItemsComments>) => {
  return request({
    url: '/system/hazardousItemsComments/batSave',
    method: 'post',
    data: data
  });
};

/**
 * 查询质监站隐患清单整改详细
 * @param taskId
 */
export const getComments = (taskId: string): AxiosPromise<HazardousItemsCommentsVO> => {
  return request({
    url: '/system/hazardousItemsComments/detail/' + taskId,
    method: 'get'
  });
};

/**
 * 隐患清单详细信息
 * @param id
 * @param taskId
 */
export const getPrj_hazardous_items_ai_detail = (id: string | number): AxiosPromise<any> => {
  return request({
    url: `/system/custom/flow/aiDangerDetail/${id}`,
    method: 'get'
  });
};


/**
 * 单个修改
 * @param data
 */
export const update = (data: HazardousItemsComments) => {
  return request({
    url: '/system/hazardousItemsComments/modify',
    method: 'post',
    data: data
  });
};

/**
 * 批量修改质监站隐患清单整改
 * @param data
 */
export const updateBatch = (data: Array<HazardousItemsComments>) => {
  return request({
    url: '/system/hazardousItemsComments/modifyAll',
    method: 'post',
    data: data
  });
};

// 人工复检工单提交
export const manualSubmit = (data: any) => {
  return request({
    url: '/system/hazardousItemsComments/add',
    method: 'post',
    data
  });
}
// 人工复检工单详情获取
export const getManualDetail = (taskId: string | number) => {
  return request({
    url: `/system/hazardousItemsComments/detail/${taskId}`,
    method: 'post'
  });
}

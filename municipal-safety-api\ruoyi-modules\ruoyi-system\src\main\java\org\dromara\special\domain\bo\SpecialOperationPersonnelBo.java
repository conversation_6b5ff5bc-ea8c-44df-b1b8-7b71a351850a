package org.dromara.special.domain.bo;

import org.dromara.special.domain.SpecialOperationPersonnel;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.common.translation.annotation.Translation;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.translation.constant.TransConstant;

/**
 * 特种作业人员信息业务对象 special_operation_personnel
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SpecialOperationPersonnel.class, reverseConvertGenerate = false)
public class SpecialOperationPersonnelBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long sopId;

    /**
     * 证书编号
     */
    @NotBlank(message = "证书编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String certificateNumber;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idCard;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gender;

    /**
     * 出生日期
     */
    private Date birthdate;

    /**
     * 操作类别
     */
    @NotBlank(message = "操作类别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String operationCategory;

    /**
     * 发证机关
     */
    @NotBlank(message = "发证机关不能为空", groups = { AddGroup.class, EditGroup.class })
    private String issuer;

    /**
     * 初次领证日期
     */
    private Date firstIssueDate;

    /**
     * 最近发证日期
     */
    private Date lastIssueDate;

    /**
     * 有效期开始
     */
    private Date validityStart;

    /**
     * 有效期截止
     */
    private Date validityEnd;

    /**
     * 证书状态(有效,无效,挂失,注销)
     */
    @NotBlank(message = "证书状态(有效,无效,挂失,注销)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 电子证照链接
     */
    private String electronicLicenseUrl;

    /**
     * 电子证照文件ID
     */
    private Long electronicLicenseId;

    /**
     * 项目ID
     */
    private Long projectId;


}

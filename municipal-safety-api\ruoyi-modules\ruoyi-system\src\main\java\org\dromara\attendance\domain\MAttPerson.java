package org.dromara.attendance.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * attPerson对象 m_att_person
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("m_att_person")
public class MAttPerson extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 人员id
     */
    private Long personId;

    /**
     * 设备id
     */
    private Long snId;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;


}

export interface DivisionVO {
  /**
   * 区划代码
   */
  divisionCode: string;

  /**
   * 区划名称
   */
  divisionName: string;

  /**
   * 区划级别
   */
  level: string;

  /**
   * 父级区划代码
   */
  parentCode: string;

    /**
     * 子对象
     */
    children: DivisionVO[];
}

export interface DivisionForm extends BaseEntity {
  /**
   * 区划代码
   */
  divisionCode?: string;

  /**
   * 区划名称
   */
  divisionName?: string;

  /**
   * 区划级别
   */
  level?: string;

  /**
   * 父级区划代码
   */
  parentCode?: string;

}

export interface DivisionQuery {

  /**
   * 区划代码
   */
  divisionCode?: string;

  /**
   * 区划名称
   */
  divisionName?: string;

  /**
   * 区划级别
   */
  level?: string;

  /**
   * 父级区划代码
   */
  parentCode?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}




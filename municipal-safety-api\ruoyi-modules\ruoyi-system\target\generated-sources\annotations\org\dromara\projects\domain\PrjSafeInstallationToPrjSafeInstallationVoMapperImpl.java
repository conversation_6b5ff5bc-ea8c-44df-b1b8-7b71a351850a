package org.dromara.projects.domain;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.vo.PrjSafeInstallationVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjSafeInstallationToPrjSafeInstallationVoMapperImpl implements PrjSafeInstallationToPrjSafeInstallationVoMapper {

    @Override
    public PrjSafeInstallationVo convert(PrjSafeInstallation arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjSafeInstallationVo prjSafeInstallationVo = new PrjSafeInstallationVo();

        prjSafeInstallationVo.setInstallationId( arg0.getInstallationId() );
        prjSafeInstallationVo.setUserName( arg0.getUserName() );
        prjSafeInstallationVo.setUserPositionName( arg0.getUserPositionName() );
        prjSafeInstallationVo.setFace( arg0.getFace() );
        prjSafeInstallationVo.setCertificate( arg0.getCertificate() );
        prjSafeInstallationVo.setOpenTaskId( arg0.getOpenTaskId() );

        return prjSafeInstallationVo;
    }

    @Override
    public PrjSafeInstallationVo convert(PrjSafeInstallation arg0, PrjSafeInstallationVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setInstallationId( arg0.getInstallationId() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setUserPositionName( arg0.getUserPositionName() );
        arg1.setFace( arg0.getFace() );
        arg1.setCertificate( arg0.getCertificate() );
        arg1.setOpenTaskId( arg0.getOpenTaskId() );

        return arg1;
    }
}

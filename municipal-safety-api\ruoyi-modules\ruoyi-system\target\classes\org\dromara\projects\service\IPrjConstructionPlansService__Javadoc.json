{"doc": " [项目管理] 存储危大工程专项施工方案信息及其审批状态Service接口\n\n <AUTHOR>\n @date 2025-05-15\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询[项目管理] 存储危大工程专项施工方案信息及其审批状态\n\n @param planId 主键\n @return [项目管理] 存储危大工程专项施工方案信息及其审批状态\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.projects.domain.bo.PrjConstructionPlansBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询[项目管理] 存储危大工程专项施工方案信息及其审批状态列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return [项目管理] 存储危大工程专项施工方案信息及其审批状态分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.projects.domain.bo.PrjConstructionPlansBo"], "doc": " 查询符合条件的[项目管理] 存储危大工程专项施工方案信息及其审批状态列表\n\n @param bo 查询条件\n @return [项目管理] 存储危大工程专项施工方案信息及其审批状态列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.projects.domain.bo.PrjConstructionPlansBo"], "doc": " 新增[项目管理] 存储危大工程专项施工方案信息及其审批状态\n\n @param bo [项目管理] 存储危大工程专项施工方案信息及其审批状态\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.projects.domain.bo.PrjConstructionPlansBo"], "doc": " 修改[项目管理] 存储危大工程专项施工方案信息及其审批状态\n\n @param bo [项目管理] 存储危大工程专项施工方案信息及其审批状态\n @return 是否修改成功\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除[项目管理] 存储危大工程专项施工方案信息及其审批状态信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}, {"name": "analyse", "paramTypes": ["org.dromara.projects.domain.bo.AnalyseResultBo"], "doc": " 接收文档分析回调接口\n @param resultBo\n"}], "constructors": []}
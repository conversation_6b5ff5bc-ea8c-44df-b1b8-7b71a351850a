2025-05-09 08:34:48 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-05-09 08:34:48 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-05-09 08:34:48 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-09 08:34:48 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-09 10:03:44 [main] INFO  o.d.m.admin.MonitorAdminApplication - Starting MonitorAdminApplication using Java 17.0.15 with PID 20728 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-monitor-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-09 10:03:44 [main] INFO  o.d.m.admin.MonitorAdminApplication - The following 1 profile is active: "dev"
2025-05-09 10:03:46 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-05-09 10:03:46 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-09 10:03:46 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1381 ms
2025-05-09 10:03:46 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-05-09 10:03:46 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-05-09 10:03:47 [main] WARN  o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/templates/ (please add some templates, check your Thymeleaf configuration, or set spring.thymeleaf.check-template-location=false)
2025-05-09 10:03:47 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-09 10:03:47 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-09 10:03:47 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-09 10:03:47 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-09 10:03:47 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 9090 (http) with context path '/'
2025-05-09 10:03:47 [main] INFO  o.d.m.admin.MonitorAdminApplication - Started MonitorAdminApplication in 3.719 seconds (process running for 4.852)
2025-05-09 10:03:47 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-09 10:03:47 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-09 10:03:47 [XNIO-1 task-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-09 10:03:48 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 5b2b5aa6be02
2025-05-09 10:03:48 [reactor-http-nio-2] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-monitor-admin】, 实例ID【5b2b5aa6be02】, 状态【UP】, 服务URL【http://**********:9090/】
2025-05-09 10:03:55 [reactor-http-nio-3] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【ruoyi-snailjob-server】, 实例ID【56c94c188ac7】, 状态【UP】, 服务URL【http://**********:8800/snail-job】
2025-05-09 10:04:00 [reactor-http-nio-4] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务上线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【8143852f6ba3】, 状态【UP】, 服务URL【http://**********:8080/】
2025-05-09 10:07:59 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-05-09 10:07:59 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-05-09 10:07:59 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-09 10:07:59 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-09 10:07:59 [reactor-http-nio-6] INFO  d.c.b.a.s.services.StatusUpdater - Couldn't retrieve status for Instance(id=8143852f6ba3, version=2, registration=Registration(name=RuoYi-Vue-Plus, managementUrl=http://**********:8080/actuator, healthUrl=http://**********:8080/actuator/health, serviceUrl=http://**********:8080/, source=http-api), registered=true, statusInfo=StatusInfo(status=UP, details={ssl={status=UP, details={validChains=[], invalidChains=[]}}, db={status=UP, details={database=MySQL, validationQuery=isValid()}}, diskSpace={status=UP, details={total=355626323968, free=308556148736, threshold=10485760, path=D:\Work\job\municipal-safety\municipal-safety-api\., exists=true}}, ping={status=UP}, redis={status=UP, details={version=********}}}), statusTimestamp=2025-05-09T02:04:00.704455600Z, info=Info(values={}), endpoints=Endpoints(endpoints={caches=Endpoint(id=caches, url=http://**********:8080/actuator/caches), loggers=Endpoint(id=loggers, url=http://**********:8080/actuator/loggers), logfile=Endpoint(id=logfile, url=http://**********:8080/actuator/logfile), health=Endpoint(id=health, url=http://**********:8080/actuator/health), env=Endpoint(id=env, url=http://**********:8080/actuator/env), heapdump=Endpoint(id=heapdump, url=http://**********:8080/actuator/heapdump), scheduledtasks=Endpoint(id=scheduledtasks, url=http://**********:8080/actuator/scheduledtasks), mappings=Endpoint(id=mappings, url=http://**********:8080/actuator/mappings), startup=Endpoint(id=startup, url=http://**********:8080/actuator/startup), beans=Endpoint(id=beans, url=http://**********:8080/actuator/beans), configprops=Endpoint(id=configprops, url=http://**********:8080/actuator/configprops), threaddump=Endpoint(id=threaddump, url=http://**********:8080/actuator/threaddump), sbom=Endpoint(id=sbom, url=http://**********:8080/actuator/sbom), metrics=Endpoint(id=metrics, url=http://**********:8080/actuator/metrics), conditions=Endpoint(id=conditions, url=http://**********:8080/actuator/conditions), info=Endpoint(id=info, url=http://**********:8080/actuator/info)}), buildVersion=null, tags=Tags(values={}))
org.springframework.web.reactive.function.client.WebClientRequestException: executor not accepting a task
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoErrorSupplied] :
	reactor.core.publisher.Mono.error(Mono.java:315)
	org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.wrapException(ExchangeFunctions.java:137)
Error has been observed at the following site(s):
	*____________Mono.error ⇢ at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.wrapException(ExchangeFunctions.java:137)
	*____Mono.onErrorResume ⇢ at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.exchange(ExchangeFunctions.java:106)
	|_             Mono.map ⇢ at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.exchange(ExchangeFunctions.java:107)
	|_        Mono.doOnNext ⇢ at org.springframework.web.reactive.function.client.DefaultWebClient$ObservationFilterFunction.filter(DefaultWebClient.java:745)
	|_         Mono.timeout ⇢ at de.codecentric.boot.admin.server.web.client.InstanceExchangeFilterFunctions.lambda$timeout$14(InstanceExchangeFilterFunctions.java:201)
	|_           Mono.retry ⇢ at de.codecentric.boot.admin.server.web.client.InstanceExchangeFilterFunctions.lambda$retry$13(InstanceExchangeFilterFunctions.java:191)
	|_             Mono.map ⇢ at de.codecentric.boot.admin.server.web.client.InstanceExchangeFilterFunctions.lambda$handleCookies$18(InstanceExchangeFilterFunctions.java:230)
	|_             Mono.map ⇢ at de.codecentric.boot.admin.server.web.client.InstanceExchangeFilterFunctions.lambda$convertLegacyEndpoints$8(InstanceExchangeFilterFunctions.java:143)
	*__________Mono.flatMap ⇢ at de.codecentric.boot.admin.server.web.client.InstanceWebClient.lambda$setInstance$3(InstanceWebClient.java:61)
	*____________Mono.defer ⇢ at org.springframework.web.reactive.function.client.DefaultWebClient$DefaultRequestBodyUriSpec.lambda$exchange$12(DefaultWebClient.java:467)
	|_           checkpoint ⇢ Request to GET health [DefaultWebClient]
	|_   Mono.switchIfEmpty ⇢ at org.springframework.web.reactive.function.client.DefaultWebClient$DefaultRequestBodyUriSpec.lambda$exchange$12(DefaultWebClient.java:472)
	|_        Mono.doOnNext ⇢ at org.springframework.web.reactive.function.client.DefaultWebClient$DefaultRequestBodyUriSpec.lambda$exchange$12(DefaultWebClient.java:478)
	|_       Mono.doOnError ⇢ at org.springframework.web.reactive.function.client.DefaultWebClient$DefaultRequestBodyUriSpec.lambda$exchange$12(DefaultWebClient.java:479)
	|_       Mono.doFinally ⇢ at org.springframework.web.reactive.function.client.DefaultWebClient$DefaultRequestBodyUriSpec.lambda$exchange$12(DefaultWebClient.java:480)
	|_    Mono.contextWrite ⇢ at org.springframework.web.reactive.function.client.DefaultWebClient$DefaultRequestBodyUriSpec.lambda$exchange$12(DefaultWebClient.java:486)
	*__Mono.deferContextual ⇢ at org.springframework.web.reactive.function.client.DefaultWebClient$DefaultRequestBodyUriSpec.exchange(DefaultWebClient.java:453)
	|_         Mono.flatMap ⇢ at org.springframework.web.reactive.function.client.DefaultWebClient$DefaultRequestBodyUriSpec.exchangeToMono(DefaultWebClient.java:421)
	|_             Mono.log ⇢ at de.codecentric.boot.admin.server.services.StatusUpdater.doUpdateStatus(StatusUpdater.java:84)
	|_         Mono.timeout ⇢ at de.codecentric.boot.admin.server.services.StatusUpdater.doUpdateStatus(StatusUpdater.java:85)
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onError(FluxPeekFuseable.java:234)
		at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onError(FluxPeekFuseable.java:234)
		at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onError(FluxPeekFuseable.java:234)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor.tryEmitNext(SinkManyEmitterProcessor.java:273)
		at reactor.core.publisher.SinkManySerialized.tryEmitNext(SinkManySerialized.java:100)
		at reactor.core.publisher.InternalManySink.emitNext(InternalManySink.java:27)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:194)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect$ClientTransportSubscriber.onError(HttpClientConnect.java:323)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onError(DefaultPooledConnectionProvider.java:174)
		at reactor.netty.internal.shaded.reactor.pool.AbstractPool$Borrower.fail(AbstractPool.java:479)
		at reactor.netty.internal.shaded.reactor.pool.SimpleDequePool.lambda$drainLoop$9(SimpleDequePool.java:443)
		at reactor.core.publisher.FluxDoOnEach$DoOnEachSubscriber.onError(FluxDoOnEach.java:186)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator$PooledConnectionInitializer.onError(DefaultPooledConnectionProvider.java:593)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:638)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:550)
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:61)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoError] :
	reactor.core.publisher.Mono.error(Mono.java:299)
	reactor.netty.transport.TransportConnector.doResolveAndConnect(TransportConnector.java:308)
Error has been observed at the following site(s):
	*____________Mono.error ⇢ at reactor.netty.transport.TransportConnector.doResolveAndConnect(TransportConnector.java:308)
	|_   Mono.onErrorResume ⇢ at reactor.netty.transport.TransportConnector.lambda$connect$6(TransportConnector.java:166)
	*__________Mono.flatMap ⇢ at reactor.netty.transport.TransportConnector.connect(TransportConnector.java:165)
	*___________Mono.create ⇢ at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnectionAllocator.connectChannel(DefaultPooledConnectionProvider.java:537)
	*___________Mono.create ⇢ at reactor.netty.resources.PooledConnectionProvider.acquire(PooledConnectionProvider.java:130)
	*___________Mono.create ⇢ at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect.subscribe(HttpClientConnect.java:213)
	|_       Mono.retryWhen ⇢ at reactor.netty.http.client.HttpClientConnect$MonoHttpConnect.subscribe(HttpClientConnect.java:287)
	*________Flux.concatMap ⇢ at reactor.util.retry.RetrySpec.lambda$generateCompanion$6(RetrySpec.java:360)
	|_     Flux.onErrorStop ⇢ at reactor.util.retry.RetrySpec.lambda$generateCompanion$6(RetrySpec.java:379)
	*__Flux.deferContextual ⇢ at reactor.util.retry.RetrySpec.generateCompanion(RetrySpec.java:357)
	*______Mono.flatMapMany ⇢ at reactor.netty.http.client.HttpClientFinalizer.responseConnection(HttpClientFinalizer.java:102)
	|_            Flux.next ⇢ at org.springframework.http.client.reactive.ReactorClientHttpConnector.connect(ReactorClientHttpConnector.java:174)
	|_      Mono.doOnCancel ⇢ at org.springframework.http.client.reactive.ReactorClientHttpConnector.connect(ReactorClientHttpConnector.java:175)
	|_     Mono.doOnRequest ⇢ at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.exchange(ExchangeFunctions.java:104)
	|_      Mono.doOnCancel ⇢ at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.exchange(ExchangeFunctions.java:105)
Original Stack Trace:
		at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:61)
		at reactor.netty.transport.TransportConnector.doResolveAndConnect(TransportConnector.java:303)
		at reactor.netty.transport.TransportConnector.lambda$connect$6(TransportConnector.java:165)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:638)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:550)
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-09 10:07:59 [reactor-http-nio-6] INFO  o.d.m.admin.notifier.CustomNotifier - Instance Status Change: 状态名称【服务离线】, 注册名称【RuoYi-Vue-Plus】, 实例ID【8143852f6ba3】, 状态【OFFLINE】, 服务URL【http://**********:8080/】

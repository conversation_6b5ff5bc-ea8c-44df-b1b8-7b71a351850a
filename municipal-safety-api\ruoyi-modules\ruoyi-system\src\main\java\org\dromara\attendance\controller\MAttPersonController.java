package org.dromara.attendance.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.attendance.domain.vo.MAttPersonVo;
import org.dromara.attendance.domain.bo.MAttPersonBo;
import org.dromara.attendance.service.IMAttPersonService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * attPerson
 *
 * <AUTHOR> Li
 * @date 2025-06-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/attendance/attPerson")
public class MAttPersonController extends BaseController {

    private final IMAttPersonService mAttPersonService;

    /**
     * 查询attPerson列表
     */
    @SaCheckPermission("attendance:attPerson:list")
    @GetMapping("/list")
    public TableDataInfo<MAttPersonVo> list(MAttPersonBo bo, PageQuery pageQuery) {
        return mAttPersonService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出attPerson列表
     */
    @SaCheckPermission("attendance:attPerson:export")
    @Log(title = "attPerson", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MAttPersonBo bo, HttpServletResponse response) {
        List<MAttPersonVo> list = mAttPersonService.queryList(bo);
        ExcelUtil.exportExcel(list, "attPerson", MAttPersonVo.class, response);
    }

    /**
     * 获取attPerson详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("attendance:attPerson:query")
    @GetMapping("/{id}")
    public R<MAttPersonVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mAttPersonService.queryById(id));
    }

    /**
     * 新增attPerson
     */
    @SaCheckPermission("attendance:attPerson:add")
    @Log(title = "attPerson", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MAttPersonBo bo) {
        return toAjax(mAttPersonService.insertByBo(bo));
    }

    /**
     * 修改attPerson
     */
    @SaCheckPermission("attendance:attPerson:edit")
    @Log(title = "attPerson", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MAttPersonBo bo) {
        return toAjax(mAttPersonService.updateByBo(bo));
    }

    /**
     * 删除attPerson
     *
     * @param ids 主键串
     */
    @SaCheckPermission("attendance:attPerson:remove")
    @Log(title = "attPerson", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mAttPersonService.deleteWithValidByIds(List.of(ids), true));
    }
}

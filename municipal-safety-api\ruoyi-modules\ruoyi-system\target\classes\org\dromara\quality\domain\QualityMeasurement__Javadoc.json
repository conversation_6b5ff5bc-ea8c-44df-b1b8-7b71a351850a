{"doc": " 实测实量对象 quality_measurement\n\n <AUTHOR>\n @date 2024-01-01\n", "fields": [{"name": "measurementId", "doc": " 测量ID\n"}, {"name": "measurementTime", "doc": " 测量时间\n"}, {"name": "measurementItem", "doc": " 测量事项\n"}, {"name": "deviceId", "doc": " 设备ID\n"}, {"name": "deviceName", "doc": " 设备名称\n"}, {"name": "deviceCode", "doc": " 设备编号\n"}, {"name": "measurementResult", "doc": " 测量结果\n"}, {"name": "isCompliant", "doc": " 是否合规（0合规 1不合规）\n"}, {"name": "projectName", "doc": " 项目名称\n"}, {"name": "measurementLocation", "doc": " 测量位置\n"}, {"name": "standardValue", "doc": " 标准值\n"}, {"name": "deviationValue", "doc": " 偏差值\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": " 测量人员\n"}, {"name": "isHazardMarked", "doc": " 是否标记隐患（0否 1是）\n"}, {"name": "hazardDescription", "doc": " 隐患描述\n"}, {"name": "status", "doc": " 状态（0正常 1已推送 2已处理）\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "version", "doc": " 版本\n"}, {"name": "delFlag", "doc": " 删除标志\n"}], "enumConstants": [], "methods": [], "constructors": []}
<template>
  <div>
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="60%" append-to-body @close="handleClose">
      <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
        <div v-show="showSearch" class="mb-[10px]">
          <el-card shadow="never">
            <el-form ref="queryFormRef" :model="queryParams" :inline="true">
              <el-form-item label="部门名称" prop="name">
                <el-input v-model="queryParams.deptName" placeholder="请输入部门名称" clearable />
              </el-form-item>
              <el-form-item label="专家所在地" prop="province" label-width="82px">
                <el-select v-model="queryParams.provinceCode" placeholder="请选择省份" @change="selectSearchChange($event, 'province')">
                  <el-option
                    v-for="(item, index) in provinceSearchOptions"
                    :key="index"
                    :label="item.divisionName"
                    :value="item.divisionCode"
                  ></el-option>
                </el-select>
                <el-select v-model="queryParams.cityCode" placeholder="请选择市" @change="selectSearchChange($event, 'city')" style="margin: 0 10px">
                  <el-option
                    v-for="(item, index) in citySearchOptions"
                    :key="index"
                    :label="item.divisionName"
                    :value="item.divisionCode"
                  ></el-option>
                </el-select>
                <el-select v-model="queryParams.districtCode" placeholder="请选择区">
                  <el-option
                    v-for="(item, index) in areaSearchOptions"
                    :key="index"
                    :label="item.divisionName"
                    :value="item.divisionCode"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>

      <el-card shadow="never">
        <el-table
          border
          ref="tableRef"
          v-loading="loading"
          :data="expertList"
          show-overflow-tooltip
          highlight-current-row
          row-key="projectId"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" align="center" width="55" />
          <el-table-column label="部门名称" align="center" prop="deptName"> </el-table-column>
          <el-table-column label="社会信用代码" align="center" prop="deptCode"> </el-table-column>
          <!-- <el-table-column label="行政区划" align="center" prop="provinceCode"> </el-table-column> -->
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-card>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="confirmChange">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getProvinceList, getCityList, getAreaList } from '@/api/expert/expert';
import { ExpertVO, ExpertQuery } from '@/api/expert/expert/types';
import { deptPageList } from '@/api/plan/patrolPlan/index';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const provinceOptions = ref<ExpertVO[]>([]); // 省份选项
const cityOptions = ref<ExpertVO[]>([]); // 城市选项
const areaOptions = ref<ExpertVO[]>([]); // 区选项
const tableRef = ref();
const provinceSearchOptions = ref<ExpertVO[]>([]); // 省份选项
const citySearchOptions = ref<ExpertVO[]>([]); // 城市选项
const areaSearchOptions = ref<ExpertVO[]>([]); // 区选项
const expertList = ref([]);
const total = ref(0);
const showSearch = ref(true);
const dialog = reactive({
  visible: false,
  title: '添加部门'
});
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  deptName: undefined,
  provinceCode: null,
  cityCode: null,
  districtCode: null,
  params: {}
});
const queryFormRef = ref<ElFormInstance>();
const buttonLoading = ref(false);
const loading = ref(true);
const selectedData = ref<{ deptId: number | string; deptName: string }[]>([]);

const props = defineProps({
  isShowModel: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits(['update:isShowModel', 'selectionExpertData']);

watch(
  () => props.isShowModel,
  (newVal) => {
    dialog.visible = newVal;
    if (newVal) {
      resetQuery();
      getList();
    }
  }
);
// 获取省份列表信息
const getProvince = async () => {
  const res = await getProvinceList();
  if (res.code === 200) {
    provinceOptions.value = res.data;
    provinceSearchOptions.value = res.data;
  }
};

// const handleRowClick = (row: ExpertVO) => {
//   const index = selectedData.value.findIndex((item) => item.deptId === row.deptId);

//   if (index > -1) {
//     // 已存在，移除
//     selectedData.value.splice(index, 1);
//   } else {
//     // 不存在，添加
//     selectedData.value.push({ deptId: row.deptId, deptName: row.name });
//   }
// };
const handleRowClick = (row: ExpertVO) => {
  const index = selectedData.value.findIndex((item) => item.deptId === row.deptId);

  if (index > -1) {
    // 已存在，移除
    selectedData.value.splice(index, 1);
    tableRef.value.toggleRowSelection(row, false); // 取消勾选
  } else {
    // 不存在，添加
    selectedData.value.push(row);
    tableRef.value.toggleRowSelection(row, true); // 触发勾选
  }
};
const handleSelectionChange = (rows: ExpertVO[]) => {
  const uniqueMap = new Map();
  const uniqueRows = rows.filter((row) => {
    if (uniqueMap.has(row.deptId)) return false;
    uniqueMap.set(row.deptId, true);
    return true;
  });

  selectedData.value = uniqueRows;
};

const getList = async () => {
  loading.value = true;
  try {
    const res = await deptPageList(queryParams);
    expertList.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('获取部门列表失败', error);
  } finally {
    loading.value = false;
  }
};

const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.deptName = null;
  queryParams.provinceCode = null;
  queryParams.cityCode = null;
  queryParams.districtCode = null;
  handleQuery();
};

const confirmChange = () => {
  console.log('selectedData.value', selectedData.value);
  if (selectedData.value.length === 0) {
    proxy?.$modal.msgWarning('请至少选择一个部门');
    return;
  }
  emit('selectionProjectData', selectedData.value);
  handleClose();
};

const cancel = () => {
  dialog.visible = false;
};

const handleClose = () => {
  emit('update:showExpertModel2', false);
};
const selectSearchChange = (divisionCode: string, flag: string) => {
  if (flag === 'province') {
    getCity1(divisionCode);
  } else if (flag === 'city') {
    getArea1(divisionCode);
  }
};

// 获取市列表信息
const getCity1 = async (divisionCode: string) => {
  // 清空市和区选项
  queryParams.cityCode = undefined;
  queryParams.districtCode = undefined;
  const res = await getCityList(divisionCode);
  if (res.code === 200) {
    // 处理返回的数据，例如更新城市选项等
    citySearchOptions.value = res.data;
  }
};
// 获取区列表信息
const getArea1 = async (divisionCode: string) => {
  // 清空区选项
  queryParams.districtCode = undefined;
  const res = await getAreaList(divisionCode);
  if (res.code === 200) {
    // 处理返回的数据，例如更新区选项等
    areaSearchOptions.value = res.data;
  }
};
onMounted(() => {
  getList();
  getProvince();
});
</script>

<style lang="scss" scoped></style>

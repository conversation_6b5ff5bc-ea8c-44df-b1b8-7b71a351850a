package org.dromara.facility.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.MonitorFacility;
import org.dromara.facility.domain.MonitorFacilityToMonitorFacilityVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {MonitorFacilityToMonitorFacilityVoMapper.class},
    imports = {}
)
public interface MonitorFacilityVoToMonitorFacilityMapper extends BaseMapper<MonitorFacilityVo, MonitorFacility> {
}

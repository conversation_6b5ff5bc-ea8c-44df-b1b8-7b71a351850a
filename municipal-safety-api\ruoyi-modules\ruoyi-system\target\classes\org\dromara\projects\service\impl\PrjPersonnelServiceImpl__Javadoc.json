{"doc": " 项目人员关联表Service业务层处理\n\n <AUTHOR>\n @date 2025-05-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询项目人员关联表\n\n @param projectPersonnelId 主键\n @return 项目人员关联表\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.projects.domain.bo.PrjPersonnelBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询项目人员关联表列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 项目人员关联表分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.projects.domain.bo.PrjPersonnelBo"], "doc": " 查询符合条件的项目人员关联表列表\n\n @param bo 查询条件\n @return 项目人员关联表列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.projects.domain.bo.PrjPersonnelBo"], "doc": " 新增项目人员关联表\n\n @param bo 项目人员关联表\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.projects.domain.bo.PrjPersonnelBo"], "doc": " 修改项目人员关联表\n\n @param bo 项目人员关联表\n @return 是否修改成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.projects.domain.PrjPersonnel"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除项目人员关联表信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}, {"name": "queryPersonnelListByProjectId", "paramTypes": ["java.lang.Long"], "doc": " 查询项目人员列表\n\n @param projectId 项目ID\n @return 项目人员列表\n"}], "constructors": []}
<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="lifterRealList">
      <el-table-column label="设备编号" align="center" prop="devNo" v-if="columns[0].visible" />
      <el-table-column label="吊笼编号" align="center" prop="tcNo" v-if="columns[1].visible" />
      <el-table-column label="吊笼方向" align="center" prop="lifterRight" v-if="columns[2].visible">
        <template v-slot="scope">
          {{ scope.row.lifterRight == "0" ? "左" : "右" }}
        </template>
      </el-table-column>
      <el-table-column label="吊笼编号和左右笼" align="center" prop="tcRightStr" v-if="columns[3].visible" width="130" />
      <el-table-column label="时间" align="center" prop="date" width="120" v-if="columns[4].visible">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.date, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实时起重量" align="center" prop="weight" v-if="columns[5].visible" width="110">
        <template v-slot="scope"> {{ scope.row.weight }}KG </template>
      </el-table-column>
      <el-table-column label="重量百分比" align="center" prop="weightPercent" v-if="columns[6].visible" width="110">
        <template v-slot="scope"> {{ scope.row.weight }}KG </template>
      </el-table-column>
      <el-table-column label="实时人数" align="center" prop="person" v-if="columns[7].visible" />
      <el-table-column label="实时高度" align="center" prop="height" v-if="columns[8].visible">
        <template v-slot="scope"> {{ scope.row.height }}米 </template>
      </el-table-column>
      <el-table-column label="高度百分比" align="center" prop="heightPercent" v-if="columns[9].visible" width="110">
        <template v-slot="scope"> {{ scope.row.heightPercent }}% </template>
      </el-table-column>
      <el-table-column label="实时速度" align="center" prop="speed" v-if="columns[10].visible">
        <template v-slot="scope"> {{ scope.row.speed }}米/秒 </template>
      </el-table-column>
      <el-table-column label="速度方向" align="center" prop="speedDir" v-if="columns[11].visible">
        <template v-slot="scope">
          {{
            scope.row.speedDir == "0"
              ? "停止"
              : scope.row.speedDir == "1"
                ? "上"
                : "下"
          }}
        </template>
      </el-table-column>

      <el-table-column label="实时倾斜度1" align="center" prop="slant1" v-if="columns[12].visible" width="110">
        <template v-slot="scope"> {{ scope.row.slant1 }}度 </template>
      </el-table-column>
      <el-table-column label="倾斜百分比1" align="center" prop="slant1Percent" v-if="columns[13].visible" width="110">
        <template v-slot="scope"> {{ scope.row.slant1Percent }}% </template>
      </el-table-column>
      <el-table-column label="实时倾斜度2" align="center" prop="slant2" v-if="columns[14].visible" width="110">
        <template v-slot="scope"> {{ scope.row.slant2 }}度 </template>
      </el-table-column>
      <el-table-column label="倾斜百分比2" align="center" prop="slant2Percent" v-if="columns[15].visible" width="110">
        <template v-slot="scope"> {{ scope.row.slant2Percent }}% </template>
      </el-table-column>
      <el-table-column label="驾驶员身份认证结果" align="center" prop="driverAuth" v-if="columns[16].visible" width="110">
        <template v-slot="scope">
          {{ scope.row.driverAuth == "00" ? "未认证" : "已认证" }}
        </template>
      </el-table-column>
      <el-table-column label="前门状态" align="center" prop="frontDoor" v-if="columns[17].visible">
        <template v-slot="scope">
          {{ scope.row.frontDoor == "1" ? "开启" : "关闭" }}
        </template>
      </el-table-column>
      <el-table-column label="后门状态" align="center" prop="backDoor" v-if="columns[18].visible">
        <template v-slot="scope">
          {{ scope.row.backDoor == "1" ? "开启" : "关闭" }}
        </template>
      </el-table-column>
      <el-table-column label="门锁异常指示" align="center" prop="doorLock" v-if="columns[19].visible" width="110">
        <template v-slot="scope">
          {{ scope.row.doorLock == "1" ? "有异常" : "无异常" }}
        </template>
      </el-table-column>
      <el-table-column label="重量" align="center" prop="systemStatusWeight" v-if="columns[20].visible">
        <template v-slot="scope">
          {{ scope.row.systemStatusWeight }}KG
        </template>
      </el-table-column>
      <el-table-column label="高度限位" align="center" prop="systemStatusHeight" v-if="columns[21].visible" />
      <el-table-column label="超速" align="center" prop="systemStatusSpeed" v-if="columns[22].visible" />
      <el-table-column label="人数" align="center" prop="systemStatusPerson" v-if="columns[23].visible" />
      <el-table-column label="倾斜" align="center" prop="systemStatusSlant" v-if="columns[24].visible" />
      <el-table-column label="前门锁状态" align="center" prop="systemStatusFrontDoor" v-if="columns[25].visible" width="110">
        <template v-slot="scope">
          {{ scope.row.systemStatusFrontDoor == "1" ? "异常" : "正常" }}
        </template>
      </el-table-column>
      <el-table-column label="后门锁状态" align="center" prop="systemStatusBackDoor" v-if="columns[26].visible" width="110">
        <template v-slot="scope">
          {{ scope.row.systemStatusBackDoor == "1" ? "异常" : "正常" }}
        </template>
      </el-table-column>
      <el-table-column label="风速" align="center" prop="systemStatusWindSpeed" v-if="columns[27].visible">
        <template v-slot="scope">
          {{
            scope.row.systemStatusWindSpeed == "0"
              ? "正常"
              : scope.row.systemStatusWindSpeed == "1"
                ? "预警"
                : "报警"
          }}
        </template>
      </el-table-column>
      <el-table-column label="上限位" align="center" prop="systemStatusUpperLimit" v-if="columns[28].visible">
        <template v-slot="scope">
          {{ scope.row.systemStatusUpperLimit == "1" ? "报警" : "正常" }}
        </template>
      </el-table-column>
      <el-table-column label="防坠器" align="center" prop="systemStatusFallingProtector" v-if="columns[29].visible">
        <template v-slot="scope">
          {{ scope.row.systemStatusFallingProtector == "1" ? "报警" : "正常" }}
        </template>
      </el-table-column>
      <el-table-column label="实时风速" align="center" prop="windSpeed" v-if="columns[30].visible">
        <template v-slot="scope"> {{ scope.row.windSpeed }}米/秒 </template>
      </el-table-column>
      <el-table-column label="当前楼层" align="center" prop="currentFloor" v-if="columns[31].visible" />
      <el-table-column label="未戴安全帽的人数" align="center" prop="uncovered" v-if="columns[32].visible" />
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script>
import { getLiftRealTimeData } from "@/api/special/equipment/index";
export default {
  name: "LifterReal",
  props: {
    devNo: {
      type: String,
      default: "",
    }
  },
  data () {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 升降机实时数据表格数据
      lifterRealList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        devNo: null
      },
      //隐藏列
      columns: [
        { key: 2, label: `设备编号`, visible: true },
        { key: 3, label: `吊笼编号`, visible: true },
        { key: 4, label: `0 是左   1是右`, visible: true },
        { key: 5, label: `吊笼编号和左右笼`, visible: true },
        { key: 6, label: `时间`, visible: true },
        { key: 7, label: `实时起重量`, visible: true },
        { key: 8, label: `重量百分比`, visible: true },
        { key: 9, label: `实时人数`, visible: true },
        { key: 10, label: `实时高度`, visible: true },
        { key: 11, label: `高度百分比`, visible: true },
        { key: 12, label: `实时速度`, visible: true },
        { key: 13, label: `速度方向 0停止，1上，2下`, visible: true },
        { key: 14, label: `实时倾斜度`, visible: true },
        { key: 15, label: `倾斜百分比`, visible: true },
        { key: 16, label: `实时倾斜度`, visible: true },
        { key: 17, label: `倾斜百分比`, visible: true },
        {
          key: 18,
          label: `驾驶员身份认证结果 00为未认证 01为已认证`,
          visible: true,
        },
        { key: 19, label: `前门状态 数值1代表开启，0带便关闭`, visible: true },
        { key: 20, label: `后门状态 数值1代表开启，0带便关闭`, visible: true },
        { key: 21, label: `门锁异常指示 0无异常1有异常`, visible: true },
        { key: 22, label: `重量`, visible: true },
        { key: 23, label: `高度限位`, visible: true },
        { key: 24, label: `超速`, visible: true },
        { key: 25, label: `人数`, visible: true },
        { key: 26, label: `倾斜`, visible: true },
        { key: 27, label: `前门锁状态：数字0正常,数值1异常`, visible: true },
        { key: 28, label: `后门锁状态：数字0正常,数值1异常`, visible: true },
        {
          key: 29,
          label: `风速，0表示正常，1表示预警，2表示报警`,
          visible: true,
        },
        { key: 30, label: `上限位，0表示正常，1表示报警`, visible: true },
        { key: 31, label: `防坠器，0表示正常，1表示报警`, visible: true },
        { key: 32, label: `实时风速`, visible: true },
        { key: 33, label: `当前楼层`, visible: true },
        { key: 34, label: `未戴安全帽的人数`, visible: true },
      ],
      // 表单校验
      rules: {},
      stype: 21,
    };
  },
  created () {
    this.getList();
  },
  methods: {
    SearchList (val) {
      this.queryParams.tn = val;
      this.getList();
    },
    /** 查询升降机实时数据列表 */
    async getList () {
      this.loading = true;
      this.queryParams.devNo = this.devNo;
      const response = await getLiftRealTimeData(this.queryParams);
      this.lifterRealList = response.rows;
      this.total = response.total;
      this.loading = false;
    }
  }
};
</script>

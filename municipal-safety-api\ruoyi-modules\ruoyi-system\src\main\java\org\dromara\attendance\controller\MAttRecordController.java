package org.dromara.attendance.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.dromara.attendance.domain.bo.MAttRecordBo;
import org.dromara.attendance.domain.vo.CalendarDayVo;
import org.dromara.attendance.domain.vo.MAttRecordVo;
import org.dromara.attendance.domain.vo.MAttRuleVo;
import org.dromara.attendance.service.IMAttRecordService;
import org.dromara.attendance.service.IMAttRuleService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.service.DictService;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.person.service.ISysPersonService;
import org.dromara.projects.domain.vo.PrjPersonnelVo;
import org.dromara.projects.service.IPrjPersonnelService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.Month;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 考勤记录
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/attendance/attRecord")
public class MAttRecordController extends BaseController {

    private final IMAttRecordService mAttRecordService;
    private final IMAttRuleService mAttRuleService;
    private final IPrjPersonnelService prjPersonnelService;
    private final ISysPersonService sysPersonService;
    private final DictService dictService;

    /**
     * 查询考勤记录列表
     */
    @SaCheckPermission("attendance:attRecord:list")
    @GetMapping("/list")
    public TableDataInfo<MAttRecordVo> list(MAttRecordBo bo, PageQuery pageQuery) {
        return mAttRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出考勤记录列表
     */
    @SaCheckPermission("attendance:attRecord:export")
    @Log(title = "考勤记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MAttRecordBo bo, HttpServletResponse response) {
        List<MAttRecordVo> list = mAttRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "考勤记录", MAttRecordVo.class, response);
    }

    /**
     * 获取考勤记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("attendance:attRecord:query")
    @GetMapping("/{id}")
    public R<MAttRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(mAttRecordService.queryById(id));
    }

    /**
     * 新增考勤记录
     */
    @SaCheckPermission("attendance:attRecord:add")
    @Log(title = "考勤记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MAttRecordBo bo) {
        return toAjax(mAttRecordService.insertByBo(bo));
    }

    /**
     * 修改考勤记录
     */
    @SaCheckPermission("attendance:attRecord:edit")
    @Log(title = "考勤记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MAttRecordBo bo) {
        return toAjax(mAttRecordService.updateByBo(bo));
    }

    /**
     * 删除考勤记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("attendance:attRecord:remove")
    @Log(title = "考勤记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mAttRecordService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取人员考勤列表
     * @param personId
     * @return
     */
    @GetMapping("/getPersonAtt/{personId}/{attDate}")
    public R<List<MAttRecordVo>> getPersonAtt(@PathVariable Long personId, @PathVariable String attDate) {
        List<MAttRecordVo> mAttRecordVos = mAttRecordService.selectMAttRecordByPersonIdAndAttDate(personId, attDate);
        return R.ok(mAttRecordVos);
    }

    /**
     * 获取人员考勤列表
     * @param personId
     * @return
     */
    @GetMapping("/getMonthAtt/{personId}/{attDate}")
    public R<List<CalendarDayVo>> getMonthAtt(@PathVariable Long personId, @PathVariable String attDate) {
        // 1. 解析输入的月份（格式应为"yyyy-MM"）
        YearMonth targetMonth;
        try {
            targetMonth = YearMonth.parse(attDate, DateTimeFormatter.ofPattern("yyyy-MM"));
        } catch (Exception e) {
            return R.fail("月份格式不正确，请使用yyyy-MM格式");
        }

        // 2. 获取该月份的范围（仅当月，不包含跨月）
        LocalDate startDate = targetMonth.atDay(1);
        LocalDate endDate = targetMonth.atEndOfMonth();

        // 3. 获取该人员的所有考勤记录
        List<MAttRecordVo> mAttRecordVos = mAttRecordService.selectMAttRecordByPersonId(personId);

        // 4. 将考勤记录按日期分组（每个日期对应多条考勤记录）
        Map<String, List<MAttRecordVo>> attendanceMap = mAttRecordVos.stream()
            .filter(record -> isDateInMonth(record.getAttDate(), targetMonth))
            .collect(Collectors.groupingBy(
                this::normalizeDate,
                Collectors.toList()
            ));

        // 5. 生成日历列表并填充考勤数据
        List<CalendarDayVo> calendarList = new ArrayList<>();
        LocalDate currentDate = startDate;

        while (!currentDate.isAfter(endDate)) {
            CalendarDayVo dayVo = new CalendarDayVo();
            dayVo.setDate(currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            dayVo.setDayOfMonth(currentDate.getDayOfMonth());
            dayVo.setCurrentMonth(true); // 现在都是当前月份的数据

            // 填充考勤数据（可能有多条）
            String normalizedDate = normalizeDate(currentDate);
            dayVo.setAttendances(attendanceMap.getOrDefault(normalizedDate, Collections.emptyList()));

            calendarList.add(dayVo);
            currentDate = currentDate.plusDays(1);
        }

        return R.ok(calendarList);
    }

    /**
     * 检查考勤记录是否属于指定月份
     */
    private boolean isDateInMonth(String attDate, YearMonth targetMonth) {
        try {
            // 假设attDate可能是"yyyy-MM-dd"或"MM-dd"格式
            LocalDate recordDate;
            if (attDate.length() > 5) { // 包含年份
                recordDate = LocalDate.parse(attDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else {
                // 无年份信息，假设为当前查询年份
                recordDate = LocalDate.of(targetMonth.getYear(),
                    Month.of(Integer.parseInt(attDate.substring(0, 2))),
                    Integer.parseInt(attDate.substring(3)));
            }
            return YearMonth.from(recordDate).equals(targetMonth);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 标准化日期格式为"MM-dd"用于匹配
     */
    private String normalizeDate(Object date) {
        if (date instanceof LocalDate) {
            return ((LocalDate) date).format(DateTimeFormatter.ofPattern("MM-dd"));
        } else if (date instanceof MAttRecordVo) {
            String attDate = ((MAttRecordVo) date).getAttDate();
            // 假设attDate可能是"yyyy-MM-dd"或"MM-dd"格式
            if (attDate.length() > 5) { // 包含年份
                return attDate.substring(5); // 取"MM-dd"部分
            }
            return attDate;
        }
        return date.toString();
    }
    // =================================================================================================================
    /**
     * 获取人员考勤列表
     * @param personId
     * @return
     */
    @GetMapping("/getPersonAttByMonth/{personId}/{month}")
    public R<List<CalendarDayVo>> getPersonAttByMonth(@PathVariable Long personId, @PathVariable String month) {
        try {
            List<CalendarDayVo> calendar = mAttRecordService.getAttendanceCalendar(personId, month);
            return R.ok(calendar);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    // =================================================================================================================
    /**
     * 从设备上传考勤记录
     *
     * @param capJson
     * @return
     */
    @PostMapping("/getFaceImage")
    public R<JSONObject> getFaceImage(@RequestBody JSONObject capJson) {
        String device_sn = capJson.getStr("device_sn");     // 设备号
        Date cap_time = capJson.getDate("cap_time");        // 打卡时间
        String match = capJson.getStr("match");             // 人脸库中的信息(对象)
//        String person = capJson.getStr("person");           // 人像基本信息
//        String overall_pic = capJson.getStr("overall_pic"); // 打卡返回的结果(全景)
        String closeup_pic = capJson.getStr("closeup_pic"); // 打卡返回的结果(特写)

//        JsonObject overall_picJsonObject = JsonParser.parseString(overall_pic).getAsJsonObject();
//        System.out.println("全景图信息：" + overall_picJsonObject);
        JsonObject closeup_picJsonObject = JsonParser.parseString(closeup_pic).getAsJsonObject();
//        System.out.println("特写图信息：" + closeup_picJsonObject);
//        JsonObject personObject = JsonParser.parseString(person).getAsJsonObject();
//        System.out.println("人像基本信息：" + personObject);
        JsonObject jsonObject = JsonParser.parseString(match).getAsJsonObject();
//        System.out.println("人脸库中的信息：" + match);

        String personName = jsonObject.get("person_name").getAsString();
        System.out.println("姓名：" + personName);
        String idCard = jsonObject.get("person_id").getAsString();
        System.out.println("身份ID：" + idCard);
        String project_id = jsonObject.get("customer_text").getAsString();
        System.out.println("项目id：" + project_id);

        String data = closeup_picJsonObject.get("data").getAsString();

        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        String timeOnly = sdf.format(cap_time);

        System.out.println("当前时分秒: " + timeOnly);

        // 创建打卡记录对象
        MAttRecordBo mAttRecordBo = new MAttRecordBo();

        // 根据身份证号查询人员id
        Long personId = sysPersonService.getPersonIdByIdCard(idCard);

        if (StringUtils.isNotEmpty(String.valueOf(personId))) {
            JSONArray jsonArray = new JSONArray();
            List<TimeSlot> timeSlots = new ArrayList<>();
            // 查询人员在哪个项目下(一个人可能多个角色,多角色多条数据)
            List<PrjPersonnelVo> prjPersonnelVos = prjPersonnelService.selectPrjPersonByPersonId(project_id, personId);
            // 多个角色多条考勤记录
            for (PrjPersonnelVo prjPersonnelVo : prjPersonnelVos) {
                String roleId = prjPersonnelVo.getRoleOnProject();
                Long projectId = prjPersonnelVo.getProjectId();
                MAttRuleVo mAttRuleVo = mAttRuleService.selectMAttRuleByRoleIdAndByProjectId(roleId, projectId);
                if (mAttRuleVo != null) {
                    jsonArray = new JSONArray(mAttRuleVo.getCheckTime());
                    mAttRecordBo.setRuleId(mAttRuleVo.getId());
                } else {
                    String label = dictService.getDictLabel("personnel_position", roleId);
                    log.error("【" + label + "】未设置打卡规则");
                    throw new ServiceException("【" + label + "】未设置打卡规则");
                }

                for (int j = 0; j < jsonArray.size(); j++) {
                    JSONObject jsonObject1 = jsonArray.getJSONObject(j);
                    TimeSlot slot = new TimeSlot();
                    slot.setNum(jsonObject1.getInt("num"));
                    slot.setStartTime(jsonObject1.getStr("startTime"));
                    slot.setEndTime(jsonObject1.getStr("endTime"));
                    timeSlots.add(slot);
                }

                LocalTime checkTime = LocalTime.parse(timeOnly);

                for (int k = 0; k < timeSlots.size(); k++) {
                    TimeSlot slot = timeSlots.get(k);
                    System.out.println(timeOnly + " 在 " + slot + " 内: " + isInTimeSlot(checkTime, slot));

                    if (isInTimeSlot(checkTime, slot)) {
                        // 判断是否在同一时段打卡,如果已经存在就将最新打卡设置为2:无效打卡
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        MAttRecordVo attRecordVo = mAttRecordService.selectMAttRuleByAttDate(simpleDateFormat.format(cap_time), k + 1, personId);
                        mAttRecordBo.setAttResult(attRecordVo != null ? 2 : 0);
                        mAttRecordBo.setWhichTime(k + 1);
                        break;
                    } else {
                        mAttRecordBo.setAttResult(1);
                    }
                }
                mAttRecordBo.setSource(0);
                mAttRecordBo.setSn(device_sn);
                mAttRecordBo.setIdNumber(idCard);
                mAttRecordBo.setPersonId(prjPersonnelVo.getProjectPersonnelId());
                mAttRecordBo.setPersonType(roleId);
                mAttRecordBo.setRealName(personName);
                mAttRecordBo.setRealTimeFace(data);
                mAttRecordBo.setAttTime(cap_time);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                mAttRecordBo.setAttDate(simpleDateFormat.format(cap_time));
                mAttRecordService.insertMAttRecord(mAttRecordBo);
            }
        }
        return R.ok(capJson);
    }

    public static boolean isInTimeSlot(LocalTime time, TimeSlot slot) {
        LocalTime start = LocalTime.parse(slot.getStartTime());
        LocalTime end = LocalTime.parse(slot.getEndTime());

        // 检查时间是否在区间内（包含边界）
        return !time.isBefore(start) && !time.isAfter(end);
    }

    @Setter
    @Getter
    public static class TimeSlot {
        private int num;
        private String startTime;
        private String endTime;

        @Override
        public String toString() {
            return "TimeSlot{num=" + num + ", startTime='" + startTime + "', endTime='" + endTime + "'}";
        }
    }
}

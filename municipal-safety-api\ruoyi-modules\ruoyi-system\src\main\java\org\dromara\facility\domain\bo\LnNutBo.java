package org.dromara.facility.domain.bo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson2.annotation.JSONField;
import org.dromara.facility.domain.LnNut;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 绿能螺母业务对象 ln_nut
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LnNut.class, reverseConvertGenerate = false)
public class LnNutBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 参数
     */
    private String para;

    /**
     * 值类型
     */
    private String vt;

    /**
     * 值状态
     */
    private String qds;

    /**
     * 数据时间
     */
    @JSONField(format = DatePattern.NORM_DATETIME_PATTERN)
    private Date time;

    /**
     * 数值
     */
    private String value;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}

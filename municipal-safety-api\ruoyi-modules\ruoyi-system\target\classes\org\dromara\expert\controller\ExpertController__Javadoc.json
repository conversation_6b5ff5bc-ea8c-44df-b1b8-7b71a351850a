{"doc": "  专家主\n @date 2025-05-03\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.expert.domain.bo.ExpertBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询 专家主列表\n"}, {"name": "expertList", "paramTypes": ["org.dromara.expert.domain.bo.ExpertBo"], "doc": " 查询 专家主列表\n"}, {"name": "export", "paramTypes": ["org.dromara.expert.domain.bo.ExpertBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出 专家列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取 专家详细信息\n\n @param expertId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.expert.domain.bo.ExpertBo"], "doc": " 新增 专家\n"}, {"name": "edit", "paramTypes": ["org.dromara.expert.domain.bo.ExpertBo"], "doc": " 修改 专家\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除 专家主\n\n @param expertIds 主键串\n"}, {"name": "removeField", "paramTypes": ["java.lang.Long"], "doc": "  删除 专家领域\n"}, {"name": "removeProject", "paramTypes": ["java.lang.Long"], "doc": "  删除 专家项目\n"}, {"name": "resetPwd", "paramTypes": ["org.dromara.expert.domain.bo.ExpertBo"], "doc": " 重置密码\n"}], "constructors": []}
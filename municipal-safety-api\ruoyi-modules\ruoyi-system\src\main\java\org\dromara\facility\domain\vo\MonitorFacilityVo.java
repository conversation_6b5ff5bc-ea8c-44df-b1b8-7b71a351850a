package org.dromara.facility.domain.vo;

import org.dromara.facility.domain.MonitorFacility;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 监测设备视图对象 monitor_facility
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MonitorFacility.class)
public class MonitorFacilityVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String devNo;

    /**
     * 设备类型
     */
    @ExcelProperty(value = "设备类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "hefei_faiclity_type")
    private String deviceType;

    /**
     * 生产厂家
     */
    @ExcelProperty(value = "生产厂家", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "hefei_manufacturers")
    private String manufacturers;

    /**
     * 数据来源
     */
    @ExcelProperty(value = "数据来源")
    private String dataSources;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 工程id
     */
    @ExcelProperty(value = "工程id")
    private Long itemId;

    /**
     * 工程名称
     */
    private String itemName;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}

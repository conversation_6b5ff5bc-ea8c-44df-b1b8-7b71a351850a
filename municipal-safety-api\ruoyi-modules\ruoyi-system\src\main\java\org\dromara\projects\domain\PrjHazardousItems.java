package org.dromara.projects.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * [项目管理] 列出项目内具体的危险性较大的分部分项工程对象 prj_hazardous_items
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prj_hazardous_items")
public class PrjHazardousItems extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 危大工程项ID
     */
    @TableId(value = "item_id")
    private Long itemId;

    /**
     * 所属项目ID (逻辑外键至 prj_projects.project_id)
     */
    private Long projectId;

    /**
     * 涉危工程清单ID（支持多选逗号隔开）
     */
    private String dangerId;

    /**
     * 危大工程名称/描述
     */
    private String itemName;

    /**
     * 具体范围详情
     */
    private String scopeDetails;

    /**
     * 危大类型 (1:危大, 2:超危大)
     */
    private Long dangerListType;

    /**
     * 计划开工日期
     */
    private Date startDate;

    /**
     * 计划竣工日期
     */
    private Date plannedEndDate;

    /**
     * 实际竣工日期
     */
    private Date actualEndDate;

    /**
     * 实际开工日期
     */
    private Date actualStartDate;

    /**
     * 状态
     */
    private String status;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;


}

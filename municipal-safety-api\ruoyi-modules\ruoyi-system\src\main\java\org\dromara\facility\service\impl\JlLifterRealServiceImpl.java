package org.dromara.facility.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.facility.domain.JlLifterReal;
import org.dromara.facility.domain.JlTowerReal;
import org.dromara.facility.domain.bo.JlLifterRealBo;
import org.dromara.facility.domain.bo.JlTowerRealBo;
import org.dromara.facility.domain.vo.JlLifterRealVo;
import org.dromara.facility.mapper.JlLifterRealMapper;
import org.dromara.facility.service.IJlLifterRealService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 升降机实时数据Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@RequiredArgsConstructor
@Service
public class JlLifterRealServiceImpl implements IJlLifterRealService {

    private final JlLifterRealMapper baseMapper;

    /**
     * 查询升降机实时数据
     *
     * @param id 主键
     * @return 升降机实时数据
     */
    @Override
    public JlLifterRealVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询升降机实时数据列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 升降机实时数据分页列表
     */
    @Override
    public TableDataInfo<JlLifterRealVo> queryPageList(JlLifterRealBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<JlLifterReal> lqw = buildQueryWrapper(bo);
        Page<JlLifterRealVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    private LambdaQueryWrapper<JlLifterReal> buildQueryWrapper(JlLifterRealBo bo) {
        LambdaQueryWrapper<JlLifterReal> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(JlLifterReal::getCreateTime);
        lqw.eq(JlLifterReal::getDevNo, bo.getDevNo());
        return lqw;
    }

    /**
     * 新增升降机实时数据
     *
     * @param bo 升降机实时数据
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(JlLifterRealBo bo) {
        JlLifterReal add = MapstructUtils.convert(bo, JlLifterReal.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public void insertByJson(String jsonString) {
        JlLifterRealBo bo = JSON.parseObject(jsonString, JlLifterRealBo.class);

        JlLifterReal add = MapstructUtils.convert(bo, JlLifterReal.class);

        validEntityBeforeSave(add);
        add.setCreateTime(new Date());
        baseMapper.insert(add);
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(JlLifterReal entity) {
        //TODO 做一些数据校验,如唯一约束

    }

}

package org.dromara.attendance.domain.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 更新后的日历日期VO类，支持多条考勤记录
 */
@Setter
@Getter
public class CalendarDayVo {
    private String date;                    // 日期格式"yyyy-MM-dd"

    private int dayOfMonth;                 // 当月天数

    private boolean currentMonth;           // 是否属于当前月份

    private boolean workDay;                // 是否是工作日

    private List<MAttRecordVo> attendances; // 考勤记录列表

    private int attStatus;                  // 考勤状态

    private String statusDetail;            // 状态详情，如"NORMAL"

    private String statusDescription;       // 状态描述，如"迟到15分钟"

}

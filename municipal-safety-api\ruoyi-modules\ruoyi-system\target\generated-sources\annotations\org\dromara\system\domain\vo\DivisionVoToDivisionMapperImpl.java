package org.dromara.system.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.Division;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class DivisionVoToDivisionMapperImpl implements DivisionVoToDivisionMapper {

    @Override
    public Division convert(DivisionVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Division division = new Division();

        division.setDivisionCode( arg0.getDivisionCode() );
        division.setDivisionName( arg0.getDivisionName() );
        division.setLevel( arg0.getLevel() );
        division.setParentCode( arg0.getParentCode() );

        return division;
    }

    @Override
    public Division convert(DivisionVo arg0, Division arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setDivisionCode( arg0.getDivisionCode() );
        arg1.setDivisionName( arg0.getDivisionName() );
        arg1.setLevel( arg0.getLevel() );
        arg1.setParentCode( arg0.getParentCode() );

        return arg1;
    }
}

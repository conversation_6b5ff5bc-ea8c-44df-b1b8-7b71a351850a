package org.dromara.projects.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.bo.PrjExpertReviewsBoToPrjExpertReviewsMapper;
import org.dromara.projects.domain.vo.PrjExpertReviewsVo;
import org.dromara.projects.domain.vo.PrjExpertReviewsVoToPrjExpertReviewsMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjExpertReviewsVoToPrjExpertReviewsMapper.class,PrjExpertReviewsBoToPrjExpertReviewsMapper.class},
    imports = {}
)
public interface PrjExpertReviewsToPrjExpertReviewsVoMapper extends BaseMapper<PrjExpertReviews, PrjExpertReviewsVo> {
}

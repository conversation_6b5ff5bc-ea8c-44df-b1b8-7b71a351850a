{"doc": " dangerList\n\n <AUTHOR>\n @date 2025-04-30\n", "fields": [{"name": "REDIS_CACHE_KEY_PREFIX", "doc": " Redis缓存键前缀\n"}], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.DangerListBo"], "doc": " 查询dangerList列表\n"}, {"name": "treeList", "paramTypes": ["org.dromara.system.domain.bo.DangerListBo"], "doc": " 查询dangerList树形列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.DangerListBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出dangerList列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取dangerList详细信息\n\n @param dangerId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.DangerListBo"], "doc": " 新增dangerList\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.DangerListBo"], "doc": " 修改dangerList\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除dangerList\n\n @param dangerIds 主键串\n"}, {"name": "clearCache", "paramTypes": [], "doc": " 清除危大工程缓存\n"}], "constructors": []}
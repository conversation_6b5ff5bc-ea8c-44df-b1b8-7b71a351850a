{"doc": " 省厅自动工单\n\n <AUTHOR>\n @date 2025-06-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询省厅自动工单列表\n"}, {"name": "export", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出省厅自动工单列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取省厅自动工单详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistBo"], "doc": " 新增省厅自动工单\n"}, {"name": "edit", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistBo"], "doc": " 修改省厅自动工单\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除省厅自动工单\n\n @param ids 主键串\n"}, {"name": "getDetail", "paramTypes": ["java.lang.String"], "doc": " 获取省厅自动工单详细信息\n"}, {"name": "saveThree", "paramTypes": ["org.dromara.flow.domain.dto.SpecialistThreeDTO"], "doc": " 存储厅局决策\n\n @param dto\n @return\n"}, {"name": "saveThree2", "paramTypes": ["org.dromara.flow.domain.PrjHazardousItemsSpecialist"], "doc": " 存储厅局决策\n\n @param specialist\n @return\n"}, {"name": "threeDetail", "paramTypes": ["java.lang.String"], "doc": " 存储厅局决策\n\n @param taskId\n @return\n"}], "constructors": []}
package org.dromara.monito.mapper;

import org.dromara.dp.domain.bo.DataViewBo;
import org.dromara.monito.domain.DeviceMonito;
import org.dromara.monito.domain.bo.DeviceMonitoBo;
import org.dromara.monito.domain.vo.DeviceMonitoVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;
import java.util.Map;

/**
 * 监控管理Mapper接口
 *
 * <AUTHOR> Li
 * @date 2025-05-18
 */
public interface DeviceMonitoMapper extends BaseMapperPlus<DeviceMonito, DeviceMonitoVo> {

    List<DeviceMonitoVo> baseMapper(DeviceMonitoBo bo);

    DeviceMonitoVo getById(Long monitoId);

    public List<Map<String, Object>> getDeviceMonitoTreeInfo(DeviceMonitoBo bo);

    public List<Map<String, Object>> getItemNum(DataViewBo bo);

    public List<Map<String, Object>> getDangerNum(DataViewBo bo);

    public List<Map<String, Object>> getProItemList(DataViewBo bo);

    public int getYearItemNum(DataViewBo bo);

    public int getAreaItemNum(DataViewBo bo);

}

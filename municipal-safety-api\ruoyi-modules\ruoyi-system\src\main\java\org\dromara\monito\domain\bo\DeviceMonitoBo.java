package org.dromara.monito.domain.bo;

import org.dromara.monito.domain.DeviceMonito;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 监控管理业务对象 device_monito
 *
 * <AUTHOR> Li
 * @date 2025-05-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DeviceMonito.class, reverseConvertGenerate = false)
public class DeviceMonitoBo extends BaseEntity {

    /**
     * 监控ID
     */
    @NotNull(message = "监控ID不能为空", groups = { EditGroup.class })
    private Long monitoId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目工程ID
     */
    private Long itemId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备类型(001:萤石云)
     */
    private String deviceType;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备在线状态(在线、不在线)
     */
    private String deviceStatus;

    /**
     * 是否启用抓拍
     */
    private String enableSnapshot;

    /**
     * 抓拍时间间隔(秒)
     */
    private Long snapshotTime;

    /**
     * 备注
     */
    private String remarks;

    /** 项目名称 */
    private String projectName;

    /** 工程名称 */
    private String itemName;

    /** 省名称 */
    private String provinceName;

    /** 市名称 */
    private String cityName;

    /** 区县名称 */
    private String districtName;

    /** 通道名称 */
    private Integer channelNo;
}

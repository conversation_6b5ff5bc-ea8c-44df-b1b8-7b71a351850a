package org.dromara.monito.domain.vo;

import org.dromara.monito.domain.DeviceMonito;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 监控管理视图对象 device_monito
 *
 * <AUTHOR> Li
 * @date 2025-05-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DeviceMonito.class)
public class DeviceMonitoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 监控ID
     */
    @ExcelProperty(value = "监控ID")
    private Long monitoId;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID")
    private Long projectId;

    /**
     * 项目工程ID
     */
    @ExcelProperty(value = "项目工程ID")
    private Long itemId;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 设备类型(001:萤石云)
     */
    @ExcelProperty(value = "设备类型(001:萤石云)", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "device_type")
    private String deviceType;

    /**
     * 设备编码
     */
    @ExcelProperty(value = "设备编码")
    private String deviceCode;

    /**
     * 设备在线状态(在线、不在线)
     */
    @ExcelProperty(value = "设备在线状态(在线、不在线)", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "device_status")
    private String deviceStatus;

    /**
     * 是否启用抓拍
     */
    @ExcelProperty(value = "是否启用抓拍", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "enable_snapshot")
    private String enableSnapshot;

    /**
     * 抓拍时间间隔(秒)
     */
    @ExcelProperty(value = "抓拍时间间隔(秒)")
    private Long snapshotTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;


    /** 项目名称 */
    private String projectName;

    /** 工程名称 */
    private String itemName;

    /** 通道编号 */
    private Integer channelNo;
}

<template>
  <div>
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="60%" append-to-body @close="handleClose">
      <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
        <div v-show="showSearch" class="mb-[10px]">
          <el-card shadow="never">
            <el-form ref="queryFormRef" :model="queryParams" :inline="true">
              <el-form-item label="项目名称" prop="projectName">
                <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable />
              </el-form-item>
              <el-form-item label="项目编码/标识" prop="projectCode" label-width="100px">
                <el-input v-model="queryParams.projectCode" placeholder="请输入项目编码/标识" clearable />
              </el-form-item>
              <el-form-item label="所在地区" prop="areaSearch">
                <el-cascader
                  v-model="searchSelectedArea"
                  :options="areaOptions"
                  :props="{
                    checkStrictly: true,
                    expandTrigger: 'click',
                    value: 'id',
                    label: 'name',
                    children: 'children',
                    multiple: false
                  }"
                  clearable
                  filterable
                  style="width: 100%"
                  placeholder="请选择省/市/区县"
                  @change="handleSearchAreaChange"
                />
              </el-form-item>
              <el-form-item label="项目状态" prop="status" label-width="100px">
                <el-select v-model="queryParams.status" placeholder="请选择项目状态" clearable>
                  <el-option v-for="dict in prj_projects_status" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>

      <el-card shadow="never">
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="prj_projectsList"
          show-overflow-tooltip
          highlight-current-row
          row-key="projectId"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" align="center" width="55" />
          <el-table-column label="项目名称" align="center" prop="projectName" />
          <el-table-column label="项目编码/标识" align="center" prop="projectCode" />
          <!--        <el-table-column label="工程概况" align="center" prop="projectOverview" show-overflow-tooltip />-->
          <el-table-column label="施工许可证编号" align="center" prop="constructionPermitNo" />
          <el-table-column label="所在地区" align="center">
            <template #default="scope">
              {{ [scope.row.provinceName, scope.row.cityName, scope.row.districtName].filter(Boolean).join('/') || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="项目位置" align="center" prop="locationDetail"></el-table-column>>
          <el-table-column label="项目状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag :options="prj_projects_status" :value="scope.row.status" />
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-card>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="confirmChange">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { listPrj_projects } from '@/api/projects/prj_projects/index';
import { getAreaTree } from '@/api/system/area';
import { Prj_projectsVO, Prj_projectsQuery } from '@/api/projects/prj_projects/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { prj_projects_status } = toRefs<any>(proxy?.useDict('prj_projects_status'));
const prj_projectsList = ref<Prj_projectsVO[]>([]);
const searchSelectedArea = ref<any[]>([]);
const areaOptions = ref<any[]>([]);
const total = ref(0);
const showSearch = ref(true);
const tableRef = ref();
const dialog = reactive({
  visible: false,
  title: '添加项目'
});
const queryParams = reactive<Prj_projectsQuery>({
  pageNum: 1,
  pageSize: 10,
  projectName: undefined,
  projectCode: undefined,
  provinceCode: undefined,
  cityCode: undefined,
  districtCode: undefined,
  status: undefined,
  params: {
    dateRange: []
  }
});
const queryFormRef = ref<ElFormInstance>();
const buttonLoading = ref(false);
const loading = ref(true);
// const selectedData = ref<{ projectId: string | number, projectName: string }>({
//   projectId: '',
//   projectName: ''
// });
const selectedData = ref([]);
const selectedRows = ref<Prj_projectsVO[]>([]); // 新增：存储选中的行数据
const props = defineProps({
  isShowModel: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits(['update:isShowModel', 'selectionProjectData']);
watch(
  () => props.isShowModel,
  (newVal) => {
    dialog.visible = newVal;
    if (newVal) {
      resetQuery();
      getList();
    }
  }
);
// 处理选择变化（包括全选和单选）
const handleSelectionChange = (rows: Prj_projectsVO[]) => {
  selectedRows.value = rows;
  selectedData.value = rows;
  // selectedData.value = rows
};
// 处理行点击事件

const handleRowClick = (row: ExpertVO) => {
  console.log(row);

  const index = selectedData.value.findIndex((item) => item.projectId === row.projectId);

  if (index > -1) {
    // 已存在，移除
    selectedData.value.splice(index, 1);
    tableRef.value.toggleRowSelection(row, false); // 取消勾选
  } else {
    // 不存在，添加
    selectedData.value.push(row);
    tableRef.value.toggleRowSelection(row, true); // 触发勾选
  }
};
/** 查询项目录入列表 */
const getList = async () => {
  loading.value = true;
  try {
    const res = await listPrj_projects(queryParams);
    prj_projectsList.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('获取项目列表失败', error);
  } finally {
    loading.value = false;
  }
};
/** 获取地区树 */
const getArea = async () => {
  try {
    const res = await getAreaTree();
    console.log('获取地区树成功');
    areaOptions.value = res.data || [];
  } catch (error) {
    console.error('获取地区树失败', error);
    proxy?.$modal.msgError('获取地区树失败');
  }
};
/** 地区搜索值变更处理 */
const handleSearchAreaChange = (value: any[]) => {
  console.log('搜索地区值变更:', value);

  // 清空之前的地区搜索参数
  queryParams.provinceCode = undefined;
  queryParams.cityCode = undefined;
  queryParams.districtCode = undefined;

  if (!value || value.length === 0) {
    return; // 如果清空了选择，直接返回
  }

  // 按照选择的层级填充查询参数
  if (value.length >= 1) {
    queryParams.provinceCode = String(value[0]);
  }
  if (value.length >= 2) {
    queryParams.cityCode = String(value[1]);
  }
  if (value.length >= 3) {
    queryParams.districtCode = String(value[2]);
  }
};
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  searchSelectedArea.value = [];
  queryParams.provinceCode = undefined;
  queryParams.cityCode = undefined;
  queryParams.districtCode = undefined;
  queryFormRef.value?.resetFields();
  handleQuery();
};
// const confirmChange = () => {
//   emit('selectionProjectData', selectedData.value);
//   handleClose();
// }
// const confirmChange = () => {
//   console.log('selectedData.value', selectedData.value);

//   // emit('selectionProjectData', selectedData.value);
//   // handleClose();
// };
// 确认选择
const confirmChange = () => {
  console.log('selectedData.value', selectedData.value);
  if (selectedData.value.length === 0) {
    proxy?.$modal.msgWarning('请至少选择一个项目');
    return;
  }
  emit('selectionProjectData', selectedData.value);
  handleClose();
};
// 选中表格数据的点击事件
// const handleSelectionProject = (val: Prj_projectsVO) => {
//   selectedData.value.projectId = val.projectId;
//   selectedData.value.projectName = val.projectName;
// }
const handleSelectionProject = (val: Prj_projectsVO) => {
  const index = selectedData.value.findIndex((item) => item.projectId === val.projectId);
  if (index > -1) {
    selectedData.value.splice(index, 1);
  } else {
    selectedData.value.push({ projectId: val.projectId, projectName: val.projectName });
  }
};
const cancel = () => {
  dialog.visible = false;
};
const handleClose = () => {
  emit('update:isShowModel', false);
};
// const handleClose = () => {
//   resetSelection();
//   emit('update:isShowModel', false);
// };
onMounted(() => {
  getArea();
});
</script>

<style lang="scss" scoped></style>

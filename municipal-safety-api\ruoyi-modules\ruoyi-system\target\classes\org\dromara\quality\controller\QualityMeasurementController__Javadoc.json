{"doc": " 实测实量Controller\n\n <AUTHOR>\n @date 2024-01-01\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.quality.domain.bo.QualityMeasurementBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询实测实量列表\n"}, {"name": "export", "paramTypes": ["org.dromara.quality.domain.bo.QualityMeasurementBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出实测实量列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取实测实量详细信息\n\n @param measurementId 测量ID\n"}, {"name": "add", "paramTypes": ["org.dromara.quality.domain.bo.QualityMeasurementBo"], "doc": " 新增实测实量\n"}, {"name": "edit", "paramTypes": ["org.dromara.quality.domain.bo.QualityMeasurementBo"], "doc": " 修改实测实量\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除实测实量\n\n @param measurementIds 测量ID串\n"}, {"name": "pushMeasurementInfo", "paramTypes": ["java.lang.Long[]"], "doc": " 推送测量信息\n\n @param measurementIds 测量ID串\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 标记隐患\n\n"}, {"name": "unmarkHazard", "paramTypes": ["java.lang.Long"], "doc": " 取消标记隐患\n\n @param measurementId 测量ID\n"}, {"name": "getDeviceInfo", "paramTypes": ["java.lang.Long"], "doc": " 根据设备ID获取设备信息\n\n @param deviceId 设备ID\n"}], "constructors": []}
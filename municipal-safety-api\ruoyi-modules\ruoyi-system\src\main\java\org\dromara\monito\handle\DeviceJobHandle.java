package org.dromara.monito.handle;

import cn.hutool.core.util.IdUtil;
import cn.hutool.http.ContentType;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ObjectUtils;
import org.dromara.ai.domain.bo.AiHazAnalysisTasksBo;
import org.dromara.ai.enums.AiHazAnalysisTasksStatus;
import org.dromara.ai.service.IAiHazAnalysisTasksService;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.monito.domain.DeviceMonito;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.ISysOssService;
import org.dromara.util.YsyClient;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> Zu Da
 * @date 2025/5/18 15:36
 * @Description 摄像头定时任务处理类
 * @Version 1.0
 */
@Component
@JobExecutor(name = "deviceJobHandle")
public class DeviceJobHandle {

    @Resource
    private YsyClient ysyClient;

    @Resource
    private ISysOssService ossService;

    @Resource
    private IAiHazAnalysisTasksService aiHazAnalysisTasksService;

    /**
     * 定时任务对应摄像头
     */
    private final Map<Long, DeviceMonito> JOB_MAP = new ConcurrentHashMap<>();

    /**
     * 抓拍核心
     *
     * @param jobArgs
     * @return
     */
    public ExecuteResult jobExecute(JobArgs jobArgs) {
        try {
            if (JOB_MAP.containsKey(jobArgs.getJobId())) {
                //抓拍图片
                DeviceMonito monito = JOB_MAP.get(jobArgs.getJobId());

                if (!"1".equals(monito.getDeviceType())) {
                    return ExecuteResult.failure("抓拍失败!当前只支持萤石云" + jobArgs.getJobId());
                }

                String fileId = ysyClient.capture(monito.getDeviceCode(), ObjectUtils.isEmpty(monito.getChannelNo()) ? 1 : monito.getChannelNo());

                //间隔1秒
                Thread.sleep(3000);

                InputStream inputStream = ysyClient.downloadImg(fileId);

                //存储oss
                SysOssVo sysOssVo = ossService.uploadNoLogin(inputStream, IdUtil.fastSimpleUUID()
                    , ".jpeg", ContentType.MULTIPART.getValue());

                //删除文件
                ysyClient.removeFile(fileId);

                //创建隐患分析任务
                AiHazAnalysisTasksBo bo = uploadHazRecord(monito.getProjectId(), monito.getItemId(), monito.getDeviceName() + "【自动抓拍】", sysOssVo.getOssId());

                bo.setCreateDept(100L);
                bo.setCreateBy(1L);
                bo.setUpdateBy(1L);

                Date createTime = new Date();
                bo.setUpdateTime(createTime);
                bo.setCreateTime(createTime);
                bo.setExpertUserId(1L);
                bo.setPhotoDocumentId(sysOssVo.getOssId());
                this.pushAiResolver(bo, sysOssVo.getUrl());
                return ExecuteResult.success("抓拍成功!" + jobArgs.getJobId());
            }
        } catch (Exception e) {
            return ExecuteResult.failure("抓拍失败！" + jobArgs.getJobId() + ">>" + e.getMessage());
        }

        return ExecuteResult.failure("抓拍失败！" + jobArgs.getJobId());
    }


    /**
     * 创建隐患分析任务
     *
     * @param projectId   项目id
     * @param itemId      分部分析id
     * @param description 隐患描述
     * @param imageOssId  截图存储id
     * @return
     */
    public AiHazAnalysisTasksBo uploadHazRecord(Long projectId, Long itemId, String description, Long imageOssId) {
        // 1. 创建隐患分析任务
        AiHazAnalysisTasksBo bo = new AiHazAnalysisTasksBo();
        //项目id
        bo.setProjectId(projectId);
        bo.setSourceType("CAMERA");
        //分部分项id
        bo.setItemId(itemId);
        bo.setExpertUserId(LoginHelper.getUserId());
        bo.setUploadTime(new Date());
        //截图oss存储id
        bo.setPhotoDocumentId(imageOssId);
        bo.setGpsLocation("");
        bo.setLocationDescription(description);
        // 设置初始状态为"待分析"
        bo.setStatus(AiHazAnalysisTasksStatus.PENDING_AI_ANALYSIS.getType());
        return bo;
    }

    /**
     * @param bo
     * @param imageUrl
     * @return
     */
    public void pushAiResolver(AiHazAnalysisTasksBo bo, String imageUrl) {
        //送ai分析
        // 2. 插入数据库
        aiHazAnalysisTasksService.insertByBo(bo);
        aiHazAnalysisTasksService.submitToAiAnalysis(bo.getTaskId(), imageUrl, bo.getItemId());
    }

    /**
     * 注册任务
     *
     * @param deviceMonito
     */
    public void registerJob(DeviceMonito deviceMonito) {
        this.JOB_MAP.put(deviceMonito.getJobId(), deviceMonito);
    }

    /**
     * 注册任务
     *
     * @param deviceMonito
     */
    public void registerJobAndStart(DeviceMonito deviceMonito) {
        this.registerJob(deviceMonito);
        //启动任务
        this.ysyClient.startJob(deviceMonito.getJobId());
    }

    /**
     * 卸载任务
     *
     * @param jobId
     */
    public void unloadAndStop(Long jobId) {
        if (this.JOB_MAP.containsKey(jobId)) {
            this.JOB_MAP.remove(jobId);
            this.ysyClient.stopJob(jobId);
        }
    }
}

package org.dromara.facility.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.facility.domain.MonitorFacility;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class MonitorFacilityBoToMonitorFacilityMapperImpl implements MonitorFacilityBoToMonitorFacilityMapper {

    @Override
    public MonitorFacility convert(MonitorFacilityBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MonitorFacility monitorFacility = new MonitorFacility();

        monitorFacility.setSearchValue( arg0.getSearchValue() );
        monitorFacility.setCreateDept( arg0.getCreateDept() );
        monitorFacility.setCreateBy( arg0.getCreateBy() );
        monitorFacility.setCreateTime( arg0.getCreateTime() );
        monitorFacility.setUpdateBy( arg0.getUpdateBy() );
        monitorFacility.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            monitorFacility.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        monitorFacility.setId( arg0.getId() );
        monitorFacility.setDevNo( arg0.getDevNo() );
        monitorFacility.setDeviceType( arg0.getDeviceType() );
        monitorFacility.setManufacturers( arg0.getManufacturers() );
        monitorFacility.setDataSources( arg0.getDataSources() );
        monitorFacility.setProjectId( arg0.getProjectId() );
        monitorFacility.setItemId( arg0.getItemId() );
        monitorFacility.setRemark( arg0.getRemark() );

        return monitorFacility;
    }

    @Override
    public MonitorFacility convert(MonitorFacilityBo arg0, MonitorFacility arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setDeviceType( arg0.getDeviceType() );
        arg1.setManufacturers( arg0.getManufacturers() );
        arg1.setDataSources( arg0.getDataSources() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}

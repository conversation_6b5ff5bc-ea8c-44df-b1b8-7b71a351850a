package org.dromara.flow.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialWarningBo;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialWarningVo;
import org.dromara.flow.service.IPrjHazardousItemsSpecialWarningService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;

/**
 * 特殊预警
 *
 * <AUTHOR> Li
 * @date 2025-06-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/hazardousItemsSpecialWarning")
public class PrjHazardousItemsSpecialWarningController extends BaseController {

    private final IPrjHazardousItemsSpecialWarningService prjHazardousItemsSpecialWarningService;

    /**
     * 查询特殊预警列表
     */
    @SaCheckPermission("system:hazardousItemsSpecialWarning:list")
    @GetMapping("/list")
    public TableDataInfo<PrjHazardousItemsSpecialWarningVo> list(PrjHazardousItemsSpecialWarningBo bo, PageQuery pageQuery) {

        return prjHazardousItemsSpecialWarningService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出特殊预警列表
     */
    @SaCheckPermission("system:hazardousItemsSpecialWarning:export")
    @Log(title = "特殊预警", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PrjHazardousItemsSpecialWarningBo bo, HttpServletResponse response) {
        List<PrjHazardousItemsSpecialWarningVo> list = prjHazardousItemsSpecialWarningService.queryList(bo);
        ExcelUtil.exportExcel(list, "特殊预警", PrjHazardousItemsSpecialWarningVo.class, response);
    }

    /**
     * 获取特殊预警详细信息
     *
     * @param warningId 主键
     */
    @SaCheckPermission("system:hazardousItemsSpecialWarning:query")
    @GetMapping("/{warningId}")
    public R<PrjHazardousItemsSpecialWarningVo> getInfo(@NotNull(message = "主键不能为空")
                                                        @PathVariable Long warningId) {
        return R.ok(prjHazardousItemsSpecialWarningService.queryById(warningId));
    }

    /**
     * 新增特殊预警
     */
    @Log(title = "特殊预警", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PrjHazardousItemsSpecialWarningBo bo) {
        return toAjax(prjHazardousItemsSpecialWarningService.insertByBo(bo));
    }

    /**
     * 修改特殊预警
     */
    @SaCheckPermission("system:hazardousItemsSpecialWarning:edit")
    @Log(title = "特殊预警", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PrjHazardousItemsSpecialWarningBo bo) {
        return toAjax(prjHazardousItemsSpecialWarningService.updateByBo(bo));
    }

    /**
     * 删除特殊预警
     *
     * @param warningIds 主键串
     */
    @SaCheckPermission("system:hazardousItemsSpecialWarning:remove")
    @Log(title = "特殊预警", businessType = BusinessType.DELETE)
    @DeleteMapping("/{warningIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] warningIds) {
        return toAjax(prjHazardousItemsSpecialWarningService.deleteWithValidByIds(List.of(warningIds), true));
    }
}

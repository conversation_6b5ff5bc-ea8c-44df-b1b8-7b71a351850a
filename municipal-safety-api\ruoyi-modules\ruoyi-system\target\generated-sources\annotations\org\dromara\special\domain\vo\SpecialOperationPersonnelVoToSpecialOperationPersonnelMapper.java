package org.dromara.special.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.special.domain.SpecialOperationPersonnel;
import org.dromara.special.domain.SpecialOperationPersonnelToSpecialOperationPersonnelVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {SpecialOperationPersonnelToSpecialOperationPersonnelVoMapper.class},
    imports = {}
)
public interface SpecialOperationPersonnelVoToSpecialOperationPersonnelMapper extends BaseMapper<SpecialOperationPersonnelVo, SpecialOperationPersonnel> {
}

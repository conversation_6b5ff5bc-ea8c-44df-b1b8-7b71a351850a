package org.dromara.projects.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.bo.PrjSafeTaskBoToPrjSafeTaskMapper;
import org.dromara.projects.domain.vo.PrjSafeTaskVo;
import org.dromara.projects.domain.vo.PrjSafeTaskVoToPrjSafeTaskMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjSafeTaskBoToPrjSafeTaskMapper.class,PrjSafeTaskVoToPrjSafeTaskMapper.class},
    imports = {}
)
public interface PrjSafeTaskToPrjSafeTaskVoMapper extends BaseMapper<PrjSafeTask, PrjSafeTaskVo> {
}

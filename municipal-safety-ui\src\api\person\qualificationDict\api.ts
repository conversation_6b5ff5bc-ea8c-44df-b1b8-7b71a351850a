import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { QualificationDictVO, QualificationDictForm, QualificationDictQuery } from '@/api/person/qualificationDict/types';

/**
 * 查询人员证书属性类型列表
 * @param query
 * @returns {*}
 */

export const listQualificationDict = (query?: QualificationDictQuery): AxiosPromise<QualificationDictVO[]> => {
  return request({
    url: '/person/qualificationDict/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询人员证书属性类型详细
 * @param id
 */
export const getQualificationDict = (id: string | number): AxiosPromise<QualificationDictVO> => {
  return request({
    url: '/person/qualificationDict/' + id,
    method: 'get'
  });
};

/**
 * 新增人员证书属性类型
 * @param data
 */
export const addQualificationDict = (data: QualificationDictForm) => {
  return request({
    url: '/person/qualificationDict',
    method: 'post',
    data: data
  });
};

/**
 * 修改人员证书属性类型
 * @param data
 */
export const updateQualificationDict = (data: QualificationDictForm) => {
  return request({
    url: '/person/qualificationDict',
    method: 'put',
    data: data
  });
};

/**
 * 删除人员证书属性类型
 * @param id
 */
export const delQualificationDict = (id: string | number | Array<string | number>) => {
  return request({
    url: '/person/qualificationDict/' + id,
    method: 'delete'
  });
};

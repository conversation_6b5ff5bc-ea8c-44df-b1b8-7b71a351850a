{"doc": " 部门视图对象 sys_dept\n\n <AUTHOR>\n", "fields": [{"name": "deptId", "doc": " 部门id\n"}, {"name": "parentId", "doc": " 父部门id\n"}, {"name": "parentName", "doc": " 父部门名称\n"}, {"name": "ancestors", "doc": " 祖级列表\n"}, {"name": "deptName", "doc": " 部门名称\n"}, {"name": "deptCategory", "doc": " 部门类别编码\n"}, {"name": "deptType", "doc": " 组织机构类型 (例如: CLIENT[建设方], CONSTRUCTION[施工方], SUPERVISION[监理], GOV_MINISTRY[部级监管], GOV_PROVINCE[省级监管], GOV_CITY[市级监管], GOV_DISTRICT[区县级监管/质监站], DESIGN[设计院], SURVEY[勘察单位], EXPERT_ORG[专家库管理单位])\n"}, {"name": "deptCode", "doc": " 组织机构代码 (例如: 统一社会信用代码)\n"}, {"name": "orderNum", "doc": " 显示顺序\n"}, {"name": "provinceCode", "doc": " 省/直辖市编码\n"}, {"name": "cityCode", "doc": " 市编码\n"}, {"name": "districtCode", "doc": " 区/县编码\n"}, {"name": "address", "doc": " 地址\n"}, {"name": "leader", "doc": " 负责人ID\n"}, {"name": "leader<PERSON><PERSON>", "doc": " 负责人\n"}, {"name": "phone", "doc": " 联系电话\n"}, {"name": "email", "doc": " 邮箱\n"}, {"name": "status", "doc": " 部门状态（0正常 1停用）\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "children", "doc": " 子部门\n"}], "enumConstants": [], "methods": [], "constructors": []}
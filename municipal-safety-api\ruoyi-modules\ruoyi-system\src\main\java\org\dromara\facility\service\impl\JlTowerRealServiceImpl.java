package org.dromara.facility.service.impl;

import com.alibaba.fastjson2.JSON;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.facility.domain.bo.JlTowerRealBo;
import org.dromara.facility.domain.vo.JlTowerRealVo;
import org.dromara.facility.domain.JlTowerReal;
import org.dromara.facility.mapper.JlTowerRealMapper;
import org.dromara.facility.service.IJlTowerRealService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 塔机实时数据Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@RequiredArgsConstructor
@Service
public class JlTowerRealServiceImpl implements IJlTowerRealService {

    private final JlTowerRealMapper baseMapper;

    /**
     * 查询塔机实时数据
     *
     * @param id 主键
     * @return 塔机实时数据
     */
    @Override
    public JlTowerRealVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询塔机实时数据列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 塔机实时数据分页列表
     */
    @Override
    public TableDataInfo<JlTowerRealVo> queryPageList(JlTowerRealBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<JlTowerReal> lqw = buildQueryWrapper(bo);
        Page<JlTowerRealVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    private LambdaQueryWrapper<JlTowerReal> buildQueryWrapper(JlTowerRealBo bo) {
        LambdaQueryWrapper<JlTowerReal> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(JlTowerReal::getCreateTime);
        lqw.eq(JlTowerReal::getDevNo, bo.getDevNo());
        return lqw;
    }

    /**
     * 新增塔机实时数据
     *
     * @param bo 塔机实时数据
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(JlTowerRealBo bo) {
        JlTowerReal add = MapstructUtils.convert(bo, JlTowerReal.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public void insertByJson(String jsonString) {
        JlTowerRealBo bo = JSON.parseObject(jsonString, JlTowerRealBo.class);

        JlTowerReal add = MapstructUtils.convert(bo, JlTowerReal.class);

        validEntityBeforeSave(add);

        baseMapper.insert(add);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(JlTowerReal entity){
        //TODO 做一些数据校验,如唯一约束
        entity.setCreateTime(new Date());
    }
}

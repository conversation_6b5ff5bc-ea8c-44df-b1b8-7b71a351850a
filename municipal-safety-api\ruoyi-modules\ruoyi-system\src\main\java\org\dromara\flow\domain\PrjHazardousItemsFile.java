package org.dromara.flow.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 通用流程附件对象 prj_hazardous_items_file
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prj_hazardous_items_file")
public class PrjHazardousItemsFile extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 附件id
     */
    @TableId(value = "item_file_id")
    private Long itemFileId;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 取sys_oss.oss_id,文件id
     */
    private Long fileId;

    /**
     * 业务id，取 雪花||UUID
     */
    private String taskId;

    /**
     * 服务类型，取字典表flow_service_type
     */
    private String serviceType;

    /**
     * 取sys_oss.oss_id,反馈文件id（多个逗号隔开）
     */
    private String callFileId;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;
}

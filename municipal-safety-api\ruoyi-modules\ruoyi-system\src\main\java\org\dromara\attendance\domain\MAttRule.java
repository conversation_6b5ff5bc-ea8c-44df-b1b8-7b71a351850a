package org.dromara.attendance.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 考勤规则对象 m_att_rule
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("m_att_rule")
public class MAttRule extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 规则类型（0：模板规则，1：普通规则）
     */
    private Long ruleType;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 人员类型
     */
    private String personType;

    /**
     * 是否全部人员通用
     */
    private Integer isAll;

    /**
     * 设置打卡时间（支持多时段，如午休分段）。
     */
    private String checkTime;

    /**
     * 弹性时间：允许迟到/早退的宽限时间（如上班后30分钟内打卡不算迟到）
     */
    private Long elasticTime;

    /**
     * 预警机制：根据对应的漏卡次数设置（黄色、橙色、红色）
     */
    private String warning;

    /**
     * 外勤打卡：0：关，1：开
     */
    private Long fieldCheck;

    /**
     * 自定义内容
     */
    private String content;


}

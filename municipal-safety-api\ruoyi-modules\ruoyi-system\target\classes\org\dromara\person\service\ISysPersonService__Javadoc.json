{"doc": " 人员基本信息Service接口\n\n <AUTHOR> zu <PERSON>\n @date 2025-05-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询人员基本信息\n\n @param personId 主键\n @return 人员基本信息\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.person.domain.bo.SysPersonBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询人员基本信息列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 人员基本信息分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.person.domain.bo.SysPersonBo"], "doc": " 查询符合条件的人员基本信息列表\n\n @param bo 查询条件\n @return 人员基本信息列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.person.domain.bo.SysPersonBo"], "doc": " 新增人员基本信息\n\n @param bo 人员基本信息\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.person.domain.bo.SysPersonBo"], "doc": " 修改人员基本信息\n\n @param bo 人员基本信息\n @return 是否修改成功\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除人员基本信息信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}, {"name": "getPersonIdByIdCard", "paramTypes": ["java.lang.String"], "doc": " 根据身份证号获取 人员id\n @param idCard\n @return\n"}], "constructors": []}
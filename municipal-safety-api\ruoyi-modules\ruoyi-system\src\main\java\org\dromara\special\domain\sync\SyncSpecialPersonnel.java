package org.dromara.special.domain.sync;

import lombok.Data;
/** 同步省厅数据   接受特种人员信息  */
@Data
public class SyncSpecialPersonnel {

    /** 备案编号 */
    private String RECORDNUM;

    /** 施工许可证编号 */
    private String CONSTRUCTIONPERMITNUM;

    /** 人员名称 */
    private String NAME;

    /** 证件号 */
    private String IDENTITYCARD;

    /** 联系电话 */
    private String LXDH;

    /** 单位的 类别分类 */
    private String LBFL;

    /** 工种名称 */
    private String PERSONTYPE;

    /** 证书编号 */
    private String ZSBH;

    /** 所属单位名称 */
    private String CORPNAME;

    /** 单位统一社会信用代码 */
    private String CORPCODE;
}

package org.dromara.projects.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjHazardousItems;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsVoToPrjHazardousItemsMapperImpl implements PrjHazardousItemsVoToPrjHazardousItemsMapper {

    @Override
    public PrjHazardousItems convert(PrjHazardousItemsVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItems prjHazardousItems = new PrjHazardousItems();

        prjHazardousItems.setItemId( arg0.getItemId() );
        prjHazardousItems.setProjectId( arg0.getProjectId() );
        prjHazardousItems.setDangerId( arg0.getDangerId() );
        prjHazardousItems.setItemName( arg0.getItemName() );
        prjHazardousItems.setScopeDetails( arg0.getScopeDetails() );
        prjHazardousItems.setDangerListType( arg0.getDangerListType() );
        prjHazardousItems.setStartDate( arg0.getStartDate() );
        prjHazardousItems.setPlannedEndDate( arg0.getPlannedEndDate() );
        prjHazardousItems.setActualEndDate( arg0.getActualEndDate() );
        prjHazardousItems.setActualStartDate( arg0.getActualStartDate() );
        prjHazardousItems.setStatus( arg0.getStatus() );

        return prjHazardousItems;
    }

    @Override
    public PrjHazardousItems convert(PrjHazardousItemsVo arg0, PrjHazardousItems arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setItemId( arg0.getItemId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setDangerId( arg0.getDangerId() );
        arg1.setItemName( arg0.getItemName() );
        arg1.setScopeDetails( arg0.getScopeDetails() );
        arg1.setDangerListType( arg0.getDangerListType() );
        arg1.setStartDate( arg0.getStartDate() );
        arg1.setPlannedEndDate( arg0.getPlannedEndDate() );
        arg1.setActualEndDate( arg0.getActualEndDate() );
        arg1.setActualStartDate( arg0.getActualStartDate() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}

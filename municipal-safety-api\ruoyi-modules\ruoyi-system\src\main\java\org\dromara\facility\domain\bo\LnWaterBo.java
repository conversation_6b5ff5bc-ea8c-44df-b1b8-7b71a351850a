package org.dromara.facility.domain.bo;

import org.dromara.facility.domain.LnWater;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 绿能水表业务对象 ln_water
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LnWater.class, reverseConvertGenerate = false)
public class LnWaterBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 0.001m³ (例：30m³传30000)
     */
    private Long sdds;

    /**
     * 上报时间
     */
    private Date recordTime;

    /**
     * 上月累计用水量 0.001m³
     */
    private Long sylj;

    /**
     * 昨日累计用水量 0.001m³
     */
    private Long zrlj;

    /**
     * 前一个小时累计用水量 0.001m³
     */
    private Long qyxslj;

    /**
     * 当前小时内累计用水量 0.001m³
     */
    private Long dqxsnlj;

    /**
     * 实时流速 0.001m³/s
     */
    private Long ssls;

    /**
     * 电池状态 dczt
     */
    private Long dczt;

    /**
     * 电池电压 0.1V
     */
    private String dcdy;

    /**
     * 电池电量 1%
     */
    private String dcdl;

    /**
     * 保留
     */
    private String bl;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}

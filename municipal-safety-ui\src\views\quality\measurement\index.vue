<template>
  <div class="p-2">
    <!-- 搜索区域 -->
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="设备名称" prop="deviceId">
              <el-select v-model="queryParams.deviceId" placeholder="请选择设备" clearable filterable remote
                :remote-method="searchDevices" :loading="deviceLoading">
                <el-option v-for="device in deviceOptions" :key="device.deviceId" :label="device.deviceName"
                  :value="device.deviceId" />
              </el-select>
            </el-form-item>
            <el-form-item label="测量类型" prop="measurementItem">
              <el-select v-model="queryParams.measurementItem" placeholder="测量类型" clearable>
                <el-option label="尺寸测量" value="dimension" />
                <el-option label="重量测量" value="weight" />
                <el-option label="温度测量" value="temperature" />
                <el-option label="压力测量" value="pressure" />
                <el-option label="其他测量" value="other" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="状态" clearable>
                <el-option label="正常" value="0" />
                <el-option label="异常" value="1" />
                <el-option label="待复测" value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间" style="width: 308px">
              <el-date-picker v-model="dateRange" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
                range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <!-- 主内容区域 -->
    <el-card shadow="hover">
      <template #header>
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button v-has-permi="['quality:measurement:add']" type="primary" plain icon="Plus" @click="handleAdd">
              新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-has-permi="['quality:measurement:edit']" type="success" plain :disabled="single" icon="Edit"
              @click="handleUpdate()">
              修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-has-permi="['quality:measurement:remove']" type="danger" plain :disabled="multiple"
              icon="Delete" @click="handleDelete()">
              删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-has-permi="['quality:measurement:export']" type="warning" plain icon="Download"
              @click="handleExport">
              导出
            </el-button>
          </el-col>
          <right-toolbar v-model:show-search="showSearch" :columns="columns" :search="true" @query-table="getList" />
        </el-row>
      </template>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="measurementList" @selection-change="handleSelectionChange"
        class="shiceTable">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column v-if="columns[0].visible" key="measurementId" label="测量ID" align="center" prop="measurementId"
          width="120" show-overflow-tooltip />
        <el-table-column v-if="columns[1].visible" key="deviceName" label="设备名称" align="center" prop="deviceName"
          :show-overflow-tooltip="true" width="120px" />
        <el-table-column v-if="columns[2].visible" key="measurementItem" label="测量类型" align="center"
          prop="measurementItem" width="130">
          <template #default="scope">
            <el-tag :type="getmeasurementItemTag(scope.row.measurementItem)">
              {{ getmeasurementItemText(scope.row.measurementItem) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[3].visible" key="measurementResult" label="测量值" align="center" width="180"
          show-overflow-tooltip>
          <template #default="scope">
            <span :class="getValueClass(scope.row)">
              {{ scope.row.measurementResult }} {{ scope.row.unit }}
            </span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[4].visible" key="standardValue" label="标准值" align="center" width="100">
          <template #default="scope">
            {{ scope.row.standardValue }} {{ scope.row.unit }}
          </template>
        </el-table-column>
        <el-table-column v-if="columns[5].visible" key="tolerance" label="公差" align="center" width="80">
          <template #default="scope">
            ±{{ scope.row.tolerance }} {{ scope.row.unit }}
          </template>
        </el-table-column>
        <el-table-column v-if="columns[6].visible" key="measurementTime" label="测量日期" align="center"
          prop="measurementTime" width="160" show-overflow-tooltip />
        <el-table-column v-if="columns[7].visible" key="measurementLocation" label="测量位置" align="center"
          prop="measurementLocation" :show-overflow-tooltip="true" width="150px" />
        <el-table-column v-if="columns[8].visible" key="measurementPerson" label="测量人员" align="center"
          prop="measurementPerson" width="100" />
        <el-table-column v-if="columns[9].visible" key="isHazardMarked" label="隐患标记" align="center" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.isHazardMarked == '1'" type="danger" effect="dark">
              <el-icon>
                <Warning />
              </el-icon>
              隐患
            </el-tag>
            <el-tag v-else type="success">正常</el-tag>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[10].visible" key="status" label="状态" align="center" width="80">
          <template #default="scope">
            <el-tag :type="getStatusTag(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[11].visible" label="创建时间" align="center" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" width="280" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="查看详情" placement="top">
              <el-button v-has-permi="['quality:measurement:query']" link type="primary" icon="View"
                @click="handleDetail(scope.row)" />
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-has-permi="['quality:measurement:edit']" link type="primary" icon="Edit"
                @click="handleUpdate(scope.row)" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-has-permi="['quality:measurement:remove']" link type="primary" icon="Delete"
                @click="handleDelete(scope.row)" />
            </el-tooltip>
            <el-tooltip content="推送信息" placement="top">
              <el-button v-has-permi="['quality:measurement:push']" link type="primary" icon="Promotion"
                @click="handlePush(scope.row)" />
            </el-tooltip>
            <el-tooltip v-if="scope.row.isHazardMarked != '1'" content="标记隐患" placement="top">
              <el-button v-has-permi="['quality:measurement:hazard']" link type="danger" icon="Warning"
                @click="handleMarkHazard(scope.row)" />
            </el-tooltip>
            <el-tooltip v-else content="取消标记" placement="top">
              <el-button v-has-permi="['quality:measurement:hazard']" link type="success" icon="CircleCheck"
                @click="handleCancelMark(scope.row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
        :total="total" @pagination="getList" />
    </el-card>

    <!-- 添加或修改实测实量对话框 -->
    <el-dialog ref="formDialogRef" v-model="dialog.visible" :title="dialog.title" width="800px" append-to-body
      @close="closeDialog">
      <el-form ref="measurementFormRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备" prop="deviceId">
              <el-select v-model="form.deviceId" placeholder="请选择设备" filterable remote :remote-method="searchDevices"
                :loading="deviceLoading" @change="handleDeviceChange">
                <el-option v-for="device in deviceOptions" :key="device.deviceId" :label="device.deviceName"
                  :value="device.deviceId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测量类型" prop="measurementItem">
              <el-select v-model="form.measurementItem" placeholder="请选择测量类型">
                <el-option label="尺寸测量" value="dimension" />
                <el-option label="重量测量" value="weight" />
                <el-option label="温度测量" value="temperature" />
                <el-option label="压力测量" value="pressure" />
                <el-option label="其他测量" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="测量值" prop="measurementResult">
              <el-input-number v-model="form.measurementResult" :precision="2" controls-position="right"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="标准值" prop="standardValue">
              <el-input-number v-model="form.standardValue" :precision="2" controls-position="right"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="公差" prop="tolerance">
              <el-input-number v-model="form.tolerance" :precision="2" :min="0" controls-position="right"
                style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input v-model="form.unit" placeholder="请输入单位" maxlength="10" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测量日期" prop="measurementTime">
              <el-date-picker v-model="form.measurementTime" type="date" placeholder="选择测量日期" value-format="YYYY-MM-DD"
                style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="测量位置" prop="measurementLocation">
              <el-input v-model="form.measurementLocation" placeholder="请输入测量位置" maxlength="100" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测量人员" prop="measurementPerson">
              <el-input v-model="form.measurementPerson" placeholder="请输入测量人员" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio value="0">正常</el-radio>
                <el-radio value="1">异常</el-radio>
                <el-radio value="2">待复测</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="3" maxlength="500"
                show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 实测实量详情对话框 -->
    <el-dialog v-model="detailDialog.visible" title="实测实量详情" width="1000px" append-to-body class="shiceliang">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="设备名称">{{ detailData.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="测量类型">
          <el-tag :type="getmeasurementItemTag(detailData.measurementItem)">
            {{ getmeasurementItemText(detailData.measurementItem) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="测量值" :span="2">
          <span :class="getValueClass(detailData)">
            {{ detailData.measurementResult }} {{ detailData.unit }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="标准值">{{ detailData.standardValue }} {{ detailData.unit }}</el-descriptions-item>
        <el-descriptions-item label="公差">±{{ detailData.tolerance }} {{ detailData.unit }}</el-descriptions-item>
        <el-descriptions-item label="测量日期">{{ detailData.measurementTime }}</el-descriptions-item>
        <el-descriptions-item label="测量位置">{{ detailData.measurementLocation }}</el-descriptions-item>
        <el-descriptions-item label="测量人员">{{ detailData.measurementPerson }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTag(detailData.status)">
            {{ getStatusText(detailData.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="隐患标记">
          <el-tag v-if="detailData.isHazardMarked == '1'" type="danger" effect="dark">
            <el-icon>
              <Warning />
            </el-icon>
            隐患
          </el-tag>
          <el-tag v-else type="success">正常</el-tag>
        </el-descriptions-item>
        <el-descriptions-item v-if="detailData.hazardDescription" label="隐患描述" :span="2">
          {{ detailData.hazardDescription }}
        </el-descriptions-item>
        <el-descriptions-item v-if="detailData.remark" label="备注" :span="2">{{ detailData.remark
        }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detailData.createTime }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ detailData.createByName }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 推送信息对话框 -->
    <el-dialog v-model="pushDialog.visible" title="推送测量信息" width="600px" append-to-body>
      <el-form ref="pushFormRef" :model="pushForm" :rules="pushRules" label-width="100px">
        <el-form-item label="推送类型" prop="pushType">
          <el-radio-group v-model="pushForm.pushType">
            <el-radio value="email">邮件推送</el-radio>
            <el-radio value="sms">短信推送</el-radio>
            <el-radio value="system">系统通知</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="推送内容" prop="pushContent">
          <el-input v-model="pushForm.pushContent" type="textarea" placeholder="请输入推送内容" :rows="4" maxlength="500"
            show-word-limit />
        </el-form-item>
        <el-form-item label="接收人员" prop="recipients">
          <el-select v-model="pushForm.recipients" multiple placeholder="请选择接收人员" style="width: 100%">
            <el-option label="质量管理员" value="quality_manager" />
            <el-option label="设备负责人" value="device_manager" />
            <el-option label="项目经理" value="project_manager" />
            <el-option label="技术负责人" value="tech_manager" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="pushDialog.visible = false">取 消</el-button>
          <el-button type="primary" @click="confirmPush">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 标记隐患对话框 -->
    <el-dialog v-model="hazardDialog.visible" title="标记隐患" width="500px" append-to-body>
      <el-form ref="hazardFormRef" :model="hazardForm" :rules="hazardRules" label-width="100px">
        <el-form-item label="隐患描述" prop="hazardDescription">
          <el-input v-model="hazardForm.hazardDescription" type="textarea" placeholder="请详细描述发现的隐患问题" :rows="4"
            maxlength="500" show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="hazardDialog.visible = false">取 消</el-button>
          <el-button type="danger" @click="confirmMarkHazard">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Measurement" lang="ts">
import {
  listMeasurement,
  getMeasurement,
  delMeasurement,
  addMeasurement,
  updateMeasurement,
  exportMeasurement,
  pushMeasurementInfo,
  markHazard,
  cancelMarkHazard,
  getDeviceInfo
} from '@/api/quality/measurement';
import type { MeasurementVO, MeasurementForm, MeasurementQuery, PushInfoRequest, MarkHazardRequest } from '@/api/quality/measurement/types';
import { listDevice } from '@/api/quality/device';
import type { DeviceVO } from '@/api/quality/device/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const measurementList = ref<MeasurementVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref<[DateModelType, DateModelType]>(['', '']);

// 设备选项
const deviceOptions = ref<DeviceVO[]>([]);
const deviceLoading = ref(false);

// 列显示控制
const columns = ref([
  { key: 0, label: `测量ID`, visible: true },
  { key: 1, label: `设备名称`, visible: true },
  { key: 2, label: `测量类型`, visible: true },
  { key: 3, label: `测量值`, visible: true },
  { key: 4, label: `标准值`, visible: true },
  { key: 5, label: `公差`, visible: true },
  { key: 6, label: `测量日期`, visible: true },
  { key: 7, label: `测量位置`, visible: true },
  { key: 8, label: `测量人员`, visible: true },
  { key: 9, label: `隐患标记`, visible: true },
  { key: 10, label: `状态`, visible: true },
  { key: 11, label: `创建时间`, visible: true }
]);

// 查询参数
const queryParams = ref<MeasurementQuery>({
  pageNum: 1,
  pageSize: 10,
  deviceId: undefined,
  measurementItem: '',
  status: ''
});

// 表单参数
const form = ref<MeasurementForm>({
  deviceId: 0,
  measurementItem: '',
  measurementResult: 0,
  standardValue: 0,
  tolerance: 0,
  unit: '',
  measurementTime: '',
  measurementLocation: '',
  measurementPerson: '',
  status: '0',
  remark: ''
});

// 推送表单
const pushForm = ref<PushInfoRequest>({
  measurementId: 0,
  pushType: 'system',
  pushContent: '',
  recipients: []
});

// 隐患表单
const hazardForm = ref<MarkHazardRequest>({
  measurementId: 0,
  hazardDescription: ''
});

// 表单校验
const rules = reactive({
  deviceId: [{ required: true, message: '请选择设备', trigger: 'change' }],
  measurementItem: [{ required: true, message: '请选择测量类型', trigger: 'change' }],
  measurementResult: [{ required: true, message: '请输入测量值', trigger: 'blur' }],
  standardValue: [{ required: true, message: '请输入标准值', trigger: 'blur' }],
  tolerance: [{ required: true, message: '请输入公差', trigger: 'blur' }],
  unit: [{ required: true, message: '请输入单位', trigger: 'blur' }],
  measurementTime: [{ required: true, message: '请选择测量日期', trigger: 'change' }],
  measurementLocation: [{ required: true, message: '请输入测量位置', trigger: 'blur' }],
  measurementPerson: [{ required: true, message: '请输入测量人员', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
});

// 推送表单校验
const pushRules = reactive({
  pushType: [{ required: true, message: '请选择推送类型', trigger: 'change' }],
  pushContent: [{ required: true, message: '请输入推送内容', trigger: 'blur' }],
  recipients: [{ required: true, message: '请选择接收人员', trigger: 'change' }]
});

// 隐患表单校验
const hazardRules = reactive({
  hazardDescription: [{ required: true, message: '请输入隐患描述', trigger: 'blur' }]
});

// 对话框
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const detailDialog = reactive({
  visible: false
});

const pushDialog = reactive({
  visible: false
});

const hazardDialog = reactive({
  visible: false
});

// 详情数据
const detailData = ref<MeasurementVO>({} as MeasurementVO);

const queryFormRef = ref<ElFormInstance>();
const measurementFormRef = ref<ElFormInstance>();
const pushFormRef = ref<ElFormInstance>();
const hazardFormRef = ref<ElFormInstance>();

/** 查询实测实量列表 */
const getList = async () => {
  loading.value = true;
  try {
    const res = await listMeasurement(proxy?.addDateRange(queryParams.value, dateRange.value));
    measurementList.value = res.rows;
    total.value = res.total;
  } finally {
    loading.value = false;
  }
};

/** 搜索设备 */
const searchDevices = async (query: string) => {
  if (query) {
    deviceLoading.value = true;
    try {
      const res = await listDevice({ deviceName: query, pageNum: 1, pageSize: 20 });
      deviceOptions.value = res.rows;
    } finally {
      deviceLoading.value = false;
    }
  } else {
    deviceOptions.value = [];
  }
};

/** 设备变化处理 */
const handleDeviceChange = async (deviceId: number) => {
  if (deviceId) {
    try {
      const res = await getDeviceInfo(deviceId);
      // 可以根据设备信息自动填充一些字段
    } catch (error) {
      console.error('获取设备信息失败:', error);
    }
  }
};

/** 取消按钮 */
const closeDialog = () => {
  dialog.visible = false;
  reset();
};

/** 表单重置 */
const reset = () => {
  form.value = {
    deviceId: 0,
    measurementItem: '',
    measurementResult: 0,
    standardValue: 0,
    tolerance: 0,
    unit: '',
    measurementTime: '',
    measurementLocation: '',
    measurementPerson: '',
    status: '0',
    remark: ''
  };
  measurementFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: MeasurementVO[]) => {
  ids.value = selection.map(item => item.measurementId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加实测实量';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: MeasurementVO) => {
  reset();
  const measurementId = row?.measurementId || ids.value[0];
  try {
    const res = await getMeasurement(measurementId);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = '修改实测实量';
  } catch (error) {
    proxy?.$modal.msgError('获取实测实量信息失败');
  }
};

/** 提交按钮 */
const submitForm = () => {
  measurementFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (form.value.measurementId) {
          await updateMeasurement(form.value);
          proxy?.$modal.msgSuccess('修改成功');
        } else {
          await addMeasurement(form.value);
          proxy?.$modal.msgSuccess('新增成功');
        }
        dialog.visible = false;
        await getList();
      } catch (error) {
        proxy?.$modal.msgError('操作失败');
      }
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: MeasurementVO) => {
  const measurementIds = row?.measurementId || ids.value;
  try {
    await proxy?.$modal.confirm('是否确认删除测量ID为"' + measurementIds + '"的数据项？');
    await delMeasurement(measurementIds);
    await getList();
    proxy?.$modal.msgSuccess('删除成功');
  } catch (error) {
    proxy?.$modal.msgError('删除失败');
  }
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'quality/measurement/export',
    {
      ...queryParams.value
    },
    `measurement_${new Date().getTime()}.xlsx`
  );
};

/** 查看详情 */
const handleDetail = async (row: MeasurementVO) => {
  try {
    const res = await getMeasurement(row.measurementId);
    detailData.value = res.data;
    detailDialog.visible = true;
  } catch (error) {
    proxy?.$modal.msgError('获取实测实量详情失败');
  }
};

/** 推送信息 */
const handlePush = (row: MeasurementVO) => {
  pushForm.value = {
    measurementId: row.measurementId,
    pushType: 'system',
    pushContent: `设备【${row.deviceName}】测量异常，测量值：${row.measurementResult}${row.unit}，标准值：${row.standardValue}${row.unit}，请及时处理。`,
    recipients: []
  };
  pushDialog.visible = true;
};

/** 确认推送 */
const confirmPush = () => {
  pushFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      try {
        await pushMeasurementInfo(pushForm.value);
        proxy?.$modal.msgSuccess('推送成功');
        pushDialog.visible = false;
      } catch (error) {
        proxy?.$modal.msgError('推送失败');
      }
    }
  });
};

/** 标记隐患 */
const handleMarkHazard = (row: MeasurementVO) => {
  hazardForm.value = {
    measurementId: row.measurementId,
    hazardDescription: ''
  };
  hazardDialog.visible = true;
};

/** 确认标记隐患 */
const confirmMarkHazard = () => {
  hazardFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      try {
        await markHazard(hazardForm.value);
        proxy?.$modal.msgSuccess('标记隐患成功');
        hazardDialog.visible = false;
        await getList();
      } catch (error) {
        proxy?.$modal.msgError('标记隐患失败');
      }
    }
  });
};

/** 取消标记隐患 */
const handleCancelMark = async (row: MeasurementVO) => {
  try {
    await proxy?.$modal.confirm('是否确认取消隐患标记？');
    await cancelMarkHazard(row.measurementId);
    proxy?.$modal.msgSuccess('取消标记成功');
    await getList();
  } catch (error) {
    proxy?.$modal.msgError('取消标记失败');
  }
};

/** 获取测量类型标签 */
const getmeasurementItemTag = (type: string) => {
  const typeMap: Record<string, string> = {
    dimension: 'primary',
    weight: 'success',
    temperature: 'warning',
    pressure: 'danger',
    other: 'info'
  };
  return typeMap[type] || 'info';
};

/** 获取测量类型文本 */
const getmeasurementItemText = (type: string) => {
  const typeMap: Record<string, string> = {
    dimension: '尺寸测量',
    weight: '重量测量',
    temperature: '温度测量',
    pressure: '压力测量',
    other: '其他测量'
  };
  return typeMap[type] || type;
};

/** 获取状态标签 */
const getStatusTag = (status: string) => {
  const statusMap: Record<string, string> = {
    '0': 'success',
    '1': 'danger',
    '2': 'warning'
  };
  return statusMap[status] || 'info';
};

/** 获取状态文本 */
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '0': '正常',
    '1': '异常',
    '2': '待复测'
  };
  return statusMap[status] || status;
};

/** 获取测量值样式 */
const getValueClass = (row: MeasurementVO) => {
  const diff = Math.abs(row.measurementResult - row.standardValue);
  if (diff > row.tolerance) {
    return 'text-red-500 font-bold';
  } else if (diff > row.tolerance * 0.8) {
    return 'text-orange-500 font-medium';
  }
  return 'text-green-500';
};

onMounted(() => {
  getList();
  // 初始化设备选项
  searchDevices('');
});
</script>

<style scoped>
.text-red-500 {
  color: #ef4444;
}

.text-orange-500 {
  color: #f97316;
}

.text-green-500 {
  color: #22c55e;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}
</style>
<style lang="scss">
.shiceTable {

  .el-popper,
  .is-dark {
    max-width: 300px;
  }
}

.shiceliang {
  .el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell {
    width: 0px !important;
  }
}
</style>

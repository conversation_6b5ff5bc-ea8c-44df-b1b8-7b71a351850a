package org.dromara.facility.service.impl;

import com.alibaba.fastjson2.JSON;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.facility.domain.bo.LnHighFormworkRealBo;
import org.dromara.facility.domain.vo.LnHighFormworkRealVo;
import org.dromara.facility.domain.LnHighFormworkReal;
import org.dromara.facility.mapper.LnHighFormworkRealMapper;
import org.dromara.facility.service.ILnHighFormworkRealService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 绿能高支模实时数据Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@RequiredArgsConstructor
@Service
public class LnHighFormworkRealServiceImpl implements ILnHighFormworkRealService {

    private final LnHighFormworkRealMapper baseMapper;

    /**
     * 查询绿能高支模实时数据
     *
     * @param id 主键
     * @return 绿能高支模实时数据
     */
    @Override
    public LnHighFormworkRealVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询绿能高支模实时数据列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能高支模实时数据分页列表
     */
    @Override
    public TableDataInfo<LnHighFormworkRealVo> queryPageList(LnHighFormworkRealBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LnHighFormworkReal> lqw = buildQueryWrapper(bo);
        Page<LnHighFormworkRealVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的绿能高支模实时数据列表
     *
     * @param bo 查询条件
     * @return 绿能高支模实时数据列表
     */
    @Override
    public List<LnHighFormworkRealVo> queryList(LnHighFormworkRealBo bo) {
        LambdaQueryWrapper<LnHighFormworkReal> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LnHighFormworkReal> buildQueryWrapper(LnHighFormworkRealBo bo) {
        LambdaQueryWrapper<LnHighFormworkReal> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(LnHighFormworkReal::getCreateTime);
        lqw.eq(LnHighFormworkReal::getDevNo, bo.getDevNo());
        return lqw;
    }

    /**
     * 新增绿能高支模实时数据
     *
     * @param bo 绿能高支模实时数据
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LnHighFormworkRealBo bo) {
        LnHighFormworkReal add = MapstructUtils.convert(bo, LnHighFormworkReal.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public void insertByJson(String jsonString) {
        LnHighFormworkReal add = MapstructUtils.convert(JSON.parseObject(jsonString, LnHighFormworkRealBo.class), LnHighFormworkReal.class);
        assert add != null;
        add.setCreateTime(new Date());
        baseMapper.insert(add);
    }

    /**
     * 修改绿能高支模实时数据
     *
     * @param bo 绿能高支模实时数据
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LnHighFormworkRealBo bo) {
        LnHighFormworkReal update = MapstructUtils.convert(bo, LnHighFormworkReal.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LnHighFormworkReal entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除绿能高支模实时数据信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


}

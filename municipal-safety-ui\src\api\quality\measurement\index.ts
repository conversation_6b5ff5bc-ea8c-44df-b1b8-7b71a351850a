import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  MeasurementForm,
  MeasurementQuery,
  MeasurementVO,
  DeviceInfoResponse,
  PushInfoRequest,
  MarkHazardRequest
} from './types';

/**
 * 查询实测实量列表
 * @param query 查询参数
 */
export const listMeasurement = (query: MeasurementQuery): AxiosPromise<MeasurementVO[]> => {
  return request({
    url: '/quality/measurement/list',
    method: 'get',
    params: query
  });
};

/**
 * 导出实测实量列表
 * @param query 查询参数
 */
export const exportMeasurement = (query: MeasurementQuery) => {
  return request({
    url: '/quality/measurement/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  });
};

/**
 * 获取实测实量详细信息
 * @param measurementId 实测实量ID
 */
export const getMeasurement = (measurementId: string | number): AxiosPromise<MeasurementVO> => {
  return request({
    url: '/quality/measurement/' + measurementId,
    method: 'get'
  });
};

/**
 * 新增实测实量
 * @param data 实测实量数据
 */
export const addMeasurement = (data: MeasurementForm) => {
  return request({
    url: '/quality/measurement',
    method: 'post',
    data: data
  });
};

/**
 * 修改实测实量
 * @param data 实测实量数据
 */
export const updateMeasurement = (data: MeasurementForm) => {
  return request({
    url: '/quality/measurement',
    method: 'put',
    data: data
  });
};

/**
 * 删除实测实量
 * @param measurementId 实测实量ID
 */
export const delMeasurement = (measurementId: Array<string | number> | string | number) => {
  return request({
    url: '/quality/measurement/' + measurementId,
    method: 'delete'
  });
};

/**
 * 推送测量信息
 * @param data 推送信息数据
 */
export const pushMeasurementInfo = (data: PushInfoRequest) => {
  return request({
    url: '/quality/measurement/push',
    method: 'post',
    data: data
  });
};

/**
 * 标记隐患
 * @param data 隐患信息
 */
export const markHazard = (data: MarkHazardRequest) => {
  return request({
    url: '/quality/measurement/markHazard',
    method: 'post',
    params: data
  });
};

/**
 * 取消标记隐患
 * @param measurementId 实测实量ID
 */
export const cancelMarkHazard = (measurementId: string | number) => {
  return request({
    url: '/quality/measurement/unmarkHazard/' + measurementId,
    method: 'post'
  });
};

/**
 * 根据设备ID获取设备信息
 * @param deviceId 设备ID
 */
export const getDeviceInfo = (deviceId: string | number): AxiosPromise<DeviceInfoResponse> => {
  return request({
    url: '/quality/measurement/deviceInfo/' + deviceId,
    method: 'get'
  });
};

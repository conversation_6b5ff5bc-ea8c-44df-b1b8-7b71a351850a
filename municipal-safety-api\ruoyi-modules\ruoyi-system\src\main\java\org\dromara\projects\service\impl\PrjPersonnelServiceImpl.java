package org.dromara.projects.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.dromara.attendance.domain.vo.MAttPersonVo;
import org.dromara.attendance.domain.vo.MAttSnVo;
import org.dromara.attendance.mapper.MAttPersonMapper;
import org.dromara.attendance.mapper.MAttSnMapper;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.projects.domain.PrjPersonnel;
import org.dromara.projects.domain.bo.PrjPersonnelBo;
import org.dromara.projects.domain.vo.PrjPersonnelVo;
import org.dromara.projects.mapper.PrjPersonnelMapper;
import org.dromara.projects.mapper.PrjProjectsMapper;
import org.dromara.projects.service.IPrjPersonnelService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 项目人员关联表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@RequiredArgsConstructor
@Service
public class PrjPersonnelServiceImpl implements IPrjPersonnelService {

    private final PrjPersonnelMapper baseMapper;

    private final PrjProjectsMapper prjProjectsMapper;

    @Resource
    private MAttSnMapper mAttSnMapper;

    @Resource
    private MAttPersonMapper mAttPersonMapper;

    /**
     * 查询项目人员关联表
     *
     * @param projectPersonnelId 主键
     * @return 项目人员关联表
     */
    @Override
    public PrjPersonnelVo queryById(Long projectPersonnelId){
        return baseMapper.selectVoById(projectPersonnelId);
    }

    /**
     * 分页查询项目人员关联表列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目人员关联表分页列表
     */
    @Override
    public TableDataInfo<PrjPersonnelVo> queryPageList(PrjPersonnelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PrjPersonnel> lqw = buildQueryWrapper(bo);
        Page<PrjPersonnelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的项目人员关联表列表
     *
     * @param bo 查询条件
     * @return 项目人员关联表列表
     */
    @Override
    public List<PrjPersonnelVo> queryList(PrjPersonnelBo bo) {
        LambdaQueryWrapper<PrjPersonnel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PrjPersonnel> buildQueryWrapper(PrjPersonnelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PrjPersonnel> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PrjPersonnel::getProjectPersonnelId);
        lqw.eq(bo.getProjectId() != null, PrjPersonnel::getProjectId, bo.getProjectId());
        lqw.eq(bo.getPersonId() != null, PrjPersonnel::getPersonId, bo.getPersonId());
        lqw.eq(bo.getUserId() != null, PrjPersonnel::getUserId, bo.getUserId());
        lqw.eq(bo.getOrgId() != null, PrjPersonnel::getOrgId, bo.getOrgId());
        lqw.eq(StringUtils.isNotBlank(bo.getRoleOnProject()), PrjPersonnel::getRoleOnProject, bo.getRoleOnProject());
        lqw.eq(bo.getIsSpecialOps() != null, PrjPersonnel::getIsSpecialOps, bo.getIsSpecialOps());
        lqw.eq(bo.getStartDateOnProject() != null, PrjPersonnel::getStartDateOnProject, bo.getStartDateOnProject());
        lqw.eq(bo.getEndDateOnProject() != null, PrjPersonnel::getEndDateOnProject, bo.getEndDateOnProject());
        return lqw;
    }

    /**
     * 新增项目人员关联表
     *
     * @param bo 项目人员关联表
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PrjPersonnelBo bo) {
        PrjPersonnel add = MapstructUtils.convert(bo, PrjPersonnel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setProjectPersonnelId(add.getProjectPersonnelId());
        }
        return flag;
    }

    /**
     * 修改项目人员关联表
     *
     * @param bo 项目人员关联表
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PrjPersonnelBo bo) {
        PrjPersonnel update = MapstructUtils.convert(bo, PrjPersonnel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PrjPersonnel entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除项目人员关联表信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<PrjPersonnelVo> selectPrjPersonByPersonId(String projectId, Long personId) {
        return baseMapper.selectPrjPersonByPersonId(projectId, personId);
    }

    /**
     * 查询项目人员列表
     *
     * @param projectId 项目ID
     * @return 项目人员列表
     */
    @Override
//    public List<PrjPersonnelVo> queryPersonnelListByProjectId(Long projectId) {
//        List<MAttSnVo> mAttSnVos1 = mAttSnMapper.selectMAttSnByProjectId(projectId);
//        List<PrjPersonnelVo> prjPersonnelVos = baseMapper.selectPersonnelListByProjectId(projectId);
//        for (PrjPersonnelVo prjPersonnelVo : prjPersonnelVos) {
//            List<MAttSnVo> mAttSnVos = new ArrayList<>();
//            List<MAttPersonVo> mAttPersonVos = mAttPersonMapper.selectMAttPersonByPersonId(prjPersonnelVo.getProjectPersonnelId());
//            for (MAttPersonVo mAttPersonVo : mAttPersonVos) {
//                MAttSnVo mAttSnVo = mAttSnMapper.selectVoById(mAttPersonVo.getSnId());
//                mAttSnVo.setPersonSnId(mAttPersonVo.getId());
//                mAttSnVos.add(mAttSnVo);
//            }
//            prjPersonnelVo.setDisable(mAttSnVos1.size() == mAttSnVos.size());
//            prjPersonnelVo.setMAttSnVos(mAttSnVos);
//        }
//        return prjPersonnelVos;
//    }
    public List<PrjPersonnelVo> queryPersonnelListByProjectId(Long projectId) {
        // 预加载项目下所有SN设备
        List<MAttSnVo> projectSnList = mAttSnMapper.selectMAttSnByProjectId(projectId);
        int totalSnCount = projectSnList.size();

        // 获取项目人员列表
        List<PrjPersonnelVo> personnelList = baseMapper.selectPersonnelListByProjectId(projectId);

        // 并行处理每个人员
        return personnelList.parallelStream()
            .map(personnel -> enhancePersonnelVo(personnel, totalSnCount))
            .collect(Collectors.toList());
    }

    private PrjPersonnelVo enhancePersonnelVo(PrjPersonnelVo personnel, int totalSnCount) {
        // 获取人员关联的所有SN设备
        List<MAttPersonVo> personSnRelations = mAttPersonMapper.selectMAttPersonByPersonId(personnel.getProjectPersonnelId());

        // 转换为MAttSnVo列表并设置personSnId
        List<MAttSnVo> personSnList = personSnRelations.stream()
            .map(relation -> {
                MAttSnVo snVo = mAttSnMapper.selectVoById(relation.getSnId());
                snVo.setPersonSnId(relation.getId());
                return snVo;
            })
            .collect(Collectors.toList());

        // 设置人员属性
        personnel.setDisable(totalSnCount == personSnList.size());
        personnel.setMAttSnVos(personSnList);

        return personnel;
    }
}

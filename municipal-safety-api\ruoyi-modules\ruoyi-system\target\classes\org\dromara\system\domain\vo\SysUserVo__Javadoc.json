{"doc": " 用户信息视图对象 sys_user\n\n <AUTHOR>\n", "fields": [{"name": "userId", "doc": " 用户ID\n"}, {"name": "tenantId", "doc": " 租户ID\n"}, {"name": "deptId", "doc": " 部门ID\n"}, {"name": "userName", "doc": " 用户账号\n"}, {"name": "nick<PERSON><PERSON>", "doc": " 用户昵称\n"}, {"name": "userType", "doc": " 用户类型（sys_user系统用户）\n"}, {"name": "email", "doc": " 用户邮箱\n"}, {"name": "phonenumber", "doc": " 手机号码\n"}, {"name": "sex", "doc": " 用户性别（0男 1女 2未知）\n"}, {"name": "avatar", "doc": " 头像地址\n"}, {"name": "password", "doc": " 密码\n"}, {"name": "status", "doc": " 帐号状态（0正常 1停用）\n"}, {"name": "loginIp", "doc": " 最后登录IP\n"}, {"name": "loginDate", "doc": " 最后登录时间\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "deptName", "doc": " 部门名\n"}, {"name": "roles", "doc": " 角色对象\n"}, {"name": "roleIds", "doc": " 角色组\n"}, {"name": "postIds", "doc": " 岗位组\n"}, {"name": "roleId", "doc": " 数据权限 当前角色ID\n"}], "enumConstants": [], "methods": [], "constructors": []}
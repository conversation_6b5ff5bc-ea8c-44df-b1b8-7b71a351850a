package org.dromara.facility.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.facility.domain.bo.JlDustRealBo;
import org.dromara.facility.domain.vo.JlDustRealVo;
import org.dromara.facility.service.IJlDustRealService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 扬尘数据
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/dustReal")
public class JlDustRealController extends BaseController {

    private final IJlDustRealService jlDustRealService;

    /**
     * 查询扬尘数据列表
     */
    @GetMapping("/list")
    public TableDataInfo<JlDustRealVo> list(JlDustRealBo bo, PageQuery pageQuery) {
        return jlDustRealService.queryPageList(bo, pageQuery);
    }
}

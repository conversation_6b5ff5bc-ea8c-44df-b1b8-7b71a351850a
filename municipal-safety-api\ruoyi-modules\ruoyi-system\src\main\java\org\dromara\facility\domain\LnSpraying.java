package org.dromara.facility.domain;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 绿能喷淋设备对象 ln_spraying
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ln_spraying")
public class LnSpraying {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 继电器状态
     */
    private String relayStatus;

    /**
     * 工作模式 0-自动 1-手动
     */
    private Long workMode;

    /**
     * 水位传感器 0-正常 1-下限位 2- 上限位 3-异常
     */
    private Long waterSensor;

    /**
     * 设备状态
     */
    private Long status;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

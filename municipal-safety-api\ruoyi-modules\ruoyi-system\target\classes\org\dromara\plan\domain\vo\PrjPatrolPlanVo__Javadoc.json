{"doc": " 巡检计划视图对象 prj_patrol_plan\n\n <AUTHOR>\n @date 2025-06-18\n", "fields": [{"name": "planId", "doc": " 主键\n"}, {"name": "planName", "doc": " 计划名称\n"}, {"name": "beginTime", "doc": " 计划开始时间\n"}, {"name": "endTime", "doc": " 计划结束时间\n"}, {"name": "deptIds", "doc": " 监督机构id\n"}, {"name": "projectIds", "doc": " 检查项目id\n"}, {"name": "expertIds", "doc": " 专家ids\n"}, {"name": "remarks", "doc": " 备注\n"}, {"name": "mainDeptId", "doc": " 主办部门\n"}, {"name": "mainDeptName", "doc": "  主办部门名称\n"}, {"name": "projectList", "doc": "  项目信息列表\n"}, {"name": "expertList", "doc": "  专家信息列表\n"}, {"name": "deptList", "doc": " 部门信息列表\n"}], "enumConstants": [], "methods": [], "constructors": []}
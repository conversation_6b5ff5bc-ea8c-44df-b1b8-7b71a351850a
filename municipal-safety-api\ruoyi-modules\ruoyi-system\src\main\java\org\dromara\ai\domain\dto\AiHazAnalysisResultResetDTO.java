package org.dromara.ai.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.ai.domain.AiHazAnalysisTasksResult;
import org.dromara.projects.domain.vo.ViolationVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/13 16:27
 * @Description TODO
 * @Version 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AiHazAnalysisResultResetDTO {

    /**
     * ai隐患id
     */
    private Long taskId;

    /**
     * 上传的照片文档ID
     */
    private Long photoDocumentId;

    /**
     * AI分析后返回的带标注的照片文档ID
     */
    private Long aiPhotoDocumentId;

    /**
     * 隐患描述列表
     */
    private List<AiHazAnalysisTasksResult> violations;
}

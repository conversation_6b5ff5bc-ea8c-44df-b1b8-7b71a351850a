package org.dromara.projects.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.PrjSafeSupervision;
import org.dromara.projects.domain.PrjSafeSupervisionToPrjSafeSupervisionVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjSafeSupervisionToPrjSafeSupervisionVoMapper.class},
    imports = {}
)
public interface PrjSafeSupervisionVoToPrjSafeSupervisionMapper extends BaseMapper<PrjSafeSupervisionVo, PrjSafeSupervision> {
}

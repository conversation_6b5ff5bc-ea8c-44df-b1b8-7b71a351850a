<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="lnNutList">
      <el-table-column label="参数" align="center" prop="para" />
      <el-table-column label="值类型" align="center" prop="vt" />
      <el-table-column label="值状态" align="center" prop="qds" />
      <el-table-column label="数据时间" align="center" prop="time" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.time, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数值" align="center" prop="value" />
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

  </div>
</template>

<script>
import { listLnNut } from "@/api/projects/facility/index";

export default {
  name: "LnNut",
  props: {
    devNo: {
      type: String,
      default: ""
    }
  },
  data () {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 绿能螺母表格数据
      lnNutList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        devNo: null
      },
    };
  },
  created () {
    this.getList();
  },
  methods: {
    /** 查询绿能螺母列表 */
    async getList () {
      this.queryParams.devNo = this.devNo
      this.lnNutList = []
      this.loading = true;
      const res = await listLnNut(this.queryParams)
      this.lnNutList = res.rows;
      this.total = res.total;
      this.loading = false;
    },
  }
};
</script>

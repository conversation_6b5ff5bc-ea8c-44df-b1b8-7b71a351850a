package org.dromara.projects.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjExpertReviews;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjExpertReviewsVoToPrjExpertReviewsMapperImpl implements PrjExpertReviewsVoToPrjExpertReviewsMapper {

    @Override
    public PrjExpertReviews convert(PrjExpertReviewsVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjExpertReviews prjExpertReviews = new PrjExpertReviews();

        prjExpertReviews.setReviewId( arg0.getReviewId() );
        prjExpertReviews.setPlanId( arg0.getPlanId() );
        prjExpertReviews.setReviewDate( arg0.getReviewDate() );
        prjExpertReviews.setMeetingLocation( arg0.getMeetingLocation() );
        prjExpertReviews.setConclusion( arg0.getConclusion() );
        prjExpertReviews.setExpertOpinionSummary( arg0.getExpertOpinionSummary() );
        prjExpertReviews.setConflictOfInterestWarning( arg0.getConflictOfInterestWarning() );
        prjExpertReviews.setReportDocumentId( arg0.getReportDocumentId() );
        prjExpertReviews.setConvenorUserId( arg0.getConvenorUserId() );

        return prjExpertReviews;
    }

    @Override
    public PrjExpertReviews convert(PrjExpertReviewsVo arg0, PrjExpertReviews arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setReviewId( arg0.getReviewId() );
        arg1.setPlanId( arg0.getPlanId() );
        arg1.setReviewDate( arg0.getReviewDate() );
        arg1.setMeetingLocation( arg0.getMeetingLocation() );
        arg1.setConclusion( arg0.getConclusion() );
        arg1.setExpertOpinionSummary( arg0.getExpertOpinionSummary() );
        arg1.setConflictOfInterestWarning( arg0.getConflictOfInterestWarning() );
        arg1.setReportDocumentId( arg0.getReportDocumentId() );
        arg1.setConvenorUserId( arg0.getConvenorUserId() );

        return arg1;
    }
}

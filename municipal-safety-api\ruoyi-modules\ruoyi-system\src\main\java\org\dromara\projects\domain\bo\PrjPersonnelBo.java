package org.dromara.projects.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.projects.domain.PrjPersonnel;

import java.util.Date;

/**
 * 项目人员关联表业务对象 prj_personnel
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjPersonnel.class, reverseConvertGenerate = false)
public class PrjPersonnelBo extends BaseEntity {

    /**
     * 项目人员关联ID
     */
    @NotNull(message = "项目人员关联ID不能为空", groups = {EditGroup.class})
    private Long projectPersonnelId;

    /**
     * 项目ID (逻辑外键至 prj_projects.project_id)
     */
    @NotNull(message = "项目ID (逻辑外键至 prj_projects.project_id)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long projectId;

    /**
     * 人员表id
     */
    @NotNull(message = "人员表id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long personId;

    /**
     * 用户ID (逻辑外键至 sys_users.user_id)
     */
    @NotNull(message = "用户ID (逻辑外键至 sys_users.user_id)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 该人员在项目中的所属单位ID (逻辑外键至 sys_organizations.org_id)
     */
    private Long orgId;

    /**
     * 在本项目中的具体角色/岗位（取字典值）
     */
    @NotBlank(message = "在本项目中的具体角色/岗位（取字典值）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String roleOnProject;

    /**
     * 是否特种作业人员 (0:否, 1:是)
     */
    private Long isSpecialOps;

    /**
     * 进入项目日期
     */
    private Date startDateOnProject;

    /**
     * 离开项目日期
     */
    private Date endDateOnProject;

}

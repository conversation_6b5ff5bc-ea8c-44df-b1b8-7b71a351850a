package org.dromara.projects.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.attendance.domain.vo.MAttSnVo;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.projects.domain.PrjPersonnel;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 项目人员关联表视图对象 prj_personnel
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjPersonnel.class)
public class PrjPersonnelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 项目人员关联ID
     */
    @ExcelProperty(value = "项目人员关联ID")
    private Long projectPersonnelId;

    /**
     * 项目ID (逻辑外键至 prj_projects.project_id)
     */
    @ExcelProperty(value = "项目ID (逻辑外键至 prj_projects.project_id)")
    private Long projectId;

    /**
     * 人员表id
     */
    @ExcelProperty(value = "人员表id")
    private Long personId;

    /**
     * 用户ID (逻辑外键至 sys_users.user_id)
     */
    @ExcelProperty(value = "用户ID (逻辑外键至 sys_users.user_id)")
    private Long userId;

    /**
     * 该人员在项目中的所属单位ID (逻辑外键至 sys_organizations.org_id)
     */
    @ExcelProperty(value = "该人员在项目中的所属单位ID (逻辑外键至 sys_organizations.org_id)")
    private Long orgId;

    /**
     * 在本项目中的具体角色/岗位（取字典值）
     */
    @ExcelProperty(value = "在本项目中的具体角色/岗位", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "personnel_position")
    private String roleOnProject;

    /**
     * 是否特种作业人员 (0:否, 1:是)
     */
    @ExcelProperty(value = "是否特种作业人员 (0:否, 1:是)")
    private Long isSpecialOps;

    /**
     * 进入项目日期
     */
    @ExcelProperty(value = "进入项目日期")
    private Date startDateOnProject;

    /**
     * 离开项目日期
     */
    @ExcelProperty(value = "离开项目日期")
    private Date endDateOnProject;

    /**
     * 人员信息
     */
    private String name;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 性别
     */
    private String gender;

    /**
     * 文化程度
     */
    private String education;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 企业统一社会信用代码
     */
    private String unifiedSocialCreditCode;

    private Long deptId;

    private Long headImgId;

    private String url;

    private List<MAttSnVo> mAttSnVos;

    private Boolean disable;

}

package org.dromara.flow.domain.bo;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.flow.domain.PrjHazardousItemsComments;

/**
 * 质监站隐患清单整改业务对象 prj_hazardous_items_comments
 *
 * <AUTHOR> zu da
 * @date 2025-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjHazardousItemsComments.class, reverseConvertGenerate = false)
public class PrjHazardousItemsCommentsBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 问题点id
     */
    private String question;

    /**
     * 整改时限
     */
    private Long timeLimit;

    /**
     * 整改时限类型 1小时 2天
     */
    private Long timeType;

    /**
     * 限期整改文件
     */
    private Long correctionsFile;

    /**
     * 限期整改内容
     */
    private String correctionsContent;

    /**
     * 停工通知文件
     */
    private Long suspensionFile;

    /**
     * 停工通知内容
     */
    private String suspensionContent;

    /**
     * 行政处罚文件
     */
    private Long penaltyFile;

    /**
     * 行政处罚内容
     */
    private String penaltyContent;

    /**
     * 业务id
     */
    private String taskId;
}

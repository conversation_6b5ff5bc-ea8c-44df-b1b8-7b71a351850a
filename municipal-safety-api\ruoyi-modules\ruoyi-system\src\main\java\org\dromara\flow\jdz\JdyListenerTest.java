package org.dromara.flow.jdz;

import org.dromara.warm.flow.core.dto.FlowParams;
import org.dromara.warm.flow.core.entity.Node;
import org.dromara.warm.flow.core.entity.Task;
import org.dromara.warm.flow.core.listener.Listener;
import org.dromara.warm.flow.core.listener.ListenerVariable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/26 15:22
 * @Description TODO
 * @Version 1.0
 */
@Component
public class JdyListenerTest implements Listener {
    @Override
    public void notify(ListenerVariable listenerVariable) {
        FlowParams params = listenerVariable.getFlowParams();
        List<Task> nextTasks = listenerVariable.getNextTasks();
        Map<String, Object> variable = params.getVariable();

        if (variable != null) {
            System.out.println(variable.get("real"));

            List<Node> nextNodes = listenerVariable.getNextNodes();
            Node node = nextNodes.get(0);

            variable.put("real", 1);
            //强制转发超管
            variable.put(node.getNodeCode(), "1");
        }

        System.out.println(params);
    }
}

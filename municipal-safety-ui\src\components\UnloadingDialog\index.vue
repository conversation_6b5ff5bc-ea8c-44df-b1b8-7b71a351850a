<template>
  <div class="UnloadingDialog">
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" append-to-body @close="handleClose"
      width="80%">
      <div>
        <DumpPlat v-if="dialogVisible.visible" ref="dumpPlat" :dev-no="devNo" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import DumpPlat from "./dump_plat.vue";

const props = defineProps({
  isShowDumpModel: {
    type: Boolean,
    default: false
  },
  devNo: {
    type: String,
    default: ''
  }
});
const emit = defineEmits(['update:isShowDumpModel']);
const dialogVisible = reactive<DialogOption>({
  visible: false,
  title: '实时数据'
});
watch(() => props.isShowDumpModel, (newVal) => {
  dialogVisible.visible = newVal;
}
);
const handleClose = () => {
  emit('update:isShowDumpModel', false);
};
</script>

<style lang="scss" scoped></style>
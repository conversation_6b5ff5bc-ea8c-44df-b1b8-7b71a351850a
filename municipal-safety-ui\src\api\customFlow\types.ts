export interface HazardousItemsCommentsVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 隐患描述
   */
  description: string;

  /**
   * 违反条款
   */
  ordinances: string;

  /**
   * ai整改意见
   */
  aiComments: string;

  /**
   * 预警是否真实 1是 0否
   */
  commentStatus: string | number;

  /**
   * 问题id
   */
  resultId: string;

  /**
   * 质监站整改意见
   */
  personComments: string;

  /**
   * 已整改图片
   */
  abarbeitungImg: number;

  /**
   * 未整改图片
   */
  noAbarbeitung: number;

  /**
   * 整改说明
   */
  abarbeitungComments: string;

  /**
   * 流程id
   */
  taskId: string | number;

}

export interface HazardousItemsComments {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 问题id
   */
  resultId: string;

  /**
   * 预警是否真实 1是 0否
   */
  commentStatus: string | number;

  /**
   * 质监站整改意见
   */
  personComments: string;

  /**
   * 已整改图片
   */
  abarbeitungImg: number;

  /**
   * 未整改图片
   */
  noAbarbeitung: number;

  /**
   * 整改说明
   */
  abarbeitungComments: string;

  /**
   * 流程id
   */
  taskId: string | number;

}


export interface HazardousItemsCommentsQuery extends PageQuery {

  /**
   * 日期范围参数
   */
  params?: any;
}

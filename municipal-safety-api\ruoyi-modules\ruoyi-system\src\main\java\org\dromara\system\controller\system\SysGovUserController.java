package org.dromara.system.controller.system;

import cn.dev33.satoken.annotation.SaIgnore;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.service.IGovUserImportService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 政府用户导入
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/gov-user")
public class SysGovUserController extends BaseController {

    private final IGovUserImportService govUserImportService;

    /**
     * 政府用户导入
     */
    @SaIgnore
    @PostMapping("/importData")
    public R<String> importData(MultipartFile file, boolean updateSupport) throws IOException {
        return govUserImportService.importGovUsers(file, updateSupport);
    }

    /**
     * 批量导入政府用户
     */
    @SaIgnore
    @PostMapping("/batchImport")
    public R<String> batchImport(@RequestBody List<Object> govUsers, boolean updateSupport) {
        return govUserImportService.batchImportGovUsers(govUsers, updateSupport);
    }

    /**
     * 从CSV导入政府用户
     */
    @SaIgnore
    @PostMapping("/importFromCsv")
    public R<String> importFromCsv(@RequestBody String csvContent, boolean updateSupport) {
        return govUserImportService.importFromCsv(csvContent, updateSupport);
    }

    /**
     * 下载政府用户导入模板
     */
    @SaIgnore
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        try {
            String fileName = "gov_user_template.xlsx";
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName);

            byte[] templateBytes = govUserImportService.downloadTemplate();
            response.getOutputStream().write(templateBytes);
            response.getOutputStream().flush();
        } catch (IOException e) {
            throw new RuntimeException("下载模板失败", e);
        }
    }

    /**
     * 智能分析导入政府用户
     */
    @SaIgnore
    @PostMapping("/smartImport")
    public R<String> smartImport(MultipartFile file, boolean updateSupport) throws IOException {
        // 这里可以实现智能分析逻辑，根据CSV数据自动识别字段
        // 暂时使用标准导入
        return importData(file, updateSupport);
    }
}

package org.dromara.person.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.person.domain.SysPerson;
import org.dromara.person.domain.vo.SysPersonVo;

/**
 * 人员基本信息Mapper接口
 *
 * <AUTHOR> zu <PERSON>
 * @date 2025-05-09
 */
public interface SysPersonMapper extends BaseMapperPlus<SysPerson, SysPersonVo> {

    Long selectPersonIdByIdCard(@Param("idCard") String idCard);

    SysPersonVo queryByIdCard(@Param("idCard") String idCard);
}

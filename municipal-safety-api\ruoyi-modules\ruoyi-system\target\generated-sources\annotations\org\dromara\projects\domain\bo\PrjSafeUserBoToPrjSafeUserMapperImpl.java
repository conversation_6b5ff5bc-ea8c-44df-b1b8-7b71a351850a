package org.dromara.projects.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjSafeUser;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjSafeUserBoToPrjSafeUserMapperImpl implements PrjSafeUserBoToPrjSafeUserMapper {

    @Override
    public PrjSafeUser convert(PrjSafeUserBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjSafeUser prjSafeUser = new PrjSafeUser();

        prjSafeUser.setSearchValue( arg0.getSearchValue() );
        prjSafeUser.setCreateDept( arg0.getCreateDept() );
        prjSafeUser.setCreateBy( arg0.getCreateBy() );
        prjSafeUser.setCreateTime( arg0.getCreateTime() );
        prjSafeUser.setUpdateBy( arg0.getUpdateBy() );
        prjSafeUser.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            prjSafeUser.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        prjSafeUser.setSaleUserId( arg0.getSaleUserId() );
        prjSafeUser.setMobile( arg0.getMobile() );
        prjSafeUser.setUserName( arg0.getUserName() );
        prjSafeUser.setIdCard( arg0.getIdCard() );
        prjSafeUser.setPositionType( arg0.getPositionType() );
        prjSafeUser.setOpenTaskId( arg0.getOpenTaskId() );

        return prjSafeUser;
    }

    @Override
    public PrjSafeUser convert(PrjSafeUserBo arg0, PrjSafeUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSaleUserId( arg0.getSaleUserId() );
        arg1.setMobile( arg0.getMobile() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setPositionType( arg0.getPositionType() );
        arg1.setOpenTaskId( arg0.getOpenTaskId() );

        return arg1;
    }
}

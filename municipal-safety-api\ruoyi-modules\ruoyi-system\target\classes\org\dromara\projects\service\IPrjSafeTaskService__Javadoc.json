{"doc": " 【项目管理】安拆任务Service接口\n\n <AUTHOR>\n @date 2025-08-08\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询【项目管理】安拆任务\n\n @param openTaskId 主键\n @return 【项目管理】安拆任务\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.projects.domain.bo.PrjSafeTaskBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询【项目管理】安拆任务列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 【项目管理】安拆任务分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.projects.domain.bo.PrjSafeTaskBo"], "doc": " 查询符合条件的【项目管理】安拆任务列表\n\n @param bo 查询条件\n @return 【项目管理】安拆任务列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.projects.domain.bo.PrjSafeTaskBo"], "doc": " 新增【项目管理】安拆任务\n\n @param bo 【项目管理】安拆任务\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.projects.domain.bo.PrjSafeTaskBo"], "doc": " 修改【项目管理】安拆任务\n\n @param bo 【项目管理】安拆任务\n @return 是否修改成功\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除【项目管理】安拆任务信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}, {"name": "getToProjectId", "paramTypes": ["java.lang.Long"], "doc": " 通过projectId转换安拆任务\n\n @param projectId\n @return\n"}], "constructors": []}
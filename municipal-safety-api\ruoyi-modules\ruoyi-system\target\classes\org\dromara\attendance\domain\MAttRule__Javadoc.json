{"doc": " 考勤规则对象 m_att_rule\n\n <AUTHOR>\n @date 2025-05-06\n", "fields": [{"name": "id", "doc": " 主键id\n"}, {"name": "ruleType", "doc": " 规则类型（0：模板规则，1：普通规则）\n"}, {"name": "projectId", "doc": " 项目ID\n"}, {"name": "personType", "doc": " 人员类型\n"}, {"name": "isAll", "doc": " 是否全部人员通用\n"}, {"name": "checkTime", "doc": " 设置打卡时间（支持多时段，如午休分段）。\n"}, {"name": "elasticTime", "doc": " 弹性时间：允许迟到/早退的宽限时间（如上班后30分钟内打卡不算迟到）\n"}, {"name": "warning", "doc": " 预警机制：根据对应的漏卡次数设置（黄色、橙色、红色）\n"}, {"name": "field<PERSON><PERSON><PERSON>", "doc": " 外勤打卡：0：关，1：开\n"}, {"name": "content", "doc": " 自定义内容\n"}], "enumConstants": [], "methods": [], "constructors": []}
package org.dromara.system.service;

import org.dromara.common.core.domain.R;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 政府用户导入服务接口
 *
 * <AUTHOR>
 */
public interface IGovUserImportService {

    /**
     * 导入政府用户
     *
     * @param file          Excel文件
     * @param updateSupport 是否支持更新已存在用户
     * @return 导入结果
     */
    R<String> importGovUsers(MultipartFile file, boolean updateSupport) throws IOException;

    /**
     * 批量导入政府用户
     *
     * @param govUsers      政府用户数据列表
     * @param updateSupport 是否支持更新已存在用户
     * @return 导入结果
     */
    R<String> batchImportGovUsers(List<Object> govUsers, boolean updateSupport);

    /**
     * 从CSV文件导入政府用户
     *
     * @param csvContent    CSV内容
     * @param updateSupport 是否支持更新已存在用户
     * @return 导入结果
     */
    R<String> importFromCsv(String csvContent, boolean updateSupport);

    /**
     * 下载导入模板
     *
     * @return Excel模板字节数组
     */
    byte[] downloadTemplate();
}

package org.dromara.facility.domain.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.facility.domain.LnSpraying;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 绿能喷淋设备视图对象 ln_spraying
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LnSpraying.class)
public class LnSprayingVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 继电器状态
     */
    @ExcelProperty(value = "继电器状态")
    private String relayStatus;

    /**
     * 工作模式 0-自动 1-手动
     */
    @ExcelProperty(value = "工作模式 0-自动 1-手动")
    private Long workMode;

    /**
     * 水位传感器 0-正常 1-下限位 2- 上限位 3-异常
     */
    @ExcelProperty(value = "水位传感器 0-正常 1-下限位 2- 上限位 3-异常")
    private Long waterSensor;

    /**
     * 设备状态
     */
    @ExcelProperty(value = "设备状态")
    private Long status;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String devNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

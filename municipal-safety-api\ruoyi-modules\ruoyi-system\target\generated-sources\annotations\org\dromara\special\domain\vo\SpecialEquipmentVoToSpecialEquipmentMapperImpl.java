package org.dromara.special.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.special.domain.SpecialEquipment;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class SpecialEquipmentVoToSpecialEquipmentMapperImpl implements SpecialEquipmentVoToSpecialEquipmentMapper {

    @Override
    public SpecialEquipment convert(SpecialEquipmentVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SpecialEquipment specialEquipment = new SpecialEquipment();

        specialEquipment.setEquipmentId( arg0.getEquipmentId() );
        specialEquipment.setConstructionPermitNum( arg0.getConstructionPermitNum() );
        specialEquipment.setRecordNumber( arg0.getRecordNumber() );
        specialEquipment.setEquipmentCategory( arg0.getEquipmentCategory() );
        specialEquipment.setEquipmentName( arg0.getEquipmentName() );
        specialEquipment.setModelSpec( arg0.getModelSpec() );
        specialEquipment.setManufacturer( arg0.getManufacturer() );
        specialEquipment.setManufacturerCode( arg0.getManufacturerCode() );
        specialEquipment.setFactoryNumber( arg0.getFactoryNumber() );
        specialEquipment.setFactoryDate( arg0.getFactoryDate() );
        specialEquipment.setProductionLicense( arg0.getProductionLicense() );
        specialEquipment.setUseYears( arg0.getUseYears() );
        specialEquipment.setPropertyOwner( arg0.getPropertyOwner() );
        specialEquipment.setPropertyOwnerCode( arg0.getPropertyOwnerCode() );
        specialEquipment.setPropertyOwnerAddress( arg0.getPropertyOwnerAddress() );
        specialEquipment.setLegalPerson( arg0.getLegalPerson() );
        specialEquipment.setLegalPersonLicense( arg0.getLegalPersonLicense() );
        specialEquipment.setContacts( arg0.getContacts() );
        specialEquipment.setContactsPhone( arg0.getContactsPhone() );
        specialEquipment.setPrice( arg0.getPrice() );
        specialEquipment.setPurchaseDate( arg0.getPurchaseDate() );
        specialEquipment.setAuthority( arg0.getAuthority() );
        specialEquipment.setAuthorityCode( arg0.getAuthorityCode() );
        specialEquipment.setLocationCity( arg0.getLocationCity() );
        specialEquipment.setLocationCounty( arg0.getLocationCounty() );
        specialEquipment.setLocationArea( arg0.getLocationArea() );
        specialEquipment.setTowerCraneWeight( arg0.getTowerCraneWeight() );
        specialEquipment.setWeightTorque( arg0.getWeightTorque() );
        specialEquipment.setWeightLength( arg0.getWeightLength() );
        specialEquipment.setWorkRange( arg0.getWorkRange() );
        specialEquipment.setWorkRangeWeight( arg0.getWorkRangeWeight() );
        specialEquipment.setImproveHeight( arg0.getImproveHeight() );
        specialEquipment.setLiftingHeight( arg0.getLiftingHeight() );
        specialEquipment.setStructureNumber( arg0.getStructureNumber() );
        specialEquipment.setMaxHeight( arg0.getMaxHeight() );
        specialEquipment.setStandardSectionSpecifications( arg0.getStandardSectionSpecifications() );
        specialEquipment.setStrengthenSection( arg0.getStrengthenSection() );
        specialEquipment.setStandardSection( arg0.getStandardSection() );
        specialEquipment.setElevatorPower( arg0.getElevatorPower() );
        specialEquipment.setImproveSpeed( arg0.getImproveSpeed() );
        specialEquipment.setSafetyModel( arg0.getSafetyModel() );
        specialEquipment.setConstructionElevatorSize( arg0.getConstructionElevatorSize() );
        specialEquipment.setMaxSpan( arg0.getMaxSpan() );
        specialEquipment.setIssuer( arg0.getIssuer() );
        specialEquipment.setCertificateNumber( arg0.getCertificateNumber() );
        specialEquipment.setIssueDate( arg0.getIssueDate() );
        specialEquipment.setUseRegistrationCertificate( arg0.getUseRegistrationCertificate() );
        specialEquipment.setProjectName( arg0.getProjectName() );
        specialEquipment.setProjectAddress( arg0.getProjectAddress() );
        specialEquipment.setProjectId( arg0.getProjectId() );
        specialEquipment.setItemId( arg0.getItemId() );
        specialEquipment.setUsageUnit( arg0.getUsageUnit() );
        specialEquipment.setMaintenanceUnit( arg0.getMaintenanceUnit() );
        specialEquipment.setInstallationUnit( arg0.getInstallationUnit() );
        specialEquipment.setInspectionUnit( arg0.getInspectionUnit() );
        specialEquipment.setProjectManager( arg0.getProjectManager() );
        specialEquipment.setInstallationDate( arg0.getInstallationDate() );
        specialEquipment.setInspectionDate( arg0.getInspectionDate() );
        specialEquipment.setEnterDate( arg0.getEnterDate() );
        specialEquipment.setExitDate( arg0.getExitDate() );
        specialEquipment.setLocation( arg0.getLocation() );
        specialEquipment.setSopId( arg0.getSopId() );
        specialEquipment.setRemarks( arg0.getRemarks() );
        specialEquipment.setDelFlag( arg0.getDelFlag() );
        specialEquipment.setDevNo( arg0.getDevNo() );
        specialEquipment.setProjectCraneNum( arg0.getProjectCraneNum() );
        specialEquipment.setCraneType( arg0.getCraneType() );

        return specialEquipment;
    }

    @Override
    public SpecialEquipment convert(SpecialEquipmentVo arg0, SpecialEquipment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setEquipmentId( arg0.getEquipmentId() );
        arg1.setConstructionPermitNum( arg0.getConstructionPermitNum() );
        arg1.setRecordNumber( arg0.getRecordNumber() );
        arg1.setEquipmentCategory( arg0.getEquipmentCategory() );
        arg1.setEquipmentName( arg0.getEquipmentName() );
        arg1.setModelSpec( arg0.getModelSpec() );
        arg1.setManufacturer( arg0.getManufacturer() );
        arg1.setManufacturerCode( arg0.getManufacturerCode() );
        arg1.setFactoryNumber( arg0.getFactoryNumber() );
        arg1.setFactoryDate( arg0.getFactoryDate() );
        arg1.setProductionLicense( arg0.getProductionLicense() );
        arg1.setUseYears( arg0.getUseYears() );
        arg1.setPropertyOwner( arg0.getPropertyOwner() );
        arg1.setPropertyOwnerCode( arg0.getPropertyOwnerCode() );
        arg1.setPropertyOwnerAddress( arg0.getPropertyOwnerAddress() );
        arg1.setLegalPerson( arg0.getLegalPerson() );
        arg1.setLegalPersonLicense( arg0.getLegalPersonLicense() );
        arg1.setContacts( arg0.getContacts() );
        arg1.setContactsPhone( arg0.getContactsPhone() );
        arg1.setPrice( arg0.getPrice() );
        arg1.setPurchaseDate( arg0.getPurchaseDate() );
        arg1.setAuthority( arg0.getAuthority() );
        arg1.setAuthorityCode( arg0.getAuthorityCode() );
        arg1.setLocationCity( arg0.getLocationCity() );
        arg1.setLocationCounty( arg0.getLocationCounty() );
        arg1.setLocationArea( arg0.getLocationArea() );
        arg1.setTowerCraneWeight( arg0.getTowerCraneWeight() );
        arg1.setWeightTorque( arg0.getWeightTorque() );
        arg1.setWeightLength( arg0.getWeightLength() );
        arg1.setWorkRange( arg0.getWorkRange() );
        arg1.setWorkRangeWeight( arg0.getWorkRangeWeight() );
        arg1.setImproveHeight( arg0.getImproveHeight() );
        arg1.setLiftingHeight( arg0.getLiftingHeight() );
        arg1.setStructureNumber( arg0.getStructureNumber() );
        arg1.setMaxHeight( arg0.getMaxHeight() );
        arg1.setStandardSectionSpecifications( arg0.getStandardSectionSpecifications() );
        arg1.setStrengthenSection( arg0.getStrengthenSection() );
        arg1.setStandardSection( arg0.getStandardSection() );
        arg1.setElevatorPower( arg0.getElevatorPower() );
        arg1.setImproveSpeed( arg0.getImproveSpeed() );
        arg1.setSafetyModel( arg0.getSafetyModel() );
        arg1.setConstructionElevatorSize( arg0.getConstructionElevatorSize() );
        arg1.setMaxSpan( arg0.getMaxSpan() );
        arg1.setIssuer( arg0.getIssuer() );
        arg1.setCertificateNumber( arg0.getCertificateNumber() );
        arg1.setIssueDate( arg0.getIssueDate() );
        arg1.setUseRegistrationCertificate( arg0.getUseRegistrationCertificate() );
        arg1.setProjectName( arg0.getProjectName() );
        arg1.setProjectAddress( arg0.getProjectAddress() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setUsageUnit( arg0.getUsageUnit() );
        arg1.setMaintenanceUnit( arg0.getMaintenanceUnit() );
        arg1.setInstallationUnit( arg0.getInstallationUnit() );
        arg1.setInspectionUnit( arg0.getInspectionUnit() );
        arg1.setProjectManager( arg0.getProjectManager() );
        arg1.setInstallationDate( arg0.getInstallationDate() );
        arg1.setInspectionDate( arg0.getInspectionDate() );
        arg1.setEnterDate( arg0.getEnterDate() );
        arg1.setExitDate( arg0.getExitDate() );
        arg1.setLocation( arg0.getLocation() );
        arg1.setSopId( arg0.getSopId() );
        arg1.setRemarks( arg0.getRemarks() );
        arg1.setDelFlag( arg0.getDelFlag() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setProjectCraneNum( arg0.getProjectCraneNum() );
        arg1.setCraneType( arg0.getCraneType() );

        return arg1;
    }
}

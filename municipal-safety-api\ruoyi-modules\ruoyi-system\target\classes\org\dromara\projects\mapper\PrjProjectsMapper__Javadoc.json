{"doc": " 项目录入Mapper接口\n\n <AUTHOR>\n @date 2025-05-07\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectVoList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 查询项目列表（带部门名称）\n"}, {"name": "selectVoListSq", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 查询项目列表（带部门名称）\n"}, {"name": "selectVoPage", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 分页查询项目列表（带部门名称）\n"}, {"name": "selectVoPageSq", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 分页查询项目列表（带部门名称）\n"}, {"name": "selectVoById", "paramTypes": ["java.lang.Long"], "doc": " 根据ID查询项目详情（带部门名称）\n"}], "constructors": []}
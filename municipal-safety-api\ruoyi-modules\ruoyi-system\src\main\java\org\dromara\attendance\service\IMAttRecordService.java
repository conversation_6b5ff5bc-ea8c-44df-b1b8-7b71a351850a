package org.dromara.attendance.service;

import org.dromara.attendance.domain.bo.MAttRecordBo;
import org.dromara.attendance.domain.vo.CalendarDayVo;
import org.dromara.attendance.domain.vo.MAttRecordVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 考勤记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
public interface IMAttRecordService {

    /**
     * 查询考勤记录
     *
     * @param id 主键
     * @return 考勤记录
     */
    MAttRecordVo queryById(Long id);

    /**
     * 分页查询考勤记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 考勤记录分页列表
     */
    TableDataInfo<MAttRecordVo> queryPageList(MAttRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的考勤记录列表
     *
     * @param bo 查询条件
     * @return 考勤记录列表
     */
    List<MAttRecordVo> queryList(MAttRecordBo bo);

    /**
     * 新增考勤记录
     *
     * @param bo 考勤记录
     * @return 是否新增成功
     */
    Boolean insertByBo(MAttRecordBo bo);

    /**
     * 修改考勤记录
     *
     * @param bo 考勤记录
     * @return 是否修改成功
     */
    Boolean updateByBo(MAttRecordBo bo);

    /**
     * 校验并批量删除考勤记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    MAttRecordVo selectMAttRuleByAttDate(String format, int i, Long personId);

    void insertMAttRecord(MAttRecordBo mAttRecordBo);

    List<MAttRecordVo> selectMAttRecordByPersonIdAndAttDate(Long personId, String attDate);

    List<MAttRecordVo> selectMAttRecordByPersonId(Long personId);

    List<CalendarDayVo> getAttendanceCalendar(Long personId, String month);
}

package org.dromara.projects.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.ai.domain.dto.AiHazAnalysisResultResetDTO;
import org.dromara.ai.domain.dto.AiHazAnalysisTasksDto;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.dromara.expert.domain.Expert;
import org.dromara.expert.mapper.ExpertMapper;
import org.dromara.plan.domain.PrjPatrolPlan;
import org.dromara.plan.mapper.PrjPatrolPlanMapper;
import org.dromara.projects.domain.bo.PrjHazardousItemsAppBo;
import org.dromara.projects.domain.bo.PrjHazardousItemsBo;
import org.dromara.projects.domain.vo.ItemsAiDetailVO;
import org.dromara.projects.domain.vo.PrjHazardousItemsVo;
import org.dromara.projects.service.IPrjHazardousItemsService;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.SysUserRole;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.mapper.SysRoleMapper;
import org.dromara.system.mapper.SysUserMapper;
import org.dromara.system.mapper.SysUserRoleMapper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * [项目管理] 列出项目内具体的危险性较大的分部分项工程
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/projects/prj_hazardous_items")
public class PrjHazardousItemsController extends BaseController {

    private final IPrjHazardousItemsService prjHazardousItemsService;
    private final SysUserRoleMapper sysUserRoleMapper;
    private final SysRoleMapper sysRoleMapper;
    private final PrjPatrolPlanMapper patrolPlanMapper;
    private final ExpertMapper expertMapper;
    private final SysUserMapper sysUserMapper;


    /**
     * 查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表
     */
    @GetMapping("/list")
    public TableDataInfo<PrjHazardousItemsVo> list(PrjHazardousItemsBo bo, PageQuery pageQuery) {
        return prjHazardousItemsService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表
     */
    @SaCheckPermission("projects:prj_hazardous_items:list")
    @GetMapping("/alist")
    public TableDataInfo<PrjHazardousItemsVo> adminList(PrjHazardousItemsBo bo, PageQuery pageQuery) {
        return prjHazardousItemsService.queryAdminPageList(bo, pageQuery);
    }

    /**
     * todo APP查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表
     */
    @GetMapping("/app/list")
    public R<IPage<PrjHazardousItemsVo>> appList(PrjHazardousItemsAppBo bo) {
        return R.ok(prjHazardousItemsService.queryAppList(bo));
    }

    public PrjHazardousItemsAppBo role(PrjHazardousItemsAppBo bo) {
        // 获取用户id
        Long userId = LoginHelper.getUserId();
        SysUser sysUser = sysUserMapper.selectById(userId);
        // 查询关联角色
        SysUserRole sysUserRole = sysUserRoleMapper.selectOne(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        SysRoleVo sysRoleVo = sysRoleMapper.selectRoleById(sysUserRole.getRoleId());
        switch (sysRoleVo.getRoleKey()) {
            case "expert": {
                Expert expert = expertMapper.selectOne(new LambdaQueryWrapper<Expert>().eq(Expert::getIdCard, sysUser.getUserName()));
                List<PrjPatrolPlan> prjPatrolPlans = patrolPlanMapper.selectList(new LambdaQueryWrapper<PrjPatrolPlan>()
                    .like(PrjPatrolPlan::getExpertIds, expert.getExpertId())
                    .le(PrjPatrolPlan::getBeginTime, DateUtil.date())
                    .ge(PrjPatrolPlan::getEndTime, DateUtil.date()));
                List<String> projectIdsList = prjPatrolPlans.stream()
                    .map(PrjPatrolPlan::getProjectIds) // 提取 projectIds 字段
                    .filter(StringUtils::isNotEmpty)   // 非空校验
                    .flatMap(projectIds -> Arrays.stream(projectIds.split(","))) // 按逗号分割并展平为流
                    .distinct()                        // 去重
                    .collect(Collectors.toList());
                if (projectIdsList.isEmpty()) {
                    bo.setProjectType("0");
                } else {
                    bo.setProjectIdList(projectIdsList);
                    return bo;
                }
            }
            default:
                return bo;
        }
    }

    @GetMapping("/app/listAll")
    public R<List<PrjHazardousItemsVo>> appListAll(PrjHazardousItemsBo bo) {
        return R.ok(prjHazardousItemsService.queryList(bo));
    }

    @GetMapping("/app/{itemId}")
    public R<PrjHazardousItemsVo> getInfoApp(@NotNull(message = "主键不能为空")
                                             @PathVariable Long itemId) {
        return R.ok(prjHazardousItemsService.queryByIdApp(itemId));
    }

    /**
     * 导出[项目管理] 列出项目内具体的危险性较大的分部分项工程列表
     */
    @SaCheckPermission("projects:prj_hazardous_items:export")
    @Log(title = "[项目管理] 列出项目内具体的危险性较大的分部分项工程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PrjHazardousItemsBo bo, HttpServletResponse response) {
        List<PrjHazardousItemsVo> list = prjHazardousItemsService.queryList(bo);
        ExcelUtil.exportExcel(list, "[项目管理] 列出项目内具体的危险性较大的分部分项工程", PrjHazardousItemsVo.class, response);
    }

    /**
     * 获取[项目管理] 列出项目内具体的危险性较大的分部分项工程详细信息
     *
     * @param itemId 主键
     */
    @GetMapping("/{itemId}")
    public R<PrjHazardousItemsVo> getInfo(@NotNull(message = "主键不能为空")
                                          @PathVariable Long itemId) {
        return R.ok(prjHazardousItemsService.queryById(itemId));
    }

    /**
     * 获取[项目管理] 列出项目内具体的危险性较大的分部分项工程详细信息
     *
     * @param itemId 主键
     */
    @RepeatSubmit(interval = 1000)
    @GetMapping("/detail/{itemId}")
    public R<PrjHazardousItemsVo> getInfoDetail(@NotNull(message = "主键不能为空")
                                                @PathVariable Long itemId) {
        return R.ok(prjHazardousItemsService.queryDetailById(itemId));
    }

    /**
     * 新增[项目管理] 列出项目内具体的危险性较大的分部分项工程
     */
    @SaCheckPermission("projects:prj_hazardous_items:add")
    @Log(title = "[项目管理] 列出项目内具体的危险性较大的分部分项工程", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<String> add(@Validated(AddGroup.class) @RequestBody PrjHazardousItemsBo bo) {
        return R.ok("", prjHazardousItemsService.insertByBo(bo));
    }

    /**
     * 修改[项目管理] 列出项目内具体的危险性较大的分部分项工程
     */
    @SaCheckPermission("projects:prj_hazardous_items:edit")
    @Log(title = "[项目管理] 列出项目内具体的危险性较大的分部分项工程", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PrjHazardousItemsBo bo) {
        return toAjax(prjHazardousItemsService.updateByBo(bo));
    }

    /**
     * 删除[项目管理] 列出项目内具体的危险性较大的分部分项工程
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("projects:prj_hazardous_items:remove")
    @Log(title = "[项目管理] 列出项目内具体的危险性较大的分部分项工程", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(prjHazardousItemsService.deleteWithValidByIds(List.of(itemIds), true));
    }

    /**
     * ai隐患获取详情信息
     *
     * @param id
     * @return
     */
    @GetMapping("/aiDangerDetail/{id}")
    public R<ItemsAiDetailVO> aiDetailVOR(@PathVariable Long id) {
        return R.ok(prjHazardousItemsService.getAiHazAnalysisTaskDetail(id));
    }

    @GetMapping("/getItemList/{projectId}")
    public R<List<PrjHazardousItemsVo>> getItemList(@PathVariable Long projectId) {
        PrjHazardousItemsBo prjHazardousItemsBo = new PrjHazardousItemsBo();
        prjHazardousItemsBo.setProjectId(projectId);
        return R.ok(prjHazardousItemsService.queryList(prjHazardousItemsBo));
    }

    /**
     * 隐患清单审核
     *
     * @param dto
     * @return
     */
    @Log(title = "[项目管理] 隐患清单审核", businessType = BusinessType.GRANT)
    @PostMapping("/saveAiTaskAndResult")
    @SaCheckPermission("projects:prj_hazardous_items:detail_condition")
    @RepeatSubmit(interval = 2000)
    public R<Boolean> saveAiTaskAndResult(@RequestBody AiHazAnalysisResultResetDTO dto) {
        return R.ok(prjHazardousItemsService.saveAiTaskAndResult(dto));
    }
}

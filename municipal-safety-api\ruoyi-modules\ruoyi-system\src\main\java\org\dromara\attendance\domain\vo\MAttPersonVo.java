package org.dromara.attendance.domain.vo;

import org.dromara.attendance.domain.MAttPerson;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * attPerson视图对象 m_att_person
 *
 * <AUTHOR> Li
 * @date 2025-06-10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MAttPerson.class)
public class MAttPersonVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    @ExcelProperty(value = "设备id")
    private Long id;

    /**
     * 人员id
     */
    @ExcelProperty(value = "人员id")
    private Long personId;

    /**
     * 设备id
     */
    @ExcelProperty(value = "设备id")
    private Long snId;


}

package org.dromara.facility.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.facility.domain.vo.LnHighFormworkRealVo;
import org.dromara.facility.domain.bo.LnHighFormworkRealBo;
import org.dromara.facility.service.ILnHighFormworkRealService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 绿能高支模实时数据
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/lnHighFormworkReal")
public class LnHighFormworkRealController extends BaseController {

    private final ILnHighFormworkRealService lnHighFormworkRealService;

    /**
     * 查询绿能高支模实时数据列表
     */
    @GetMapping("/list")
    public TableDataInfo<LnHighFormworkRealVo> list(LnHighFormworkRealBo bo, PageQuery pageQuery) {
        return lnHighFormworkRealService.queryPageList(bo, pageQuery);
    }
}

import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { AttSnVO, AttSnForm, AttSnQuery } from '@/api/attendance/attSn/types';

/**
 * 查询考勤设备列表
 * @param query
 * @returns {*}
 */

export const listAttSn = (query?: AttSnQuery): AxiosPromise<AttSnVO[]> => {
  return request({
    url: '/attendance/attSn/selectAll',
    method: 'get',
    params: query
  });
};

/**
 * 查询考勤设备详细
 * @param snId
 */
export const getAttSn = (snId: string | number): AxiosPromise<AttSnVO> => {
  return request({
    url: '/attendance/attSn/' + snId,
    method: 'get'
  });
};

/**
 * 新增考勤设备
 * @param data
 */
export const addAttSn = (data: AttSnForm) => {
  return request({
    url: '/attendance/attSn',
    method: 'post',
    data: data
  });
};

/**
 * 修改考勤设备
 * @param data
 */
export const updateAttSn = (data: AttSnForm) => {
  return request({
    url: '/attendance/attSn',
    method: 'put',
    data: data
  });
};

/**
 * 删除考勤设备
 * @param snId
 */
export const delAttSn = (snId: string | number | Array<string | number>) => {
  return request({
    url: '/attendance/attSn/' + snId,
    method: 'delete'
  });
};

package org.dromara.projects.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.projects.domain.PrjExpertReviews;

import java.util.Date;

/**
 * [项目管理] 记录专项施工方案专家论证会议，包含利害关系预警业务对象 prj_expert_reviews
 *
 * <AUTHOR> Li
 * @date 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjExpertReviews.class, reverseConvertGenerate = false)
public class PrjExpertReviewsBo extends BaseEntity {

    /**
     * 专家论证ID
     */
    @NotNull(message = "专家论证ID不能为空", groups = { EditGroup.class })
    private Long reviewId;

    /**
     * 施工方案ID (逻辑外键至 prj_construction_plans.plan_id)
     */
    @NotNull(message = "施工方案ID (逻辑外键至 prj_construction_plans.plan_id)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long planId;

    /**
     * 论证会议日期
     */
    @NotNull(message = "论证会议日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date reviewDate;

    /**
     * 会议地点
     */
    private String meetingLocation;

    /**
     * 论证结论 (通过, 修改后通过, 不通过)
     */
    @NotBlank(message = "论证结论 (通过, 修改后通过, 不通过)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String conclusion;

    /**
     * 专家意见摘要/修改要求
     */
    private String expertOpinionSummary;

    /**
     * 专家利害关系预警信息 (对应附件四.(二).1)
     */
    private String conflictOfInterestWarning;

    /**
     * 论证报告文档ID (逻辑外键至 sys_documents.document_id)
     */
    private Long reportDocumentId;

    /**
     * 会议组织者ID (逻辑外键至 sys_users.user_id)
     */
    private Long convenorUserId;
}

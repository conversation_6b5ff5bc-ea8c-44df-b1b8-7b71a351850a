package org.dromara.attendance.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.attendance.domain.MAttRecord;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class MAttRecordVoToMAttRecordMapperImpl implements MAttRecordVoToMAttRecordMapper {

    @Override
    public MAttRecord convert(MAttRecordVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MAttRecord mAttRecord = new MAttRecord();

        mAttRecord.setId( arg0.getId() );
        mAttRecord.setRuleId( arg0.getRuleId() );
        mAttRecord.setPersonId( arg0.getPersonId() );
        mAttRecord.setPersonType( arg0.getPersonType() );
        mAttRecord.setRealName( arg0.getRealName() );
        mAttRecord.setIdNumber( arg0.getIdNumber() );
        mAttRecord.setRealTimeFace( arg0.getRealTimeFace() );
        mAttRecord.setSn( arg0.getSn() );
        mAttRecord.setSource( arg0.getSource() );
        mAttRecord.setContent( arg0.getContent() );
        mAttRecord.setAttTime( arg0.getAttTime() );
        mAttRecord.setAttDate( arg0.getAttDate() );
        mAttRecord.setAttResult( arg0.getAttResult() );
        mAttRecord.setWhichTime( arg0.getWhichTime() );

        return mAttRecord;
    }

    @Override
    public MAttRecord convert(MAttRecordVo arg0, MAttRecord arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setRuleId( arg0.getRuleId() );
        arg1.setPersonId( arg0.getPersonId() );
        arg1.setPersonType( arg0.getPersonType() );
        arg1.setRealName( arg0.getRealName() );
        arg1.setIdNumber( arg0.getIdNumber() );
        arg1.setRealTimeFace( arg0.getRealTimeFace() );
        arg1.setSn( arg0.getSn() );
        arg1.setSource( arg0.getSource() );
        arg1.setContent( arg0.getContent() );
        arg1.setAttTime( arg0.getAttTime() );
        arg1.setAttDate( arg0.getAttDate() );
        arg1.setAttResult( arg0.getAttResult() );
        arg1.setWhichTime( arg0.getWhichTime() );

        return arg1;
    }
}

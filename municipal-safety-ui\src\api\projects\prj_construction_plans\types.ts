export interface ConstructionPlansVO {
  /**
   * 方案ID (主键)
   */
  planId: string | number;

  /**
   * 危大工程项ID
   */
  itemId: string | number;

  /**
   * 专项施工方案名称
   */
  planName: string;

  /**
   * 方案版本号
   */
  planVersion: string;

  /**
   * 专项施工方案电子版文件ID
   */
  planDocumentId: string | number;

  /**
   * 专项施工方案审批表文件ID
   */
  approvalFormDocId: string | number;

  /**
   * AI校验状态
   */
  reviewStatus: string;

  /**
   * AI对比方案缺陷预警提示内容
   */
  aiDefectWarningDetails: string;

}

export interface ConstructionPlansForm extends BaseEntity {
  /**
   * 方案ID (主键)
   */
  planId?: string | number;

  /**
   * 危大工程项ID
   */
  itemId?: string | number;

  /**
   * 专项施工方案名称
   */
  planName?: string;

  /**
   * 方案版本号
   */
  planVersion?: string;

  /**
   * 专项施工方案电子版文件ID
   */
  planDocumentId?: string | number;

  /**
   * 专项施工方案审批表文件ID
   */
  approvalFormDocId?: string | number;

  /**
   * AI校验状态
   */
  reviewStatus?: string;

  /**
   * AI对比方案缺陷预警提示内容
   */
  aiDefectWarningDetails?: string;

}

export interface ConstructionPlansQuery extends PageQuery {

  itemId?: string | number;
  /**
   * 专项施工方案名称
   */
  planName?: string;

  /**
   * 方案版本号
   */
  planVersion?: string;

  /**
   * AI校验状态
   */
  reviewStatus?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

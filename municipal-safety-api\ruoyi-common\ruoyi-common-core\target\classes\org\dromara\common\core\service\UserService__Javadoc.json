{"doc": " 通用 用户服务\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectUserNameById", "paramTypes": ["java.lang.Long"], "doc": " 通过用户ID查询用户账户\n\n @param userId 用户ID\n @return 用户账户\n"}, {"name": "selectNicknameById", "paramTypes": ["java.lang.Long"], "doc": " 通过用户ID查询用户账户\n\n @param userId 用户ID\n @return 用户名称\n"}, {"name": "selectNicknameByIds", "paramTypes": ["java.lang.String"], "doc": " 通过用户ID查询用户账户\n\n @param userIds 用户ID 多个用逗号隔开\n @return 用户名称\n"}, {"name": "selectPhonenumberById", "paramTypes": ["java.lang.Long"], "doc": " 通过用户ID查询用户手机号\n\n @param userId 用户id\n @return 用户手机号\n"}, {"name": "selectEmailById", "paramTypes": ["java.lang.Long"], "doc": " 通过用户ID查询用户邮箱\n\n @param userId 用户id\n @return 用户邮箱\n"}, {"name": "selectListByIds", "paramTypes": ["java.util.List"], "doc": " 通过用户ID查询用户列表\n\n @param userIds 用户ids\n @return 用户列表\n"}, {"name": "selectUserIdsByRoleIds", "paramTypes": ["java.util.List"], "doc": " 通过角色ID查询用户ID\n\n @param roleIds 角色ids\n @return 用户ids\n"}, {"name": "selectUsersByRoleIds", "paramTypes": ["java.util.List"], "doc": " 通过角色ID查询用户\n\n @param roleIds 角色ids\n @return 用户\n"}, {"name": "selectUsersByDeptIds", "paramTypes": ["java.util.List"], "doc": " 通过部门ID查询用户\n\n @param deptIds 部门ids\n @return 用户\n"}, {"name": "selectUsersByPostIds", "paramTypes": ["java.util.List"], "doc": " 通过岗位ID查询用户\n\n @param postIds 岗位ids\n @return 用户\n"}, {"name": "selectDeptIdByUserId", "paramTypes": ["java.lang.Long"], "doc": " 通过用户id获取部门id\n @param id\n @return\n"}], "constructors": []}
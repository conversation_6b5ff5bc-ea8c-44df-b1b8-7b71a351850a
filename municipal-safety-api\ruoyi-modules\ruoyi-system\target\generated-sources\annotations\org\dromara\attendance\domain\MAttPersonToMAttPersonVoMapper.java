package org.dromara.attendance.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.attendance.domain.bo.MAttPersonBoToMAttPersonMapper;
import org.dromara.attendance.domain.vo.MAttPersonVo;
import org.dromara.attendance.domain.vo.MAttPersonVoToMAttPersonMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {MAttPersonVoToMAttPersonMapper.class,MAttPersonBoToMAttPersonMapper.class},
    imports = {}
)
public interface MAttPersonToMAttPersonVoMapper extends BaseMapper<MAttPerson, MAttPersonVo> {
}

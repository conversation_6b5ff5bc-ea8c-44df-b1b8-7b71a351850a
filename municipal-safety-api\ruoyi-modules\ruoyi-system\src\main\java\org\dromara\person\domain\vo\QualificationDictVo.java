package org.dromara.person.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.person.domain.QualificationDict;

import java.io.Serial;
import java.io.Serializable;



/**
 * 人员证书属性类型视图对象 t_qualification_dict
 *
 * <AUTHOR> Li
 * @date 2025-05-10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = QualificationDict.class)
public class QualificationDictVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 父级id
     */
    @ExcelProperty(value = "父级id")
    private Long preId;
}

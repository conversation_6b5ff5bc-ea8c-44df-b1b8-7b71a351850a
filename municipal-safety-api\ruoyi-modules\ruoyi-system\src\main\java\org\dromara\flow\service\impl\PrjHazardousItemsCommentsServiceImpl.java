package org.dromara.flow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.apache.poi.xssf.model.Comments;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.flow.domain.PrjHazardousItemsComments;
import org.dromara.flow.domain.PrjHazardousItemsFile;
import org.dromara.flow.domain.PrjHazardousItemsSpecialWarning;
import org.dromara.flow.domain.bo.PrjHazardousItemsCommentsBo;
import org.dromara.flow.domain.vo.PrjHazardousItemsCommentsVo;
import org.dromara.flow.mapper.PrjHazardousItemsCommentsMapper;
import org.dromara.flow.mapper.PrjHazardousItemsFileMapper;
import org.dromara.flow.mapper.PrjHazardousItemsSpecialWarningMapper;
import org.dromara.flow.service.IPrjHazardousItemsCommentsService;
import org.dromara.warm.flow.orm.entity.FlowInstance;
import org.dromara.warm.flow.orm.mapper.FlowInstanceMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 质监站隐患清单整改Service业务层处理
 *
 * <AUTHOR> zu da
 * @date 2025-05-28
 */
@RequiredArgsConstructor
@Service
public class PrjHazardousItemsCommentsServiceImpl implements IPrjHazardousItemsCommentsService {

    private final PrjHazardousItemsCommentsMapper baseMapper;
    private final PrjHazardousItemsFileMapper prjHazardousItemsFileMapper;
    private final FlowInstanceMapper flowInstanceMapper;
    private final PrjHazardousItemsSpecialWarningMapper prjHazardousItemsSpecialWarningMapper;

    /**
     * 修改质监站隐患清单整改
     *
     * @param bo 质监站隐患清单整改
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PrjHazardousItemsCommentsBo bo) {
        PrjHazardousItemsComments update = MapstructUtils.convert(bo, PrjHazardousItemsComments.class);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public boolean updateById(PrjHazardousItemsComments comments) {
        return baseMapper.updateById(comments) > 0;
    }

    @Override
    public String save(PrjHazardousItemsComments comments) {

        comments.setTaskId(String.valueOf(IdUtil.getSnowflakeNextId()));
        //附件列表存储
        List<PrjHazardousItemsFile> elseFile = comments.getElseFile();

        if (CollectionUtil.isNotEmpty(elseFile)) {
            for (PrjHazardousItemsFile file : elseFile) {
                file.setTaskId(comments.getTaskId());
            }
            prjHazardousItemsFileMapper.insertBatch(elseFile);
        }

        baseMapper.insert(comments);

        return comments.getTaskId();
    }

    @Override
    public PrjHazardousItemsCommentsVo getDetail(String taskId) {

        LambdaQueryWrapper<PrjHazardousItemsComments> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PrjHazardousItemsComments::getTaskId, taskId);

        PrjHazardousItemsCommentsVo itemsCommentsVo = baseMapper.selectVoOne(wrapper);

        if (itemsCommentsVo != null) {
            Date date = itemsCommentsVo.getCreateTime();
            DateTime future;
            if (itemsCommentsVo.getTimeType() == 1) {
                future = DateUtil.offsetHour(date, itemsCommentsVo.getTimeLimit().intValue());
            } else {
                future = DateUtil.offsetDay(date, itemsCommentsVo.getTimeLimit().intValue());
            }
            long time = future.getTime();
            itemsCommentsVo.setFutureTime(time);

            //查询附件列表
            List<PrjHazardousItemsFile> fileList = prjHazardousItemsFileMapper.selectList(
                Wrappers.<PrjHazardousItemsFile>lambdaQuery()
                    .eq(PrjHazardousItemsFile::getTaskId, taskId));

            itemsCommentsVo.setElseFile(fileList);
        }


        //查特殊预警
        LambdaQueryWrapper<FlowInstance> flowWrapper = Wrappers.lambdaQuery();
        flowWrapper.eq(FlowInstance::getBusinessId, taskId)
            .select(FlowInstance::getId);

        FlowInstance instance = flowInstanceMapper.selectOne(flowWrapper);

        if (instance != null) {
            LambdaQueryWrapper<PrjHazardousItemsSpecialWarning> warningWrapper = Wrappers.lambdaQuery();
            warningWrapper.eq(PrjHazardousItemsSpecialWarning::getTaskId,instance.getId());

            PrjHazardousItemsSpecialWarning itemsSpecialWarning = prjHazardousItemsSpecialWarningMapper.selectOne(warningWrapper);
            itemsCommentsVo.setWarning(itemsSpecialWarning);
        }

        return itemsCommentsVo;
    }

    @Override
    public Boolean consturctionUpBackFiles(PrjHazardousItemsComments comments) {

        PrjHazardousItemsComments itemsComments = new PrjHazardousItemsComments();
        itemsComments.setId(comments.getId());
        itemsComments.setCorrectionsBackFile(comments.getCorrectionsBackFile());
        itemsComments.setSuspensionBackFile(comments.getSuspensionBackFile());
        itemsComments.setPenaltyBackFile(comments.getPenaltyBackFile());

        return this.baseMapper.updateById(itemsComments) > 0;
    }
}

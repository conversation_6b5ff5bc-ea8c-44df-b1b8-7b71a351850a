package org.dromara.ai.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.ai.domain.bo.AiHazAnalysisTasksBo;
import org.dromara.ai.domain.dto.AiAnalysisResultDto;
import org.dromara.ai.domain.dto.AiHazAnalysisTasksDto;
import org.dromara.ai.domain.dto.HazRecordUploadDto;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksVo;
import org.dromara.ai.enums.AiHazAnalysisTasksStatus;
import org.dromara.ai.service.IAiHazAnalysisTasksService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.SysUserRole;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.mapper.SysRoleMapper;
import org.dromara.system.mapper.SysUserMapper;
import org.dromara.system.mapper.SysUserRoleMapper;
import org.dromara.system.service.ISysOssService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 智能隐患分析任务
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/ai/ai_haz_analysis_tasks")
public class AppAiHazAnalysisTasksController extends BaseController {

    private final IAiHazAnalysisTasksService aiHazAnalysisTasksService;
    private final ISysOssService sysOssService;
    private final SysUserMapper sysUserMapper;
    private final SysUserRoleMapper sysUserRoleMapper;
    private final SysRoleMapper sysRoleMapper;

    /**
     * 查询智能隐患分析任务列表
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks:list")
    @GetMapping("/list")
    public TableDataInfo<AiHazAnalysisTasksVo> list(AiHazAnalysisTasksBo bo, PageQuery pageQuery) {
        return aiHazAnalysisTasksService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出智能隐患分析任务列表
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks:export")
    @Log(title = "智能隐患分析任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiHazAnalysisTasksBo bo, HttpServletResponse response) {
        List<AiHazAnalysisTasksVo> list = aiHazAnalysisTasksService.queryList(bo);
        ExcelUtil.exportExcel(list, "智能隐患分析任务", AiHazAnalysisTasksVo.class, response);
    }

    /**
     * 获取智能隐患分析任务详细信息
     *
     * @param taskId 主键
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks:query")
    @GetMapping("/{taskId}")
    public R<AiHazAnalysisTasksVo> getInfo(@NotNull(message = "主键不能为空")
                                               @PathVariable Long taskId) {
        return R.ok(aiHazAnalysisTasksService.queryById(taskId));
    }

    /**
     * 新增智能隐患分析任务
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks:add")
    @Log(title = "智能隐患分析任务", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiHazAnalysisTasksBo bo) {
        return toAjax(aiHazAnalysisTasksService.insertByBo(bo));
    }

    /**
     * 修改智能隐患分析任务
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks:edit")
    @Log(title = "智能隐患分析任务", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiHazAnalysisTasksBo bo) {
        return toAjax(aiHazAnalysisTasksService.updateByBo(bo));
    }

    /**
     * 删除智能隐患分析任务
     *
     * @param taskIds 主键串
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks:remove")
    @Log(title = "智能隐患分析任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] taskIds) {
        return toAjax(aiHazAnalysisTasksService.deleteWithValidByIds(List.of(taskIds), true));
    }

    // -------------- APP端相关接口 ---------------

    /**
     * APP查询隐患列表
     * 支持通过项目名称和工程名称搜索
     */
    @GetMapping("/app/list")
    public TableDataInfo<AiHazAnalysisTasksVo> appList(AiHazAnalysisTasksDto dto, PageQuery pageQuery) {
        return aiHazAnalysisTasksService.queryNewPageList(dto, pageQuery);
    }

    public AiHazAnalysisTasksDto role(AiHazAnalysisTasksDto dto){
        // 获取用户id
        Long userId = LoginHelper.getUserId();
        // 查询关联角色
        SysUserRole sysUserRole = sysUserRoleMapper.selectOne(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        SysRoleVo sysRoleVo = sysRoleMapper.selectRoleById(sysUserRole.getRoleId());
        switch (sysRoleVo.getRoleKey()) {
            case "expert": {
                ArrayList<String> createPeopleList = new ArrayList<>();
                createPeopleList.add(String.valueOf(userId));
                dto.setCreatePeopleList(createPeopleList);
                return dto;
            }
            default:
                return dto;
        }
    }

    /**
     * 根据项目ID查询隐患列表
     * 从工程管理页面点击查看该项目的隐患列表
     */
    @GetMapping("/app/list/{itemId}")
    public TableDataInfo<AiHazAnalysisTasksVo> getProjectHazards(@PathVariable Long itemId, PageQuery pageQuery) {
        AiHazAnalysisTasksDto dto = new AiHazAnalysisTasksDto();
        dto.setItemId(itemId);
        return aiHazAnalysisTasksService.queryNewPageList(dto, pageQuery);
    }

    /**
     * 获取APP端智能隐患分析任务详细信息
     *
     * @param taskId 主键
     */
    @GetMapping("/app/new/{taskId}")
    public R<AiHazAnalysisTasksVo> getAppInfo(@NotNull(message = "主键不能为空")
                                              @PathVariable Long taskId) {
        return R.ok(aiHazAnalysisTasksService.queryByIdNew(taskId));
    }

    /**
     * 专家上传隐患记录和图片URL
     *
     * @param dto 隐患记录信息，包含图片URL
     * @return 上传结果
     */
    @Log(title = "上传隐患记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/upload")
    public R<AiHazAnalysisTasksVo> uploadHazRecord(@Validated @RequestBody HazRecordUploadDto dto) {
        try {
            // 1. 创建隐患分析任务
            AiHazAnalysisTasksBo bo = new AiHazAnalysisTasksBo();
            bo.setProjectId(dto.getProjectId());
            bo.setItemId(dto.getItemId());
            bo.setSourceType("APP");
            bo.setExpertUserId(LoginHelper.getUserId());
            bo.setUploadTime(new Date());
            bo.setPhotoDocumentId(dto.getPhotoDocumentId());
            bo.setGpsLocation(dto.getGpsLocation());
            bo.setLocationDescription(dto.getLocationDescription());
            bo.setStatus(AiHazAnalysisTasksStatus.PENDING_AI_ANALYSIS.getType()); // 设置初始状态为"待分析"

            // 2. 插入数据库
            boolean success = aiHazAnalysisTasksService.insertByBo(bo);
            if (!success) {
                return R.fail("创建隐患分析任务失败");
            }

            // 3. 调用AI分析服务
            AiHazAnalysisTasksVo taskVo = aiHazAnalysisTasksService.submitToAiAnalysis(bo.getTaskId(), dto.getPhotoDocumentUrl(), dto.getItemId());

            return R.ok(taskVo);
        } catch (Exception e) {
            return R.fail("上传处理失败: " + e.getMessage());
        }
    }

    /**
     * AI回调接口 - 接收外部AI系统分析结果
     *
     * @param aiResult AI分析结果
     */
    @SaIgnore
    @PostMapping("/callback")
    public R<Void> aiCallback(@RequestBody AiAnalysisResultDto aiResult) {
        // 处理AI分析结果
        return toAjax(aiHazAnalysisTasksService.processAiAnalysisResult(aiResult));
    }
}

package org.dromara.person.domain.bo;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.person.domain.QualificationDict;

/**
 * 人员证书属性类型业务对象 t_qualification_dict
 *
 * <AUTHOR> Li
 * @date 2025-05-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = QualificationDict.class, reverseConvertGenerate = false)
public class QualificationDictBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 父级id
     */
    private Long preId;
}

package org.dromara.ai.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.ai.domain.AiHazAnalysisTasksResult;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 隐患AI分析结果业务对象 ai_haz_analysis_tasks_result
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiHazAnalysisTasksResult.class, reverseConvertGenerate = false)
public class AiHazAnalysisTasksResultBo extends BaseEntity {

    /**
     * 隐患结果主键
     */
    @NotNull(message = "隐患结果主键不能为空", groups = {EditGroup.class})
    private Long resultId;

    /**
     * 隐患描述
     */
    private Long taskId;

    /**
     * 隐患描述
     */
    @NotBlank(message = "隐患描述不能为空", groups = {AddGroup.class, EditGroup.class})
    private String violation;

    /**
     * 涉及条款
     */
    @NotBlank(message = "涉及条款不能为空", groups = {AddGroup.class, EditGroup.class})
    private String regulation;

    /**
     * 位置坐标
     */
    @NotBlank(message = "位置坐标不能为空", groups = {AddGroup.class, EditGroup.class})
    private String coordinate;

    /**
     * 危险评估等级（2：重大隐患，1：一般隐患）
     */
    @NotBlank(message = "危险评估等级（2：重大隐患，1：一般隐患）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String level;

    /**
     * 整改措施建议
     */
    @NotBlank(message = "整改措施建议不能为空", groups = {AddGroup.class, EditGroup.class})
    private String measure;


}

package org.dromara.projects.domain.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/13 16:27
 * @Description TODO
 * @Version 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ItemsAiListVO {

    /**
     * 照片上传时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date uploadTime;

    /**
     * 分析任务ID
     */
    private Long taskId;

    /**
     * AI分析后返回的带标注的照片文档ID
     */
    private Long aiPhotoDocumentId;

    /**
     * 拍照位置文字描述
     */
    private String locationDescription;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 复检状态（PENDING_RECHECK[待复检]、FINISH_RECHECK[复检完成]）
     */
    private String recheckStatus;
}

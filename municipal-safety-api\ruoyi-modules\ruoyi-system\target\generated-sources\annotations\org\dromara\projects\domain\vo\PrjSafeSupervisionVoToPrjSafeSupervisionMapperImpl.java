package org.dromara.projects.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjSafeSupervision;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjSafeSupervisionVoToPrjSafeSupervisionMapperImpl implements PrjSafeSupervisionVoToPrjSafeSupervisionMapper {

    @Override
    public PrjSafeSupervision convert(PrjSafeSupervisionVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjSafeSupervision prjSafeSupervision = new PrjSafeSupervision();

        prjSafeSupervision.setSupervisionId( arg0.getSupervisionId() );
        prjSafeSupervision.setUserName( arg0.getUserName() );
        prjSafeSupervision.setUserPositionName( arg0.getUserPositionName() );
        prjSafeSupervision.setFace( arg0.getFace() );
        prjSafeSupervision.setCertificate( arg0.getCertificate() );
        prjSafeSupervision.setOpenTaskId( arg0.getOpenTaskId() );

        return prjSafeSupervision;
    }

    @Override
    public PrjSafeSupervision convert(PrjSafeSupervisionVo arg0, PrjSafeSupervision arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSupervisionId( arg0.getSupervisionId() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setUserPositionName( arg0.getUserPositionName() );
        arg1.setFace( arg0.getFace() );
        arg1.setCertificate( arg0.getCertificate() );
        arg1.setOpenTaskId( arg0.getOpenTaskId() );

        return arg1;
    }
}

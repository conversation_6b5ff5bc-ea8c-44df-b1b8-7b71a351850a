package org.dromara.flow.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.flow.domain.PrjHazardousItemsFile;

import java.io.Serial;
import java.io.Serializable;


/**
 * 通用流程附件视图对象 prj_hazardous_items_file
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjHazardousItemsFile.class)
public class PrjHazardousItemsFileVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 附件id
     */
    @ExcelProperty(value = "附件id")
    private Long itemFileId;

    /**
     * 文件名称
     */
    @ExcelProperty(value = "文件名称")
    private String name;

    /**
     * 取sys_oss.oss_id,文件id
     */
    @ExcelProperty(value = "取sys_oss.oss_id,文件id")
    private Long fileId;

    /**
     * 业务id，取 雪花||UUID
     */
    @ExcelProperty(value = "业务id，取 雪花||UUID")
    private String taskId;

    /**
     * 服务类型，取字典表flow_service_type
     */
    @ExcelProperty(value = "服务类型，取字典表flow_service_type")
    private String serviceType;

    /**
     * 取sys_oss.oss_id,反馈文件id（多个逗号隔开）
     */
    @ExcelProperty(value = "取sys_oss.oss_id,反馈文件id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "多=个逗号隔开")
    private String callFileId;
}

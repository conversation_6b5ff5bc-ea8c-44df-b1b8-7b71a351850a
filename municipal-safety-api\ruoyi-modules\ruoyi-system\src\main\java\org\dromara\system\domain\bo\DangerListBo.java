package org.dromara.system.domain.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import org.dromara.system.domain.DangerList;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * dangerList业务对象 t_danger_list
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DangerList.class, reverseConvertGenerate = false)
public class DangerListBo extends BaseEntity {

    /**
     * 主键
     */
    private Long dangerId;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 父级id
     */
    private Long preId;

    /**
     * 危大类型
     */
    private Integer type;

    /**
     * 备注
     */
    private String remark;
}

<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true"  label-width="80px">
            <el-form-item label="项目id" prop="projectId">
              <el-input v-model="queryParams.projectId" placeholder="请输入项目id" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备sn号" prop="sn">
              <el-input v-model="queryParams.sn" placeholder="请输入设备sn号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备名称" prop="snName">
              <el-input v-model="queryParams.snName" placeholder="请输入设备名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="方向" prop="direction">
              <el-input v-model="queryParams.direction" placeholder="请输入方向" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['attendance:attSn:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['attendance:attSn:edit']">修改</el-button>
          </el-col>
<!--          <el-col :span="1.5">-->
<!--            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['attendance:attSn:remove']">删除</el-button>-->
<!--          </el-col>-->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['attendance:attSn:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="attSnList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column label="设备sn号" align="center" prop="sn" />
        <el-table-column label="设备名称" align="center" prop="snName" />
        <el-table-column label="方向" align="center" prop="direction">
          <template #default="scope">
            <span>{{ scope.row.direction == '0' ? '进' : '出'}}</span>
          </template>
        </el-table-column>
        <el-table-column label="设备状态" align="center" prop="status">
          <template #default="scope">
            <el-tag :type="scope.row.status == '0' ? 'success' : 'danger'">在线</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
<!--            <el-tooltip content="修改" placement="top">-->
<!--              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['attendance:attSn:edit']"></el-button>-->
<!--            </el-tooltip>-->
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['attendance:attSn:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改考勤设备对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="attSnFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目id" prop="projectId">
<!--          <el-input v-model="form.projectId" placeholder="请输入项目id" />-->
          <el-select v-model="form.projectId" placeholder="请选择项目" style="width: 100%" filterable>
            <el-option v-for="item in proList" :key="item.projectId" :label="item.projectName" :value="item.projectId">
              {{ item.projectName }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备sn号" prop="sn">
          <el-input v-model="form.sn" placeholder="请输入设备sn号" />
        </el-form-item>
        <el-form-item label="设备名称" prop="snName">
          <el-input v-model="form.snName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="方向" prop="direction">
          <el-radio-group v-model="form.direction">
            <el-radio value="0" border>进</el-radio>
            <el-radio value="1" border>出</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="AttSn" lang="ts">
import { listAttSn, getAttSn, delAttSn, addAttSn, updateAttSn } from '@/api/attendance/attSn';
import { AttSnVO, AttSnQuery, AttSnForm } from '@/api/attendance/attSn/types';
import { getProjectList } from '@/api/attendance/attRule'


const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const attSnList = ref<AttSnVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const attSnFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: AttSnForm = {
  snId: undefined,
  projectId: undefined,
  sn: undefined,
  snName: undefined,
  direction: undefined,
  status: undefined,
}
const data = reactive<PageData<AttSnForm, AttSnQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    sn: undefined,
    snName: undefined,
    direction: undefined,
    status: undefined,
    params: {
    }
  },
  rules: {
    snId: [
      { required: true, message: "设备id不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询考勤设备列表 */
const getList = async () => {
  loading.value = true;
  const res = await listAttSn(queryParams.value);
  attSnList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  attSnFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: AttSnVO[]) => {
  ids.value = selection.map(item => item.snId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  getProList();
  dialog.visible = true;
  dialog.title = "添加考勤设备";

}

let proList = ref([])
const getProList = async() => {
  const res = await getProjectList()
  proList.value = res.data
}

/** 修改按钮操作 */
const handleUpdate = async (row?: AttSnVO) => {
  reset();
  const _snId = row?.snId || ids.value[0]
  const res = await getAttSn(_snId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改考勤设备";
}

/** 提交按钮 */
const submitForm = () => {
  attSnFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.snId) {
        await updateAttSn(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addAttSn(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: AttSnVO) => {
  const _snIds = row?.snId || ids.value;
  await proxy?.$modal.confirm('是否确认删除考勤设备编号为"' + _snIds + '"的数据项？').finally(() => loading.value = false);
  await delAttSn(_snIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('attendance/attSn/export', {
    ...queryParams.value
  }, `attSn_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>

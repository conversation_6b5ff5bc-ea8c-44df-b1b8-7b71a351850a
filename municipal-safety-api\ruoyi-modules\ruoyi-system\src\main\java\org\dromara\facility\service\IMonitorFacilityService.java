package org.dromara.facility.service;

import org.dromara.facility.domain.vo.MonitorFacilityVo;
import org.dromara.facility.domain.bo.MonitorFacilityBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 监测设备Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IMonitorFacilityService {

    /**
     * 查询监测设备
     *
     * @param id 主键
     * @return 监测设备
     */
    MonitorFacilityVo queryById(Long id);

    /**
     * 分页查询监测设备列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 监测设备分页列表
     */
    TableDataInfo<MonitorFacilityVo> queryPageList(MonitorFacilityBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的监测设备列表
     *
     * @param bo 查询条件
     * @return 监测设备列表
     */
    List<MonitorFacilityVo> queryList(MonitorFacilityBo bo);

    /**
     * 新增监测设备
     *
     * @param bo 监测设备
     * @return 是否新增成功
     */
    Boolean insertByBo(MonitorFacilityBo bo);

    /**
     * 修改监测设备
     *
     * @param bo 监测设备
     * @return 是否修改成功
     */
    Boolean updateByBo(MonitorFacilityBo bo);

    /**
     * 校验并批量删除监测设备信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

package org.dromara.facility.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.facility.domain.bo.LnSprayingBo;
import org.dromara.facility.domain.vo.LnSprayingVo;
import org.dromara.facility.service.ILnSprayingService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 绿能喷淋设备
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/lnSpraying")
public class LnSprayingController extends BaseController {

    private final ILnSprayingService lnSprayingService;

    /**
     * 查询绿能喷淋设备列表
     */
    @GetMapping("/list")
    public TableDataInfo<LnSprayingVo> list(LnSprayingBo bo, PageQuery pageQuery) {
        return lnSprayingService.queryPageList(bo, pageQuery);
    }
}

<template>
  <div class="LiftDialog">
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" append-to-body @close="handleClose"
      width="80%">
      <div>
        <LifterReal v-if="dialogVisible.visible" ref="lifterReal" :dev-no="devNo" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import LifterReal from "./real/lifter_real.vue";

const props = defineProps({
  isShowLiftModel: {
    type: Boolean,
    default: false
  },
  devNo: {
    type: String,
    default: ''
  }
});
const emit = defineEmits(['update:isShowLiftModel']);
const dialogVisible = reactive<DialogOption>({
  visible: false,
  title: '实时数据'
});
watch(() => props.isShowLiftModel, (newVal) => {
  dialogVisible.visible = newVal;
}
);
const handleClose = () => {
  emit('update:isShowLiftModel', false);
};
</script>

<style lang="scss" scoped></style>
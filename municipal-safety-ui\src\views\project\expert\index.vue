<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="名称" prop="name" label-width="82px">
              <el-input v-model="queryParams.name" placeholder="请输入名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="queryParams.idCard" placeholder="请输入身份证号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="性别" prop="sex">
              <el-select v-model="queryParams.sex" placeholder="请选择性别" clearable>
                <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="工作单位" prop="workUnit">
              <el-input v-model="queryParams.workUnit" placeholder="请输入工作单位" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="电话" prop="phone" label-width="82px">
              <el-input v-model="queryParams.phone" placeholder="请输入电话" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="职称" prop="title">
              <el-input v-model="queryParams.title" placeholder="请输入职称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="专业" prop="major">
              <el-select v-model="queryParams.major" placeholder="请选择专业" clearable>
                <el-option v-for="dict in expert_major" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="行业" prop="industry">
              <el-select v-model="queryParams.industry" placeholder="请选择行业" clearable>
                <el-option v-for="dict in expert_industry" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="专家所在地" prop="province" label-width="82px">
              <el-select v-model="queryParams.province" placeholder="请选择省份" @change="selectSearchChange($event, 'province')">
                <el-option
                  v-for="(item, index) in provinceSearchOptions"
                  :key="index"
                  :label="item.divisionName"
                  :value="item.divisionCode"
                ></el-option>
              </el-select>
              <el-select v-model="queryParams.city" placeholder="请选择市" @change="selectSearchChange($event, 'city')" style="margin: 0 10px">
                <el-option v-for="(item, index) in citySearchOptions" :key="index" :label="item.divisionName" :value="item.divisionCode"></el-option>
              </el-select>
              <el-select v-model="queryParams.area" placeholder="请选择区">
                <el-option v-for="(item, index) in areaSearchOptions" :key="index" :label="item.divisionName" :value="item.divisionCode"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['expert:expert:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['expert:expert:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['expert:expert:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['expert:expert:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="expertList" @selection-change="handleSelectionChange" show-overflow-tooltip>
        <el-table-column type="selection" width="55" />
        <el-table-column label="专家主键" align="center" prop="expertId" v-if="false" />
        <el-table-column label="名称" align="center" prop="name">
          <template #default="scope">
            <el-button text type="primary" @click="expertDetailClick(scope.row)">{{ scope.row.name }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="身份证号" align="center" prop="idCard" />
        <el-table-column label="性别" align="center" prop="sex">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.sex" />
          </template>
        </el-table-column>
        <el-table-column label="工作单位" align="center" prop="workUnit" />
        <el-table-column label="电话" align="center" prop="phone" />
        <el-table-column label="专家类型" align="center" prop="type">
          <template #default="scope">
            <dict-tag :options="expert_type" :value="scope.row.type" />
          </template>
        </el-table-column>
        <el-table-column label="职称" align="center" prop="title" />
        <el-table-column label="专家所在地" align="center" prop="expertArea" />
        <el-table-column label="专业" align="center" prop="major">
          <template #default="scope">
            <dict-tag :options="expert_major" :value="scope.row.major" />
          </template>
        </el-table-column>
        <el-table-column label="行业" align="center" prop="industry">
          <template #default="scope">
            <dict-tag :options="expert_industry" :value="scope.row.industry" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="重置密码" placement="top">
              <el-button link type="primary" icon="RefreshRight" @click="restpassworld(scope.row)" v-hasPermi="['expert:expert:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['expert:expert:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="View" @click="expertDetailClick(scope.row)" v-hasPermi="['projects:expert:detail']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['expert:expert:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改 专家主对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" append-to-body @close="handleDialogClose">
      <el-form ref="expertFormRef" :model="form" :rules="rules">
        <el-row>
          <el-col :span="24">
            <el-form-item label="头像" prop="avatar">
              <header-upload @update:modelValue="handleAvatar" :limit="1" :modelValue="form.avatar" :isShowTip="false"></header-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="7">
            <el-form-item label="名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="性别" prop="sex">
              <el-select v-model="form.sex" placeholder="请选择性别">
                <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入身份证件号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="7">
            <el-form-item label="职称" prop="title">
              <el-input v-model="form.title" placeholder="请输入职称" />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入电话" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="工作单位" prop="workUnit">
              <el-input v-model="form.workUnit" placeholder="请输入工作单位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="简介" prop="introduce">
              <el-input v-model="form.introduce" type="textarea" :rows="5" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="专业" prop="major">
              <el-select v-model="form.major" placeholder="请选择专业">
                <el-option v-for="dict in expert_major" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="行业" prop="industry">
              <el-select v-model="form.industry" placeholder="请选择行业">
                <el-option v-for="dict in expert_industry" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专家类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择专家类型">
                <el-option v-for="dict in expert_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="省份" prop="province">
              <el-select v-model="form.province" placeholder="请选择省份" @change="selectChange($event, 'province')">
                <el-option v-for="(item, index) in provinceOptions" :key="index" :label="item.divisionName" :value="item.divisionCode"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="市" prop="city" label-width="49px">
              <el-select v-model="form.city" placeholder="请选择市" @change="selectChange($event, 'city')">
                <el-option v-for="(item, index) in cityOptions" :key="index" :label="item.divisionName" :value="item.divisionCode"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="区" prop="area" label-width="78px">
              <el-select v-model="form.area" placeholder="请选择区">
                <el-option v-for="(item, index) in areaOptions" :key="index" :label="item.divisionName" :value="item.divisionCode"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 新增 参与项目-->
        <div class="projectAdd">
          <div class="projectTitle">
            <span>参与项目</span>
            <span @click="handleProjectAdd">新增</span>
          </div>
          <div class="projectContent">
            <el-row v-for="(item, index) in form.expertProjectList" :key="index" style="display: flex; align-items: center">
              <el-col :span="23">
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-form-item
                      label="项目名称"
                      :prop="'expertProjectList.' + index + '.name'"
                      :rules="{
                        required: true,
                        message: '请输入名称',
                        trigger: 'blur'
                      }"
                    >
                      <el-input v-model="item.name" placeholder="请输入项目名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      label="项目状态"
                      :prop="'expertProjectList.' + index + '.status'"
                      :rules="{
                        required: true,
                        message: '请选中项目状态',
                        trigger: 'change'
                      }"
                    >
                      <el-select v-model="item.status" placeholder="请选择项目状态">
                        <el-option v-for="dict in expert_project_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-form-item
                      label="开始时间"
                      :prop="'expertProjectList.' + index + '.startDate'"
                      :rules="{
                        required: true,
                        message: '请选择开始时间',
                        trigger: 'change'
                      }"
                    >
                      <el-date-picker
                        v-model="item.startDate"
                        type="date"
                        value-format="YYYY-MM-DD"
                        placeholder="请选择开始时间"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="&nbsp;&nbsp;结束时间" :prop="'expertProjectList.' + index + '.endDate'">
                      <el-date-picker v-model="item.endDate" type="date" value-format="YYYY-MM-DD" placeholder="请选择结束时间" style="width: 100%" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item
                      label="项目地点"
                      :prop="'expertProjectList.' + index + '.location'"
                      :rules="{
                        required: true,
                        message: '请输入项目地点',
                        trigger: 'blur'
                      }"
                    >
                      <el-input v-model="item.location" placeholder="请输入项目地点" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item
                      label="项目描述"
                      :prop="'expertProjectList.' + index + '.description'"
                      :rules="{
                        required: true,
                        message: '请输入项目地点',
                        trigger: 'blur'
                      }"
                    >
                      <el-input v-model="item.description" type="textarea" :rows="5" placeholder="请输入项目描述" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="1">
                <span v-if="form.expertProjectList.length !== 1" class="projectDelete" @click="handleProjectDelete(index)">删除</span>
              </el-col>
            </el-row>
          </div>
        </div>
        <!-- 新增 专家领域-->
        <div class="projectAdd">
          <div class="projectTitle">
            <span>专家领域</span>
            <span @click="handleExpertFieldAdd">新增</span>
          </div>
          <div class="projectContent">
            <el-row v-for="(item, index) in form.expertFieldList" :key="index" style="display: flex; align-items: center">
              <el-col :span="23">
                <el-row>
                  <el-col :span="24">
                    <el-form-item
                      label="领域名称"
                      :prop="'expertFieldList.' + index + '.name'"
                      :rules="{
                        required: true,
                        message: '请输入领域名称',
                        trigger: 'blur'
                      }"
                    >
                      <el-input v-model="item.name" placeholder="请输入领域名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item
                      label="领域简介"
                      :prop="'expertFieldList.' + index + '.description'"
                      :rules="{
                        required: true,
                        message: '请输入领域简介',
                        trigger: 'blur'
                      }"
                    >
                      <el-input v-model="item.description" type="textarea" :rows="5" placeholder="请输入领域简介" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="1">
                <span v-if="form.expertFieldList.length !== 1" class="projectDelete" @click="handleExpertFieldDelete(index)">删除</span>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 专家信息详情弹框 -->
    <ExpertDetail :isShowModel="isShowModel" @update:isShowModel="expertDetailChange" :expertId="expertId"> </ExpertDetail>
  </div>
</template>

<script setup name="Expert" lang="ts">
import HeaderUpload from '@/components/ImageUpload/index.vue';
import {
  listExpert,
  getExpert,
  delExpert,
  addExpert,
  updateExpert,
  getProvinceList,
  getCityList,
  getAreaList,
  delProject,
  delField,
  resetpassworld
} from '@/api/expert/expert';
import { ExpertVO, ExpertQuery, ExpertForm } from '@/api/expert/expert/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { expert_industry, sys_user_sex, expert_major, expert_type, expert_project_status } = toRefs<any>(
  proxy?.useDict('expert_industry', 'sys_user_sex', 'expert_major', 'expert_type', 'expert_project_status')
);
import HeaderPrewiew from '@/components/ImagePreview/index.vue';
import ExpertDetail from '@/components/expertDetail/index.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// 控制详情弹框的变量
const isShowModel = ref<boolean>(false);
const expertList = ref<ExpertVO[]>();
const buttonLoading = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const expertId = ref<string>();

const queryFormRef = ref<ElFormInstance>();
const expertFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const initFormData: ExpertForm = {
  avatar: undefined,
  name: undefined,
  idCard: undefined,
  sex: undefined,
  workUnit: undefined,
  phone: undefined,
  introduce: undefined,
  title: undefined,
  province: undefined,
  city: undefined,
  area: undefined,
  major: undefined,
  industry: undefined,
  type: undefined,
  expertProjectList: [
    {
      name: undefined,
      status: undefined,
      location: undefined,
      startDate: null,
      endDate: null,
      description: undefined
    }
  ],
  expertFieldList: [
    {
      name: undefined,
      description: undefined
    }
  ]
};
const data = reactive<PageData<ExpertForm, ExpertQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    idCard: undefined,
    sex: undefined,
    workUnit: undefined,
    phone: undefined,
    introduce: undefined,
    title: undefined,
    province: undefined,
    city: undefined,
    area: undefined,
    major: undefined,
    industry: undefined,
    params: {}
  },
  rules: {
    avatar: [{ required: true, message: '请上传头像', trigger: 'change' }],
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    sex: [{ required: true, message: '请输入身份证号', trigger: 'change' }],
    idCard: [
      { required: true, message: '请输入身份证号', trigger: 'blur' },
      {
        pattern: /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/,
        message: '请输入正确的身份证号',
        trigger: 'blur'
      }
    ],
    title: [{ required: true, message: '请输入职称', trigger: 'blur' }],
    phone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      {
        pattern: /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[1589]))\d{8}$/,
        message: '请输入正确的手机号',
        trigger: 'blur'
      }
    ],
    workUnit: [{ required: true, message: '请输入工作单位', trigger: 'blur' }],
    introduce: [{ required: true, message: '请输入简介', trigger: 'blur' }],
    major: [{ required: true, message: '请选择专业', trigger: 'change' }],
    industry: [{ required: true, message: '请选择行业', trigger: 'change' }],
    type: [{ required: true, message: '请选择专家类型', trigger: 'change' }],
    province: [{ required: true, message: '请选择省份', trigger: 'change' }],
    city: [{ required: true, message: '请选择市', trigger: 'change' }],
    area: [{ required: true, message: '请选择区', trigger: 'change' }]
  }
});

const provinceOptions = ref<ExpertVO[]>([]); // 省份选项
const cityOptions = ref<ExpertVO[]>([]); // 城市选项
const areaOptions = ref<ExpertVO[]>([]); // 区选项

const provinceSearchOptions = ref<ExpertVO[]>([]); // 省份选项
const citySearchOptions = ref<ExpertVO[]>([]); // 城市选项
const areaSearchOptions = ref<ExpertVO[]>([]); // 区选项

const { queryParams, form, rules } = toRefs(data);

/** 查询 专家主列表 */
const getList = async () => {
  loading.value = true;
  const res = await listExpert(queryParams.value);
  expertList.value = res.rows;
  expertList.value.forEach((item) => {
    item.expertArea = item.provinceName + ' ' + item.cityName + ' ' + ' ' + item.areaName;
  });
  total.value = res.total;
  loading.value = false;
};
// 获取省份列表信息
const getProvince = async () => {
  const res = await getProvinceList();
  if (res.code === 200) {
    provinceOptions.value = res.data;
    provinceSearchOptions.value = res.data;
  }
};
// 重置密码
const restpassworld = (data) => {
  ElMessageBox.alert(`是否重置【${data.name}】的密码？`, '提示', {
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(({ value }) => {
      resetpassworld(data.idCard).then(() => {
        ElMessage.success('重置密码成功');
        getList();
      });
    })
    .catch(() => {});
};
// 获取市列表信息
const getCity = async (divisionCode: string) => {
  // 清空市和区选项
  form.value.city = undefined;
  form.value.area = undefined;
  const res = await getCityList(divisionCode);
  if (res.code === 200) {
    // 处理返回的数据，例如更新城市选项等
    cityOptions.value = res.data;
  }
};
// 获取区列表信息
const getArea = async (divisionCode: string) => {
  // 清空区选项
  form.value.area = undefined;
  const res = await getAreaList(divisionCode);
  if (res.code === 200) {
    // 处理返回的数据，例如更新区选项等
    areaOptions.value = res.data;
  }
};
// 获取市列表信息
const getCity1 = async (divisionCode: string) => {
  // 清空市和区选项
  queryParams.value.city = undefined;
  queryParams.value.area = undefined;
  const res = await getCityList(divisionCode);
  if (res.code === 200) {
    // 处理返回的数据，例如更新城市选项等
    citySearchOptions.value = res.data;
  }
};
// 获取区列表信息
const getArea1 = async (divisionCode: string) => {
  // 清空区选项
  queryParams.value.area = undefined;
  const res = await getAreaList(divisionCode);
  if (res.code === 200) {
    // 处理返回的数据，例如更新区选项等
    areaSearchOptions.value = res.data;
  }
};
const selectSearchChange = (divisionCode: string, flag: string) => {
  if (flag === 'province') {
    getCity1(divisionCode);
  } else if (flag === 'city') {
    getArea1(divisionCode);
  }
};
// 选择省份
const selectChange = (divisionCode: string, flag: string) => {
  if (flag === 'province') {
    getCity(divisionCode);
  } else if (flag === 'city') {
    getArea(divisionCode);
  }
};
/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  expertFormRef.value?.resetFields();
  form.value.expertProjectList = [
    {
      name: undefined,
      location: undefined,
      status: undefined,
      startDate: undefined,
      endDate: undefined,
      description: undefined
    }
  ];
  form.value.expertFieldList = [
    {
      name: undefined,
      description: undefined
    }
  ];
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value.province = undefined;
  queryParams.value.city = undefined;
  queryParams.value.area = undefined;
  provinceSearchOptions.value = undefined;
  citySearchOptions.value = undefined;
  areaSearchOptions.value = undefined;
  getProvince();
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ExpertVO[]) => {
  ids.value = selection.map((item) => item.expertId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加专家信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ExpertVO) => {
  reset();
  const _expertId = row?.expertId || ids.value[0];
  const { data } = await getExpert(_expertId);
  dialog.visible = true;
  dialog.title = '修改专家信息';
  await getCity(data.province);
  await getArea(data.city);
  Object.assign(form.value, data);
};
const getendtime = () => {
  // 判断状态是否是已完成2，是则必填结束时间
  let number = 0;
  form.value.expertProjectList.forEach((item) => {
    if (item.status === '2') {
      if (!item.endDate) {
        number++;
      }
    }
  });
  if (number > 0) {
    return false;
  } else {
    return true;
  }
};
/** 提交按钮 */
const submitForm = async () => {
  expertFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (getendtime()) {
        if (form.value.expertId) {
          await updateExpert(form.value).finally(() => (buttonLoading.value = false));
        } else {
          await addExpert(form.value).finally(() => (buttonLoading.value = false));
        }
        proxy?.$modal.msgSuccess('操作成功');
        dialog.visible = false;
        reset();
        await getList();
      } else {
        buttonLoading.value = false;
        proxy?.$modal.msgError('请填写已完工项目的结束日期');
      }
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ExpertVO) => {
  const _expertIds = row?.expertId || ids.value;
  await proxy?.$modal.confirm('是否确认删除 专家主编号为"' + _expertIds + '"的数据项？').finally(() => (loading.value = false));
  await delExpert(_expertIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'expert/expert/export',
    {
      ...queryParams.value
    },
    `expert_${new Date().getTime()}.xlsx`
  );
};
// dialog弹窗关闭时的方法
const handleDialogClose = () => {
  reset();
};
// 新增项目
const handleProjectAdd = () => {
  form.value.expertProjectList.unshift({
    name: undefined,
    location: undefined,
    status: undefined,
    startDate: undefined,
    endDate: undefined,
    description: undefined
  });
};
// 删除项目
const handleProjectDelete = async (index: number) => {
  if (form.value.expertProjectList[index]?.projectId) {
    await proxy?.$modal
      .confirm('确定要删除该项目吗？')
      .then(async () => {
        const res = await delProject(form.value.expertProjectList[index].projectId);
        if (res.code === 200) {
          form.value.expertProjectList.splice(index, 1);
          proxy?.$modal.msgSuccess('删除成功');
        }
      })
      .catch(() => {});
    return;
  }
  form.value.expertProjectList.splice(index, 1);
};
// 新增专家领域
const handleExpertFieldAdd = () => {
  form.value.expertFieldList.unshift({
    name: undefined,
    description: undefined
  });
};
// 删除专家领域
const handleExpertFieldDelete = async (index: number) => {
  if (form.value.expertFieldList[index]?.fieldId) {
    await proxy?.$modal
      .confirm('确定要删除该专家领域吗？')
      .then(async () => {
        const res = await delField(form.value.expertFieldList[index].fieldId);
        if (res.code === 200) {
          form.value.expertFieldList.splice(index, 1);
          proxy?.$modal.msgSuccess('删除成功');
        }
      })
      .catch(() => {});
    return;
  }
  form.value.expertFieldList.splice(index, 1);
};
// 上传头像的回调函数
const handleAvatar = (avatarOssId: string) => {
  form.value.avatar = avatarOssId;
};
// 点击名称查看详情的点击事件
const expertDetailClick = async (row: ExpertVO) => {
  expertId.value = row.expertId;
  isShowModel.value = true;
};
// 接收子组件的emit自定义方法
const expertDetailChange = (val: boolean) => {
  isShowModel.value = val;
};
onMounted(() => {
  getList();
  getProvince();
});
</script>
<style lang="scss" scoped>
.projectAdd {
  width: 100%;

  .projectTitle {
    margin-top: 10px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    span {
      &:first-child {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }

      &:last-child {
        color: #409eff;
        cursor: pointer;
      }
    }
  }

  .projectContent {
    .projectDelete {
      width: 28px;
      display: block;
      color: #f56c6c;
      margin-left: 10px;
      cursor: pointer;
    }
  }
}
</style>

package org.dromara.facility.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.LnNut;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnNutVoToLnNutMapperImpl implements LnNutVoToLnNutMapper {

    @Override
    public LnNut convert(LnNutVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnNut lnNut = new LnNut();

        lnNut.setId( arg0.getId() );
        lnNut.setPara( arg0.getPara() );
        lnNut.setVt( arg0.getVt() );
        lnNut.setQds( arg0.getQds() );
        lnNut.setTime( arg0.getTime() );
        lnNut.setValue( arg0.getValue() );
        lnNut.setDevNo( arg0.getDevNo() );
        lnNut.setCreateTime( arg0.getCreateTime() );

        return lnNut;
    }

    @Override
    public LnNut convert(LnNutVo arg0, LnNut arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setPara( arg0.getPara() );
        arg1.setVt( arg0.getVt() );
        arg1.setQds( arg0.getQds() );
        arg1.setTime( arg0.getTime() );
        arg1.setValue( arg0.getValue() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}

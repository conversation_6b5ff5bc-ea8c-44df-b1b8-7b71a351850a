package org.dromara.flow.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.PrjHazardousItemsComments;
import org.dromara.flow.domain.PrjHazardousItemsCommentsToPrjHazardousItemsCommentsVoMapper;
import org.dromara.flow.domain.PrjHazardousItemsFileToPrjHazardousItemsFileVoMapper;
import org.dromara.flow.domain.PrjHazardousItemsSpecialWarningToPrjHazardousItemsSpecialWarningVoMapper;
import org.dromara.flow.domain.bo.PrjHazardousItemsFileBoToPrjHazardousItemsFileMapper;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialWarningBoToPrjHazardousItemsSpecialWarningMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjHazardousItemsFileBoToPrjHazardousItemsFileMapper.class,PrjHazardousItemsFileVoToPrjHazardousItemsFileMapper.class,PrjHazardousItemsFileToPrjHazardousItemsFileVoMapper.class,PrjHazardousItemsSpecialWarningVoToPrjHazardousItemsSpecialWarningMapper.class,PrjHazardousItemsSpecialWarningBoToPrjHazardousItemsSpecialWarningMapper.class,PrjHazardousItemsSpecialWarningToPrjHazardousItemsSpecialWarningVoMapper.class,PrjHazardousItemsCommentsToPrjHazardousItemsCommentsVoMapper.class},
    imports = {}
)
public interface PrjHazardousItemsCommentsVoToPrjHazardousItemsCommentsMapper extends BaseMapper<PrjHazardousItemsCommentsVo, PrjHazardousItemsComments> {
}

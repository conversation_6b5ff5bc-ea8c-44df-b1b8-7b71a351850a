package org.dromara.projects.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjSafeTask;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjSafeTaskBoToPrjSafeTaskMapperImpl implements PrjSafeTaskBoToPrjSafeTaskMapper {

    @Override
    public PrjSafeTask convert(PrjSafeTaskBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjSafeTask prjSafeTask = new PrjSafeTask();

        prjSafeTask.setSearchValue( arg0.getSearchValue() );
        prjSafeTask.setCreateDept( arg0.getCreateDept() );
        prjSafeTask.setCreateBy( arg0.getCreateBy() );
        prjSafeTask.setCreateTime( arg0.getCreateTime() );
        prjSafeTask.setUpdateBy( arg0.getUpdateBy() );
        prjSafeTask.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            prjSafeTask.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        prjSafeTask.setProjectName( arg0.getProjectName() );
        prjSafeTask.setProjectAddress( arg0.getProjectAddress() );
        prjSafeTask.setProjectLongitude( arg0.getProjectLongitude() );
        prjSafeTask.setProjectLatitude( arg0.getProjectLatitude() );
        prjSafeTask.setProjectCraneNum( arg0.getProjectCraneNum() );
        prjSafeTask.setCraneType( arg0.getCraneType() );
        prjSafeTask.setCraneModel( arg0.getCraneModel() );
        prjSafeTask.setCraneSn( arg0.getCraneSn() );
        prjSafeTask.setCraneProductionDate( arg0.getCraneProductionDate() );
        prjSafeTask.setPropertyCompanyName( arg0.getPropertyCompanyName() );
        prjSafeTask.setFactoryName( arg0.getFactoryName() );
        prjSafeTask.setJackingType( arg0.getJackingType() );
        prjSafeTask.setExecutioinDate( arg0.getExecutioinDate() );
        prjSafeTask.setSectionNum( arg0.getSectionNum() );
        if ( arg0.getModifiedCraneHeight() != null ) {
            prjSafeTask.setModifiedCraneHeight( arg0.getModifiedCraneHeight().longValue() );
        }
        if ( arg0.getInitialCraneHeight() != null ) {
            prjSafeTask.setInitialCraneHeight( arg0.getInitialCraneHeight().longValue() );
        }
        prjSafeTask.setInstallationUnitName( arg0.getInstallationUnitName() );
        prjSafeTask.setInstallationUnitQualification( arg0.getInstallationUnitQualification() );
        prjSafeTask.setSafetyProductionPermit( arg0.getSafetyProductionPermit() );
        prjSafeTask.setStatus( arg0.getStatus() );

        return prjSafeTask;
    }

    @Override
    public PrjSafeTask convert(PrjSafeTaskBo arg0, PrjSafeTask arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setProjectName( arg0.getProjectName() );
        arg1.setProjectAddress( arg0.getProjectAddress() );
        arg1.setProjectLongitude( arg0.getProjectLongitude() );
        arg1.setProjectLatitude( arg0.getProjectLatitude() );
        arg1.setProjectCraneNum( arg0.getProjectCraneNum() );
        arg1.setCraneType( arg0.getCraneType() );
        arg1.setCraneModel( arg0.getCraneModel() );
        arg1.setCraneSn( arg0.getCraneSn() );
        arg1.setCraneProductionDate( arg0.getCraneProductionDate() );
        arg1.setPropertyCompanyName( arg0.getPropertyCompanyName() );
        arg1.setFactoryName( arg0.getFactoryName() );
        arg1.setJackingType( arg0.getJackingType() );
        arg1.setExecutioinDate( arg0.getExecutioinDate() );
        arg1.setSectionNum( arg0.getSectionNum() );
        if ( arg0.getModifiedCraneHeight() != null ) {
            arg1.setModifiedCraneHeight( arg0.getModifiedCraneHeight().longValue() );
        }
        else {
            arg1.setModifiedCraneHeight( null );
        }
        if ( arg0.getInitialCraneHeight() != null ) {
            arg1.setInitialCraneHeight( arg0.getInitialCraneHeight().longValue() );
        }
        else {
            arg1.setInitialCraneHeight( null );
        }
        arg1.setInstallationUnitName( arg0.getInstallationUnitName() );
        arg1.setInstallationUnitQualification( arg0.getInstallationUnitQualification() );
        arg1.setSafetyProductionPermit( arg0.getSafetyProductionPermit() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}

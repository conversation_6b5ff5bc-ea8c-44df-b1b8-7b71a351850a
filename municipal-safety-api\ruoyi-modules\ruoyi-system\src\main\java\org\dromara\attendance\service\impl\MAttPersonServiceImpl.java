package org.dromara.attendance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.attendance.domain.MAttPerson;
import org.dromara.attendance.domain.bo.MAttPersonBo;
import org.dromara.attendance.domain.vo.MAttPersonVo;
import org.dromara.attendance.mapper.MAttPersonMapper;
import org.dromara.attendance.service.IMAttPersonService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * attPersonService业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-06-10
 */
@RequiredArgsConstructor
@Service
public class MAttPersonServiceImpl implements IMAttPersonService {

    private final MAttPersonMapper baseMapper;

    /**
     * 查询attPerson
     *
     * @param id 主键
     * @return attPerson
     */
    @Override
    public MAttPersonVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询attPerson列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return attPerson分页列表
     */
    @Override
    public TableDataInfo<MAttPersonVo> queryPageList(MAttPersonBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MAttPerson> lqw = buildQueryWrapper(bo);
        Page<MAttPersonVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的attPerson列表
     *
     * @param bo 查询条件
     * @return attPerson列表
     */
    @Override
    public List<MAttPersonVo> queryList(MAttPersonBo bo) {
        LambdaQueryWrapper<MAttPerson> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MAttPerson> buildQueryWrapper(MAttPersonBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MAttPerson> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MAttPerson::getId);
        lqw.eq(bo.getPersonId() != null, MAttPerson::getPersonId, bo.getPersonId());
        lqw.eq(bo.getSnId() != null, MAttPerson::getSnId, bo.getSnId());
        return lqw;
    }

    /**
     * 新增attPerson
     *
     * @param bo attPerson
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MAttPersonBo bo) {
        MAttPerson add = MapstructUtils.convert(bo, MAttPerson.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改attPerson
     *
     * @param bo attPerson
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MAttPersonBo bo) {
        MAttPerson update = MapstructUtils.convert(bo, MAttPerson.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MAttPerson entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除attPerson信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<MAttPersonVo> selectMAttPersonByPersonId(Long personId) {
        return baseMapper.selectMAttPersonByPersonId(personId);
    }

    @Override
    public List<MAttPersonVo> selectMAttPersonBySnId(Long snId) {
        return baseMapper.selectMAttPersonBySnId(snId);
    }

    @Override
    public MAttPersonVo selectMAttPersonByPersonIdAndSnId(String snId, Long personId) {
        return baseMapper.selectMAttPersonByPersonIdAndSnId(snId, personId);
    }
}

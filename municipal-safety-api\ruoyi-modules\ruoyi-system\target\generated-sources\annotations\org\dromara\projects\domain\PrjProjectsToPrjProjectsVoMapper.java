package org.dromara.projects.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.monito.domain.DeviceMonitoToDeviceMonitoVoMapper;
import org.dromara.monito.domain.vo.DeviceMonitoVoToDeviceMonitoMapper;
import org.dromara.projects.domain.bo.PrjProjectsBoToPrjProjectsMapper;
import org.dromara.projects.domain.vo.PrjPersonnelVoToPrjPersonnelMapper;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.domain.vo.PrjProjectsVoToPrjProjectsMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjPersonnelVoToPrjPersonnelMapper.class,PrjPersonnelToPrjPersonnelVoMapper.class,DeviceMonitoVoToDeviceMonitoMapper.class,DeviceMonitoToDeviceMonitoVoMapper.class,PrjProjectsVoToPrjProjectsMapper.class,PrjProjectsBoToPrjProjectsMapper.class},
    imports = {}
)
public interface PrjProjectsToPrjProjectsVoMapper extends BaseMapper<PrjProjects, PrjProjectsVo> {
}

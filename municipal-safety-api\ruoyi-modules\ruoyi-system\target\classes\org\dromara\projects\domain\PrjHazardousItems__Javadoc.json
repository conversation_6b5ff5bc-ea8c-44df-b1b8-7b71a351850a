{"doc": " [项目管理] 列出项目内具体的危险性较大的分部分项工程对象 prj_hazardous_items\n\n <AUTHOR>\n @date 2025-05-07\n", "fields": [{"name": "itemId", "doc": " 危大工程项ID\n"}, {"name": "projectId", "doc": " 所属项目ID (逻辑外键至 prj_projects.project_id)\n"}, {"name": "dangerId", "doc": " 涉危工程清单ID（支持多选逗号隔开）\n"}, {"name": "itemName", "doc": " 危大工程名称/描述\n"}, {"name": "scopeDetails", "doc": " 具体范围详情\n"}, {"name": "dangerListType", "doc": " 危大类型 (1:危大, 2:超危大)\n"}, {"name": "startDate", "doc": " 计划开工日期\n"}, {"name": "plannedEndDate", "doc": " 计划竣工日期\n"}, {"name": "actualEndDate", "doc": " 实际竣工日期\n"}, {"name": "actualStartDate", "doc": " 实际开工日期\n"}, {"name": "status", "doc": " 状态\n"}, {"name": "delFlag", "doc": " 删除标志 (0代表存在 1代表删除)\n"}], "enumConstants": [], "methods": [], "constructors": []}
<template>
    <div class="p-2">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
                    :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
<!--                                    <el-form-item label="设置打卡时间" prop="checkTime">-->
<!--                                        <el-input v-model="queryParams.checkTime"-->
<!--                                                  placeholder="请输入设置打卡时间" clearable @keyup.enter="handleQuery"/>-->
<!--                                    </el-form-item>-->
<!--                                    <el-form-item label="弹性时间：允许迟到/早退的宽限时间" prop="elasticTime">-->
<!--                                        <el-input v-model="queryParams.elasticTime"-->
<!--                                                  placeholder="请输入弹性时间：允许迟到/早退的宽限时间" clearable @keyup.enter="handleQuery"/>-->
<!--                                    </el-form-item>-->
<!--                                    <el-form-item label="预警机制：根据对应的漏卡次数设置" prop="warning">-->
<!--                                        <el-input v-model="queryParams.warning"-->
<!--                                                  placeholder="请输入预警机制：根据对应的漏卡次数设置" clearable @keyup.enter="handleQuery"/>-->
<!--                                    </el-form-item>-->
                                    <el-form-item label="外勤" prop="fieldCheck">
                                        <el-input v-model="queryParams.fieldCheck"
                                                  placeholder="请输入外勤打卡" clearable @keyup.enter="handleQuery"/>
                                    </el-form-item>
<!--                                    <el-form-item label="是否删除。0-否，1-是" prop="deleted">-->
<!--                                        <el-input v-model="queryParams.deleted"-->
<!--                                                  placeholder="请输入是否删除。0-否，1-是" clearable @keyup.enter="handleQuery"/>-->
<!--                                    </el-form-item>-->
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd"
                                   v-hasPermi="['attendance:attRule:add']">新增
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
                                   v-hasPermi="['attendance:attRule:edit']">修改
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
                                   v-hasPermi="['attendance:attRule:remove']">删除
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="warning" plain icon="Download" @click="handleExport"
                                   v-hasPermi="['attendance:attRule:export']">导出
                        </el-button>
                    </el-col>
                    <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
            </template>

            <el-table v-loading="loading" :data="attRuleList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center"/>
                    <el-table-column label="规则类型" align="center" prop="ruleType">
                        <template #default="scope">
                            {{ scope.row.ruleType == 0 ? '通用规则' : '项目规则' }}
                        </template>
                    </el-table-column>
                    <<el-table-column label="项目名称" align="center" prop="projectName"/>
                    <el-table-column label="人员类型" align="center" prop="personTypeName">
                        <template #default='scope'>
                          <el-tag type="primary" style='margin:0 5px 5px 0' v-for='item in scope.row.personTypeName'>{{item}}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="打卡规则" align="center" prop="checkTime">
                        <template #default="scope">
                            <div v-for='(item,index) in JSON.parse(scope.row.checkTime)' :key='index'>
                              <span style='font-weight: bold'>第{{index+1}}次：</span>{{item.startTime}}-{{item.endTime}}
                            </div>
                        </template>
                    </el-table-column>
<!--                        <el-table-column label="弹性时间：允许迟到/早退的宽限时间" align="center" prop="elasticTime"/>-->
<!--                        <el-table-column label="预警机制：根据对应的漏卡次数设置" align="center" prop="warning"/>-->
                    <el-table-column label="外勤打卡" align="center" prop="fieldCheck">
                      <template #default="scope">
                        {{ scope.row.fieldCheck == 0 ? '关' : '开' }}
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template #default="scope">
                            <el-tooltip content="修改" placement="top">
                                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                                           v-hasPermi="['attendance:attRule:edit']"></el-button>
                            </el-tooltip>
                            <el-tooltip content="删除" placement="top">
                                <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                                           v-hasPermi="['attendance:attRule:remove']"></el-button>
                            </el-tooltip>
                        </template>
                    </el-table-column>
            </el-table>

            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                        v-model:limit="queryParams.pageSize" @pagination="getList"/>
        </el-card>
        <!-- 添加或修改考勤规则对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.visible" width="50vw" append-to-body @close='cancel'>
              <el-form ref="attRuleFormRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="规则类型" prop="ruleType">
                  <el-select v-model="form.ruleType" placeholder="请选择规则类型" @change='changeRule'>
                    <el-option label="通用规则" :value="0"></el-option>
                    <el-option label="项目规则" :value="1"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="项目名称" prop="projectId" v-if='form.ruleType == 1'>
                  <el-select v-model="form.projectId" placeholder="请选择项目名称" @change='getPersonType'>
                    <el-option v-for="item in proList" :key="item.projectId" :label="item.projectName" :value="item.projectId">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="人员类型" prop="personType" v-if='form.ruleType == 1'>
                  <el-select v-model="form.personType" placeholder="请选择人员类型" multiple :disabled="formStatus == 'edit'">
                    <el-option v-for="dict in personTypes" :key="dict.dictValue"
                     :label="dict.dictLabel" :value="dict.dictValue" :disabled="formStatus == 'edit' ? false : formStatus == 'create' && dict.disabled"/>
                  </el-select>
                </el-form-item>
                <el-form-item label="打卡规则" style='align-items: unset;' prop='clockRule'>
                  <div style='height: 100%'>
                    <el-button type="primary" icon="CirclePlus" size="default" @click="addPlan()" style='margin-right:20px;margin-bottom: 10px'>添加</el-button>
                  </div>

                  <div>
                      <div v-for="(it,ind) in classRuleList" :key="ind" style='margin-bottom: 10px'>
                        <span style='font-size: 16px;font-weight: bold;margin-right: 10px'>第{{ind+1}}次打卡时段</span>
                        <!-- :model="item" -->
                        <el-time-select
                          placeholder="起始时间"
                          v-model="it.startTime"
                          style="margin-right: 10px;width: 200px"
                          start = "06:00"
                          step = "00:30"
                          end = "21:00"
                          :minTime = "ind-1 >= 0 && classRuleList[ind-1].endTime ? classRuleList[ind-1].endTime : ''"
                        >
                        </el-time-select>
                        <!--    :class="item.isValidate && !it.startTime ? 'is-error' : ''" -->
                        <el-time-select
                          placeholder="结束时间"
                          v-model="it.endTime"
                          style="margin-right: 10px;width: 200px"
                          start = "06:30"
                          step = "00:30"
                          end = "21:00"
                          :minTime = "it.startTime"
                        >
                          <!--  :class="item.isValidate && !it.endTime ? 'is-error' : ''"-->
                        </el-time-select>
                        <el-button style="margin-left: 10px" @click="delTimePlan(classRuleList,it,ind)" type="danger" icon="Delete" circle></el-button>
                      </div>
                    </div>
                </el-form-item>
                <el-form-item label="弹性时间" prop="elasticTime">
                    <el-input type='number' v-model="form.elasticTime" placeholder="请输入弹性时间"/>
                </el-form-item>
                <el-form-item label="预警机制" prop="warning">
                  <div style='display: flex;flex-wrap: wrap;width: 100%'>
                    <div class='warningCont' style='background-image: linear-gradient(to top, #f8aeae, #fff,#fff , #f8aeae)'>
                      <span style='margin-right: 5px;color: #929292'>特别严重预警</span>
                      <el-input-number v-model="tempWarning.specialSeriousWarning" :min="tempWarning.seriousWarning+1" :max="20" />
                    </div>
                    <div class='warningCont' style='background-image: linear-gradient(to top, #f6d7a3, #fff,#fff , #f6d7a3)'>
                      <span style='margin-right: 5px;color: #929292'>严重预警</span>
                      <el-input-number v-model="tempWarning.seriousWarning" :min="tempWarning.generalWarning+1" :max="tempWarning.specialSeriousWarning-1" />
                    </div>
                    <div class='warningCont' style='background-image: linear-gradient(to top, #fafab0, #fff,#fff, #fafab0)'>
                      <span style='margin-right: 5px;color: #929292'>一般预警</span>
                      <el-input-number v-model="tempWarning.generalWarning" :min="1" :max="tempWarning.seriousWarning-1" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="外勤打卡" prop="fieldCheck">
                    <el-switch v-model="form.fieldCheck" :active-value="1" :inactive-value="0">
                    </el-switch>
                </el-form-item>
                <el-form-item label="备注">
                  <el-input v-model="form.content" type="textarea" placeholder="请输入内容"/>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="AttRule" lang="ts">
    import { addAttRule, delAttRule, getAttRule, listAttRules, updateAttRule,getProjectList,personTypeList } from '@/api/attendance/attRule';
    import { AttRuleVO, AttRuleForm , AttRuleQuery } from '@/api/attendance/attRule/types';
    const {proxy} = getCurrentInstance() as ComponentInternalInstance;
    const { personnel_position } = toRefs<any>(proxy?.useDict('personnel_position'));
    const attRuleList = ref<AttRuleVO[]>([]);
    const buttonLoading = ref(false);
    const loading = ref(true);
    const showSearch = ref(true);
    const ids = ref<Array<string | number>>([]);
    const single = ref(true);
    const multiple = ref(true);
    const total = ref(0);
    const queryFormRef = ref<ElFormInstance>();
    const attRuleFormRef = ref<ElFormInstance>();
    const dialog = reactive<DialogOption>({
        visible: false,
        title: ''
    });

    const formStatus = ref('')
    const classRuleList = ref<Array<any>>([]);
    const proList = ref<Array<any>>([]);
    const tempWarning = ref<any>({
        generalWarning: 1,
        seriousWarning: 2,
        specialSeriousWarning: 3
    });
    const initFormData: AttRuleForm = {
      id: undefined,
      ruleType: undefined,
      personType: undefined,
      checkTime: undefined,
      elasticTime: undefined,
      warning: [],
      fieldCheck: '1',
      content: undefined,
      deleted: undefined,
      projectId : undefined
    }
    const data = reactive<PageData<AttRuleForm, AttRuleQuery>>({
        form: {...initFormData},
        queryParams: {
            pageNum: 1,
            pageSize: 10,
            ruleType: undefined,
            personType: undefined,
            checkTime: undefined,
            elasticTime: undefined,
            warning: [],
            fieldCheck: undefined,
            content: undefined,
            deleted: undefined,
            params: {
            }
        },
        rules: {
          ruleType: [
              {required: true, message: "规则类型不能为空", trigger: "change" }
          ],
          projectId : [
              {required: true, message: "项目名称不能为空", trigger: "change" }
          ],
          personType : [
              {required: true, message: "人员类型不能为空", trigger: "change" }
          ],
          clockRule : [
              {
                validator(rule, value, callback) {
                  if(classRuleList.value.length <= 0){
                    callback('请填写完整打卡规则');
                    return
                  }
                  let isAllDone = classRuleList.value.map((item, index) => {
                    console.log(item,index);
                    if (item.startTime && item.endTime){
                      return true
                    }else{
                      return false
                    }
                  })
                  if (isAllDone.filter(item => !item).length == 0) {
                    callback();
                  } else {
                    callback('请填写完整打卡规则');
                  }
                }
              }
          ],
        }
    });

    const {queryParams, form, rules} = toRefs(data);

    /** 查询考勤规则列表 */
    const getList = async () => {
        loading.value = true;
        const res = await listAttRules(queryParams.value);
        attRuleList.value = res.rows;
        total.value = res.total;
        loading.value = false;
    }

    const changeRule = (rule) => {
      if (rule == 1){
        getProjectList().then(res =>{
            proList.value = res.data
        })
      }else{
          form.value.projectId = undefined
          form.value.personType = []
      }
    }
    /** 取消按钮 */
    const cancel = () => {
        reset();
        classRuleList.value = [];
        tempWarning.value = {
          generalWarning: 1,
          seriousWarning: 2,
          specialSeriousWarning: 3
        }
        dialog.visible = false;
    }

    /** 表单重置 */
    const reset = () => {
        form.value = {...initFormData};
            attRuleFormRef.value?.resetFields();
    }

    /** 搜索按钮操作 */
    const handleQuery = () => {
        queryParams.value.pageNum = 1;
        getList();
    }

    /** 重置按钮操作 */
    const resetQuery = () => {
        queryFormRef.value?.resetFields();
        handleQuery();
    }

    /** 多选框选中数据 */
    const handleSelectionChange = (selection: AttRuleVO[]) => {
        ids.value = selection.map(item => item.id);
        single.value = selection.length != 1;
        multiple.value = !selection.length;
    }

    /** 新增按钮操作 */
    const handleAdd = () => {
        reset();
        formStatus.value = 'create';
        dialog.visible = true;
        dialog.title = "添加考勤规则";
    }

    /** 修改按钮操作 */
    const handleUpdate = async (row?: AttRuleVO) => {
        formStatus.value = 'edit';
        reset();
        const _id = row?.id || ids.value[0]
        const res = await getAttRule(_id);
        classRuleList.value = JSON.parse(res.data.checkTime)
        tempWarning.value = {
          generalWarning: res.data.warning.split(',')[0] / 1,
          seriousWarning: res.data.warning.split(',')[1] / 1,
          specialSeriousWarning: res.data.warning.split(',')[2] / 1
        }
        Object.assign(form.value, res.data);
        if(form.value.ruleType == 1){
          changeRule(1)
        }
        getPersonType(form.value.projectId)
        form.value.personType = form.value.personType?.split(',')
        dialog.visible = true;
        dialog.title = "修改考勤规则";
    }

    const addPlan = () => {
      classRuleList.value.push({
        num : '',
        startTime: '',
        endTime: ''
      })
    }
    let personTypes = ref([])

    const getPersonType = async (val) =>{
      const res = await personTypeList(val)
      personTypes.value = res.data
    }

    const delTimePlan = (index: number) => {
      classRuleList.value.splice(index, 1)
    }

    /** 提交按钮 */
    const submitForm = () => {
          attRuleFormRef.value?.validate(async (valid: boolean) => {
            if (valid) {
                // 处理打卡规则数据
                classRuleList.value.forEach((item, index) => {
                  item.num = index + 1
                })
                form.value.checkTime = JSON.stringify(classRuleList.value)
                // 处理预警机制数据
                form.value.warning = []
                for (let valueKey in tempWarning.value) {
                  // console.log(valueKey, tempWarning.value[valueKey]);
                  form.value.warning.push(tempWarning.value[valueKey])
                }
                form.value.warning = form.value.warning.join(',')
                // 处理人员类型
                form.value.personType = form.value.personType.join(',')
                // 请求接口
                buttonLoading.value = true;
                if (form.value.id) {
                    await updateAttRule(form.value).finally(() => buttonLoading.value = false);
                } else {
                    await addAttRule(form.value).finally(() => buttonLoading.value = false);
                }
                proxy?.$modal.msgSuccess("操作成功");
                dialog.visible = false;
                await getList();
            }
          });
    }
    /** 导出按钮操作 */
    const handleExport = () => {
        proxy?.download('attendance/attRule/export', {
            ...queryParams.value
        }, `attRule_${new Date().getTime()}.xlsx`)
    }
    /** 删除按钮操作 */
    const handleDelete = async (row?: AttRuleVO) => {
      const _expertIds = row?.id || ids.value;
      await proxy?.$modal.confirm('是否确认删除当前数据项？').finally(() => loading.value = false);
      await delAttRule(_expertIds);
      proxy?.$modal.msgSuccess("删除成功");
      await getList();
    }
    onMounted(() => {
        getList();
    });
</script>

<style scoped>
.warningCont {
  padding:3px 7px 5px 7px;
  margin-right: 15px;
  border: 1px solid #e2e2e2;
  font-weight: bold;
  border-radius: 3px;
}
</style>

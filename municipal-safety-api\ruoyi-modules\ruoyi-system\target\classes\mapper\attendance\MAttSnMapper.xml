<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.attendance.mapper.MAttSnMapper">

    <select id="selectMAttSnList" resultType="org.dromara.attendance.domain.vo.MAttSnVo">
        select a.*, pp.project_name
        from m_att_sn a
        left join prj_projects pp on pp.project_id = a.project_id
        <where>
            <if test="projectId != null ">and a.project_id = #{projectId}</if>
            <if test="sn != null ">and a.sn = #{sn}</if>
            <if test="snName != null ">and a.sn_name = #{snName}</if>
            <if test="direction != null ">and a.direction = #{direction}</if>
            <if test="status != null ">and a.sn_name = #{status}</if>
            <if test="createDept != null ">and a.create_dept = #{createDept}</if>
            and a.del_flag = 0
        </where>
        order by a.create_time desc
    </select>

    <select id="selectMAttSnBySn" resultType="org.dromara.attendance.domain.vo.MAttSnVo">
        select a.* from m_att_sn a
        where a.sn = #{sn} and a.del_flag = 0
    </select>

    <select id="selectMAttSnByProjectId" resultType="org.dromara.attendance.domain.vo.MAttSnVo">
        select a.* from m_att_sn a
        where a.project_id = #{projectId} and a.del_flag = 0
        order by a.create_time desc
    </select>
</mapper>

package org.dromara.facility.domain;

import cn.hutool.core.date.DatePattern;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 绿能用电监测对象 ln_energy
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ln_energy")
public class LnEnergy {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * A相电压 0.1V
     */
    private Long ua;

    /**
     * B相电压 0.1V
     */
    private Long ub;

    /**
     * C相电压 0.1V
     */
    private Long uc;

    /**
     * A相电流 0.1V
     */
    private Long ia;

    /**
     * B相电流 0.1V
     */
    private Long ib;

    /**
     * C相电流 0.1V
     */
    private Long ic;

    /**
     * 漏电流 0.1V
     */
    private Long il;

    /**
     * A相温度 0.1°
     */
    private Long ta;

    /**
     * B相温度 0.1°
     */
    private Long tb;

    /**
     * C相温度 0.1°
     */
    private Long tc;

    /**
     * N相温度 0.1°
     */
    private Long tn;

    /**
     * 采集时间
     */
    private Date recordTime;

    /**
     * 设备编号
     */
    private String deviceNo;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

{"doc": " 特种作业人员信息\n\n <AUTHOR>\n @date 2025-05-13\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.special.domain.bo.SpecialOperationPersonnelBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询特种作业人员信息列表\n"}, {"name": "export", "paramTypes": ["org.dromara.special.domain.bo.SpecialOperationPersonnelBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出特种作业人员信息列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取特种作业人员信息详细信息\n\n @param sopId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.special.domain.bo.SpecialOperationPersonnelBo"], "doc": " 新增特种作业人员信息\n"}, {"name": "edit", "paramTypes": ["org.dromara.special.domain.bo.SpecialOperationPersonnelBo"], "doc": " 修改特种作业人员信息\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除特种作业人员信息\n\n @param sopIds 主键串\n"}], "constructors": []}
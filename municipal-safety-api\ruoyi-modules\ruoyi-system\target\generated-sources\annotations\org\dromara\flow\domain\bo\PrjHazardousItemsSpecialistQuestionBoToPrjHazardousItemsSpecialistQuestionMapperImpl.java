package org.dromara.flow.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.flow.domain.PrjHazardousItemsSpecialistQuestion;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsSpecialistQuestionBoToPrjHazardousItemsSpecialistQuestionMapperImpl implements PrjHazardousItemsSpecialistQuestionBoToPrjHazardousItemsSpecialistQuestionMapper {

    @Override
    public PrjHazardousItemsSpecialistQuestion convert(PrjHazardousItemsSpecialistQuestionBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsSpecialistQuestion prjHazardousItemsSpecialistQuestion = new PrjHazardousItemsSpecialistQuestion();

        prjHazardousItemsSpecialistQuestion.setSearchValue( arg0.getSearchValue() );
        prjHazardousItemsSpecialistQuestion.setCreateDept( arg0.getCreateDept() );
        prjHazardousItemsSpecialistQuestion.setCreateBy( arg0.getCreateBy() );
        prjHazardousItemsSpecialistQuestion.setCreateTime( arg0.getCreateTime() );
        prjHazardousItemsSpecialistQuestion.setUpdateBy( arg0.getUpdateBy() );
        prjHazardousItemsSpecialistQuestion.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            prjHazardousItemsSpecialistQuestion.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        prjHazardousItemsSpecialistQuestion.setId( arg0.getId() );
        prjHazardousItemsSpecialistQuestion.setName( arg0.getName() );
        prjHazardousItemsSpecialistQuestion.setDetail( arg0.getDetail() );
        prjHazardousItemsSpecialistQuestion.setResultId( arg0.getResultId() );
        prjHazardousItemsSpecialistQuestion.setSpecialist( arg0.getSpecialist() );

        return prjHazardousItemsSpecialistQuestion;
    }

    @Override
    public PrjHazardousItemsSpecialistQuestion convert(PrjHazardousItemsSpecialistQuestionBo arg0, PrjHazardousItemsSpecialistQuestion arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setDetail( arg0.getDetail() );
        arg1.setResultId( arg0.getResultId() );
        arg1.setSpecialist( arg0.getSpecialist() );

        return arg1;
    }
}

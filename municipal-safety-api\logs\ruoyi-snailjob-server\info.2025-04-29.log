2025-04-29 18:47:11 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-04-29 18:47:11 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.15 with PID 29024 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-snailjob-server\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-29 18:47:11 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-04-29 18:47:12 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-04-29 18:47:12 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-04-29 18:47:12 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-29 18:47:12 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-04-29 18:47:12 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-04-29 18:47:12 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1458 ms
2025-04-29 18:47:14 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-04-29 18:47:14 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-29 18:47:14 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-29 18:47:14 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-29 18:47:14 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-29 18:47:15 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-04-29 18:47:15 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-04-29 18:47:15 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-04-29 18:47:15 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.4.0
2025-04-29 18:47:15 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-04-29 18:47:15 [main] INFO  c.a.s.s.common.rpc.server.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.GrpcServer, port = 17888
2025-04-29 18:47:15 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-04-29 18:47:15 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-04-29 18:47:15 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-04-29 18:47:15 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-04-29 18:47:15 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-04-29 18:47:15 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-04-29 18:47:15 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-04-29 18:47:15 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-04-29 18:47:15 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-04-29 18:47:15 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-04-29 18:47:15 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-04-29 18:47:15 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-04-29 18:47:15 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.4.0
2025-04-29 18:47:15 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 4.689 seconds (process running for 5.677)
2025-04-29 18:47:15 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-29 18:47:15 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 56c94c188ac7
2025-04-29 18:47:15 [http-nio-8800-exec-1] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-29 18:47:15 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-29 18:47:15 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-29 18:47:15 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5f5ca2f
2025-04-29 18:47:15 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-29 18:48:22 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.4.0
2025-04-29 18:48:22 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-04-29 18:48:23 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-04-29 18:48:23 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-29 18:48:23 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-29 18:48:26 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-04-29 18:48:26 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.15 with PID 6072 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-snailjob-server\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-29 18:48:26 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-04-29 18:48:27 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-04-29 18:48:27 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-04-29 18:48:27 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-29 18:48:27 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-04-29 18:48:27 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-04-29 18:48:27 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1369 ms
2025-04-29 18:48:29 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-04-29 18:48:29 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-29 18:48:29 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-29 18:48:30 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-29 18:48:30 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-29 18:48:30 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-04-29 18:48:30 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-04-29 18:48:30 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-04-29 18:48:30 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.4.0
2025-04-29 18:48:30 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-04-29 18:48:30 [main] INFO  c.a.s.s.common.rpc.server.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.GrpcServer, port = 17888
2025-04-29 18:48:30 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-04-29 18:48:30 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-04-29 18:48:30 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-04-29 18:48:30 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-04-29 18:48:30 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-04-29 18:48:30 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-04-29 18:48:30 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-04-29 18:48:30 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-04-29 18:48:30 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-04-29 18:48:30 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-04-29 18:48:30 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-04-29 18:48:30 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-04-29 18:48:30 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.4.0
2025-04-29 18:48:30 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 4.675 seconds (process running for 5.368)
2025-04-29 18:48:30 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-29 18:48:30 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 56c94c188ac7
2025-04-29 18:48:30 [http-nio-8800-exec-1] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-29 18:48:30 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-29 18:48:30 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-04-29 18:48:31 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7521ed6a
2025-04-29 18:48:31 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-29 18:48:31 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1917169105476825088]
2025-04-29 18:48:40 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-29 18:48:40 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-29 18:49:22 [snail-job-scheduled-thread-3] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1917169270031917056]
2025-04-29 18:50:01 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-9] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[1] 任务调度成功.
2025-04-29 18:51:01 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-13] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[2] 任务调度成功.
2025-04-29 18:52:01 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-17] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[3] 任务调度成功.
2025-04-29 18:53:01 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-21] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[4] 任务调度成功.
2025-04-29 18:54:01 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-25] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[5] 任务调度成功.
2025-04-29 18:55:01 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-29] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[6] 任务调度成功.
2025-04-29 18:55:42 [snail-job-scheduled-thread-3] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917169270031917056]
2025-04-29 18:56:01 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-33] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[7] 任务调度成功.
2025-04-29 18:57:01 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-37] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[8] 任务调度成功.
2025-04-29 18:58:01 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-41] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[9] 任务调度成功.
2025-04-29 18:59:02 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-45] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[10] 任务调度成功.
2025-04-29 18:59:45 [snail-job-scheduled-thread-3] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917170684586221568]
2025-04-29 19:00:01 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-49] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[11] 任务调度成功.
2025-04-29 19:01:01 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-53] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[12] 任务调度成功.
2025-04-29 19:02:01 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-57] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[13] 任务调度成功.
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.4.0
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-04-29 19:02:16 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1917169105476825088]
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.4.0
2025-04-29 19:02:16 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-04-29 19:02:16 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-29 19:02:16 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-29 19:46:49 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-04-29 19:46:49 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.15 with PID 30624 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-snailjob-server\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-29 19:46:49 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-04-29 19:46:51 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-04-29 19:46:51 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-04-29 19:46:51 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-29 19:46:51 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-04-29 19:46:51 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-04-29 19:46:51 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1451 ms
2025-04-29 19:46:52 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-04-29 19:46:53 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-29 19:46:53 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-29 19:46:53 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-29 19:46:53 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-29 19:46:53 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-04-29 19:46:53 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-04-29 19:46:53 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-04-29 19:46:53 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.4.0
2025-04-29 19:46:53 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-04-29 19:46:53 [main] INFO  c.a.s.s.common.rpc.server.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.GrpcServer, port = 17888
2025-04-29 19:46:53 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-04-29 19:46:53 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-04-29 19:46:53 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-04-29 19:46:53 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-04-29 19:46:53 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-04-29 19:46:53 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-04-29 19:46:53 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-04-29 19:46:54 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-04-29 19:46:54 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-04-29 19:46:54 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-04-29 19:46:54 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-04-29 19:46:54 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-04-29 19:46:54 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.4.0
2025-04-29 19:46:54 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 4.85 seconds (process running for 5.793)
2025-04-29 19:46:54 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-29 19:46:54 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as 56c94c188ac7
2025-04-29 19:46:54 [http-nio-8800-exec-1] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-29 19:46:54 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-29 19:46:54 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-04-29 19:46:54 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6d611f80
2025-04-29 19:46:54 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-29 19:46:54 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1917183799541387264]
2025-04-29 19:47:04 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-29 19:47:04 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-29 19:47:51 [snail-job-scheduled-thread-2] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1917183991112081408]
2025-04-29 19:48:24 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-11] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[14] 任务调度成功.
2025-04-29 19:49:24 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-15] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[15] 任务调度成功.
2025-04-29 19:50:25 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-19] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[16] 任务调度成功.
2025-04-29 19:51:24 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-23] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[17] 任务调度成功.
2025-04-29 19:52:24 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-27] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[18] 任务调度成功.
2025-04-29 19:53:24 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-31] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[19] 任务调度成功.
2025-04-29 19:53:38 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[1] remoteNodeSize:[2]
2025-04-29 19:53:38 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-29 19:53:38 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63]]
2025-04-29 19:54:33 [snail-job-scheduled-thread-1] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917183991112081408]
2025-04-29 19:54:48 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[2] remoteNodeSize:[3]
2025-04-29 19:54:48 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-29 19:54:48 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85]]
2025-04-29 19:56:38 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[3] remoteNodeSize:[2]
2025-04-29 19:56:38 [server-node-balance] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[DEFAULT_SERVER] hostId:[1917185460967833600]
2025-04-29 19:56:38 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-29 19:56:38 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-29 19:56:58 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[2] remoteNodeSize:[3]
2025-04-29 19:56:58 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-29 19:56:58 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-29 19:57:24 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-35] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[23] 任务调度成功.
2025-04-29 19:57:49 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[3] remoteNodeSize:[2]
2025-04-29 19:57:49 [server-node-balance] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[DEFAULT_SERVER] hostId:[1917186307680374784]
2025-04-29 19:57:49 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-29 19:57:49 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-29 19:58:19 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[2] remoteNodeSize:[3]
2025-04-29 19:58:19 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-29 19:58:19 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-29 19:58:24 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-38] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[24] 任务调度成功.
2025-04-29 19:59:24 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-41] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[25] 任务调度成功.
2025-04-29 20:00:24 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-44] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[26] 任务调度成功.
2025-04-29 20:01:24 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-47] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[27] 任务调度成功.
2025-04-29 20:02:24 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-50] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[28] 任务调度成功.
2025-04-29 20:03:24 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-53] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[29] 任务调度成功.
2025-04-29 20:04:24 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-56] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[30] 任务调度成功.
2025-04-29 20:05:24 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-59] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[31] 任务调度成功.
2025-04-29 20:05:42 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[3] remoteNodeSize:[4]
2025-04-29 20:05:42 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-29 20:05:42 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95]]
2025-04-29 20:06:02 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[4] remoteNodeSize:[5]
2025-04-29 20:06:02 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-29 20:06:02 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77]]
2025-04-29 20:07:14 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917187340230053888]
2025-04-29 20:07:32 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[5] remoteNodeSize:[4]
2025-04-29 20:07:32 [server-node-balance] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[DEFAULT_SERVER] hostId:[1917188534980341760]
2025-04-29 20:07:32 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-29 20:07:32 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95]]
2025-04-29 20:12:04 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[4] remoteNodeSize:[5]
2025-04-29 20:12:04 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-29 20:12:04 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77]]
2025-04-29 20:16:15 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[5] remoteNodeSize:[3]
2025-04-29 20:16:15 [server-node-balance] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[DEFAULT_SERVER] hostId:[1917186665064521728]
2025-04-29 20:16:15 [server-node-balance] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[DEFAULT_SERVER] hostId:[1917190112164495360]
2025-04-29 20:16:15 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-29 20:16:15 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85]]
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.4.0
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-04-29 20:17:32 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1917183799541387264]
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.4.0
2025-04-29 20:17:32 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-04-29 20:17:32 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-29 20:17:32 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.

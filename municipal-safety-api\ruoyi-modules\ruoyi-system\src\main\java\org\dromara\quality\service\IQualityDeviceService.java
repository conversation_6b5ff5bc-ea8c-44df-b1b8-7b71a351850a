package org.dromara.quality.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.quality.domain.bo.QualityDeviceBo;
import org.dromara.quality.domain.vo.QualityDeviceVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * 设备管理Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IQualityDeviceService {

    /**
     * 查询设备管理
     *
     * @param deviceId 设备ID
     * @return 设备管理
     */
    QualityDeviceVo queryById(Long deviceId);

    /**
     * 查询设备管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备管理集合
     */
    TableDataInfo<QualityDeviceVo> queryPageList(QualityDeviceBo bo, PageQuery pageQuery);

    /**
     * 查询设备管理列表
     *
     * @param bo 查询条件
     * @return 设备管理集合
     */
    List<QualityDeviceVo> queryList(QualityDeviceBo bo);

    /**
     * 新增设备管理
     *
     * @param bo 设备管理
     * @return 结果
     */
    Boolean insertByBo(QualityDeviceBo bo);

    /**
     * 修改设备管理
     *
     * @param bo 设备管理
     * @return 结果
     */
    Boolean updateByBo(QualityDeviceBo bo);

    /**
     * 校验并批量删除设备管理信息
     *
     * @param ids     需要删除的设备管理主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 生成设备编号
     *
     * @param deviceType 设备类型（10xx：靠尺、20xx：角尺、30xx：测距仪、40xx：楼板测厚仪、50xx：钢筋检测仪、60xx：回弹仪）
     * @return 设备编号
     */
    String generateDeviceCode(String deviceType);

    /**
     * 校验设备编号是否唯一
     *
     * @param deviceCode 设备编号
     * @param deviceId   设备ID
     * @return 结果
     */
    Boolean checkDeviceCodeUnique(String deviceCode, Long deviceId);

    /**
     * 上传设备图片
     *
     * @param deviceId 设备ID（可为空，新增时为空）
     * @param file     图片文件
     * @return OSS ID
     */
    Long uploadDeviceImage(Long deviceId, MultipartFile file);

    /**
     * 上传使用说明书
     *
     * @param deviceId 设备ID（可为空，新增时为空）
     * @param file     说明书文件
     * @return OSS ID
     */
    Long uploadManualFile(Long deviceId, MultipartFile file);

} 
{"doc": " 通用 部门服务\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectDeptNameByIds", "paramTypes": ["java.lang.String"], "doc": " 通过部门ID查询部门名称\n\n @param deptIds 部门ID串逗号分隔\n @return 部门名称串逗号分隔\n"}, {"name": "selectDeptLeaderById", "paramTypes": ["java.lang.Long"], "doc": " 根据部门ID查询部门负责人\n\n @param deptId 部门ID，用于指定需要查询的部门\n @return 返回该部门的负责人ID\n"}, {"name": "selectDeptsByList", "paramTypes": [], "doc": " 查询部门\n\n @return 部门列表\n"}], "constructors": []}
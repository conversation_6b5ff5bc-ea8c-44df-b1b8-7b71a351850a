package org.dromara.facility.domain.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.facility.domain.LnEdgeGuard;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 绿能临边防护视图对象 ln_edge_guard
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LnEdgeGuard.class)
public class LnEdgeGuardVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 编号
     */
    @ExcelProperty(value = "编号")
    private Long dumpnumber;

    /**
     * 开关状态
     */
    @ExcelProperty(value = "开关状态")
    private Long checkSensor;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private Long longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private Long latitude;

    /**
     * 电池电压
     */
    @ExcelProperty(value = "电池电压")
    private Long batvolt;

    /**
     * 电池电量
     */
    @ExcelProperty(value = "电池电量")
    private Long batPercent;

    /**
     * 报警信息
     */
    @ExcelProperty(value = "报警信息")
    private Long alarmInfo;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String devNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

package org.dromara.person.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.person.domain.SysPerson;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class SysPersonBoToSysPersonMapperImpl implements SysPersonBoToSysPersonMapper {

    @Override
    public SysPerson convert(SysPersonBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysPerson sysPerson = new SysPerson();

        sysPerson.setSearchValue( arg0.getSearchValue() );
        sysPerson.setCreateDept( arg0.getCreateDept() );
        sysPerson.setCreateBy( arg0.getCreateBy() );
        sysPerson.setCreateTime( arg0.getCreateTime() );
        sysPerson.setUpdateBy( arg0.getUpdateBy() );
        sysPerson.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysPerson.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysPerson.setPersonId( arg0.getPersonId() );
        sysPerson.setEnterpriseId( arg0.getEnterpriseId() );
        sysPerson.setUserId( arg0.getUserId() );
        sysPerson.setName( arg0.getName() );
        sysPerson.setIdCard( arg0.getIdCard() );
        sysPerson.setPhone( arg0.getPhone() );
        sysPerson.setNativePlace( arg0.getNativePlace() );
        sysPerson.setGender( arg0.getGender() );
        sysPerson.setPoliticalStatus( arg0.getPoliticalStatus() );
        sysPerson.setEducation( arg0.getEducation() );
        sysPerson.setHeadImgId( arg0.getHeadImgId() );
        sysPerson.setHeadImgMini( arg0.getHeadImgMini() );

        return sysPerson;
    }

    @Override
    public SysPerson convert(SysPersonBo arg0, SysPerson arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setPersonId( arg0.getPersonId() );
        arg1.setEnterpriseId( arg0.getEnterpriseId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setName( arg0.getName() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setNativePlace( arg0.getNativePlace() );
        arg1.setGender( arg0.getGender() );
        arg1.setPoliticalStatus( arg0.getPoliticalStatus() );
        arg1.setEducation( arg0.getEducation() );
        arg1.setHeadImgId( arg0.getHeadImgId() );
        arg1.setHeadImgMini( arg0.getHeadImgMini() );

        return arg1;
    }
}

-- AI隐患分析任务状态字典更新脚本
-- 添加新的状态值以支持新的AI分析响应码规则

-- 确保字典类型存在
INSERT INTO sys_dict_type (dict_id, tenant_id, dict_name, dict_type, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES (100, '000000', 'AI隐患分析任务状态', 'ai_haz_analysis_tasks_status', 103, 1, NOW(), NULL, NULL, 'AI隐患分析任务状态列表')
ON DUPLICATE KEY UPDATE dict_name = 'AI隐患分析任务状态';

-- 添加原有状态字典数据（如果不存在）
INSERT INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES 
(1001, '000000', 1, '待AI分析', 'PENDING_AI_ANALYSIS', 'ai_haz_analysis_tasks_status', '', 'info', 'N', 103, 1, NOW(), NULL, NULL, '待AI分析'),
(1002, '000000', 2, 'AI分析中', 'AI_ANALYSING', 'ai_haz_analysis_tasks_status', '', 'warning', 'N', 103, 1, NOW(), NULL, NULL, 'AI分析中'),
(1003, '000000', 3, 'AI分析完成', 'AI_ANALYSIS_COMPLETED', 'ai_haz_analysis_tasks_status', '', 'success', 'N', 103, 1, NOW(), NULL, NULL, 'AI分析完成'),
(1004, '000000', 4, '待专家确认', 'PENDING_EXPERT_CONFIRMATION', 'ai_haz_analysis_tasks_status', '', 'primary', 'N', 103, 1, NOW(), NULL, NULL, '待专家确认'),
(1005, '000000', 5, '专家已确认', 'EXPERT_CONFIRMED', 'ai_haz_analysis_tasks_status', '', 'success', 'N', 103, 1, NOW(), NULL, NULL, '专家已确认'),
(1006, '000000', 6, '任务完成/结果已处理', 'TASK_COMPLETED', 'ai_haz_analysis_tasks_status', '', 'success', 'N', 103, 1, NOW(), NULL, NULL, '任务完成/结果已处理')
ON DUPLICATE KEY UPDATE dict_label = VALUES(dict_label);

-- 添加新的状态字典数据（使用简洁的标签以优化前端显示）
INSERT INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES
(1007, '000000', 7, '有隐患', 'AI_ANALYSIS_COMPLETED', 'ai_haz_analysis_tasks_status', '', 'danger', 'N', 103, 1, NOW(), NULL, NULL, 'AI分析完成-存在隐患'),
(1008, '000000', 8, '非工地现场', 'AI_ANALYSIS_NOT_CONSTRUCTION_SITE', 'ai_haz_analysis_tasks_status', '', 'warning', 'N', 103, 1, NOW(), NULL, NULL, 'AI分析完成-当前不是工地现场请重新上传照片'),
(1009, '000000', 9, '无安全隐患', 'AI_ANALYSIS_NO_SAFETY_HAZARDS', 'ai_haz_analysis_tasks_status', '', 'success', 'N', 103, 1, NOW(), NULL, NULL, 'AI分析完成-当前没有安全隐患')
ON DUPLICATE KEY UPDATE dict_label = VALUES(dict_label);

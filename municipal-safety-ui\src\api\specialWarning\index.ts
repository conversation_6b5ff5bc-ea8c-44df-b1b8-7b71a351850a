import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  HazardousItemsSpecialWarningVO,
  HazardousItemsSpecialWarningForm,
  HazardousItemsSpecialWarningQuery
} from '@/api/specialWarning/types';

/**
 * 查询特殊预警列表
 * @param query
 * @returns {*}
 */

export const listHazardousItemsSpecialWarning = (query?: HazardousItemsSpecialWarningQuery): AxiosPromise<HazardousItemsSpecialWarningVO[]> => {
  return request({
    url: '/system/hazardousItemsSpecialWarning/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询特殊预警详细
 * @param warningId
 */
export const getHazardousItemsSpecialWarning = (warningId: string | number): AxiosPromise<HazardousItemsSpecialWarningVO> => {
  return request({
    url: '/system/hazardousItemsSpecialWarning/' + warningId,
    method: 'get'
  });
};

/**
 * 新增特殊预警
 * @param data
 */
export const addHazardousItemsSpecialWarning = (data: HazardousItemsSpecialWarningForm) => {
  return request({
    url: '/system/hazardousItemsSpecialWarning',
    method: 'post',
    data: data
  });
};

/**
 * 修改特殊预警
 * @param data
 */
export const updateHazardousItemsSpecialWarning = (data: HazardousItemsSpecialWarningForm) => {
  return request({
    url: '/system/hazardousItemsSpecialWarning',
    method: 'put',
    data: data
  });
};

/**
 * 删除特殊预警
 * @param warningId
 */
export const delHazardousItemsSpecialWarning = (warningId: string | number | Array<string | number>) => {
  return request({
    url: '/system/hazardousItemsSpecialWarning/' + warningId,
    method: 'delete'
  });
};

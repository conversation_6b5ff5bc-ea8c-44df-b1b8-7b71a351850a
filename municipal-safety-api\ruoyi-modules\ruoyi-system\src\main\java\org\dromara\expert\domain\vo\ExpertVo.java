package org.dromara.expert.domain.vo;

import org.dromara.expert.domain.Expert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.expert.domain.Field;
import org.dromara.expert.domain.Project;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 *  专家主视图对象 expert
 * @date 2025-05-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Expert.class)
public class ExpertVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     *  主键
     */
    private Long expertId;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 身份证件号
     */
    @ExcelProperty(value = "身份证件号")
    private String idCard;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_user_sex")
    private String sex;

    /**
     * 工作单位
     */
    @ExcelProperty(value = "工作单位")
    private String workUnit;

    /**
     * 电话
     */
    @ExcelProperty(value = "电话")
    private String phone;

    /**
     * 简介
     */
    @ExcelProperty(value = "简介")
    private String introduce;

    /**
     * 职称
     */
    @ExcelProperty(value = "职称")
    private String title;

    /**
     * 所在省
     */
    @ExcelProperty(value = "所在省")
    private String province;

    /**
     * 所在市
     */
    @ExcelProperty(value = "所在市")
    private String city;

    /**
     * 所在区
     */
    @ExcelProperty(value = "所在区")
    private String area;

    /**
     * 专业
     */
    @ExcelProperty(value = "专业", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "expert_major")
    private String major;

    /**
     * 行业
     */
    @ExcelProperty(value = "行业", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "expert_industry")
    private String industry;


    /** 专家类型 */
    @ExcelProperty(value = "专家类型")
    @ExcelDictFormat(dictType = "expert_type")
    private String type;


    /**
     *  所属项目
     */
    private List<Project> expertProjectList;


    /**
     *  专业领域
     */
    private List<Field> expertFieldList;

    /**
     * 所在省
     */
    @ExcelProperty(value = "所在省")
    private String provinceName;

    /**
     * 所在市
     */
    @ExcelProperty(value = "所在市")
    private String cityName;

    /**
     * 所在区
     */
    @ExcelProperty(value = "所在区")
    private String areaName;

    /** 头像 */
    private String avatar;

}

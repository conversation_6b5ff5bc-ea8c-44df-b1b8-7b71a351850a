package org.dromara.flow.service;

import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.flow.domain.PrjHazardousItemsSpecialist;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistBo;
import org.dromara.flow.domain.dto.SpecialistThreeDTO;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.flow.domain.vo.SpecialistThreeVO;
import org.dromara.workflow.domain.vo.FlowHisTaskVo;

import java.util.Collection;
import java.util.List;

/**
 * 省厅自动工单Service接口
 *
 * <AUTHOR> Li
 * @date 2025-06-20
 */
public interface IPrjHazardousItemsSpecialistService {

    /**
     * 查询省厅自动工单
     *
     * @param id 主键
     * @return 省厅自动工单
     */
    PrjHazardousItemsSpecialistVo queryById(Long id);

    /**
     * 分页查询省厅自动工单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 省厅自动工单分页列表
     */
    TableDataInfo<PrjHazardousItemsSpecialistVo> queryPageList(PrjHazardousItemsSpecialistBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的省厅自动工单列表
     *
     * @param bo 查询条件
     * @return 省厅自动工单列表
     */
    List<PrjHazardousItemsSpecialistVo> queryList(PrjHazardousItemsSpecialistBo bo);

    /**
     * 新增省厅自动工单
     *
     * @param bo 省厅自动工单
     * @return 是否新增成功
     */
    Boolean insertByBo(PrjHazardousItemsSpecialistBo bo);

    /**
     * 修改省厅自动工单
     *
     * @param bo 省厅自动工单
     * @return 是否修改成功
     */
    Boolean updateByBo(PrjHazardousItemsSpecialistBo bo);

    /**
     * 校验并批量删除省厅自动工单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    PrjHazardousItemsSpecialistVo getDetail(String taskId);

    Boolean saveThree(SpecialistThreeDTO dto);

    /**
     * 第三步厅局节点数据获取
     *
     * @param taskId
     * @return
     */
    SpecialistThreeVO threeDetail(String taskId);

    /**
     * 第三步厅局节点数据保存
     * @param specialist
     * @return
     */
    boolean saveThree2(PrjHazardousItemsSpecialist specialist);

    FlowHisTaskVo getInstance(String taskId);
}

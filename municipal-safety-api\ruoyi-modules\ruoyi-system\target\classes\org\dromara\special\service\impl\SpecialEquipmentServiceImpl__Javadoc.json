{"doc": " 特种设备Service业务层处理\n\n <AUTHOR>\n @date 2025-05-14\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询特种设备\n\n @param equipmentId 主键\n @return 特种设备\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.special.domain.bo.SpecialEquipmentBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询特种设备列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 特种设备分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.special.domain.bo.SpecialEquipmentBo"], "doc": " 查询符合条件的特种设备列表\n\n @param bo 查询条件\n @return 特种设备列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.special.domain.bo.SpecialEquipmentBo"], "doc": " 新增特种设备\n\n @param bo 特种设备\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.special.domain.bo.SpecialEquipmentBo"], "doc": " 修改特种设备\n\n @param bo 特种设备\n @return 是否修改成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.special.domain.SpecialEquipment"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除特种设备信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}, {"name": "isValidIdCard", "paramTypes": ["java.lang.String"], "doc": " 验证身份证号是否合法\n @param idCard 身份证号\n @return 是否合法\n"}], "constructors": []}
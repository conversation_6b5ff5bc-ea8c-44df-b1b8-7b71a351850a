package org.dromara.facility.domain.bo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson2.annotation.JSONField;
import org.dromara.facility.domain.LnHighFormworkReal;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 绿能高支模实时数据业务对象 ln_high_formwork_real
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LnHighFormworkReal.class, reverseConvertGenerate = false)
public class LnHighFormworkRealBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 设备id
     */
    private Long eid;

    /**
     * 模板沉降
     */
    private String settlement;

    /**
     * 立杆倾角
     */
    private String inclinationAngleOfVerticalPole;

    /**
     * 水平倾角
     */
    private String horizontalInclination;

    /**
     * 称重
     */
    private String bearing;

    /**
     * 水平位移
     */
    private String horizontalDisplacement;

    /**
     * 垂直位移
     */
    private String verticalDisplacement;

    /**
     * 上报时间
     */
    @JSONField(format = DatePattern.NORM_DATETIME_PATTERN)
    private Date eventTime;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

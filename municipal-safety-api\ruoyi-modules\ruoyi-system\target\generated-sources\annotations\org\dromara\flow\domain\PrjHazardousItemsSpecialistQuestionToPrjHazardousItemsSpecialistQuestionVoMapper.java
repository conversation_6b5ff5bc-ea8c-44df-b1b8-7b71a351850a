package org.dromara.flow.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistQuestionBoToPrjHazardousItemsSpecialistQuestionMapper;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistQuestionVo;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistQuestionVoToPrjHazardousItemsSpecialistQuestionMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjHazardousItemsSpecialistQuestionVoToPrjHazardousItemsSpecialistQuestionMapper.class,PrjHazardousItemsSpecialistQuestionBoToPrjHazardousItemsSpecialistQuestionMapper.class},
    imports = {}
)
public interface PrjHazardousItemsSpecialistQuestionToPrjHazardousItemsSpecialistQuestionVoMapper extends BaseMapper<PrjHazardousItemsSpecialistQuestion, PrjHazardousItemsSpecialistQuestionVo> {
}

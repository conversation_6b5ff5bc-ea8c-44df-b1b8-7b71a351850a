package org.dromara.projects.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjSafeSupervision;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjSafeSupervisionBoToPrjSafeSupervisionMapperImpl implements PrjSafeSupervisionBoToPrjSafeSupervisionMapper {

    @Override
    public PrjSafeSupervision convert(PrjSafeSupervisionBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjSafeSupervision prjSafeSupervision = new PrjSafeSupervision();

        prjSafeSupervision.setSearchValue( arg0.getSearchValue() );
        prjSafeSupervision.setCreateDept( arg0.getCreateDept() );
        prjSafeSupervision.setCreateBy( arg0.getCreateBy() );
        prjSafeSupervision.setCreateTime( arg0.getCreateTime() );
        prjSafeSupervision.setUpdateBy( arg0.getUpdateBy() );
        prjSafeSupervision.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            prjSafeSupervision.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        prjSafeSupervision.setSupervisionId( arg0.getSupervisionId() );
        prjSafeSupervision.setUserName( arg0.getUserName() );
        prjSafeSupervision.setUserPositionName( arg0.getUserPositionName() );
        prjSafeSupervision.setFace( arg0.getFace() );
        prjSafeSupervision.setCertificate( arg0.getCertificate() );
        prjSafeSupervision.setOpenTaskId( arg0.getOpenTaskId() );

        return prjSafeSupervision;
    }

    @Override
    public PrjSafeSupervision convert(PrjSafeSupervisionBo arg0, PrjSafeSupervision arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSupervisionId( arg0.getSupervisionId() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setUserPositionName( arg0.getUserPositionName() );
        arg1.setFace( arg0.getFace() );
        arg1.setCertificate( arg0.getCertificate() );
        arg1.setOpenTaskId( arg0.getOpenTaskId() );

        return arg1;
    }
}

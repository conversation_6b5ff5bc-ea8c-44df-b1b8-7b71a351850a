package org.dromara.quality.domain;

import javax.annotation.processing.Generated;
import org.dromara.quality.domain.vo.QualityDeviceVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class QualityDeviceToQualityDeviceVoMapperImpl implements QualityDeviceToQualityDeviceVoMapper {

    @Override
    public QualityDeviceVo convert(QualityDevice arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QualityDeviceVo qualityDeviceVo = new QualityDeviceVo();

        qualityDeviceVo.setDeviceId( arg0.getDeviceId() );
        qualityDeviceVo.setDeviceName( arg0.getDeviceName() );
        qualityDeviceVo.setSpecification( arg0.getSpecification() );
        qualityDeviceVo.setDeviceType( arg0.getDeviceType() );
        qualityDeviceVo.setDeviceCode( arg0.getDeviceCode() );
        qualityDeviceVo.setQuantity( arg0.getQuantity() );
        qualityDeviceVo.setDeviceImageOssId( arg0.getDeviceImageOssId() );
        qualityDeviceVo.setDeviceDescription( arg0.getDeviceDescription() );
        qualityDeviceVo.setDevicePurpose( arg0.getDevicePurpose() );
        qualityDeviceVo.setUsageInstructions( arg0.getUsageInstructions() );
        qualityDeviceVo.setManualFileOssId( arg0.getManualFileOssId() );
        qualityDeviceVo.setStatus( arg0.getStatus() );
        qualityDeviceVo.setCreateTime( arg0.getCreateTime() );
        qualityDeviceVo.setCreateBy( arg0.getCreateBy() );
        qualityDeviceVo.setUpdateTime( arg0.getUpdateTime() );
        qualityDeviceVo.setUpdateBy( arg0.getUpdateBy() );

        return qualityDeviceVo;
    }

    @Override
    public QualityDeviceVo convert(QualityDevice arg0, QualityDeviceVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setDeviceId( arg0.getDeviceId() );
        arg1.setDeviceName( arg0.getDeviceName() );
        arg1.setSpecification( arg0.getSpecification() );
        arg1.setDeviceType( arg0.getDeviceType() );
        arg1.setDeviceCode( arg0.getDeviceCode() );
        arg1.setQuantity( arg0.getQuantity() );
        arg1.setDeviceImageOssId( arg0.getDeviceImageOssId() );
        arg1.setDeviceDescription( arg0.getDeviceDescription() );
        arg1.setDevicePurpose( arg0.getDevicePurpose() );
        arg1.setUsageInstructions( arg0.getUsageInstructions() );
        arg1.setManualFileOssId( arg0.getManualFileOssId() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );

        return arg1;
    }
}

{"doc": " 特种作业人员信息对象 special_operation_personnel\n\n <AUTHOR>\n @date 2025-05-13\n", "fields": [{"name": "sopId", "doc": " ID\n"}, {"name": "certificateNumber", "doc": " 证书编号\n"}, {"name": "name", "doc": " 姓名\n"}, {"name": "idCard", "doc": " 身份证号\n"}, {"name": "gender", "doc": " 性别\n"}, {"name": "birthdate", "doc": " 出生日期\n"}, {"name": "operationCategory", "doc": " 操作类别\n"}, {"name": "issuer", "doc": " 发证机关\n"}, {"name": "firstIssueDate", "doc": " 初次领证日期\n"}, {"name": "lastIssueDate", "doc": " 最近发证日期\n"}, {"name": "validityStart", "doc": " 有效期开始\n"}, {"name": "validityEnd", "doc": " 有效期截止\n"}, {"name": "status", "doc": " 证书状态(有效,无效,挂失,注销)\n"}, {"name": "electronicLicenseUrl", "doc": " 电子证照链接\n"}, {"name": "electronicLicenseId", "doc": " 电子证照文件ID\n"}, {"name": "projectId", "doc": " 项目ID\n"}, {"name": "delFlag", "doc": " 删除标志 (0代表存在 1代表删除)\n"}, {"name": "companyName", "doc": "所属单位名称 "}, {"name": "companyCode", "doc": "单位统一社会信用代码 "}, {"name": "companyType", "doc": "单位的类别分类 "}, {"name": "personType", "doc": "人员类别 "}], "enumConstants": [], "methods": [], "constructors": []}
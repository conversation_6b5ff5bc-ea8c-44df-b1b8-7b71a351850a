package org.dromara.facility.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.facility.domain.bo.JlLifterRealBo;
import org.dromara.facility.domain.vo.JlLifterRealVo;
import org.dromara.facility.service.IJlLifterRealService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 升降机实时数据
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/lifterReal")
public class JlLifterRealController extends BaseController {

    private final IJlLifterRealService jlLifterRealService;

    /**
     * 查询升降机实时数据列表
     */
    @GetMapping("/list")
    public TableDataInfo<JlLifterRealVo> list(JlLifterRealBo bo, PageQuery pageQuery) {
        return jlLifterRealService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取升降机实时数据详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<JlLifterRealVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(jlLifterRealService.queryById(id));
    }
}

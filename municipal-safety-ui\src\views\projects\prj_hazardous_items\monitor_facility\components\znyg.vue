<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="smokeList">
      <el-table-column label="编号" align="center" prop="deviceNo" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="事件类型" align="center" prop="eventCode">
        <template v-slot="scope">
          <span v-if="scope.row.eventCode == 'Z130'">报警恢复</span>
          <span v-if="scope.row.eventCode == 'Z129'">报警</span>
          <span v-if="scope.row.eventCode == 'Z135'">消音开始</span>
          <span v-if="scope.row.eventCode == 'Z133'">按键自检</span>
          <span v-if="scope.row.eventCode == 'Z136'">消音结束</span>
        </template>
      </el-table-column>
      <el-table-column label="事件内容" align="center" prop="eventContent" />
      <el-table-column label="上报时间" align="center" prop="eventTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.eventTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

  </div>
</template>

<script>
import { listSmoke } from "@/api/projects/facility/index";

export default {
  name: "Smoke",
  props: {
    devNo: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 绿能烟感表格数据
      smokeList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        devNo: null
      },
    };
  },
  created () {
    this.getList();
  },
  methods: {
    /** 查询绿能烟感列表 */
    async getList () {
      this.smokeList = []
      this.loading = true;
      this.queryParams.devNo = this.devNo;
      const res = listSmoke(this.queryParams)
      this.smokeList = res.rows;
      this.total = res.total;
      this.loading = false;
    },
  }
};
</script>

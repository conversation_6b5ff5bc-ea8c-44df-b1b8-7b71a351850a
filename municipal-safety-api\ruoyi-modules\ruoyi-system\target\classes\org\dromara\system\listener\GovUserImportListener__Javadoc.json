{"doc": " 政府用户自定义导入\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "processDepartment", "paramTypes": ["org.dromara.system.domain.vo.GovUserImportVo"], "doc": " 处理部门信息 - 支持层级结构\n"}, {"name": "determineDeptType", "paramTypes": ["java.lang.String"], "doc": " 根据监督级别编码确定部门类型\n\n @param supervisionLevelCode 监督级别编码\n @return 部门类型\n"}, {"name": "determineRoleId", "paramTypes": ["java.lang.String"], "doc": " 根据监督级别编码确定角色ID\n\n @param supervisionLevelCode 监督级别编码\n @return 角色ID\n"}, {"name": "assignRoleToUser", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 为新用户分配角色\n\n @param userId               用户ID\n @param supervisionLevelCode 监督级别编码\n"}, {"name": "updateUserRole", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 更新用户角色分配\n\n @param userId               用户ID\n @param supervisionLevelCode 监督级别编码\n"}, {"name": "determineParentDeptId", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 根据监督级别和地区信息确定父级部门ID\n"}, {"name": "findOrCreateCityDeptParentId", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 查找或创建市级部门的父级部门ID\n"}, {"name": "findOrCreateDistrictDeptParentId", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 查找或创建区县级部门的父级部门ID\n"}, {"name": "createProvinceDept", "paramTypes": ["java.lang.String"], "doc": " 创建省级部门\n"}, {"name": "createCityDept", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 创建市级节点\n"}, {"name": "createCityBuildDept", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 创建市级住建部门\n"}, {"name": "createCityQsDept", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 创建市级质监站\n"}, {"name": "getCityNameByCode", "paramTypes": ["java.lang.String"], "doc": " 根据城市编码获取城市名称\n"}], "constructors": []}
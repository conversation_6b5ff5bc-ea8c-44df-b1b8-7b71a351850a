{"doc": " 工作流常量\n\n <AUTHOR>\n", "fields": [{"name": "INITIATOR", "doc": " 流程发起人\n"}, {"name": "PROCESS_INSTANCE_ID", "doc": " 流程实例id\n"}, {"name": "BUSINESS_ID", "doc": " 业务id\n"}, {"name": "TASK_ID", "doc": " 任务id\n"}, {"name": "DELEGATE_TASK", "doc": " 委托\n"}, {"name": "TRANSFER_TASK", "doc": " 转办\n"}, {"name": "ADD_SIGNATURE", "doc": " 加签\n"}, {"name": "REDUCTION_SIGNATURE", "doc": " 减签\n"}, {"name": "CATEGORY_ID_TO_NAME", "doc": " 流程分类Id转名称\n"}, {"name": "FLOW_CATEGORY_NAME", "doc": " 流程分类名称\n"}, {"name": "FLOW_CATEGORY_ID", "doc": " 默认租户OA申请分类id\n"}], "enumConstants": [], "methods": [], "constructors": []}
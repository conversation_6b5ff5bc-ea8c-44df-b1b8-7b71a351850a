package org.dromara.ai.service;

import org.dromara.ai.domain.bo.AiHazAnalysisTasksResultBo;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksResultVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 隐患AI分析结果Service接口
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface IAiHazAnalysisTasksResultService {

    /**
     * 查询隐患AI分析结果
     *
     * @param resultId 主键
     * @return 隐患AI分析结果
     */
    AiHazAnalysisTasksResultVo queryById(Long resultId);

    /**
     * 分页查询隐患AI分析结果列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 隐患AI分析结果分页列表
     */
    TableDataInfo<AiHazAnalysisTasksResultVo> queryPageList(AiHazAnalysisTasksResultBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的隐患AI分析结果列表
     *
     * @param bo 查询条件
     * @return 隐患AI分析结果列表
     */
    List<AiHazAnalysisTasksResultVo> queryList(AiHazAnalysisTasksResultBo bo);

    /**
     * 新增隐患AI分析结果
     *
     * @param bo 隐患AI分析结果
     * @return 是否新增成功
     */
    Boolean insertByBo(AiHazAnalysisTasksResultBo bo);

    /**
     * 修改隐患AI分析结果
     *
     * @param bo 隐患AI分析结果
     * @return 是否修改成功
     */
    Boolean updateByBo(AiHazAnalysisTasksResultBo bo);

    /**
     * 校验并批量删除隐患AI分析结果信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

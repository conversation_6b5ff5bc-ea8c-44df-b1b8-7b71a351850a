package org.dromara.dp.controller;


import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.dp.domain.bo.DataViewBo;
import org.dromara.monito.service.IDeviceMonitoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 监控管理
 *
 * <AUTHOR> Li
 * @date 2025-05-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/dataView")
public class DataViewController extends BaseController {

    private final IDeviceMonitoService deviceMonitoService;


    @SaIgnore
    @GetMapping("/getItemNum")
    public R<List<Map<String, Object>>> getItemNum(DataViewBo bo) {
        List<Map<String, Object>> list = deviceMonitoService.getItemNum(bo);
        return R.ok(list);
    }


    @SaIgnore
    @GetMapping("/getDangerNum")
    public R<List<Map<String, Object>>> getDangerNum(DataViewBo bo) {
        List<Map<String, Object>> list = deviceMonitoService.getDangerNum(bo);
        return R.ok(list);
    }

    @SaIgnore
    @GetMapping("/getProItemList")
    public R<List<Map<String, Object>>> getProItemList(DataViewBo bo) {
        List<Map<String, Object>> list = deviceMonitoService.getProItemList(bo);
        return R.ok(list);
    }

    @SaIgnore
    @GetMapping("/getYearItemNum")
    public R<Map<String, Object>> getYearItemNum(DataViewBo bo) {
        Map<String, Object> map = deviceMonitoService.getYearItemNum(bo);
        return R.ok(map);
    }

    @SaIgnore
    @GetMapping("/getAreaItemNum")
    public R<Map<String, Object>> getAreaItemNum(DataViewBo bo) {
        Map<String, Object> map = deviceMonitoService.getAreaItemNum(bo);
        return R.ok(map);
    }
}

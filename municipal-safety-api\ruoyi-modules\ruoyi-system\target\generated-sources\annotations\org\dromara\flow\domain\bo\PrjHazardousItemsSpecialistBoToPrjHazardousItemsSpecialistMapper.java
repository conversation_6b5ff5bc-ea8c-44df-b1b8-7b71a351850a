package org.dromara.flow.domain.bo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.PrjHazardousItemsSpecialist;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {},
    imports = {}
)
public interface PrjHazardousItemsSpecialistBoToPrjHazardousItemsSpecialistMapper extends BaseMapper<PrjHazardousItemsSpecialistBo, PrjHazardousItemsSpecialist> {
}

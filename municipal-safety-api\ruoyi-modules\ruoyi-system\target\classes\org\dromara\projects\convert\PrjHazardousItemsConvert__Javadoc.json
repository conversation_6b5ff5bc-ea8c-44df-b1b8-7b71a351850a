{"doc": " <AUTHOR>\n @date 2025/5/14 19:00\n @Description TODO\n @Version 1.0\n", "fields": [], "enumConstants": [], "methods": [{"name": "page2voPage", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page"], "doc": " 分页转换\n\n @param itemsPage\n @return\n"}, {"name": "items2vo", "paramTypes": ["org.dromara.projects.domain.PrjHazardousItems"], "doc": " 单个转换\n\n @param items\n @return\n"}, {"name": "listItems2voList", "paramTypes": ["java.util.List"], "doc": " 列表转换\n\n @param itemsList\n @return\n"}], "constructors": []}
package org.dromara.facility.domain;

import cn.hutool.core.date.DatePattern;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 绿能高支模实时数据对象 ln_high_formwork_real
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ln_high_formwork_real")
public class LnHighFormworkReal extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 设备id
     */
    private Long eid;

    /**
     * 模板沉降
     */
    private String settlement;

    /**
     * 立杆倾角
     */
    private String inclinationAngleOfVerticalPole;

    /**
     * 水平倾角
     */
    private String horizontalInclination;

    /**
     * 称重
     */
    private String bearing;

    /**
     * 水平位移
     */
    private String horizontalDisplacement;

    /**
     * 垂直位移
     */
    private String verticalDisplacement;

    /**
     * 上报时间
     */
    private Date eventTime;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

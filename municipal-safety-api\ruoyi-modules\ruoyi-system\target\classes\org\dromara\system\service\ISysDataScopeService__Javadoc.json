{"doc": " 通用 数据权限 服务\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "getRoleCustom", "paramTypes": ["java.lang.Long"], "doc": " 获取角色自定义权限\n\n @param roleId 角色id\n @return 部门id组\n"}, {"name": "getDeptAndChild", "paramTypes": ["java.lang.Long"], "doc": " 获取部门及以下权限\n\n @param deptId 部门id\n @return 部门id组\n"}, {"name": "getExpertProjects", "paramTypes": ["java.lang.Long"], "doc": " 获取专家可访问的项目ID列表\n\n @param userId 用户ID\n @return 项目ID组（用逗号分隔）\n"}], "constructors": []}
package org.dromara.projects.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.secure.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.helper.DataPermissionHelper;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.dromara.expert.domain.Expert;
import org.dromara.expert.mapper.ExpertMapper;
import org.dromara.person.domain.SysPerson;
import org.dromara.person.domain.bo.SysPersonBo;
import org.dromara.person.domain.vo.SysPersonVo;
import org.dromara.person.mapper.SysPersonMapper;
import org.dromara.plan.domain.PrjPatrolPlan;
import org.dromara.plan.mapper.PrjPatrolPlanMapper;
import org.dromara.projects.domain.bo.PrjPersonnelBo;
import org.dromara.projects.domain.bo.PrjProjectsBo;
import org.dromara.projects.domain.vo.PrjPersonnelVo;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.service.IPrjPersonnelService;
import org.dromara.projects.service.IPrjProjectsService;
import org.dromara.projects.service.IPrjProjectsSyncService;
import org.dromara.system.domain.SysEnterpriseInfo;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.SysUserRole;
import org.dromara.system.domain.bo.SysDeptBo;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.domain.vo.EnterpriseNameAndId;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.mapper.SysRoleMapper;
import org.dromara.system.mapper.SysUserMapper;
import org.dromara.system.mapper.SysUserRoleMapper;
import org.dromara.system.service.ISysDeptService;
import org.dromara.system.service.ISysEnterpriseInfoService;
import org.dromara.system.service.ISysRoleService;
import org.dromara.system.service.ISysUserService;
import org.dromara.util.SadaSignUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 项目录入
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/projects/prj_projects")
public class PrjProjectsController extends BaseController {

    private final IPrjProjectsService prjProjectsService;
    private final IPrjPersonnelService prjPersonnelService;
    private final IPrjProjectsSyncService prjProjectsSyncService;
    private final ISysDeptService deptService;
    private final SysUserRoleMapper sysUserRoleMapper;
    private final SysRoleMapper sysRoleMapper;
    private final PrjPatrolPlanMapper patrolPlanMapper;
    private final ExpertMapper expertMapper;
    private final SysUserMapper sysUserMapper;
    private final SysPersonMapper sysPersonMapper;
    private final ISysEnterpriseInfoService sysEnterpriseInfoService;

    private final ISysRoleService sysRoleService;
    private final ISysUserService sysUserService;
    private final SadaSignUtil sadaSignUtil;

    /**
     * 查询项目录入列表
     */
    @GetMapping("/list")
    public TableDataInfo<PrjProjectsVo> list(PrjProjectsBo bo, PageQuery pageQuery) {
        // 如果当前用户是系统角色“project_admin”，则仅返回他作为“项目管理员”的项目
        try {
            Long userId = LoginHelper.getUserId();
            List<SysRoleVo> roles = sysRoleMapper.selectRolesByUserId(userId);
            boolean isProjectAdminRole = roles != null && roles.stream()
                .anyMatch(r -> "project_admin".equals(r.getRoleKey()));
            if (isProjectAdminRole) {
                PrjPersonnelBo pbo = new PrjPersonnelBo();
                pbo.setUserId(userId);
                // 项目内岗位字典值：项目管理员
                pbo.setRoleOnProject("PROJECT_ADMIN");
                List<PrjPersonnelVo> personnel = prjPersonnelService.queryList(pbo);
                List<String> pidList = personnel.stream()
                    .map(PrjPersonnelVo::getProjectId)
                    .filter(Objects::nonNull)
                    .map(String::valueOf)
                    .distinct()
                    .collect(Collectors.toList());
                bo.setProjectIdList(pidList);
            }
        } catch (Exception ignore) {
            // 保底不影响原有查询
        }
        return prjProjectsService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询项目录入列表
     */
    @GetMapping("/listAi")
    public TableDataInfo<PrjProjectsVo> listAi(PrjProjectsBo bo, PageQuery pageQuery) {
        return prjProjectsService.queryPageListSq(bo, pageQuery);
    }

    /**
     * 查询项目录入列表
     */
    @GetMapping("/listAiAll")
    public R<List<PrjProjectsVo>> listAiAll(PrjProjectsBo bo) {
        return R.ok(prjProjectsService.listAiAll(bo));
    }

    public PrjProjectsBo role(PrjProjectsBo bo) {
        // 获取用户id
        Long userId = LoginHelper.getUserId();
        SysUser sysUser = sysUserMapper.selectById(userId);
        // 查询关联角色
        SysUserRole sysUserRole = sysUserRoleMapper.selectOne(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        SysRoleVo sysRoleVo = sysRoleMapper.selectRoleById(sysUserRole.getRoleId());
        switch (sysRoleVo.getRoleKey()) {
            case "expert": {
                Expert expert = expertMapper.selectOne(new LambdaQueryWrapper<Expert>().eq(Expert::getIdCard, sysUser.getUserName()));
                List<PrjPatrolPlan> prjPatrolPlans = patrolPlanMapper.selectList(new LambdaQueryWrapper<PrjPatrolPlan>()
                    .like(PrjPatrolPlan::getExpertIds, expert.getExpertId()));
//                    .le(PrjPatrolPlan::getBeginTime, DateUtil.date())
//                    .ge(PrjPatrolPlan::getEndTime, DateUtil.date()));
                List<String> projectIdsList = prjPatrolPlans.stream()
                    .map(PrjPatrolPlan::getProjectIds) // 提取 projectIds 字段
                    .filter(StringUtils::isNotEmpty)   // 非空校验
                    .flatMap(projectIds -> Arrays.stream(projectIds.split(","))) // 按逗号分割并展平为流
                    .distinct()                        // 去重
                    .collect(Collectors.toList());
                bo.setProjectIdList(projectIdsList);
                return bo;
            }
            default:
                return bo;
        }
    }


    /**
     * 导出项目录入列表
     */
    @SaCheckPermission("projects:prj_projects:export")
    @Log(title = "项目录入", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PrjProjectsBo bo, HttpServletResponse response) {
        List<PrjProjectsVo> list = prjProjectsService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目录入", PrjProjectsVo.class, response);
    }

    /**
     * 获取项目录入详细信息
     *
     * @param projectId 主键
     */
    @SaCheckPermission("projects:prj_projects:query")
    @GetMapping("/{projectId}")
    public R<PrjProjectsVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long projectId) {
        // 查询项目基本信息
        PrjProjectsVo prjProjectsVo = prjProjectsService.queryById(projectId);

        // 查询项目人员信息
        List<PrjPersonnelVo> personnelList = prjPersonnelService.queryPersonnelListByProjectId(projectId);
        prjProjectsVo.setPersonnelList(personnelList);

        return R.ok(prjProjectsVo);
    }

    /**
     * 新增项目录入
     */
    @SaCheckPermission("projects:prj_projects:add")
    @Log(title = "项目录入", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PrjProjectsBo bo) {
        boolean result = prjProjectsService.insertByBo(bo);
        if (result && StringUtils.isNotEmpty(bo.getPersonIds())) {
            // 处理项目人员关联
            bindProjectPersonnel(bo.getProjectId(), bo.getPersonIds());
        }
        return toAjax(result);
    }

    /**
     * 修改项目录入
     */
    @SaCheckPermission("projects:prj_projects:edit")
    @Log(title = "项目录入", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PrjProjectsBo bo) {
        boolean result = prjProjectsService.updateByBo(bo);
        if (result && StringUtils.isNotEmpty(bo.getPersonIds())) {
            // 处理项目人员关联
            bindProjectPersonnel(bo.getProjectId(), bo.getPersonIds());
        }
        return toAjax(result);
    }

    /**
     * 绑定项目人员（支持单独绑定）
     */
    @SaCheckPermission("projects:prj_projects:bindAdmin")
    @PostMapping("/{projectId}/bindPersonnel")
    public R<Void> bindPersonnel(@PathVariable Long projectId, @RequestBody String personIds) {
        if (projectId == null || org.dromara.common.core.utils.StringUtils.isEmpty(personIds)) {
            return R.fail("参数不能为空");
        }
        bindProjectPersonnel(projectId, personIds);
        return R.ok();
    }

    /**
     * 快捷绑定项目管理员（政府侧使用，支持新建人员）
     */
    @SaCheckPermission("projects:prj_projects:bindAdmin")
    @PostMapping("/{projectId}/bindAdminQuick")
    public R<Void> bindAdminQuick(@PathVariable Long projectId, @RequestBody AdminBindRequest request) {
        if (projectId == null || request == null ||
            StringUtils.isEmpty(request.getName()) ||
            StringUtils.isEmpty(request.getIdCard()) ||
            StringUtils.isEmpty(request.getPhone())) {
            return R.fail("项目ID、姓名、身份证号、手机号不能为空");
        }

        try {
            // 1. 根据身份证号查找或创建人员
            SysPerson person = sysPersonMapper.selectOne(
                new LambdaQueryWrapper<SysPerson>().eq(SysPerson::getIdCard, request.getIdCard())
            );

            if (person == null) {
                // 创建新人员
                person = new SysPerson();
                person.setName(request.getName());
                person.setIdCard(request.getIdCard());
                person.setPhone(request.getPhone());
//                if (request.getOrgId() != null) {
//                    person.setEnterpriseId(request.getOrgId());
//                }
                if (request.getOrgId() != null) {
                    // orgId 是部门ID，需要换算为企业ID（sys_enterprise_info.enterprise_id）
                    SysEnterpriseInfo enterprise = sysEnterpriseInfoService.lambdaQuery()
                        .eq(SysEnterpriseInfo::getDeptId, request.getOrgId())
                        .one();
                    if (enterprise != null) {
                        person.setEnterpriseId(enterprise.getEnterpriseId());
                    }
                }
                sysPersonMapper.insert(person);
            } else {
                // 更新现有人员信息（姓名、手机号可能有变化）
                LambdaUpdateWrapper<SysPerson> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(SysPerson::getPersonId, person.getPersonId())
                    .set(SysPerson::getName, request.getName())
                    .set(SysPerson::getPhone, request.getPhone());
//                if (request.getOrgId() != null) {
//                    updateWrapper.set(SysPerson::getEnterpriseId, request.getOrgId());
//                }
                sysPersonMapper.update(null, updateWrapper);
                // 重新查询更新后的数据
                person = sysPersonMapper.selectById(person.getPersonId());
            }

            // 2. 检查该人员在项目中的现有角色
            PrjPersonnelBo queryBo = new PrjPersonnelBo();
            queryBo.setProjectId(projectId);
            queryBo.setPersonId(person.getPersonId());
            List<PrjPersonnelVo> existingRoles = prjPersonnelService.queryList(queryBo);

            // 检查是否已经是管理员
            boolean isAlreadyAdmin = existingRoles.stream()
                .anyMatch(role -> "PROJECT_ADMIN".equals(role.getRoleOnProject()));

            if (isAlreadyAdmin) {
                return R.fail("该人员已是此项目的管理员");
            }

            // 如果已经在项目中但不是管理员，询问是否要添加管理员角色
            if (!existingRoles.isEmpty()) {
                // 直接添加管理员角色（保留原有角色）
                log.info("人员{}已在项目{}中，添加管理员角色", person.getPersonId(), projectId);
            }

            // 3. 构造 personIds 字符串并调用现有绑定逻辑
            // 格式：personId,roleOnProject,orgId
            Long orgId = request.getOrgId();
            String personIds = person.getPersonId() + ",PROJECT_ADMIN," + (orgId != null ? orgId : "");

            // 调用现有的绑定逻辑（包含自动注册用户和授权的逻辑）
            bindProjectPersonnel(projectId, personIds);

            return R.ok("绑定成功");

        } catch (Exception e) {
            log.error("快捷绑定项目管理员失败", e);
            return R.fail("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 管理员绑定请求对象
     */
    public static class AdminBindRequest {
        private String name;
        private String idCard;
        private String phone;
        private Long orgId;

        // getters and setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getIdCard() {
            return idCard;
        }

        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public Long getOrgId() {
            return orgId;
        }

        public void setOrgId(Long orgId) {
            this.orgId = orgId;
        }
    }

    /**
     * 删除项目录入
     *
     * @param projectIds 主键串
     */
    @SaCheckPermission("projects:prj_projects:remove")
    @Log(title = "项目录入", businessType = BusinessType.DELETE)
    @DeleteMapping("/{projectIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] projectIds) {
        return toAjax(prjProjectsService.deleteWithValidByIds(List.of(projectIds), true));
    }

    /**
     * 查询企业下的项目列表
     */
    @GetMapping("/selectAll")
    public R<List<PrjProjectsVo>> selectAll(PrjProjectsBo bo) {
        return R.ok(prjProjectsService.selectAll(bo));
    }

    @GetMapping("/dept/list")
    public R<List<SysDeptVo>> deptList(SysDeptBo dept) {
        // 因为角色绑定了数据权限，查询部门列表时需要忽略数据权限
        List<SysDeptVo> ignoreDepts = DataPermissionHelper.ignore(() -> deptService.selectDeptList(dept));
        return R.ok(ignoreDepts);
    }

    /**
     * 查询五方单位下任意一方的人员列表
     */
    @GetMapping("/selectFivePartyUsers")
    public TableDataInfo<SysPersonVo> selectFivePartyUsers(SysPersonBo bo, PageQuery pageQuery) {
        return prjProjectsService.selectFivePartyUsers(bo, pageQuery);
    }

    /**
     * 绑定项目人员关联
     *
     * @param projectId 项目ID
     * @param personIds 人员ID列表（格式：personId,orgId,roleOnProject|personId,orgId,roleOnProject）
     */
    private void bindProjectPersonnel(Long projectId, String personIds) {
        if (projectId == null || StringUtils.isEmpty(personIds)) {
            return;
        }

//        // 先删除该项目已有的人员关联
//        PrjPersonnelBo queryBo = new PrjPersonnelBo();
//        queryBo.setProjectId(projectId);
//        List<PrjPersonnelVo> existList = prjPersonnelService.queryList(queryBo);
//        if (!existList.isEmpty()) {
//            Long[] ids = existList.stream().map(PrjPersonnelVo::getProjectPersonnelId).toArray(Long[]::new);
//            prjPersonnelService.deleteWithValidByIds(Arrays.asList(ids), false);
//        }

        // 查询项目现有人员及其角色
        PrjPersonnelBo queryBo = new PrjPersonnelBo();
        queryBo.setProjectId(projectId);
        List<PrjPersonnelVo> existList = prjPersonnelService.queryList(queryBo);

        // 添加新的人员关联
        String[] personnelArr = personIds.split("\\|");

        // 使用临时列表记录当前批次的人员和角色组合，用于检查同批次内的重复
        List<String> currentBatchPersonRole = new java.util.ArrayList<>();

        for (String personnel : personnelArr) {
            if (StringUtils.isEmpty(personnel)) {
                continue;
            }
            String[] parts = personnel.split(",");
            if (parts.length != 3) {
                continue;
            }

            try {
                if (parts.length != 3) {
                    log.error("人员信息格式不正确，应为personId,roleOnProject,orgId格式，实际为: {}", personnel);
                    continue;
                }

                Long personId = Long.parseLong(parts[0]);
                String roleOnProject = parts[1];
                Long orgId = Long.parseLong(parts[2]);

                // 构建人员-角色的唯一标识
                String personRoleKey = personId + ":" + roleOnProject;

                // 检查当前批次中是否已有相同人员和角色的组合
                if (currentBatchPersonRole.contains(personRoleKey)) {
                    log.warn("跳过重复的人员角色组合: {}", personRoleKey);
                    continue;
                }

                // 检查该人员在此项目下是否已有相同角色
                boolean hasDuplicateRole = existList.stream()
                    .anyMatch(p -> p.getPersonId().equals(personId)
                        && p.getRoleOnProject().equals(roleOnProject));

                if (hasDuplicateRole) {
                    log.warn("人员{}在项目{}下已有角色{}", personId, projectId, roleOnProject);
                    continue;
                }

                // 将当前人员和角色组合添加到临时列表中
                currentBatchPersonRole.add(personRoleKey);

                // 若绑定的是“项目管理员”，需要保证其存在系统用户并赋予系统角色“project_admin”
                Long userIdToBind = null;
                try {
                    // 优先尝试由 sys_person.user_id 取
                    SysPerson person = sysPersonMapper.selectById(personId);
                    if (person != null) {
                        userIdToBind = person.getUserId();
                    }
                } catch (Exception ignore) {
                }

                if ("PROJECT_ADMIN".equals(roleOnProject)) {
                    if (userIdToBind == null) {
                        // 没有关联用户则自动创建
                        SysPerson person = sysPersonMapper.selectById(personId);
                        if (person != null) {
                            SysUserBo userBo = new SysUserBo();
                            // 以身份证号作为账号，以姓名为昵称，默认手机号为初始密码（加密）
                            userBo.setUserName(person.getIdCard());
                            userBo.setNickName(person.getName());
                            userBo.setPhonenumber(person.getPhone());
                            userBo.setPassword(BCrypt.hashpw(StringUtils.defaultIfEmpty(person.getPhone(), person.getIdCard())));
                            userBo.setStatus("0");
                            userBo.setUserType("sys_user");
                            // 获取 project_admin 的角色ID
                            Long projectAdminRoleId = resolveRoleIdByKey("project_admin");
                            if (projectAdminRoleId != null) {
                                userBo.setRoleIds(new Long[]{projectAdminRoleId});
                            }
                            // 插入用户
                            sysUserService.insertUser(userBo);
                            userIdToBind = userBo.getUserId();

                            // 回写到人员表
                            if (userIdToBind != null) {
                                LambdaUpdateWrapper<SysPerson> upd = new LambdaUpdateWrapper<>();
                                upd.eq(SysPerson::getPersonId, personId).set(SysPerson::getUserId, userIdToBind);
                                sysPersonMapper.update(null, upd);
                            }
                        }
                    } else {
                        // 已有关联用户，则确保其拥有系统角色 project_admin（如无则授予）
                        ensureUserHasRole(userIdToBind, "project_admin");
                    }
                }

                PrjPersonnelBo personnelBo = new PrjPersonnelBo();
                personnelBo.setProjectId(projectId);
                personnelBo.setPersonId(personId);
                personnelBo.setRoleOnProject(roleOnProject);
                personnelBo.setOrgId(orgId);
                if (userIdToBind != null) {
                    personnelBo.setUserId(userIdToBind);
                }
                prjPersonnelService.insertByBo(personnelBo);
            } catch (NumberFormatException e) {
                log.error("人员信息格式转换失败: {}, 错误: {}", personnel, e.getMessage());
            } catch (Exception e) {
                log.error("绑定项目人员失败: {}", e.getMessage(), e);
            }
        }
    }

    private Long resolveRoleIdByKey(String roleKey) {
        List<SysRoleVo> roles = sysRoleService.selectRoleList(new org.dromara.system.domain.bo.SysRoleBo() {{
            setRoleKey(roleKey);
        }});
        if (roles != null && !roles.isEmpty()) {
            return roles.get(0).getRoleId();
        }
        return null;
    }

    private void ensureUserHasRole(Long userId, String roleKey) {
        List<SysRoleVo> userRoles = sysRoleMapper.selectRolesByUserId(userId);
        boolean has = userRoles != null && userRoles.stream().anyMatch(r -> roleKey.equals(r.getRoleKey()));
        if (!has) {
            Long roleId = resolveRoleIdByKey(roleKey);
            if (roleId != null) {
                sysRoleService.insertAuthUsers(roleId, new Long[]{userId});
            }
        }
    }

    /**
     * 查询所有项目的名称和id
     *
     * @return
     */
    @SaCheckPermission("system:enterpriseInfo:searchData")
    @GetMapping("/allForSearch")
    public R<List<EnterpriseNameAndId>> getSearchData() {
        return prjProjectsService.getSearchData();
    }


    /**
     * 根据施工许可证编号同步项目信息
     *
     * @param constructionPermitNo 施工许可证编号
     */
    @SaCheckPermission("projects:prj_projects:sync")
    @GetMapping("/syncProject/{constructionPermitNo}")
    public R<PrjProjectsVo> syncProject(@NotNull(message = "施工许可证编号不能为空") @PathVariable String constructionPermitNo) {
        return prjProjectsSyncService.syncProjectByPermitNo(constructionPermitNo);
    }

    /**
     * 批量从中间表 xmxx 根据施工许可证编号同步项目信息
     */
    @GetMapping("/batchSyncProject")
    public R<Object> syncProject() {
        prjProjectsSyncService.batchSync();
        return R.ok("success");
    }

    /**
     * 获取监管端大屏地址
     *
     * @return
     */
    @GetMapping("/getSuperviseUrl")
    public R<String> getSupervise() {
        return R.ok("", sadaSignUtil.getUrl("1948669113465483266"));
    }

    /**
     * 获取监管端大屏地址
     *
     * @return
     */
    @GetMapping("/getProjectUrl")
    public R<String> getProjectUrl() {
        return R.ok("", sadaSignUtil.getUrl("1948670799789600769"));
    }
}

package org.dromara.facility.domain.vo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.facility.domain.LnDumpPlat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 绿能卸料平台视图对象 ln_dump_plat
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LnDumpPlat.class)
public class LnDumpPlatVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 编号
     */
    @ExcelProperty(value = "编号")
    private Long dumpnumber;

    /**
     * 最大载重,吨
     */
    @ExcelProperty(value = "最大载重,吨")
    private Long weightMax;

    /**
     * 重量,吨
     */
    @ExcelProperty(value = "重量,吨")
    private Long weight;

    /**
     * 倾角,度
     */
    @ExcelProperty(value = "倾角,度")
    private Long tilt;

    /**
     * 电池电压,v
     */
    @ExcelProperty(value = "电池电压,v")
    private Long batvolt;

    /**
     * 吊重比例 %
     */
    @ExcelProperty(value = "吊重比例 %")
    private Long wightPercent;

    /**
     * 倾斜比例X %
     */
    @ExcelProperty(value = "倾斜比例X %")
    private Long tiltPercentX;

    /**
     * 倾斜比例Y %
     */
    @ExcelProperty(value = "倾斜比例Y %")
    private Long tiltPercentY;

    /**
     * 报警信息
     */
    @ExcelProperty(value = "报警信息")
    private Long alarmInfo;

    /**
     * 设备状态
     */
    @ExcelProperty(value = "设备状态")
    private String status;

    /**
     * 重量空载实际值 kg
     */
    @ExcelProperty(value = "重量空载实际值 kg")
    private Long idleWeightReal;

    /**
     * 重量负载实际值 kg
     */
    @ExcelProperty(value = "重量负载实际值 kg")
    private Long loadWeightReal;

    /**
     * 载重预警百分比 %
     */
    @ExcelProperty(value = "载重预警百分比 %")
    private Long weightWarning;

    /**
     * 载重报警百分比 %
     */
    @ExcelProperty(value = "载重报警百分比 %")
    private Long weightAlarm;

    /**
     * 倾斜预警值
     */
    @ExcelProperty(value = "倾斜预警值")
    private Long tiltWarning;

    /**
     * 倾斜报警值
     */
    @ExcelProperty(value = "倾斜报警值")
    private Long tiltAlarm;

    /**
     * 设备ip
     */
    @ExcelProperty(value = "设备ip")
    private String deviceIp;

    /**
     * 实时倾斜度X
     */
    @ExcelProperty(value = "实时倾斜度X")
    private Long realTiltX;

    /**
     * 实时倾斜度Y
     */
    @ExcelProperty(value = "实时倾斜度Y")
    private Long realTiltY;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String devNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

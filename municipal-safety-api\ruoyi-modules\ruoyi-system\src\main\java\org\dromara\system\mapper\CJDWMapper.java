package org.dromara.system.mapper;

import org.dromara.system.domain.CJDW;

import java.util.List;

public interface CJDWMapper {
    int insertCjdw(CJDW cjdw);

    int batchInsertCjdw(List<CJDW> cjdwList);

    /**
     * 物理删除所有数据（慎用！）
     *
     * @return 影响的行数
     */
    int deleteAllPhysically();

    /**
     * 根据企业统一社会信用代码查询施工许可证编号
     */
    List<CJDW> selectBuilderLicenceNum(String qytyshxydm);
}

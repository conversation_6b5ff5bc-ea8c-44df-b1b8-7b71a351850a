package org.dromara.quality.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.dromara.quality.domain.QualityDevice;
import org.dromara.quality.domain.QualityMeasurement;
import org.dromara.quality.domain.vo.*;
import org.dromara.quality.mapper.QualityDeviceMapper;
import org.dromara.quality.mapper.QualityMeasurementMapper;
import org.dromara.quality.service.IQualityOverviewService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 质量管理概览Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RequiredArgsConstructor
@Service
public class QualityOverviewServiceImpl implements IQualityOverviewService {

    private final QualityDeviceMapper deviceMapper;
    private final QualityMeasurementMapper measurementMapper;

    @Override
    public QualityOverviewVo getOverviewData() {
        QualityOverviewVo overview = new QualityOverviewVo();

        // 设备统计
        QualityOverviewVo.DeviceStats deviceStats = new QualityOverviewVo.DeviceStats();
        Long deviceTotal = deviceMapper.selectCount(new LambdaQueryWrapper<QualityDevice>()
            .eq(QualityDevice::getDelFlag, 0));
        deviceStats.setTotal(deviceTotal.intValue());

        // 计算设备增长率（查询上月数据）
        LocalDateTime lastMonth = LocalDateTime.now().minusMonths(1);
        Date lastMonthDate = java.sql.Timestamp.valueOf(lastMonth);
        Long lastMonthDeviceCount = deviceMapper.selectCount(new LambdaQueryWrapper<QualityDevice>()
            .eq(QualityDevice::getDelFlag, 0)
            .le(QualityDevice::getCreateTime, lastMonthDate));
        int deviceIncrease = deviceTotal.intValue() - lastMonthDeviceCount.intValue();
        deviceStats.setIncrease(Math.max(0, deviceIncrease));
        overview.setDeviceStats(deviceStats);

        // 测量统计
        QualityOverviewVo.MeasurementStats measurementStats = new QualityOverviewVo.MeasurementStats();
        Long measurementTotal = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
            .eq(QualityMeasurement::getDelFlag, 0));
        measurementStats.setTotal(measurementTotal.intValue());

        Long normalCount = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
            .eq(QualityMeasurement::getDelFlag, 0)
            .eq(QualityMeasurement::getIsCompliant, "0"));
        measurementStats.setNormal(normalCount.intValue());

        Long hazardCount = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
            .eq(QualityMeasurement::getDelFlag, 0)
            .eq(QualityMeasurement::getIsHazardMarked, "1"));
        measurementStats.setHazard(hazardCount.intValue());

        // 计算比率
        if (measurementTotal > 0) {
            measurementStats.setNormalRate((int) Math.round((double) normalCount / measurementTotal * 100));
            measurementStats.setHazardRate((int) Math.round((double) hazardCount / measurementTotal * 100));
        } else {
            measurementStats.setNormalRate(0);
            measurementStats.setHazardRate(0);
        }

        // 计算测量增长率（查询上月数据）
        Long lastMonthMeasurementCount = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
            .eq(QualityMeasurement::getDelFlag, 0)
            .le(QualityMeasurement::getCreateTime, lastMonthDate));
        int measurementIncrease = measurementTotal.intValue() - lastMonthMeasurementCount.intValue();
        measurementStats.setIncrease(Math.max(0, measurementIncrease));
        overview.setMeasurementStats(measurementStats);

        // 待处理任务
        QualityOverviewVo.PendingTasks pendingTasks = new QualityOverviewVo.PendingTasks();
        pendingTasks.setHazardCount(hazardCount.intValue());

        Long retestCount = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
            .eq(QualityMeasurement::getDelFlag, 0)
            .eq(QualityMeasurement::getStatus, "0")
            .eq(QualityMeasurement::getIsCompliant, "1"));
        pendingTasks.setRetestCount(retestCount.intValue());

        // 计算待处理报告数量（状态为待处理的测量记录）
        Long reportCount = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
            .eq(QualityMeasurement::getDelFlag, 0)
            .eq(QualityMeasurement::getStatus, "0"));
        pendingTasks.setReportCount(reportCount.intValue());
        overview.setPendingTasks(pendingTasks);

        return overview;
    }

    @Override
    public DeviceStatisticsVo getDeviceStatistics() {
        DeviceStatisticsVo statistics = new DeviceStatisticsVo();

        Long total = deviceMapper.selectCount(new LambdaQueryWrapper<QualityDevice>()
            .eq(QualityDevice::getDelFlag, 0));
        statistics.setTotal(total.intValue());

        Long normalCount = deviceMapper.selectCount(new LambdaQueryWrapper<QualityDevice>()
            .eq(QualityDevice::getDelFlag, 0)
            .eq(QualityDevice::getStatus, "0"));
        statistics.setNormalCount(normalCount.intValue());

        Long disabledCount = deviceMapper.selectCount(new LambdaQueryWrapper<QualityDevice>()
            .eq(QualityDevice::getDelFlag, 0)
            .eq(QualityDevice::getStatus, "1"));
        statistics.setDisabledCount(disabledCount.intValue());

        // 计算增长率（查询上月数据）
        LocalDateTime lastMonth = LocalDateTime.now().minusMonths(1);
        Date lastMonthDate = java.sql.Timestamp.valueOf(lastMonth);
        Long lastMonthCount = deviceMapper.selectCount(new LambdaQueryWrapper<QualityDevice>()
            .eq(QualityDevice::getDelFlag, 0)
            .le(QualityDevice::getCreateTime, lastMonthDate));

        int increase = total.intValue() - lastMonthCount.intValue();
        statistics.setIncrease(Math.max(0, increase));

        // 计算增长率百分比
        if (lastMonthCount > 0) {
            double increaseRate = ((double) increase / lastMonthCount) * 100;
            statistics.setIncreaseRate(Math.round(increaseRate * 10.0) / 10.0); // 保留一位小数
        } else {
            statistics.setIncreaseRate(0.0);
        }

        return statistics;
    }

    @Override
    public MeasurementStatisticsVo getMeasurementStatistics() {
        MeasurementStatisticsVo statistics = new MeasurementStatisticsVo();

        Long total = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
            .eq(QualityMeasurement::getDelFlag, 0));
        statistics.setTotal(total.intValue());

        Long normalCount = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
            .eq(QualityMeasurement::getDelFlag, 0)
            .eq(QualityMeasurement::getIsCompliant, "0"));
        statistics.setNormalCount(normalCount.intValue());

        Long abnormalCount = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
            .eq(QualityMeasurement::getDelFlag, 0)
            .eq(QualityMeasurement::getIsCompliant, "1"));
        statistics.setAbnormalCount(abnormalCount.intValue());

        Long hazardCount = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
            .eq(QualityMeasurement::getDelFlag, 0)
            .eq(QualityMeasurement::getIsHazardMarked, "1"));
        statistics.setHazardCount(hazardCount.intValue());

        Long retestCount = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
            .eq(QualityMeasurement::getDelFlag, 0)
            .eq(QualityMeasurement::getStatus, "0")
            .eq(QualityMeasurement::getIsCompliant, "1"));
        statistics.setRetestCount(retestCount.intValue());

        // 计算比率
        if (total > 0) {
            statistics.setNormalRate((int) Math.round((double) normalCount / total * 100));
            statistics.setHazardRate((int) Math.round((double) hazardCount / total * 100));
        } else {
            statistics.setNormalRate(0);
            statistics.setHazardRate(0);
        }

        // 计算增长率（查询上月数据）
        LocalDateTime lastMonth = LocalDateTime.now().minusMonths(1);
        Date lastMonthDate = java.sql.Timestamp.valueOf(lastMonth);
        Long lastMonthCount = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
            .eq(QualityMeasurement::getDelFlag, 0)
            .le(QualityMeasurement::getCreateTime, lastMonthDate));

        int increase = total.intValue() - lastMonthCount.intValue();
        statistics.setIncrease(Math.max(0, increase));

        // 计算增长率百分比
        if (lastMonthCount > 0) {
            double increaseRate = ((double) increase / lastMonthCount) * 100;
            statistics.setIncreaseRate(Math.round(increaseRate * 10.0) / 10.0); // 保留一位小数
        } else {
            statistics.setIncreaseRate(0.0);
        }

        return statistics;
    }

    @Override
    public ChartDataVo getChartData(String period, String startDate, String endDate) {
        ChartDataVo chartData = new ChartDataVo();

        // 根据时间周期生成日期范围
        List<String> dates = generateDateRange(period, startDate, endDate);
        chartData.setDates(dates);

        // 根据日期查询实际数据
        List<Integer> normalData = new ArrayList<>();
        List<Integer> abnormalData = new ArrayList<>();
        List<Integer> hazardData = new ArrayList<>();

        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("MM-dd");
        for (String dateStr : dates) {
            // 构建查询日期范围（当天00:00:00到23:59:59）
            LocalDate queryDate = LocalDate.parse(LocalDate.now().getYear() + "-" + dateStr);
            LocalDateTime startOfDay = queryDate.atStartOfDay();
            LocalDateTime endOfDay = queryDate.atTime(23, 59, 59);
            Date startTime = java.sql.Timestamp.valueOf(startOfDay);
            Date endTime = java.sql.Timestamp.valueOf(endOfDay);

            // 查询当天正常测量数量
            Long normalCount = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
                .eq(QualityMeasurement::getDelFlag, 0)
                .eq(QualityMeasurement::getIsCompliant, "0")
                .between(QualityMeasurement::getMeasurementTime, startTime, endTime));
            normalData.add(normalCount.intValue());

            // 查询当天异常测量数量
            Long abnormalCount = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
                .eq(QualityMeasurement::getDelFlag, 0)
                .eq(QualityMeasurement::getIsCompliant, "1")
                .between(QualityMeasurement::getMeasurementTime, startTime, endTime));
            abnormalData.add(abnormalCount.intValue());

            // 查询当天隐患测量数量
            Long hazardCount = measurementMapper.selectCount(new LambdaQueryWrapper<QualityMeasurement>()
                .eq(QualityMeasurement::getDelFlag, 0)
                .eq(QualityMeasurement::getIsHazardMarked, "1")
                .between(QualityMeasurement::getMeasurementTime, startTime, endTime));
            hazardData.add(hazardCount.intValue());
        }

        chartData.setNormalData(normalData);
        chartData.setAbnormalData(abnormalData);
        chartData.setHazardData(hazardData);

        return chartData;
    }

    /**
     * 获取设备状态分布信息
     *
     * @return DeviceStatusDistributionVo 包含设备状态分布信息的对象
     */
    @Override
    public DeviceStatusDistributionVo getDeviceStatusDistribution() {
        DeviceStatusDistributionVo distribution = new DeviceStatusDistributionVo();

        Long normal = deviceMapper.selectCount(new LambdaQueryWrapper<QualityDevice>()
            .eq(QualityDevice::getDelFlag, 0)
            .eq(QualityDevice::getStatus, "0"));
        distribution.setNormal(normal.intValue());

        Long disabled = deviceMapper.selectCount(new LambdaQueryWrapper<QualityDevice>()
            .eq(QualityDevice::getDelFlag, 0)
            .eq(QualityDevice::getStatus, "1"));
        distribution.setDisabled(disabled.intValue());

        return distribution;
    }

    /**
     * 根据时间周期生成日期范围
     */
    private List<String> generateDateRange(String period, String startDate, String endDate) {
        List<String> dates = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");

        LocalDate start;
        LocalDate end;

        if (startDate != null && endDate != null) {
            start = LocalDate.parse(startDate);
            end = LocalDate.parse(endDate);
        } else {
            // 根据周期设置默认日期范围
            end = LocalDate.now();
            switch (period) {
                case "week":
                    start = end.minusDays(6);
                    break;
                case "month":
                    start = end.minusDays(29);
                    break;
                case "quarter":
                    start = end.minusDays(89);
                    break;
                default:
                    start = end.minusDays(6);
            }
        }

        LocalDate current = start;
        while (!current.isAfter(end)) {
            dates.add(current.format(formatter));
            current = current.plusDays(1);
        }

        return dates;
    }
}

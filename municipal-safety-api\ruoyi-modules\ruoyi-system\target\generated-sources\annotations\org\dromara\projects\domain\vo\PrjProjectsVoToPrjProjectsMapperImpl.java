package org.dromara.projects.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjProjects;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjProjectsVoToPrjProjectsMapperImpl implements PrjProjectsVoToPrjProjectsMapper {

    @Override
    public PrjProjects convert(PrjProjectsVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjProjects prjProjects = new PrjProjects();

        prjProjects.setProjectId( arg0.getProjectId() );
        prjProjects.setProjectName( arg0.getProjectName() );
        prjProjects.setProjectCode( arg0.getProjectCode() );
        prjProjects.setProjectOverview( arg0.getProjectOverview() );
        prjProjects.setConstructionPermitNo( arg0.getConstructionPermitNo() );
        prjProjects.setConstructionPermitDocId( arg0.getConstructionPermitDocId() );
        prjProjects.setLola( arg0.getLola() );
        prjProjects.setProvinceCode( arg0.getProvinceCode() );
        prjProjects.setProvinceName( arg0.getProvinceName() );
        prjProjects.setCityCode( arg0.getCityCode() );
        prjProjects.setCityName( arg0.getCityName() );
        prjProjects.setDistrictCode( arg0.getDistrictCode() );
        prjProjects.setDistrictName( arg0.getDistrictName() );
        prjProjects.setCountyCode( arg0.getCountyCode() );
        prjProjects.setCountyName( arg0.getCountyName() );
        prjProjects.setLocationDetail( arg0.getLocationDetail() );
        prjProjects.setSiteArea( arg0.getSiteArea() );
        prjProjects.setBudgetTotal( arg0.getBudgetTotal() );
        prjProjects.setSupervisingQsOrgId( arg0.getSupervisingQsOrgId() );
        prjProjects.setStatus( arg0.getStatus() );
        prjProjects.setStartDate( arg0.getStartDate() );
        prjProjects.setPlannedEndDate( arg0.getPlannedEndDate() );
        prjProjects.setActualStartDate( arg0.getActualStartDate() );
        prjProjects.setActualEndDate( arg0.getActualEndDate() );
        prjProjects.setClientOrgId( arg0.getClientOrgId() );
        prjProjects.setConstructionOrgId( arg0.getConstructionOrgId() );
        prjProjects.setSupervisionOrgId( arg0.getSupervisionOrgId() );
        prjProjects.setDesignOrgId( arg0.getDesignOrgId() );
        prjProjects.setSurveyOrgId( arg0.getSurveyOrgId() );
        prjProjects.setInstallationDismantlingOrgId( arg0.getInstallationDismantlingOrgId() );
        prjProjects.setMaintenanceOrgId( arg0.getMaintenanceOrgId() );
        prjProjects.setSubcontractorOrgIds( arg0.getSubcontractorOrgIds() );
        prjProjects.setProjectManagerUserId( arg0.getProjectManagerUserId() );
        prjProjects.setSupervisionChiefEngUserId( arg0.getSupervisionChiefEngUserId() );
        prjProjects.setSafetyMeasuresFeeDocId( arg0.getSafetyMeasuresFeeDocId() );

        return prjProjects;
    }

    @Override
    public PrjProjects convert(PrjProjectsVo arg0, PrjProjects arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setProjectId( arg0.getProjectId() );
        arg1.setProjectName( arg0.getProjectName() );
        arg1.setProjectCode( arg0.getProjectCode() );
        arg1.setProjectOverview( arg0.getProjectOverview() );
        arg1.setConstructionPermitNo( arg0.getConstructionPermitNo() );
        arg1.setConstructionPermitDocId( arg0.getConstructionPermitDocId() );
        arg1.setLola( arg0.getLola() );
        arg1.setProvinceCode( arg0.getProvinceCode() );
        arg1.setProvinceName( arg0.getProvinceName() );
        arg1.setCityCode( arg0.getCityCode() );
        arg1.setCityName( arg0.getCityName() );
        arg1.setDistrictCode( arg0.getDistrictCode() );
        arg1.setDistrictName( arg0.getDistrictName() );
        arg1.setCountyCode( arg0.getCountyCode() );
        arg1.setCountyName( arg0.getCountyName() );
        arg1.setLocationDetail( arg0.getLocationDetail() );
        arg1.setSiteArea( arg0.getSiteArea() );
        arg1.setBudgetTotal( arg0.getBudgetTotal() );
        arg1.setSupervisingQsOrgId( arg0.getSupervisingQsOrgId() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setStartDate( arg0.getStartDate() );
        arg1.setPlannedEndDate( arg0.getPlannedEndDate() );
        arg1.setActualStartDate( arg0.getActualStartDate() );
        arg1.setActualEndDate( arg0.getActualEndDate() );
        arg1.setClientOrgId( arg0.getClientOrgId() );
        arg1.setConstructionOrgId( arg0.getConstructionOrgId() );
        arg1.setSupervisionOrgId( arg0.getSupervisionOrgId() );
        arg1.setDesignOrgId( arg0.getDesignOrgId() );
        arg1.setSurveyOrgId( arg0.getSurveyOrgId() );
        arg1.setInstallationDismantlingOrgId( arg0.getInstallationDismantlingOrgId() );
        arg1.setMaintenanceOrgId( arg0.getMaintenanceOrgId() );
        arg1.setSubcontractorOrgIds( arg0.getSubcontractorOrgIds() );
        arg1.setProjectManagerUserId( arg0.getProjectManagerUserId() );
        arg1.setSupervisionChiefEngUserId( arg0.getSupervisionChiefEngUserId() );
        arg1.setSafetyMeasuresFeeDocId( arg0.getSafetyMeasuresFeeDocId() );

        return arg1;
    }
}

export interface PersonVO {

  /**
   * 主键
   */
  personId: string | number;

  /**
   * 企业id
   */
  enterpriseId: string | number;

  /**
   * 用户id
   */
  userId: string | number;

  /**
   * 姓名
   */
  name: string;

  /**
   * 身份证号码
   */
  idCard: string | number;

  /**
   * 手机号码
   */
  phone: string;

  /**
   * 籍贯
   */
  nativePlace: string;

  /**
   * 性别
   */
  gender: string;

  /**
   * 政治面貌（如群众、党员）
   */
  politicalStatus: string;

  /**
   * 文化程度
   */
  education: string;

}

export interface PersonForm extends BaseEntity {

  /**
   * 主键
   */
  personId: string | number;

  // 头像
  headImgId?: string;
  /**
   * 姓名
   */
  name?: string;

  /**
   * 身份证号码
   */
  idCard?: string | number;

  /**
   * 手机号码
   */
  phone?: string;

  /**
   * 籍贯
   */
  nativePlace?: string;

  /**
   * 性别
   */
  gender?: string;

  /**
   * 政治面貌（如群众、党员）
   */
  politicalStatus?: string;

  /**
   * 文化程度
   */
  education?: string;

}

export interface PersonQuery extends PageQuery {
  /**
   * 姓名
   */
  name?: string;

  /**
   * 企业id
   */
  enterpriseId: string | number;

  /**
   * 身份证号码
   */
  idCard?: string | number;

  /**
   * 手机号码
   */
  phone?: string;

  /**
   * 籍贯
   */
  nativePlace?: string;

  /**
   * 性别
   */
  gender?: string;

  /**
   * 政治面貌（如群众、党员）
   */
  politicalStatus?: string;

  /**
   * 文化程度
   */
  education?: string;

  /**
 * 证书种类
 */
  certificateType?: string;
  /**
   * 证书名称
   */
  certificateName?: string;
  /**
   * 日期范围参数
   */
  params?: any;
}
export interface QualificationVO {
  /**
   * 主键ID
   */
  qualificationId: string | number;

  /**
   * 关联人员ID
   */
  personId: string | number;

  /**
   * 证书种类
   */
  certificateType: string;

  /**
   * 证书名称
   */
  certificateName: string;

  /**
   * 证书编号
   */
  certificateNumber: string;

  /**
   * 获取时间
   */
  acquisitionTime: string;

  /**
   * 发证机关
   */
  issuingAuthority: string;

  /**
   * 证书等级
   */
  certificateLevel: string;

  /**
   * 资质对应岗位
   */
  correspondingPosition: string;

  /**
   * 有效期起始
   */
  validFrom: string | number;

  /**
   * 有效期结束
   */
  validTo: string | number;

  /**
   * 证件照片路径
   */
  uploadPhoto: string;

}

export interface QualificationForm extends BaseEntity {
  /**
   * 主键ID
   */
  qualificationId?: string | number;

  /**
   * 关联人员ID
   */
  personId?: string | number;

  /**
   * 证书种类
   */
  certificateType?: string;

  /**
   * 证书名称
   */
  certificateName?: string;

  /**
   * 证书编号
   */
  certificateNumber?: string;

  /**
   * 获取时间
   */
  acquisitionTime?: string;

  /**
   * 发证机关
   */
  issuingAuthority?: string;

  /**
   * 证书等级
   */
  certificateLevel?: string;

  /**
   * 资质对应岗位 | 专业
   */
  correspondingPosition?: string;

  /**
   * 有效期起始
   */
  validFrom?: string | number;

  /**
   * 有效期结束
   */
  validTo?: string | number;

  /**
   * 证件照片路径
   */
  uploadPhoto?: string;

}

export interface QualificationQuery extends PageQuery {

  /**
   * 关联人员ID
   */
  personId?: string | number;

  /**
   * 证书种类
   */
  certificateType?: string;

  /**
   * 证书名称
   */
  certificateName?: string;

  /**
   * 证书编号
   */
  certificateNumber?: string;

  /**
   * 获取时间
   */
  acquisitionTime?: string;

  /**
   * 发证机关
   */
  issuingAuthority?: string;

  /**
   * 证书等级
   */
  certificateLevel?: string;

  /**
   * 资质对应岗位
   */
  correspondingPosition?: string;

  /**
   * 有效期起始
   */
  validFrom?: string | number;

  /**
   * 有效期结束
   */
  validTo?: string | number;

  /**
   * 证件照片路径
   */
  uploadPhoto?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

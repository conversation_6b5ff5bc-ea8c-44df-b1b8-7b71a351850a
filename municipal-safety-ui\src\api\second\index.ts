import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { expertSuggestType } from './types'

// 发起第二条工单的第一步接口
export const addHazardousItemsSpecialWarningFirst = (data: any): AxiosPromise<any> => {
  return request({
    url: '/ai/ai_haz_analysis_tasks/pushWorkOrder2',
    method: 'post',
    data
  });
};
// 发起第二条工单的第一步回显接口
export const addHazardousItemsSpecialWarningFirstEcho = (taskId: string): AxiosPromise<any> => {
  return request({
    url: `/system/hazardousItemsSpecialist/detail/${taskId}`,
    method: 'get'
  });
}
// 专家评审时需要保存建议
export const saveExpertSuggestion = (data: expertSuggestType[]): AxiosPromise<any> => {
  return request({
    url: '/system/hazardousItemsSpecialistQuestion/question',
    method: 'post',
    data
  });
}
// 发起第二条工单的第二步回显接口
export const addHazardousItemsSpecialWarningSecondEcho = (taskId: string): AxiosPromise<any> => {
  return request({
    url: `/system/hazardousItemsSpecialistQuestion/detail/${taskId}`,
    method: 'get'
  });
}
// 发起第二条工单的第三步中间专家意见回显接口
export const addHazardousItemsSpecialWarningThirdEcho = (taskId: string): AxiosPromise<any> => {
  return request({
    url: `/system/hazardousItemsSpecialistQuestion/detailAll/${taskId}`,
    method: 'get'
  });
}
// 发起第二条工单的第三步审批时保存
export const addHazardousThirdSave = (data: any): AxiosPromise<any> => {
  return request({
    url: '/system/hazardousItemsSpecialist/saveThree',
    method: 'post',
    data
  });
}
// 发起第二条工单的第三步右侧的厅局意见的回显
export const addHazardousThirdEcho = (taskId: string): AxiosPromise<any> => {
  return request({
    url: `/system/hazardousItemsSpecialist/threeDetail/${taskId}`,
    method: 'get'
  });
}
// 发起第二条工单的第四步右侧点击按钮弹框里面提交质监站审核工单后需要保存数据的接口
export const addHazardousFourthSave = (data: any): AxiosPromise<any> => {
  return request({
    url: '/system/hazardousItemsSpecialist/saveThree2',
    method: 'post',
    data
  });
}
//发起第二条工单的第四步右侧点击按钮弹框里面提交质监站审核工单后的回显数据接口
export const addHazardousFourthEcho = (taskId: string): AxiosPromise<any> => {
  return request({
    url: `/system/hazardousItemsSpecialist/threeInstance/${taskId}`,
    method: 'get'
  });
}
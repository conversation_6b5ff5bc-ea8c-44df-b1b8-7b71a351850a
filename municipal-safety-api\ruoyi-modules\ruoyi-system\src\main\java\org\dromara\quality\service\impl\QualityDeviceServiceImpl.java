package org.dromara.quality.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.quality.domain.QualityDevice;
import org.dromara.quality.domain.bo.QualityDeviceBo;
import org.dromara.quality.domain.vo.QualityDeviceVo;
import org.dromara.quality.mapper.QualityDeviceMapper;
import org.dromara.quality.service.IQualityDeviceService;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.ISysOssService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 设备管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RequiredArgsConstructor
@Service
public class QualityDeviceServiceImpl implements IQualityDeviceService {

    private final QualityDeviceMapper baseMapper;
    private final ISysOssService ossService;

    /**
     * 查询设备管理
     */
    @Override
    public QualityDeviceVo queryById(Long deviceId) {
        QualityDeviceVo vo = baseMapper.selectVoById(deviceId);
        if (ObjectUtil.isNotNull(vo)) {
            fillOssInfo(vo);
        }
        return vo;
    }

    /**
     * 查询设备管理列表
     */
    @Override
    public TableDataInfo<QualityDeviceVo> queryPageList(QualityDeviceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QualityDevice> lqw = buildQueryWrapper(bo);
        Page<QualityDeviceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        // 填充OSS文件信息
        result.getRecords().forEach(this::fillOssInfo);
        return TableDataInfo.build(result);
    }

    /**
     * 查询设备管理列表
     */
    @Override
    public List<QualityDeviceVo> queryList(QualityDeviceBo bo) {
        LambdaQueryWrapper<QualityDevice> lqw = buildQueryWrapper(bo);
        List<QualityDeviceVo> list = baseMapper.selectVoList(lqw);
        // 填充OSS文件信息
        list.forEach(this::fillOssInfo);
        return list;
    }

    private LambdaQueryWrapper<QualityDevice> buildQueryWrapper(QualityDeviceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QualityDevice> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), QualityDevice::getDeviceName, bo.getDeviceName());
        lqw.like(StringUtils.isNotBlank(bo.getSpecification()), QualityDevice::getSpecification, bo.getSpecification());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceCode()), QualityDevice::getDeviceCode, bo.getDeviceCode());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), QualityDevice::getStatus, bo.getStatus());
        lqw.between(params.get("beginTime") != null && params.get("endTime") != null,
            QualityDevice::getCreateTime, params.get("beginTime"), params.get("endTime"));
        return lqw;
    }

    /**
     * 填充OSS文件信息
     */
    private void fillOssInfo(QualityDeviceVo vo) {
        // 填充设备图片信息
        if (ObjectUtil.isNotNull(vo.getDeviceImageOssId())) {
            SysOssVo imageOss = ossService.getById(vo.getDeviceImageOssId());
            if (ObjectUtil.isNotNull(imageOss)) {
                vo.setDeviceImageUrl(imageOss.getUrl());
            }
        }
        
        // 填充使用说明书信息
        if (ObjectUtil.isNotNull(vo.getManualFileOssId())) {
            SysOssVo manualOss = ossService.getById(vo.getManualFileOssId());
            if (ObjectUtil.isNotNull(manualOss)) {
                vo.setManualFileUrl(manualOss.getUrl());
                vo.setManualFileName(manualOss.getOriginalName());
            }
        }
    }

    /**
     * 新增设备管理
     */
    @Override
    public Boolean insertByBo(QualityDeviceBo bo) {
        QualityDevice add = MapstructUtils.convert(bo, QualityDevice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setDeviceId(add.getDeviceId());
        }
        return flag;
    }

    /**
     * 修改设备管理
     */
    @Override
    public Boolean updateByBo(QualityDeviceBo bo) {
        QualityDevice update = MapstructUtils.convert(bo, QualityDevice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QualityDevice entity) {
        // 校验设备编号唯一性
        if (!checkDeviceCodeUnique(entity.getDeviceCode(), entity.getDeviceId())) {
            throw new ServiceException("设备编号已存在");
        }
    }

    /**
     * 批量删除设备管理
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 删除关联的OSS文件
            List<QualityDevice> devices = baseMapper.selectByIds(ids);
            for (QualityDevice device : devices) {
                if (ObjectUtil.isNotNull(device.getDeviceImageOssId())) {
                    ossService.deleteWithValidByIds(List.of(device.getDeviceImageOssId()), false);
                }
                if (ObjectUtil.isNotNull(device.getManualFileOssId())) {
                    ossService.deleteWithValidByIds(List.of(device.getManualFileOssId()), false);
                }
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 生成设备编号
     */
    @Override
    public String generateDeviceCode(String deviceType) {
        String prefix = "JSY-ZN-ZL-";
        
        // 根据设备类型获取对应的编号前缀
        String typePrefix;
        switch (deviceType) {
            case "0":
                // 靠尺
                typePrefix = "10";
                break;
            case "1":
                // 角尺
                typePrefix = "20";
                break;
            case "2":
                // 测距仪
                typePrefix = "30";
                break;
            case "3":
                // 楼板测厚仪
                typePrefix = "40";
                break;
            case "4":
                // 钢筋检测仪
                typePrefix = "50";
                break;
            case "5":
                // 回弹仪
                typePrefix = "60";
                break;
            default:
                // 其他设备
                typePrefix = "99";
                break;
        }
        
        // 查询当前类型设备的最大编号
        LambdaQueryWrapper<QualityDevice> lqw = Wrappers.lambdaQuery();
        lqw.like(QualityDevice::getDeviceCode, prefix + typePrefix)
           .orderByDesc(QualityDevice::getDeviceCode)
           .last("LIMIT 1");
        
        QualityDevice lastDevice = baseMapper.selectOne(lqw);
        
        int nextNumber = 1;
        if (ObjectUtil.isNotNull(lastDevice)) {
            String lastCode = lastDevice.getDeviceCode();
            String numberStr = lastCode.substring(lastCode.length() - 2);
            try {
                nextNumber = Integer.parseInt(numberStr) + 1;
            } catch (NumberFormatException e) {
                nextNumber = 1;
            }
        }
        
        return prefix + typePrefix + String.format("%02d", nextNumber);
    }

    /**
     * 校验设备编号是否唯一
     */
    @Override
    public Boolean checkDeviceCodeUnique(String deviceCode, Long deviceId) {
        LambdaQueryWrapper<QualityDevice> lqw = Wrappers.lambdaQuery();
        lqw.eq(QualityDevice::getDeviceCode, deviceCode);
        if (ObjectUtil.isNotNull(deviceId)) {
            lqw.ne(QualityDevice::getDeviceId, deviceId);
        }
        return baseMapper.selectCount(lqw) == 0;
    }

    /**
     * 上传设备图片
     */
    @Override
    public Long uploadDeviceImage(Long deviceId, MultipartFile file) {
        // 校验文件类型
        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName)) {
            throw new ServiceException("文件名不能为空");
        }
        
        String suffix = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
        if (!StringUtils.equalsAny(suffix, ".jpg", ".jpeg", ".png", ".gif", ".bmp")) {
            throw new ServiceException("只支持上传jpg、jpeg、png、gif、bmp格式的图片文件");
        }
        
        // 上传文件到OSS
        SysOssVo ossVo = ossService.upload(file);
        QualityDevice device = baseMapper.selectById(deviceId);
        // 如果是修改设备，删除原有图片
        if (ObjectUtil.isNotNull(deviceId)) {
            if (ObjectUtil.isNotNull(device) && ObjectUtil.isNotNull(device.getDeviceImageOssId())) {
                ossService.deleteWithValidByIds(List.of(device.getDeviceImageOssId()), false);
            }
        }
        device.setDeviceImageOssId(ossVo.getOssId());
        baseMapper.updateById(device);
        return ossVo.getOssId();
    }

    /**
     * 上传使用说明书
     */
    @Override
    public Long uploadManualFile(Long deviceId, MultipartFile file) {
        // 校验文件类型
        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName)) {
            throw new ServiceException("文件名不能为空");
        }
        
        String suffix = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
        if (!StringUtils.equalsAny(suffix, ".pdf", ".doc", ".docx", ".txt")) {
            throw new ServiceException("只支持上传pdf、doc、docx、txt格式的文档文件");
        }
        
        // 上传文件到OSS
        SysOssVo ossVo = ossService.upload(file);

        QualityDevice device = baseMapper.selectById(deviceId);
        // 如果是修改设备，删除原有文件
        if (ObjectUtil.isNotNull(deviceId)) {
            if (ObjectUtil.isNotNull(device) && ObjectUtil.isNotNull(device.getManualFileOssId())) {
                ossService.deleteWithValidByIds(List.of(device.getManualFileOssId()), false);
            }
        }
        device.setManualFileOssId(ossVo.getOssId());
        baseMapper.updateById(device);
        
        return ossVo.getOssId();
    }

} 
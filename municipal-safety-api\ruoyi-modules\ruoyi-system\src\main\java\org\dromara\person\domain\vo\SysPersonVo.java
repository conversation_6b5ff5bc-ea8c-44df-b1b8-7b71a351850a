package org.dromara.person.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.person.domain.SysPerson;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 人员基本信息视图对象 sys_person
 *
 * <AUTHOR> zu da
 * @date 2025-05-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysPerson.class)
public class SysPersonVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 人员ID
     */
    private Long personId;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String name;

    /**
     * 身份证号码
     */
    @ExcelProperty(value = "身份证号码")
    private String idCard;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码")
    private String phone;

    /**
     * 籍贯
     */
    @ExcelProperty(value = "籍贯")
    private String nativePlace;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_user_sex")
    private String gender;

    /**
     * 政治面貌（如群众、党员）
     */
    @ExcelProperty(value = "政治面貌", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=群众、党员")
    private String politicalStatus;

    /**
     * 文化程度
     */
    @ExcelProperty(value = "文化程度", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "educational_level_code")
    private String education;

    /**
     * 头像图片id
     */
    private Long headImgId;

    /**
     *  头像地址
     */
    private String headImgUrl;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 企业信用代码
     */
    private String unifiedSocialCreditCode;

    /**
     * 证书类型
     */
    private String qualificationDetail;

    /**
     * 证书列表
     */
    private List<SysQualificationVo> qualificationDictVos;
}

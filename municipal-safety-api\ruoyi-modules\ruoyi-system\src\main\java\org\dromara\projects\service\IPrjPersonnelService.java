package org.dromara.projects.service;

import org.dromara.projects.domain.vo.PrjPersonnelVo;
import org.dromara.projects.domain.bo.PrjPersonnelBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 项目人员关联表Service接口
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
public interface IPrjPersonnelService {

    /**
     * 查询项目人员关联表
     *
     * @param projectPersonnelId 主键
     * @return 项目人员关联表
     */
    PrjPersonnelVo queryById(Long projectPersonnelId);

    /**
     * 分页查询项目人员关联表列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目人员关联表分页列表
     */
    TableDataInfo<PrjPersonnelVo> queryPageList(PrjPersonnelBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的项目人员关联表列表
     *
     * @param bo 查询条件
     * @return 项目人员关联表列表
     */
    List<PrjPersonnelVo> queryList(PrjPersonnelBo bo);

    /**
     * 新增项目人员关联表
     *
     * @param bo 项目人员关联表
     * @return 是否新增成功
     */
    Boolean insertByBo(PrjPersonnelBo bo);

    /**
     * 修改项目人员关联表
     *
     * @param bo 项目人员关联表
     * @return 是否修改成功
     */
    Boolean updateByBo(PrjPersonnelBo bo);

    /**
     * 校验并批量删除项目人员关联表信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询项目人员列表
     *
     * @param projectId 项目ID
     * @return 项目人员列表
     */
    List<PrjPersonnelVo> queryPersonnelListByProjectId(Long projectId);

    /**
     * 根据用户id查询项目信息
     * @param personId
     * @return
     */
    List<PrjPersonnelVo> selectPrjPersonByPersonId(String projectId, Long personId);
}

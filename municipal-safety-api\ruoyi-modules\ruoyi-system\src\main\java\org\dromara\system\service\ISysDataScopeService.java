package org.dromara.system.service;

/**
 * 通用 数据权限 服务
 *
 * <AUTHOR>
 */
public interface ISysDataScopeService {

    /**
     * 获取角色自定义权限
     *
     * @param roleId 角色id
     * @return 部门id组
     */
    String getRoleCustom(Long roleId);

    /**
     * 获取部门及以下权限
     *
     * @param deptId 部门id
     * @return 部门id组
     */
    String getDeptAndChild(Long deptId);

    /**
     * 获取专家可访问的项目ID列表
     *
     * @param userId 用户ID
     * @return 项目ID组（用逗号分隔）
     */
    String getExpertProjects(Long userId);

}

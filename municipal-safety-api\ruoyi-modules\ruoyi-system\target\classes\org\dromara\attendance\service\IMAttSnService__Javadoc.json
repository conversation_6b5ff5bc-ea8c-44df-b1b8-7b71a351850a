{"doc": " 考勤设备Service接口\n\n <AUTHOR>\n @date 2025-05-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询考勤设备\n\n @param snId 主键\n @return 考勤设备\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.attendance.domain.bo.MAttSnBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询考勤设备列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 考勤设备分页列表\n"}, {"name": "selectMAttSnList", "paramTypes": ["org.dromara.attendance.domain.bo.MAttSnBo"], "doc": " 分页查询考勤设备列表\n @param bo\n @return\n"}, {"name": "queryList", "paramTypes": ["org.dromara.attendance.domain.bo.MAttSnBo"], "doc": " 查询符合条件的考勤设备列表\n\n @param bo 查询条件\n @return 考勤设备列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.attendance.domain.bo.MAttSnBo"], "doc": " 新增考勤设备\n\n @param bo 考勤设备\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.attendance.domain.bo.MAttSnBo"], "doc": " 修改考勤设备\n\n @param bo 考勤设备\n @return 是否修改成功\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除考勤设备信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
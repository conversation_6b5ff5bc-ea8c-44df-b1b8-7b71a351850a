<template>
  <div>
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="60%" append-to-body @close="handleClose">
      <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
        <div v-show="showSearch" class="mb-[10px]">
          <el-card shadow="never">
            <el-form ref="queryFormRef" :model="queryParams" :inline="true">
              <el-form-item label="专家姓名" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入专家姓名" clearable />
              </el-form-item>
              <el-form-item label="身份证号" prop="idCard">
                <el-input v-model="queryParams.idCard" placeholder="请输入身份证号" clearable />
              </el-form-item>
              <!-- <el-form-item label="专业领域" prop="title">
                <el-select v-model="queryParams.title" placeholder="请选择专业领域" clearable>
                  <el-option v-for="dict in expert_field_options" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item> -->
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>

      <el-card shadow="never">
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="expertList"
          show-overflow-tooltip
          highlight-current-row
          row-key="projectId"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" align="center" width="55" />
          <el-table-column label="名称" align="center" prop="name"> </el-table-column>
          <el-table-column label="身份证号" align="center" prop="idCard" />
          <el-table-column label="性别" align="center" prop="sex">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.sex" />
            </template>
          </el-table-column>
          <el-table-column label="工作单位" align="center" prop="workUnit" />
          <el-table-column label="电话" align="center" prop="phone" />

          <el-table-column label="职称" align="center" prop="title" />
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-card>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="confirmChange">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { listExpert } from '@/api/expert/expert';
import { ExpertVO, ExpertQuery } from '@/api/expert/expert/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { expert_major, sys_user_sex } = toRefs<any>(proxy?.useDict('expert_major', 'sys_user_sex'));

const expertList = ref<ExpertVO[]>([]);
const total = ref(0);
const tableRef = ref();
const showSearch = ref(true);
const dialog = reactive({
  visible: false,
  title: '添加专家'
});
const queryParams = reactive<ExpertQuery>({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  idCard: undefined,
  field: undefined,
  params: {}
});
const queryFormRef = ref<ElFormInstance>();
const buttonLoading = ref(false);
const loading = ref(true);
const selectedData = ref<{ expertId: number | string; expertName: string }[]>([]);

const props = defineProps({
  isShowModel: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits(['update:isShowModel', 'selectionExpertData']);

watch(
  () => props.isShowModel,
  (newVal) => {
    dialog.visible = newVal;
    if (newVal) {
      resetQuery();
      getList();
    }
  }
);
// const handleRowClick = (row: ExpertVO) => {
//   const index = selectedData.value.findIndex((item) => item.expertId === row.expertId);

//   if (index > -1) {
//     // 已存在，移除
//     selectedData.value.splice(index, 1);
//   } else {
//     // 不存在，添加
//     selectedData.value.push(row);
//   }
// };
const handleRowClick = (row: ExpertVO) => {
  const index = selectedData.value.findIndex((item) => item.expertId === row.expertId);

  if (index > -1) {
    // 已存在，移除
    selectedData.value.splice(index, 1);
    tableRef.value.toggleRowSelection(row, false); // 取消勾选
  } else {
    // 不存在，添加
    selectedData.value.push(row);
    tableRef.value.toggleRowSelection(row, true); // 触发勾选
  }
};
const handleSelectionChange = (rows: ExpertVO[]) => {
  const uniqueMap = new Map();
  const uniqueRows = rows.filter((row) => {
    if (uniqueMap.has(row.expertId)) return false;
    uniqueMap.set(row.expertId, true);
    return true;
  });

  selectedData.value = uniqueRows;
};

const getList = async () => {
  loading.value = true;
  try {
    const res = await listExpert(queryParams);
    expertList.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('获取专家列表失败', error);
  } finally {
    loading.value = false;
  }
};

const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

const confirmChange = () => {
  console.log('selectedData.value', selectedData.value);

  if (selectedData.value.length === 0) {
    proxy?.$modal.msgWarning('请至少选择一个专家');
    return;
  }
  emit('selectionProjectData', selectedData.value);
  handleClose();
};

const cancel = () => {
  dialog.visible = false;
};

const handleClose = () => {
  emit('update:showExpertModel', false);
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped></style>

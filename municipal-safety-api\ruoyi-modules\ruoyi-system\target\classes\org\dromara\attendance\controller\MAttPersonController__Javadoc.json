{"doc": " att<PERSON><PERSON>\n\n <AUTHOR>\n @date 2025-06-10\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.attendance.domain.bo.MAttPersonBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询attPerson列表\n"}, {"name": "export", "paramTypes": ["org.dromara.attendance.domain.bo.MAttPersonBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出attPerson列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取attPerson详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.attendance.domain.bo.MAttPersonBo"], "doc": " 新增attPerson\n"}, {"name": "edit", "paramTypes": ["org.dromara.attendance.domain.bo.MAttPersonBo"], "doc": " 修改attPerson\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除attPerson\n\n @param ids 主键串\n"}], "constructors": []}
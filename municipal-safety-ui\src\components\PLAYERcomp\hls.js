import HlsPlayer from "xgplayer-hls"
export default {
    name: 'hlsPlayer',
    data() {
      return {
      };
    },
    mounted() {

    },
    methods:{
        initHls(){
          let formattedDanmus = this.formatDanmus(this.danmuList)
          this.hlsplayer = new HlsPlayer({
            id: 'mse',
            url: this.definitionList[0].url,
            isLive:true,
            plugins:[ HlsPlayer ],
            height: this.propHeight,
            width: this.propWidth,
            controls: this.propControls , // 控制条
            muted: this.propMuted , // 静音
            // controlsList: ['nodownload','nofullscreen','noremoteplayback'] ,  // 选择控制条关闭选项 但是只是针对原生video标签管用 控件使用ignore
            ignores: this.propIgnores, // 关闭内置控件项 'time', 'definition', 'error', 'fullscreen', 'i18n', 'loading','mobile', 'pc', 'play', 'poster', 'progress', 'replay', 'volume'
            fluid: this.propFluid , // 流式布局 ，可使播放器宽度跟随父元素的宽度大小变化，且高度按照配置项中的高度和宽度的比例来变化
            volume: this.propVolume , // 音量预设
            autoplay: this.propAutoplay , // 自动播放
            autoplayMuted: this.propAutoplayMuted , // 自动播放时静音
            definitionActive: this.propDefinitionActive , // 清晰度切换方式 click || hover
            pip: this.propPip , // 开启画中画
            cssFullscreen: this.propCssFullscreen ,// 网页全屏
            poster: this.propPoster , // 封面图
            lastPlayTime: 0, //视频起播时间（单位：秒）
            download: this.propDownload , // 是否下载
            rotateFullscreen: this.propRotateFullscreen , // 样式横屏全屏
            errorTips: this.propErrorTips, // 错误提示
            rotate: this.propRotate ,
            screenShot: {   // 截图+
              saveImg: true,
              quality: 0.92, // 截图质量 默认0.92
              type: 'image/png', // 格式
              format: '.png'   // 后缀名
            },
            controlPlugins: [
  
            ],
            danmu : {
              comments: formattedDanmus, // 弹幕数据
              area: {  //弹幕显示区域
                start: 0, //区域顶部到播放器顶部所占播放器高度的比例
                end: 1 //区域底部到播放器顶部所占播放器高度的比例
              },
              closeDefaultBtn: false, //开启此项后不使用默认提供的弹幕开关，默认使用西瓜播放器提供的开关
              defaultOff: false, //开启此项后弹幕不会初始化，默认初始化弹幕
              panel:true //开启弹幕面板
            },
            execBeforePluginsCall: [() => {  // 插件失效前执行
              console.log('Execute before plugins call')
            }],
            // playbackRate: [0.5, 0.75, 1, 1.5, 2], // 倍速列表
            'x5-video-player-type': 'h5' , // 微信同层播放
            'x5-video-player-fullscreen': false , // 微信全屏播放
            playsinline: false , // 内联模式 适用于ios和微信
            airplay: false , // 适用ios设备上视频传送到支持airplay设备播放
            referrerPolicy: 'strict-origin-when-cross-origin' // 设置更严格的 referrer 策略
          });
          this.hlsplayer.emit('resourceReady', this.definitionList );// 清晰度列表
        },
    }
}
{"doc": " 线程相关工具类.\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "shutdownAndAwaitTermination", "paramTypes": ["java.util.concurrent.ExecutorService"], "doc": " 停止线程池\n 先使用shutdown, 停止接收新任务并尝试完成所有已存在任务.\n 如果超时, 则调用shutdownNow, 取消在workQueue中Pending的任务,并中断所有阻塞函数.\n 如果仍然超時，則強制退出.\n 另对在shutdown时线程本身被调用中断做了处理.\n"}, {"name": "printException", "paramTypes": ["java.lang.Runnable", "java.lang.Throwable"], "doc": " 打印线程异常信息\n"}], "constructors": []}
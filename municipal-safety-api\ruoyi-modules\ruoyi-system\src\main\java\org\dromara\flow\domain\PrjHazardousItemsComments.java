package org.dromara.flow.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 质监站隐患清单整改对象 prj_hazardous_items_comments
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prj_hazardous_items_comments")
public class PrjHazardousItemsComments extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 问题点id
     */
    private String question;

    /**
     * 整改时限
     */
    private Long timeLimit;

    /**
     * 整改时限类型 1小时 2天
     */
    private Long timeType;

    /**
     * 限期整改文件
     */
    private Long correctionsFile;

    /**
     * 限期整改内容
     */
    private String correctionsContent;

    /**
     * 限期整改报告
     */
    private Long correctionsBackFile;

    /**
     * 停工通知文件
     */
    private Long suspensionFile;

    /**
     * 停工通知内容
     */
    private String suspensionContent;

    /**
     * 停工整改报告
     */
    private Long suspensionBackFile;

    /**
     * 行政处罚文件
     */
    private Long penaltyFile;

    /**
     * 行政处罚内容
     */
    private String penaltyContent;

    /**
     * 行政处罚报告
     */
    private Long penaltyBackFile;

    /**
     * 业务id
     */
    private String taskId;

    /**
     * 删除标志 (字典: 0[存在], 1[删除])
     */
    @TableLogic
    private String delFlag;

    /**
     * 附件列表
     */
    @TableField(exist = false)
    private List<PrjHazardousItemsFile> elseFile;
}

package org.dromara.flow.domain.bo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.PrjHazardousItemsFile;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {},
    imports = {}
)
public interface PrjHazardousItemsFileBoToPrjHazardousItemsFileMapper extends BaseMapper<PrjHazardousItemsFileBo, PrjHazardousItemsFile> {
}

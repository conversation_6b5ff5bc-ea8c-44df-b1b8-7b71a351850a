package org.dromara.flow.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.dromara.ai.domain.AiHazAnalysisTasksResult;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksResultVo;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksVo;
import org.dromara.ai.mapper.AiHazAnalysisTasksMapper;
import org.dromara.ai.mapper.AiHazAnalysisTasksResultMapper;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.flow.domain.PrjHazardousItemsComments;
import org.dromara.flow.domain.PrjHazardousItemsFile;
import org.dromara.flow.domain.PrjHazardousItemsSpecialist;
import org.dromara.flow.domain.vo.PrjHazardousItemsCommentsVo;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistVo;
import org.dromara.flow.mapper.PrjHazardousItemsCommentsMapper;
import org.dromara.flow.mapper.PrjHazardousItemsFileMapper;
import org.dromara.flow.mapper.PrjHazardousItemsSpecialistMapper;
import org.dromara.flow.service.CustomFlowService;
import org.dromara.projects.domain.vo.ItemsAiDetailVO;
import org.dromara.projects.domain.vo.PrjHazardousItemsVo;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.domain.vo.ViolationVO;
import org.dromara.projects.mapper.PrjHazardousItemsMapper;
import org.dromara.projects.mapper.PrjProjectsMapper;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.mapper.SysDeptMapper;
import org.dromara.warm.flow.orm.entity.FlowInstance;
import org.dromara.warm.flow.orm.entity.FlowTask;
import org.dromara.warm.flow.orm.mapper.FlowInstanceMapper;
import org.dromara.warm.flow.orm.mapper.FlowTaskMapper;
import org.dromara.workflow.domain.bo.FlowTaskBo;
import org.dromara.workflow.domain.vo.FlowTaskVo;
import org.dromara.workflow.service.IFlwTaskService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Zu Da
 * @date 2025/5/28 15:10
 * @Description TODO
 * @Version 1.0
 */
@Service
public class CustomFlowServiceImpl implements CustomFlowService {

    @Resource
    private FlowTaskMapper flowTaskMapper;

    @Resource
    private FlowInstanceMapper flowInstanceMapper;

    @Resource
    private AiHazAnalysisTasksMapper aiHazAnalysisTasksMapper;

    @Resource
    private AiHazAnalysisTasksResultMapper aiHazAnalysisTasksResultMapper;

    @Resource
    private IFlwTaskService flwTaskService;

    @Resource
    private PrjProjectsMapper prjProjectsMapper;

    @Resource
    private PrjHazardousItemsMapper prjHazardousItemsMapper;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private PrjHazardousItemsCommentsMapper prjHazardousItemsCommentsMapper;

    @Resource
    private PrjHazardousItemsFileMapper prjHazardousItemsFileMapper;

    @Resource
    private PrjHazardousItemsSpecialistMapper prjHazardousItemsSpecialistMapper;


    @Override
    public JSONObject getVariable(String busId) {

        LambdaQueryWrapper<FlowInstance> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FlowInstance::getBusinessId, busId)
            .select(FlowInstance::getId, FlowInstance::getVariable);

        FlowInstance flowInstance = flowInstanceMapper.selectOne(wrapper);

        String variable = flowInstance.getVariable();

        return JSONObject.parseObject(variable);
    }

    @Override
    public ItemsAiDetailVO getAiHazAnalysisTaskDetail(Long aiId) {

        ItemsAiDetailVO detailVO = new ItemsAiDetailVO();

        AiHazAnalysisTasksVo tasksVo = aiHazAnalysisTasksMapper.selectVoById(aiId);

        detailVO.setPhotoDocumentId(tasksVo.getPhotoDocumentId());
        detailVO.setAiPhotoDocumentId(tasksVo.getAiPhotoDocumentId());
        detailVO.setLocationDescription(tasksVo.getLocationDescription());

        LambdaQueryWrapper<AiHazAnalysisTasksResult> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AiHazAnalysisTasksResult::getTaskId, aiId);

        //查询问题列表
        List<AiHazAnalysisTasksResultVo> tasksResultVos = aiHazAnalysisTasksResultMapper.selectVoList(wrapper);

        //解析json
        if (ArrayUtil.isNotEmpty(tasksResultVos)) {

            List<ViolationVO> violationVOS = new ArrayList<>();

            for (AiHazAnalysisTasksResultVo resultVo : tasksResultVos) {
                ViolationVO violationVO = new ViolationVO();

                violationVO.setLevel(resultVo.getLevel());
                violationVO.setViolation(resultVo.getViolation());
                violationVO.setRegulation(resultVo.getRegulation());
                violationVO.setMeasure(resultVo.getMeasure());
                violationVO.setResultId(resultVo.getResultId());

                violationVOS.add(violationVO);
            }
            detailVO.setViolations(violationVOS);
        }

        return detailVO;
    }

    @Override
    public TableDataInfo<FlowTaskVo> pageByTaskWaitZjj(FlowTaskBo flowTaskBo, PageQuery pageQuery) {

        TableDataInfo<FlowTaskVo> tableDataInfo = flwTaskService.pageByTaskWait(flowTaskBo, pageQuery);

        List<FlowTaskVo> list = tableDataInfo.getRows();

        if (!list.isEmpty()) {

            List<Long> instanceId = list.stream().map(FlowTaskVo::getInstanceId).toList();

            LambdaQueryWrapper<FlowInstance> wrapper = Wrappers.lambdaQuery();
            wrapper.in(FlowInstance::getId, instanceId)
                .select(FlowInstance::getId, FlowInstance::getVariable);

            List<FlowInstance> flowInstances = flowInstanceMapper.selectList(wrapper);

            Map<Long, String> aiTaskIdMap = flowInstances.stream().collect(Collectors.toMap(FlowInstance::getId, s -> {
                JSONObject jsonObject = JSON.parseObject(s.getVariable());
                return jsonObject.containsKey("ai_task_id") ? jsonObject.getString("ai_task_id") : null;
            }));

            List<AiHazAnalysisTasksVo> aiHazAnalysisTasksVos = aiHazAnalysisTasksMapper.selectVoByIds(aiTaskIdMap.values());

            Map<Long, AiHazAnalysisTasksVo> tasksVoMap = aiHazAnalysisTasksVos.stream().collect(Collectors.toMap(AiHazAnalysisTasksVo::getTaskId, t -> t));

            for (FlowTaskVo flowTaskVo : list) {
                String aiTaskId = aiTaskIdMap.get(flowTaskVo.getInstanceId());

                AiHazAnalysisTasksVo tasksVo = tasksVoMap.get(Long.valueOf(aiTaskId));

                if (tasksVo != null) {
                    PrjProjectsVo prjProjectsVo = prjProjectsMapper.selectVoById(tasksVo.getProjectId());

                    PrjHazardousItemsVo hazardousItemsVo = prjHazardousItemsMapper.selectVoById(tasksVo.getItemId());

                    flowTaskVo.setProjectName(prjProjectsVo.getProjectName());
                    flowTaskVo.setItemName(hazardousItemsVo.getItemName());
                    flowTaskVo.setConstruction(prjProjectsVo.getConstructionOrgName());
                }
            }
        }

        return tableDataInfo;
    }

    /**
     * 额外删除流程
     *
     * @param instanceIds
     */
    @Override
    public void removeByInstanceIds(List<Long> instanceIds) {

        List<FlowInstance> flowInstances = flowInstanceMapper.selectByIds(instanceIds);

        List<String> taskIds = flowInstances.stream().map(FlowInstance::getBusinessId).toList();

        List<Long> commonFileIds = new ArrayList<>();

        //删除质监站数据
        LambdaQueryWrapper<PrjHazardousItemsComments> qualityWrapper = Wrappers.lambdaQuery();
        qualityWrapper.in(PrjHazardousItemsComments::getTaskId, taskIds);

        List<PrjHazardousItemsCommentsVo> itemsCommentsVos = prjHazardousItemsCommentsMapper.selectVoList(qualityWrapper);

        if (!itemsCommentsVos.isEmpty()) {
            itemsCommentsVos.forEach(s -> {
                if (ObjectUtils.isNotEmpty(s.getCorrectionsFile())) {
                    commonFileIds.add(s.getCorrectionsFile());
                }
                if (ObjectUtils.isNotEmpty(s.getCorrectionsBackFile())) {
                    commonFileIds.add(s.getCorrectionsBackFile());
                }
                if (ObjectUtils.isNotEmpty(s.getSuspensionFile())) {
                    commonFileIds.add(s.getSuspensionFile());
                }
                if (ObjectUtils.isNotEmpty(s.getSuspensionBackFile())) {
                    commonFileIds.add(s.getSuspensionBackFile());
                }
                if (ObjectUtils.isNotEmpty(s.getPenaltyFile())) {
                    commonFileIds.add(s.getPenaltyFile());
                }
                if (ObjectUtils.isNotEmpty(s.getPenaltyBackFile())) {
                    commonFileIds.add(s.getPenaltyBackFile());
                }
            });

            List<String> qualityTaskIds = itemsCommentsVos.stream().map(PrjHazardousItemsCommentsVo::getTaskId).toList();

            List<Long> qualityIds = itemsCommentsVos.stream().map(PrjHazardousItemsCommentsVo::getId).toList();

            prjHazardousItemsCommentsMapper.deleteByIds(qualityIds);

            LambdaQueryWrapper<PrjHazardousItemsFile> fileWrapper = new LambdaQueryWrapper<>();
            fileWrapper.in(PrjHazardousItemsFile::getTaskId, qualityTaskIds)
                .eq(PrjHazardousItemsFile::getServiceType, "quality_supervision_department");

            prjHazardousItemsFileMapper.delete(fileWrapper);
        }

        //删除厅局自动工单数据
        LambdaQueryWrapper<PrjHazardousItemsSpecialist> wrapper = Wrappers.lambdaQuery();
        wrapper.in(PrjHazardousItemsSpecialist::getTaskId, taskIds);

        List<PrjHazardousItemsSpecialistVo> specialistVos = prjHazardousItemsSpecialistMapper.selectVoList(wrapper);

        if (!specialistVos.isEmpty()) {
            specialistVos.forEach(s -> {
                if (StringUtils.isNoneEmpty(s.getDownPushFile())) {
                    commonFileIds.addAll(Arrays.stream(s.getDownPushFile().split(",")).map(Long::valueOf).toList());
                }
            });

            List<Long> specialistIds = specialistVos.stream().map(PrjHazardousItemsSpecialistVo::getId).toList();

            prjHazardousItemsSpecialistMapper.deleteByIds(specialistIds);

        }

        //删除文件
        if (!commonFileIds.isEmpty()) {
            prjHazardousItemsFileMapper.deleteByIds(commonFileIds);
        }
    }
}

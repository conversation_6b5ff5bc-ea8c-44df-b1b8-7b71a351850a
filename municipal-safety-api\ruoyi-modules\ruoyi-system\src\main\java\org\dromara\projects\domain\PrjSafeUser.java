package org.dromara.projects.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 【安拆任务】项目人员对象 prj_safe_user
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prj_safe_user")
public class PrjSafeUser extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "sale_user_id")
    private Long saleUserId;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 岗位类型
     * 项目经
     * 理:ProjectManager
     * 安全总监:SafetyDirector
     * 安全经
     * 理:SafetyManager
     * 安全员:SafetyOfficer
     * 机管员:MachineKeeper
     */
    private String positionType;

    /**
     * 关联安拆prj_safe_task.open_task_id
     */
    private Long openTaskId;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private Long delFlag;

}

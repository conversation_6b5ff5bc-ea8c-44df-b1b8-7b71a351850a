package org.dromara.flow.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.flow.domain.PrjHazardousItemsComments;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsCommentsBoToPrjHazardousItemsCommentsMapperImpl implements PrjHazardousItemsCommentsBoToPrjHazardousItemsCommentsMapper {

    @Override
    public PrjHazardousItemsComments convert(PrjHazardousItemsCommentsBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsComments prjHazardousItemsComments = new PrjHazardousItemsComments();

        prjHazardousItemsComments.setSearchValue( arg0.getSearchValue() );
        prjHazardousItemsComments.setCreateDept( arg0.getCreateDept() );
        prjHazardousItemsComments.setCreateBy( arg0.getCreateBy() );
        prjHazardousItemsComments.setCreateTime( arg0.getCreateTime() );
        prjHazardousItemsComments.setUpdateBy( arg0.getUpdateBy() );
        prjHazardousItemsComments.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            prjHazardousItemsComments.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        prjHazardousItemsComments.setId( arg0.getId() );
        prjHazardousItemsComments.setQuestion( arg0.getQuestion() );
        prjHazardousItemsComments.setTimeLimit( arg0.getTimeLimit() );
        prjHazardousItemsComments.setTimeType( arg0.getTimeType() );
        prjHazardousItemsComments.setCorrectionsFile( arg0.getCorrectionsFile() );
        prjHazardousItemsComments.setCorrectionsContent( arg0.getCorrectionsContent() );
        prjHazardousItemsComments.setSuspensionFile( arg0.getSuspensionFile() );
        prjHazardousItemsComments.setSuspensionContent( arg0.getSuspensionContent() );
        prjHazardousItemsComments.setPenaltyFile( arg0.getPenaltyFile() );
        prjHazardousItemsComments.setPenaltyContent( arg0.getPenaltyContent() );
        prjHazardousItemsComments.setTaskId( arg0.getTaskId() );

        return prjHazardousItemsComments;
    }

    @Override
    public PrjHazardousItemsComments convert(PrjHazardousItemsCommentsBo arg0, PrjHazardousItemsComments arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setQuestion( arg0.getQuestion() );
        arg1.setTimeLimit( arg0.getTimeLimit() );
        arg1.setTimeType( arg0.getTimeType() );
        arg1.setCorrectionsFile( arg0.getCorrectionsFile() );
        arg1.setCorrectionsContent( arg0.getCorrectionsContent() );
        arg1.setSuspensionFile( arg0.getSuspensionFile() );
        arg1.setSuspensionContent( arg0.getSuspensionContent() );
        arg1.setPenaltyFile( arg0.getPenaltyFile() );
        arg1.setPenaltyContent( arg0.getPenaltyContent() );
        arg1.setTaskId( arg0.getTaskId() );

        return arg1;
    }
}

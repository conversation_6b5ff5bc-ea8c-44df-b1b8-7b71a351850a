{"doc": " 隐患AI分析结果\n\n <AUTHOR>\n @date 2025-05-15\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksResultBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询隐患AI分析结果列表\n"}, {"name": "export", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksResultBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出隐患AI分析结果列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取隐患AI分析结果详细信息\n\n @param resultId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksResultBo"], "doc": " 新增隐患AI分析结果\n"}, {"name": "edit", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksResultBo"], "doc": " 修改隐患AI分析结果\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除隐患AI分析结果\n\n @param resultIds 主键串\n"}], "constructors": []}
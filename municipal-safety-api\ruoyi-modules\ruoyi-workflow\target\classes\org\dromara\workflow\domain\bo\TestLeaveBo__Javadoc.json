{"doc": " 请假业务对象 test_leave\n\n <AUTHOR>\n @date 2023-07-21\n", "fields": [{"name": "id", "doc": " 主键\n"}, {"name": "leaveType", "doc": " 请假类型\n"}, {"name": "startDate", "doc": " 开始时间\n"}, {"name": "endDate", "doc": " 结束时间\n"}, {"name": "leaveDays", "doc": " 请假天数\n"}, {"name": "startLeaveDays", "doc": " 开始时间\n"}, {"name": "endLeaveDays", "doc": " 结束时间\n"}, {"name": "remark", "doc": " 请假原因\n"}, {"name": "status", "doc": " 状态\n"}], "enumConstants": [], "methods": [], "constructors": []}
package org.dromara.monito.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.ContentType;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.ai.domain.bo.AiHazAnalysisTasksBo;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.ObjectUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.dp.domain.bo.DataViewBo;
import org.dromara.monito.domain.DeviceMonito;
import org.dromara.monito.domain.bo.CaptureBo;
import org.dromara.monito.domain.bo.DeviceMonitoBo;
import org.dromara.monito.domain.vo.DeviceMonitoTreeInfoVo;
import org.dromara.monito.domain.vo.DeviceMonitoVo;
import org.dromara.monito.handle.DeviceJobHandle;
import org.dromara.monito.mapper.DeviceMonitoMapper;
import org.dromara.monito.service.IDeviceMonitoService;
import org.dromara.projects.service.IPrjProjectsService;
import org.dromara.system.domain.Division;
import org.dromara.system.domain.bo.DivisionBo;
import org.dromara.system.domain.vo.DivisionVo;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.mapper.DivisionMapper;
import org.dromara.system.service.IDivisionService;
import org.dromara.system.service.ISysOssService;
import org.dromara.util.YsyClient;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.time.Year;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 监控管理Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-05-18
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DeviceMonitoServiceImpl implements IDeviceMonitoService {

    private final DeviceMonitoMapper baseMapper;

    private final IDivisionService divisionService;

    private final IPrjProjectsService prjProjectsService;

    private final DeviceJobHandle deviceJobHandle;

    private final ISysOssService ossService;

    private final YsyClient ysyClient;

    private final DivisionMapper divisionMapper;


    /**
     * 查询监控管理
     *
     * @param monitoId 主键
     * @return 监控管理
     */
    @Override
    public DeviceMonitoVo queryById(Long monitoId) {
        return baseMapper.selectVoById(monitoId);
    }

    /**
     * 分页查询监控管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 监控管理分页列表
     */
    @Override
    public TableDataInfo<DeviceMonitoVo> queryPageList(DeviceMonitoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<DeviceMonito> lqw = buildQueryWrapper(bo);
        Page<DeviceMonitoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的监控管理列表
     *
     * @param bo 查询条件
     * @return 监控管理列表
     */
    @Override
    public List<DeviceMonitoVo> queryList(DeviceMonitoBo bo) {
        LambdaQueryWrapper<DeviceMonito> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DeviceMonito> buildQueryWrapper(DeviceMonitoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DeviceMonito> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(DeviceMonito::getMonitoId);
        lqw.eq(bo.getProjectId() != null, DeviceMonito::getProjectId, bo.getProjectId());
        lqw.eq(bo.getItemId() != null, DeviceMonito::getItemId, bo.getItemId());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), DeviceMonito::getDeviceName, bo.getDeviceName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceType()), DeviceMonito::getDeviceType, bo.getDeviceType());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceCode()), DeviceMonito::getDeviceCode, bo.getDeviceCode());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceStatus()), DeviceMonito::getDeviceStatus, bo.getDeviceStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getEnableSnapshot()), DeviceMonito::getEnableSnapshot, bo.getEnableSnapshot());
        lqw.eq(bo.getSnapshotTime() != null, DeviceMonito::getSnapshotTime, bo.getSnapshotTime());
        lqw.eq(StringUtils.isNotBlank(bo.getRemarks()), DeviceMonito::getRemarks, bo.getRemarks());
        return lqw;
    }

    /**
     * 新增监控管理
     *
     * @param bo 监控管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(DeviceMonitoBo bo) {
        DeviceMonito add = MapstructUtils.convert(bo, DeviceMonito.class);
        validEntityBeforeSave(add);


        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            //任务绑定
            Long jobId = ysyClient.addClusterJob(add.getMonitoId(), add.getDeviceName(), add.getSnapshotTime().intValue());
            add.setJobId(jobId);
            this.baseMapper.updateById(add);
            bo.setMonitoId(add.getMonitoId());
            ysyClient.stopJob(jobId);
        }
        return flag;
    }

    /**
     * 修改监控管理
     *
     * @param bo 监控管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(DeviceMonitoBo bo) {
        DeviceMonito update = MapstructUtils.convert(bo, DeviceMonito.class);
        validEntityBeforeSave(update);

        boolean b = baseMapper.updateById(update) > 0;

        assert update != null;
        update = this.baseMapper.selectById(update.getMonitoId());

        if (update.getJobId() != null) {
            //停止任务并修改
            ysyClient.stopJob(update.getJobId());
            ysyClient.updateClusterJob(update.getJobId(), update.getSnapshotTime().intValue());
        } else {
            //任务绑定
            Long jobId = ysyClient.addClusterJob(update.getMonitoId()
                , update.getDeviceName(), update.getSnapshotTime().intValue());
            update.setJobId(jobId);
            baseMapper.updateById(update);
            ysyClient.stopJob(jobId);
        }

        if ("1".equals(update.getEnableSnapshot())) {
            //注册并启动任务
            deviceJobHandle.registerJobAndStart(update);
        } else {
            //卸载并停止任务
            deviceJobHandle.unloadAndStop(update.getJobId());
        }
        return b;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(DeviceMonito entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除监控管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }

        List<DeviceMonito> deviceMonitos = this.baseMapper.selectByIds(ids);

        for (DeviceMonito monito : deviceMonitos) {
            if (ObjectUtils.isNotEmpty(monito.getJobId())) {
                ysyClient.stopJob(monito.getJobId());
                ysyClient.deleteJob(monito.getJobId());
            }
        }

        return baseMapper.deleteByIds(ids) > 0;
    }


    @Override
    public List<DeviceMonitoVo> pageList(DeviceMonitoBo bo) {
        return baseMapper.baseMapper(bo);
    }

    @Override
    public DeviceMonitoVo selectById(Long monitoId) {
        return baseMapper.getById(monitoId);
    }

    @Override
    public Boolean capture(CaptureBo captureBo) {

        DeviceMonitoVo monitoVo = this.baseMapper.selectVoById(captureBo.getMonitoId());

        String imageBuild = StringUtils.substringAfter(captureBo.getBase64(), "base64,");

        byte[] imageBytes = Base64.decode(imageBuild);

        SysOssVo sysOssVo = ossService.upload(new ByteArrayInputStream(imageBytes), IdUtil.fastSimpleUUID()
            , ".jpeg", ContentType.MULTIPART.getValue());

        AiHazAnalysisTasksBo bo = deviceJobHandle.uploadHazRecord(monitoVo.getProjectId(), monitoVo.getItemId()
            , monitoVo.getDeviceName() + "【自动抓拍】" + monitoVo.getMonitoId(), null);

        bo.setPhotoDocumentId(sysOssVo.getOssId());

        deviceJobHandle.pushAiResolver(bo, sysOssVo.getUrl());

        return true;
    }


//    @Override
//    public Boolean capturePlane(CaptureBo captureBo) {
//
//        DeviceMonitoVo monitoVo = this.baseMapper.selectVoById(captureBo.getMonitoId());
//
//        AiHazAnalysisTasksBo bo = deviceJobHandle.uploadHazRecord(monitoVo.getProjectId(), monitoVo.getItemId()
//            , monitoVo.getDeviceName() + "【手动抓拍】" + monitoVo.getMonitoId(), null);
//
//        bo.setPhotoDocumentId(captureBo.getPhotoId());
//
//        deviceJobHandle.pushAiResolver(bo, captureBo.getUrl());
//
//        return true;
//    }

    @Override
    public List<Map<String, Object>> getDeviceMonitoTreeInfo(DeviceMonitoBo bo) {
        List<DeviceMonitoTreeInfoVo> list = JSON.parseArray(JSON.toJSONString(baseMapper.getDeviceMonitoTreeInfo(bo)), DeviceMonitoTreeInfoVo.class);
        Map<String, List<DeviceMonitoTreeInfoVo>> provinceMap = list.stream().collect(Collectors.groupingBy(DeviceMonitoTreeInfoVo::getProvinceName));
        List<Map<String, Object>> sfMapList = new ArrayList<>();
        provinceMap.forEach((k, v) -> {
            Map<String, List<DeviceMonitoTreeInfoVo>> cityMap = list.stream().filter(c -> c.getProvinceName().equals(k)).collect(Collectors.groupingBy(DeviceMonitoTreeInfoVo::getCityName));
            List<Map<String, Object>> cMapList = new ArrayList<>();
            cityMap.forEach((ck, cv) -> {
                Map<String, List<DeviceMonitoTreeInfoVo>> qxMap = list.stream().filter(c -> c.getCityName().equals(ck)).collect(Collectors.groupingBy(DeviceMonitoTreeInfoVo::getDistrictName));
                List<Map<String, Object>> qxMapList = new ArrayList<>();
                qxMap.forEach((qk, qv) -> {
                    Map<String, Object> qMap = new HashMap<>();
                    Map<String, List<DeviceMonitoTreeInfoVo>> xmMap = list.stream().filter(c -> c.getDistrictName().equals(qk)).collect(Collectors.groupingBy(DeviceMonitoTreeInfoVo::getProjectName));
                    List<Map<String, Object>> xmMapList = new ArrayList<>();
                    xmMap.forEach((xmk, xmv) -> {
                        Map<String, Object> xmmMap = new HashMap<>();
                        Map<String, List<DeviceMonitoTreeInfoVo>> gcMap = list.stream().filter(c -> c.getProjectName().equals(xmk) && c.getDistrictName().equals(qk))
                            .collect(Collectors.groupingBy(DeviceMonitoTreeInfoVo::getItemName));
                        List<Map<String, Object>> gcMapList = new ArrayList<>();
                        gcMap.forEach((gck, gcv) -> {
                            Map<String, Object> gccMap = new HashMap<>();
                            List<DeviceMonitoTreeInfoVo> sxtList = list.stream().filter(sxt -> sxt.getCityName().equals(ck) && sxt.getDistrictName()
                                .equals(qk) && sxt.getProjectName().equals(xmk) && sxt.getItemName().equals(gck
                            )).collect(Collectors.toList());
                            gccMap.put("name", gck);
                            gccMap.put("list", sxtList);
                            gcMapList.add(gccMap);
                        });
                        xmmMap.put("name", xmk);
                        xmmMap.put("list", gcMapList);
                        xmMapList.add(xmmMap);
                    });
                    qMap.put("name", qk);
                    qMap.put("list", xmMapList);
                    qxMapList.add(qMap);
                });
                Map<String, Object> cMap = new HashMap<>();
                cMap.put("name", ck);
                cMap.put("list", qxMapList);
                cMapList.add(cMap);
            });
            Map<String, Object> sf = new HashMap<>();
            sf.put("name", k);
            sf.put("list", cMapList);
            sfMapList.add(sf);
        });
        return sfMapList;
    }

    @Override
    public List<Map<String, Object>> getItemNum(DataViewBo bo) {
        return baseMapper.getItemNum(bo);
    }

    @Override
    public List<Map<String, Object>> getDangerNum(DataViewBo bo) {
        return baseMapper.getDangerNum(bo);
    }


    @Override
    public List<Map<String, Object>> getProItemList(DataViewBo bo) {
        return baseMapper.getProItemList(bo);
    }

    @Override
    public Map<String, Object> getYearItemNum(DataViewBo bo) {
        List<Integer> yearList = new ArrayList<>();
        int currentYear = Year.now().getValue();
        for (int i = 0; i < 5; i++) {
            yearList.add(currentYear - i);
        }
        Map<String, Object> resulMap = new HashMap<>();
        List<Integer> weidadataList = new ArrayList<>();
        List<Integer> chaodataList = new ArrayList<>();
        Collections.sort(yearList);
        for (Integer year : yearList) {
            bo.setStartDate(year);
            bo.setItemsType("1");   //危大
            Integer weida = baseMapper.getYearItemNum(bo);
            bo.setItemsType("2");   //超危大
            Integer chao = baseMapper.getYearItemNum(bo);
            weidadataList.add(weida);
            chaodataList.add(chao);
        }
        resulMap.put("time", yearList);
        resulMap.put("weidadata", weidadataList);
        resulMap.put("chaodata", chaodataList);
        return resulMap;
    }


    @Override
    public Map<String, Object> getAreaItemNum(DataViewBo bo) {
        Division division = null;
        List<Division> divisions;
        if (StringUtils.isNotBlank(bo.getAreaCode())) {
            divisions = divisionMapper.selectList(new LambdaQueryWrapper<Division>().eq(Division::getParentCode, bo.getAreaCode()).orderByAsc(Division::getDivisionCode));
        } else {
            //全国省份
            divisions = divisionMapper.selectList(new LambdaQueryWrapper<Division>().eq(Division::getParentCode, "100000").orderByAsc(Division::getDivisionCode));
        }
        List<Integer> weidadataList = new ArrayList<>();
        List<Integer> chaodataList = new ArrayList<>();
        for (Division d : divisions) {
            bo.setLevel(d.getLevel());
            bo.setAreaCode(d.getDivisionCode());
            bo.setItemsType("1");   //危大
            Integer weida = baseMapper.getAreaItemNum(bo);
            bo.setItemsType("2");   //超危大
            Integer chao = baseMapper.getAreaItemNum(bo);
            weidadataList.add(weida);
            chaodataList.add(chao);
        }
        Map<String, Object> resulMap = new HashMap<>();
        resulMap.put("address", divisions.stream().map(Division::getDivisionName).collect(Collectors.toList()));
        resulMap.put("weidadata", weidadataList);
        resulMap.put("chaodata", chaodataList);
        return resulMap;
    }
}

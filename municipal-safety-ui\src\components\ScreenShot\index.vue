<template>
  <!--截图组件-->
  <screen-short v-if="screenshotStatus" @destroy-component="destroyComponent" @get-image-data="getImg"></screen-short>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { uploadImage } from '@/api/projects/hazard_list_edit/index'
import { hazard_upload_img } from '@/api/projects/hazard_list_edit/types'

const emit = defineEmits(["isScreenShotStatus", "annotation"]);
// 接收父组件传递过来的参数
const props = defineProps({
  isEnableScreenshotStatus: {
    type: Boolean,
    default: false,
  }
})
// 是否启用截图
const screenshotStatus = ref<boolean>(false);
// 监听父组件传递过来的参数
watch(() => props.isEnableScreenshotStatus, (newVal) => {
  screenshotStatus.value = newVal;
})
// 销毁组件函数
const destroyComponent = function (status: boolean) {
  screenshotStatus.value = status;
  emit("isScreenShotStatus", status);
}
// 获取裁剪区域图片信息
const getImg = function (base64: string) {
  uploadImage({ img: base64 } as hazard_upload_img).then(res => {
    if (res.code == 200) {
      emit("annotation", res.data);
    }
  })
}
</script>

<style lang="scss">
#screenShotPanel {
  #toolPanel {
    height: auto;
  }

  #optionPanel {
    height: auto;
  }
}
</style>
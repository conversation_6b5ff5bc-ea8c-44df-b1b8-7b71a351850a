package org.dromara.facility.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.bo.LnWaterBoToLnWaterMapper;
import org.dromara.facility.domain.vo.LnWaterVo;
import org.dromara.facility.domain.vo.LnWaterVoToLnWaterMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {LnWaterBoToLnWaterMapper.class,LnWaterVoToLnWaterMapper.class},
    imports = {}
)
public interface LnWaterToLnWaterVoMapper extends BaseMapper<LnWater, LnWaterVo> {
}

import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { Prj_hazardous_itemsVO, Prj_hazardous_itemsForm, Prj_hazardous_itemsQuery } from '@/api/projects/prj_hazardous_items/types';

/**
 * 查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表
 * @param query
 * @returns {*}
 */

export const listPrj_hazardous_items = (query?: Prj_hazardous_itemsQuery): AxiosPromise<Prj_hazardous_itemsVO[]> => {
  return request({
    url: '/projects/prj_hazardous_items/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询[项目管理] 列出项目内具体的危险性较大的分部分项工程详细
 * @param itemId
 */
export const getPrj_hazardous_items = (itemId: string | number): AxiosPromise<Prj_hazardous_itemsVO> => {
  return request({
    url: '/projects/prj_hazardous_items/' + itemId,
    method: 'get'
  });
};
export const getPrj_hazardous_items_detail = (itemId: string | number): AxiosPromise<any> => {
  return request({
    url: '/projects/prj_hazardous_items/detail/' + itemId,
    method: 'get'
  });
};
/**
 * 新增[项目管理] 列出项目内具体的危险性较大的分部分项工程
 * @param data
 */
export const addPrj_hazardous_items = (data: Prj_hazardous_itemsForm) => {
  return request({
    url: '/projects/prj_hazardous_items',
    method: 'post',
    data: data
  });
};

/**
 * 修改[项目管理] 列出项目内具体的危险性较大的分部分项工程
 * @param data
 */
export const updatePrj_hazardous_items = (data: Prj_hazardous_itemsForm) => {
  return request({
    url: '/projects/prj_hazardous_items',
    method: 'put',
    data: data
  });
};

/**
 * 删除[项目管理] 列出项目内具体的危险性较大的分部分项工程
 * @param itemId
 */
export const delPrj_hazardous_items = (itemId: string | number | Array<string | number>) => {
  return request({
    url: '/projects/prj_hazardous_items/' + itemId,
    method: 'delete'
  });
};


// 管理员看到的总数据
export const listPrj_hazardous_items_admin = (query?: Prj_hazardous_itemsQuery): AxiosPromise<Prj_hazardous_itemsVO[]> => {
  return request({
    url: '/projects/prj_hazardous_items/alist',
    method: 'get',
    params: query
  });
};
// 隐患清单详细信息
export const getPrj_hazardous_items_ai_detail = (taskId: string | number): AxiosPromise<any> => {
  return request({
    url: '/projects/prj_hazardous_items/aiDangerDetail/' + taskId,
    method: 'get'
  });
}
//获取所有项目信息
export const get_prj_search_data = (): AxiosPromise<any> => {
  return request({
    url: '/projects/prj_projects/allForSearch',
    method: 'get'
  });
}
// 通过项目获取 工程列表
export const getItemList = (projectId) => {
  return request({
    url: '/projects/prj_hazardous_items/getItemList/' + projectId,
    method: 'get'
  });
}
// 无人机直播转码
export const getUavTranscoding = (data: any) => {
  return request({
    url: '/api/index/api/addStreamProxy',
    method: 'post',
    baseURL: '/wrj-api',
    data
  });
}
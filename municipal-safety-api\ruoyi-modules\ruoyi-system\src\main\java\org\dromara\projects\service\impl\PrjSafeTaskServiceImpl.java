package org.dromara.projects.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.projects.domain.PrjProjects;
import org.dromara.projects.mapper.PrjProjectsMapper;
import org.dromara.system.domain.SysEnterpriseInfo;
import org.dromara.system.mapper.SysEnterpriseInfoMapper;
import org.springframework.stereotype.Service;
import org.dromara.projects.domain.bo.PrjSafeTaskBo;
import org.dromara.projects.domain.vo.PrjSafeTaskVo;
import org.dromara.projects.domain.PrjSafeTask;
import org.dromara.projects.mapper.PrjSafeTaskMapper;
import org.dromara.projects.service.IPrjSafeTaskService;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.Optional;

/**
 * 【项目管理】安拆任务Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-08-08
 */
@RequiredArgsConstructor
@Service
public class PrjSafeTaskServiceImpl implements IPrjSafeTaskService {

    private final PrjSafeTaskMapper baseMapper;
    private final PrjProjectsMapper prjProjectsMapper;
    private final SysEnterpriseInfoMapper sysEnterpriseInfoMapper;

    /**
     * 查询【项目管理】安拆任务
     *
     * @param openTaskId 主键
     * @return 【项目管理】安拆任务
     */
    @Override
    public PrjSafeTaskVo queryById(Long openTaskId) {
        return baseMapper.selectVoById(openTaskId);
    }

    /**
     * 分页查询【项目管理】安拆任务列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 【项目管理】安拆任务分页列表
     */
    @Override
    public TableDataInfo<PrjSafeTaskVo> queryPageList(PrjSafeTaskBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PrjSafeTask> lqw = buildQueryWrapper(bo);
        Page<PrjSafeTaskVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的【项目管理】安拆任务列表
     *
     * @param bo 查询条件
     * @return 【项目管理】安拆任务列表
     */
    @Override
    public List<PrjSafeTaskVo> queryList(PrjSafeTaskBo bo) {
        LambdaQueryWrapper<PrjSafeTask> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PrjSafeTask> buildQueryWrapper(PrjSafeTaskBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PrjSafeTask> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PrjSafeTask::getOpenTaskId);
        lqw.like(StringUtils.isNotBlank(bo.getProjectName()), PrjSafeTask::getProjectName, bo.getProjectName());
        lqw.eq(StringUtils.isNotBlank(bo.getProjectCraneNum()), PrjSafeTask::getProjectCraneNum, bo.getProjectCraneNum());
        return lqw;
    }

    /**
     * 新增【项目管理】安拆任务
     *
     * @param bo 【项目管理】安拆任务
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PrjSafeTaskBo bo) {
        PrjSafeTask add = MapstructUtils.convert(bo, PrjSafeTask.class);
        validEntityBeforeSave(add);
        return baseMapper.insert(add) > 0;
    }

    /**
     * 修改【项目管理】安拆任务
     *
     * @param bo 【项目管理】安拆任务
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PrjSafeTaskBo bo) {
        PrjSafeTask update = MapstructUtils.convert(bo, PrjSafeTask.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PrjSafeTask entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除【项目管理】安拆任务信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 通过projectId转换安拆任务
     *
     * @param projectId
     * @return
     */
    @Override
    public PrjSafeTask getToProjectId(Long projectId) {

        PrjProjects project = prjProjectsMapper.selectById(projectId);

        if (project != null) {
            PrjSafeTask prjSafeTask = new PrjSafeTask();
            //项目名称
            prjSafeTask.setProjectName(project.getProjectName());
            //项目地址
            prjSafeTask.setProjectAddress(project.getLocationDetail());

            String[] lolaArr = Optional.ofNullable(project.getLola())
                .map(t -> t.split(","))
                .orElse(null);

            if (lolaArr != null && lolaArr.length == 2) {
                //项目经度
                prjSafeTask.setProjectLongitude(lolaArr[0]);
                //项目维度
                prjSafeTask.setProjectLatitude(lolaArr[1]);
            }

            //安拆单位
            SysEnterpriseInfo enterpriseInfo = sysEnterpriseInfoMapper.selectByDeptId(project.getInstallationDismantlingOrgId());
            prjSafeTask.setInstallationUnitName(enterpriseInfo != null ? enterpriseInfo.getEnterpriseName() : null);

            return prjSafeTask;
        }

        return null;
    }
}

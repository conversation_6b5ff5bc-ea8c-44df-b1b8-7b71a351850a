{"doc": " 政府用户导入服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "importGovUsers", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "boolean"], "doc": " 导入政府用户\n\n @param file          Excel文件\n @param updateSupport 是否支持更新已存在用户\n @return 导入结果\n"}, {"name": "batchImportGovUsers", "paramTypes": ["java.util.List", "boolean"], "doc": " 批量导入政府用户\n\n @param govUsers      政府用户数据列表\n @param updateSupport 是否支持更新已存在用户\n @return 导入结果\n"}, {"name": "importFromCsv", "paramTypes": ["java.lang.String", "boolean"], "doc": " 从CSV文件导入政府用户\n\n @param csvContent    CSV内容\n @param updateSupport 是否支持更新已存在用户\n @return 导入结果\n"}, {"name": "downloadTemplate", "paramTypes": [], "doc": " 下载导入模板\n\n @return Excel模板字节数组\n"}], "constructors": []}
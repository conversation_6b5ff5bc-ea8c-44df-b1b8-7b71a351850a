{"doc": " 通用 字典服务\n\n <AUTHOR> Li\n", "fields": [{"name": "SEPARATOR", "doc": " 分隔符\n"}], "enumConstants": [], "methods": [{"name": "getDictLabel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据字典类型和字典值获取字典标签\n\n @param dictType  字典类型\n @param dictValue 字典值\n @return 字典标签\n"}, {"name": "getDictValue", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据字典类型和字典标签获取字典值\n\n @param dictType  字典类型\n @param dictLabel 字典标签\n @return 字典值\n"}, {"name": "getDictLabel", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 根据字典类型和字典值获取字典标签\n\n @param dictType  字典类型\n @param dictValue 字典值\n @param separator 分隔符\n @return 字典标签\n"}, {"name": "getDictValue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 根据字典类型和字典标签获取字典值\n\n @param dictType  字典类型\n @param dictLabel 字典标签\n @param separator 分隔符\n @return 字典值\n"}, {"name": "getAllDictByDictType", "paramTypes": ["java.lang.String"], "doc": " 获取字典下所有的字典值与标签\n\n @param dictType 字典类型\n @return dictValue为key，dictLabel为值组成的Map\n"}, {"name": "getDictType", "paramTypes": ["java.lang.String"], "doc": " 根据字典类型查询详细信息\n\n @param dictType 字典类型\n @return 字典类型详细信息\n"}, {"name": "getDictData", "paramTypes": ["java.lang.String"], "doc": " 根据字典类型查询字典数据列表\n\n @param dictType 字典类型\n @return 字典数据列表\n"}], "constructors": []}
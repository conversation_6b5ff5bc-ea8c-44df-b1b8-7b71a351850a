2025-05-01 08:49:22 [snail-job-scheduled-thread-3] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
java.lang.ExceptionInInitializerError: null
	at com.baomidou.mybatisplus.jsqlparser.JsqlParserThreadPool.getDefaultThreadPoolExecutor(JsqlParserThreadPool.java:43)
	at com.baomidou.mybatisplus.extension.parser.JsqlParserGlobal.getExecutorService(JsqlParserGlobal.java:97)
	at com.baomidou.mybatisplus.extension.parser.JsqlParserGlobal.lambda$static$0(JsqlParserGlobal.java:58)
	at com.baomidou.mybatisplus.extension.parser.JsqlParserGlobal.parse(JsqlParserGlobal.java:102)
	at com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor.autoCountSql(PaginationInnerInterceptor.java:264)
	at com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor.willDoQuery(PaginationInnerInterceptor.java:128)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:75)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy149.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	at jdk.proxy2/jdk.proxy2.$Proxy99.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:155)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:92)
	at jdk.proxy2/jdk.proxy2.$Proxy109.selectList(Unknown Source)
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectPage(BaseMapper.java:458)
	at java.base/java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:732)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:181)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:92)
	at jdk.proxy2/jdk.proxy2.$Proxy109.selectPage(Unknown Source)
	at com.aizuda.snailjob.server.job.task.support.schedule.JobClearLogSchedule.jobTaskBatchList(JobClearLogSchedule.java:97)
	at com.aizuda.snailjob.server.job.task.support.schedule.JobClearLogSchedule.lambda$doExecute$0(JobClearLogSchedule.java:79)
	at com.aizuda.snailjob.server.common.util.PartitionTaskUtils.process(PartitionTaskUtils.java:60)
	at com.aizuda.snailjob.server.common.util.PartitionTaskUtils.process(PartitionTaskUtils.java:33)
	at com.aizuda.snailjob.server.job.task.support.schedule.JobClearLogSchedule.doExecute(JobClearLogSchedule.java:79)
	at com.aizuda.snailjob.server.common.schedule.AbstractSchedule.execute(AbstractSchedule.java:46)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.IllegalStateException: Shutdown in progress
	at java.base/java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66)
	at java.base/java.lang.Runtime.addShutdownHook(Runtime.java:216)
	at com.baomidou.mybatisplus.jsqlparser.JsqlParserThreadPool.addShutdownHook(JsqlParserThreadPool.java:52)
	at com.baomidou.mybatisplus.jsqlparser.JsqlParserThreadPool$DefaultJsqlParserFixedThreadPool.<clinit>(JsqlParserThreadPool.java:71)
	... 43 common frames omitted

package org.dromara.projects.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.projects.domain.PrjSafeSupervision;

import java.io.Serial;
import java.io.Serializable;


/**
 * 【安拆任务】旁站人员视图对象 prj_safe_supervision
 *
 * <AUTHOR> Li
 * @date 2025-08-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjSafeSupervision.class)
public class PrjSafeSupervisionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long supervisionId;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String userName;

    /**
     * 自定义岗位名称
     */
    @ExcelProperty(value = "自定义岗位名称")
    private String userPositionName;

    /**
     * 人脸照片地址
     */
    @ExcelProperty(value = "人脸照片地址")
    private String face;

    /**
     * 资质证书
     */
    @ExcelProperty(value = "资质证书")
    private String certificate;

    /**
     * 关联安拆prj_safe_task.open_task_id
     */
    @ExcelProperty(value = "关联安拆prj_safe_task.open_task_id")
    private Long openTaskId;


}

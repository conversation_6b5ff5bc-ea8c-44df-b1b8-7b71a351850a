package org.dromara.system.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.dromara.system.domain.vo.GovUserImportVo;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * CSV到Excel转换工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class CsvToExcelConverter {

    /**
     * 将CSV数据转换为政府用户导入VO列表
     *
     * @param csvContent CSV内容
     * @return 政府用户导入VO列表
     */
    public static List<GovUserImportVo> convertCsvToGovUsers(String csvContent) {
        List<GovUserImportVo> govUsers = new ArrayList<>();

        if (StrUtil.isBlank(csvContent)) {
            return govUsers;
        }

        String[] lines = csvContent.split("\n");
        if (lines.length < 2) {
            log.warn("CSV内容格式不正确，至少需要标题行和一行数据");
            return govUsers;
        }

        // 跳过标题行，从第二行开始处理数据
        for (int i = 1; i < lines.length; i++) {
            String line = lines[i].trim();
            if (StrUtil.isBlank(line)) {
                continue;
            }

            try {
                GovUserImportVo govUser = parseCsvLine(line);
                if (govUser != null) {
                    govUsers.add(govUser);
                }
            } catch (Exception e) {
                log.error("解析CSV行数据失败，行号: {}, 内容: {}", i + 1, line, e);
            }
        }

        return govUsers;
    }

    /**
     * 解析CSV行数据
     *
     * @param line CSV行数据
     * @return 政府用户导入VO
     */
    private static GovUserImportVo parseCsvLine(String line) {
        // 简单的CSV解析，按逗号分割
        String[] fields = line.split(",");
        if (fields.length < 3) {
            log.warn("CSV行数据字段不足，跳过: {}", line);
            return null;
        }

        GovUserImportVo govUser = new GovUserImportVo();

        try {
            // 根据CSV文件格式解析字段
            // 假设格式为: institution_id,enterprise_name,unified_social_credit_code,...
            if (fields.length > 0) {
                govUser.setUserName(fields[0].trim()); // institution_id作为用户名
            }
            if (fields.length > 1) {
                govUser.setNickName(fields[1].trim()); // enterprise_name作为昵称
            }
            if (fields.length > 2) {
                // unified_social_credit_code作为部门代码的一部分
                String deptCode = fields[2].trim();
                govUser.setDeptName(fields[1].trim()); // 使用企业名称作为部门名称
            }

            // 设置默认值
            govUser.setStatus("0"); // 默认正常状态
            govUser.setSex("0"); // 默认性别

            // 如果有省市区编码字段
            if (fields.length > 4) {
                govUser.setProvinceCode(fields[4].trim());
            }
            if (fields.length > 5) {
                govUser.setCityCode(fields[5].trim());
            }
            if (fields.length > 6) {
                govUser.setDistrictCode(fields[6].trim());
            }

        } catch (Exception e) {
            log.error("解析CSV字段失败: {}", line, e);
            return null;
        }

        return govUser;
    }

    /**
     * 将CSV文件转换为Excel文件
     *
     * @param csvFilePath   CSV文件路径
     * @param excelFilePath Excel文件路径
     */
    public static void convertCsvToExcel(String csvFilePath, String excelFilePath) {
        try (BufferedReader reader = new BufferedReader(
            new InputStreamReader(new FileInputStream(csvFilePath), StandardCharsets.UTF_8))) {

            StringBuilder content = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }

            List<GovUserImportVo> govUsers = convertCsvToGovUsers(content.toString());

            // 写入Excel文件
            EasyExcel.write(excelFilePath, GovUserImportVo.class)
                .sheet("政府用户")
                .doWrite(govUsers);

            log.info("CSV文件转换完成，共转换 {} 条记录", govUsers.size());

        } catch (IOException e) {
            log.error("转换CSV文件失败", e);
            throw new RuntimeException("转换CSV文件失败", e);
        }
    }

    /**
     * 将CSV内容转换为Excel字节数组
     *
     * @param csvContent CSV内容
     * @return Excel字节数组
     */
    public static byte[] convertCsvContentToExcelBytes(String csvContent) {
        List<GovUserImportVo> govUsers = convertCsvToGovUsers(csvContent);

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            EasyExcel.write(outputStream, GovUserImportVo.class)
                .sheet("政府用户")
                .doWrite(govUsers);

            return outputStream.toByteArray();
        } catch (IOException e) {
            log.error("转换CSV内容为Excel字节数组失败", e);
            throw new RuntimeException("转换失败", e);
        }
    }
}

{"doc": " 实测实量Service业务层处理\n\n <AUTHOR>\n @date 2024-01-01\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询实测实量\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.quality.domain.bo.QualityMeasurementBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询实测实量列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.quality.domain.bo.QualityMeasurementBo"], "doc": " 查询实测实量列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.quality.domain.bo.QualityMeasurementBo"], "doc": " 新增实测实量\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.quality.domain.bo.QualityMeasurementBo"], "doc": " 修改实测实量\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.quality.domain.QualityMeasurement"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 批量删除实测实量\n"}, {"name": "pushMeasurementInfo", "paramTypes": ["java.util.Collection"], "doc": " 推送测量信息\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 标记隐患\n"}, {"name": "unmarkHazard", "paramTypes": ["java.lang.Long"], "doc": " 取消标记隐患\n"}, {"name": "fillDeviceInfo", "paramTypes": ["java.lang.Long", "org.dromara.quality.domain.bo.QualityMeasurementBo"], "doc": " 根据设备ID获取设备信息并填充到测量记录\n"}], "constructors": []}
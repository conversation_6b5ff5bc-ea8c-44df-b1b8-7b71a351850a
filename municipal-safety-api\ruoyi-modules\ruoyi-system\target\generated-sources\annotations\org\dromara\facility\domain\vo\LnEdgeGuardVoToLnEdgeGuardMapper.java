package org.dromara.facility.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.LnEdgeGuard;
import org.dromara.facility.domain.LnEdgeGuardToLnEdgeGuardVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {LnEdgeGuardToLnEdgeGuardVoMapper.class},
    imports = {}
)
public interface LnEdgeGuardVoToLnEdgeGuardMapper extends BaseMapper<LnEdgeGuardVo, LnEdgeGuard> {
}

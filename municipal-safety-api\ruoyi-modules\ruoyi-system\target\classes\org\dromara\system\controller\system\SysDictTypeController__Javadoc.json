{"doc": " 数据字典信息\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询字典类型列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出字典类型列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 查询字典类型详细\n\n @param dictId 字典ID\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": " 新增字典类型\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": " 修改字典类型\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除字典类型\n\n @param dictIds 字典ID串\n"}, {"name": "refreshCache", "paramTypes": [], "doc": " 刷新字典缓存\n"}, {"name": "optionselect", "paramTypes": [], "doc": " 获取字典选择框列表\n"}], "constructors": []}
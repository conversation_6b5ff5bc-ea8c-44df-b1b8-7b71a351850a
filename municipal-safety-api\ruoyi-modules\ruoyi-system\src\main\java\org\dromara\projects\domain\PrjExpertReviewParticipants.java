package org.dromara.projects.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * [项目管理] 列出专家论证会议的参会人员对象 prj_expert_review_participants
 *
 * <AUTHOR> brother
 * @date 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prj_expert_review_participants")
public class PrjExpertReviewParticipants extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 参与记录ID
     */
    @TableId(value = "participant_id")
    private Long participantId;

    /**
     * 专家论证会议ID (逻辑外键至 prj_expert_reviews.review_id)
     */
    private Long reviewId;

    /**
     * 参与者用户ID (逻辑外键至 sys_users.user_id)
     */
    private Long userId;

    /**
     * 会议中的角色
     */
    private String roleInMeeting;

    /**
     * 是否评审专家 (0:否, 1:是)
     */
    private Long isAttendingExpert;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;
}

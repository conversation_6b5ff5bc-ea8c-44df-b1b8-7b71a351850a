<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="highFormWorkRealList">
      <el-table-column label="设备id" align="center" prop="eid" />
      <el-table-column label="模板沉降" align="center" prop="settlement" />
      <el-table-column label="立杆倾角" align="center" prop="inclinationAngleOfVerticalPole" />
      <el-table-column label="水平倾角" align="center" prop="horizontalInclination" />
      <el-table-column label="称重" align="center" prop="bearing">
        <template v-slot="scope">
          {{ scope.row.bearing }}Kg
        </template>
      </el-table-column>
      <el-table-column label="水平位移" align="center" prop="horizontalDisplacement">
        <template v-slot="scope">
          {{ scope.row.horizontalDisplacement }}mm
        </template>
      </el-table-column>
      <el-table-column label="垂直位移" align="center" prop="verticalDisplacement">
        <template v-slot="scope">
          {{ scope.row.verticalDisplacement }}mm
        </template>
      </el-table-column>
      <el-table-column label="上报时间" align="center" prop="eventTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.eventTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

  </div>
</template>

<script>
import { listHighFormWorkReal } from "@/api/projects/facility/index";

export default {
  name: "HighFormWorkReal",
  props: {
    devNo: {
      type: String,
      default: ""
    }
  },
  data () {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 绿能高支模实时数据表格数据
      highFormWorkRealList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        devNo: null
      }
    };
  },
  created () {
    this.getList();
  },
  methods: {
    /** 查询绿能高支模实时数据列表 */
    async getList () {
      this.highFormWorkRealList = []
      this.loading = true;
      this.queryParams.devNo = this.devNo
      const res = listHighFormWorkReal(this.queryParams)
      this.highFormWorkRealList = res.rows;
      this.total = res.total;
      this.loading = false;
    },
  }
};
</script>

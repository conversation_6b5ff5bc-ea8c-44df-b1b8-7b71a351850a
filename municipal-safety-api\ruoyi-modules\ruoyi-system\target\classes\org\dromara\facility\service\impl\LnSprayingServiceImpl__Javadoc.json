{"doc": " 绿能喷淋设备Service业务层处理\n\n <AUTHOR>\n @date 2025-07-24\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询绿能喷淋设备\n\n @param id 主键\n @return 绿能喷淋设备\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.facility.domain.bo.LnSprayingBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询绿能喷淋设备列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 绿能喷淋设备分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.facility.domain.bo.LnSprayingBo"], "doc": " 查询符合条件的绿能喷淋设备列表\n\n @param bo 查询条件\n @return 绿能喷淋设备列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.facility.domain.bo.LnSprayingBo"], "doc": " 新增绿能喷淋设备\n\n @param bo 绿能喷淋设备\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.facility.domain.bo.LnSprayingBo"], "doc": " 修改绿能喷淋设备\n\n @param bo 绿能喷淋设备\n @return 是否修改成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.facility.domain.LnSpraying"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除绿能喷淋设备信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
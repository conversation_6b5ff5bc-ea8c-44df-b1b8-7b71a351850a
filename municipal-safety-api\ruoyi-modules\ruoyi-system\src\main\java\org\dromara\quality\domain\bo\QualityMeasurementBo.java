package org.dromara.quality.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.quality.domain.QualityMeasurement;

import java.util.Date;

/**
 * 实测实量业务对象 quality_measurement
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = QualityMeasurement.class, reverseConvertGenerate = false)
public class QualityMeasurementBo extends BaseEntity {

    /**
     * 测量ID
     */
    @NotNull(message = "测量ID不能为空", groups = {EditGroup.class})
    private Long measurementId;

    /**
     * 测量时间
     */
    @NotNull(message = "测量时间不能为空", groups = {AddGroup.class, EditGroup.class})
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date measurementTime;

    /**
     * 测量事项
     */
    @NotBlank(message = "测量事项不能为空", groups = {AddGroup.class, EditGroup.class})
    private String measurementItem;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 测量结果
     */
    @NotBlank(message = "测量结果不能为空", groups = {AddGroup.class, EditGroup.class})
    private String measurementResult;

    /**
     * 是否合规（0合规 1不合规）
     */
    private String isCompliant;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 测量位置
     */
    private String measurementLocation;

    /**
     * 标准值
     */
    private String standardValue;

    /**
     * 偏差值
     */
    private String deviationValue;

    /**
     * 测量人员
     */
    private String measurementPerson;

    /**
     * 是否标记隐患（0否 1是）
     */
    private String isHazardMarked;

    /**
     * 隐患描述
     */
    private String hazardDescription;

    /**
     * 状态（0正常 1已推送 2已处理）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

}

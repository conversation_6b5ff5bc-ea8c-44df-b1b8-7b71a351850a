package org.dromara.special.service;

import org.dromara.common.core.domain.R;
import org.dromara.special.domain.sync.SyncEntity;
import org.dromara.special.domain.vo.SpecialEquipmentVo;
import org.dromara.special.domain.bo.SpecialEquipmentBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 特种设备Service接口
 *
 * <AUTHOR> Li
 * @date 2025-05-14
 */
public interface ISpecialEquipmentService {

    /**
     * 查询特种设备
     *
     * @param equipmentId 主键
     * @return 特种设备
     */
    SpecialEquipmentVo queryById(Long equipmentId);

    /**
     * 分页查询特种设备列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 特种设备分页列表
     */
    TableDataInfo<SpecialEquipmentVo> queryPageList(SpecialEquipmentBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的特种设备列表
     *
     * @param bo 查询条件
     * @return 特种设备列表
     */
    List<SpecialEquipmentVo> queryList(SpecialEquipmentBo bo);

    /**
     * 新增特种设备
     *
     * @param bo 特种设备
     * @return 是否新增成功
     */
    Boolean insertByBo(SpecialEquipmentBo bo);

    /**
     * 修改特种设备
     *
     * @param bo 特种设备
     * @return 是否修改成功
     */
    Boolean updateByBo(SpecialEquipmentBo bo);

    /**
     * 校验并批量删除特种设备信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    R<Object> syncProject(SyncEntity syncEntity);

    List<SpecialEquipmentVo> getAuthorityList();

}

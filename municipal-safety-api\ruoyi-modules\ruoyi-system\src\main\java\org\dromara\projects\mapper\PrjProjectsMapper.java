package org.dromara.projects.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.projects.domain.PrjProjects;
import org.dromara.projects.domain.vo.PrjProjectsVo;

import java.util.List;

/**
 * 项目录入Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
public interface PrjProjectsMapper extends BaseMapperPlus<PrjProjects, PrjProjectsVo> {

    /**
     * 查询项目列表（带部门名称）
     */
    @DataPermission({
        @DataColumn(key = "deptNameSgf", value = "construction_org_id"),
        @DataColumn(key = "deptNameJl", value = "supervision_org_id")
    })
    List<PrjProjectsVo> selectVoList(@Param(Constants.WRAPPER) Wrapper<PrjProjects> queryWrapper);

    /**
     * 查询项目列表（带部门名称）
     */
    @DataPermission({
        @DataColumn(key = "deptNameZf", value = "supervising_qs_org_id"),
        @DataColumn(key = "projectId", value = "project_id")
    })
    List<PrjProjectsVo> selectVoListSq(@Param(Constants.WRAPPER) Wrapper<PrjProjects> queryWrapper);

    /**
     * 分页查询项目列表（带部门名称）
     */
    @DataPermission(value = {
        @DataColumn(key = "deptNameSgf", value = "construction_org_id"),
        @DataColumn(key = "deptNameJl", value = "supervision_org_id"),
        @DataColumn(key = "deptNameZf", value = "supervising_qs_org_id")
    })
    Page<PrjProjectsVo> selectVoPage(Page<PrjProjects> page, @Param(Constants.WRAPPER) Wrapper<PrjProjects> queryWrapper);

    /**
     * 分页查询项目列表（带部门名称）
     */
    @DataPermission({
        @DataColumn(key = "deptNameZf", value = "supervising_qs_org_id"),
        @DataColumn(key = "projectId", value = "project_id")
    })
    Page<PrjProjectsVo> selectVoPageSq(Page<PrjProjects> page, @Param(Constants.WRAPPER) Wrapper<PrjProjects> queryWrapper);

    @DataPermission({
        @DataColumn(key = "deptNameSgf", value = "construction_org_id"),
        @DataColumn(key = "deptNameJl", value = "supervision_org_id")
    })
    List<PrjProjectsVo> selectAll(@Param(Constants.WRAPPER) Wrapper<PrjProjects> queryWrapper);

    @DataPermission({
        @DataColumn(key = "deptNameZf", value = "supervising_qs_org_id"),
        @DataColumn(key = "projectId", value = "project_id")
    })
    List<PrjProjectsVo> selectAllSq(@Param(Constants.WRAPPER) Wrapper<PrjProjects> queryWrapper);

    /**
     * 根据ID查询项目详情（带部门名称）
     */
    PrjProjectsVo selectVoById(@Param("projectId") Long projectId);
}

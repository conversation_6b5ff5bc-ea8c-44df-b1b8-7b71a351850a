{"doc": " 设备管理Controller\n\n <AUTHOR>\n @date 2024-01-01\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.quality.domain.bo.QualityDeviceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询设备管理列表\n"}, {"name": "export", "paramTypes": ["org.dromara.quality.domain.bo.QualityDeviceBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出设备管理列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取设备管理详细信息\n\n @param deviceId 设备ID\n"}, {"name": "add", "paramTypes": ["org.dromara.quality.domain.bo.QualityDeviceBo"], "doc": " 新增设备管理\n"}, {"name": "edit", "paramTypes": ["org.dromara.quality.domain.bo.QualityDeviceBo"], "doc": " 修改设备管理\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除设备管理\n\n @param deviceIds 设备ID串\n"}, {"name": "generateDeviceCode", "paramTypes": ["java.lang.String"], "doc": " 生成设备编号\n\n @param deviceType 设备类型\n"}, {"name": "checkDeviceCodeUnique", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 校验设备编号是否唯一\n\n @param deviceCode 设备编号\n @param deviceId   设备ID\n"}, {"name": "uploadImage", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Long"], "doc": " 上传设备图片\n\n @param file     图片文件\n @param deviceId 设备ID（可选，新增时为空）\n"}, {"name": "uploadManual", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Long"], "doc": " 上传使用说明书\n\n @param file     说明书文件\n @param deviceId 设备ID（可选，新增时为空）\n"}], "constructors": []}
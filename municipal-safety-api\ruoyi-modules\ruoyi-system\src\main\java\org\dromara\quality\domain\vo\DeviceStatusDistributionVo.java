package org.dromara.quality.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备状态分布数据VO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeviceStatusDistributionVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 正常状态设备数量
     */
    private Integer normal;

    /**
     * 停用状态设备数量
     */
    private Integer disabled;
} 
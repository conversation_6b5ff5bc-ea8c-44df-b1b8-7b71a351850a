package org.dromara.projects.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.bo.PrjExpertReviewParticipantsBoToPrjExpertReviewParticipantsMapper;
import org.dromara.projects.domain.vo.PrjExpertReviewParticipantsVo;
import org.dromara.projects.domain.vo.PrjExpertReviewParticipantsVoToPrjExpertReviewParticipantsMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjExpertReviewParticipantsVoToPrjExpertReviewParticipantsMapper.class,PrjExpertReviewParticipantsBoToPrjExpertReviewParticipantsMapper.class},
    imports = {}
)
public interface PrjExpertReviewParticipantsToPrjExpertReviewParticipantsVoMapper extends BaseMapper<PrjExpertReviewParticipants, PrjExpertReviewParticipantsVo> {
}

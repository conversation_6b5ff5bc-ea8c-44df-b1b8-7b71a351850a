<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="查询条件" prop="type">
              <el-select v-model="queryParams.type" placeholder="请选择查询类型" clearable style="width: 110px">
                <el-option label="项目" value="project" />
                <el-option label="工程" value="item" />
              </el-select>
              <el-input v-model="searchName" placeholder="请输入名称" clearable :disabled="!queryParams.type"
                @keyup.enter="handleQuery" style="width: 200px; margin-left: 6px" />
            </el-form-item>
            <el-form-item label="任务状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable>
                <el-option v-for="dict in ai_haz_analysis_tasks_status" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <!--      <template #header>-->
      <!--        <el-row :gutter="10" class="mb8">-->
      <!--          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
      <!--        </el-row>-->
      <!--      </template>-->

      <el-table v-loading="loading" :data="ai_haz_analysis_tasksList">
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column label="工程名称" align="center" prop="itemName" />
        <el-table-column label="危大类型" align="center" prop="dangerListType">
          <template #default="scope">
            <dict-tag :options="hidden_danger_type" :value="scope.row.dangerListType" />
          </template>
        </el-table-column>
        <el-table-column label="涉危工程" align="center" prop="parentName" />
        <el-table-column label="照片上传时间" align="center" prop="uploadTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.uploadTime) }}</span>
          </template>
        </el-table-column>
        <!--        <el-table-column label="隐患描述" align="center" prop="locationDescription" />-->
        <el-table-column label="任务状态" align="center" prop="status" width="120" show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="ai_haz_analysis_tasks_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="预警来源" align="center" prop="sourceType">
          <template #default="scope">
            <dict-tag :options="ai_haz_analysis_tasks_source_type" :value="scope.row.sourceType" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <div style="display: flex;flex-direction: column;align-items: baseline;">
              <el-button link type="primary" icon="View" @click="handleReinspection(scope.row)"
                v-has-permi="['xjjh:detail:rgfj']">人工复检</el-button>
              <el-button link type="primary" icon="Expand" v-has-permi="['xjjh:detail:lsgd']"
                @click="handleHistoricalWorkOrder(scope.row)">历史工单</el-button>
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">查看详情</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog :title="'查看智能隐患分析任务详情'" v-model="dialog.visible" width="1200px" append-to-body>
      <div class="detail-container">
        <el-tabs type="border-card" class="detail-tabs">
          <el-tab-pane label="基础信息">
            <div class="info-section">
              <div class="section-header">
                <el-icon>
                  <InfoFilled />
                </el-icon>
                <span>项目信息</span>
              </div>
              <el-descriptions :column="2" border class="custom-descriptions">
                <el-descriptions-item label="项目名称" :span="2">{{ form.prjProjectsVo?.projectName
                }}</el-descriptions-item>
                <el-descriptions-item label="项目编号">{{ form.prjProjectsVo?.projectCode }}</el-descriptions-item>
                <el-descriptions-item label="建设单位">{{ form.prjProjectsVo?.clientOrgName }}</el-descriptions-item>
                <el-descriptions-item label="施工单位">{{ form.prjProjectsVo?.constructionOrgName }}</el-descriptions-item>
                <el-descriptions-item label="监理单位">{{ form.prjProjectsVo?.supervisionOrgName }}</el-descriptions-item>
              </el-descriptions>
            </div>

            <div class="info-section">
              <div class="section-header">
                <el-icon>
                  <Tools />
                </el-icon>
                <span>工程信息</span>
              </div>
              <el-descriptions :column="2" border class="custom-descriptions">
                <el-descriptions-item label="工程名称">{{ form.prjHazardousItemsVo?.itemName }}</el-descriptions-item>
                <el-descriptions-item label="危大工程类型">{{ form.prjHazardousItemsVo?.parentName }}</el-descriptions-item>
                <el-descriptions-item label="工程范围" :span="2">{{ form.prjHazardousItemsVo?.scopeDetails
                }}</el-descriptions-item>
              </el-descriptions>
            </div>

            <div class="info-section">
              <div class="section-header">
                <el-icon>
                  <Monitor />
                </el-icon>
                <span>AI分析信息</span>
              </div>
              <el-descriptions :column="2" border class="custom-descriptions">
                <el-descriptions-item label="隐患描述">{{ form.locationDescription }}</el-descriptions-item>
                <el-descriptions-item label="拍摄时间">{{ parseTime(form.uploadTime) }}</el-descriptions-item>
                <el-descriptions-item label="预警来源">
                  <dict-tag :options="ai_haz_analysis_tasks_source_type" :value="form.sourceType" />
                </el-descriptions-item>
                <el-descriptions-item label="任务状态">
                  <dict-tag :options="ai_haz_analysis_tasks_status" :value="form.status" />
                </el-descriptions-item>
                <el-descriptions-item label="复查状态">
                  <dict-tag :options="ai_haz_analysis_tasks_recheck_status" :value="form.recheckStatus" />
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-tab-pane>

          <el-tab-pane label="AI识别结果">
            <div class="info-section">
              <div class="section-header">
                <el-icon>
                  <Picture />
                </el-icon>
                <span>现场照片</span>
              </div>
              <div class="image-comparison">
                <div class="comparison-container">
                  <div class="image-wrapper">
                    <div class="image-title">原始照片</div>
                    <ImagePreview v-if="form.photoDocumentUrl" :src="String(form.photoDocumentUrl)"
                      class="detail-image" />
                    <el-empty v-else description="暂无原始照片" />
                  </div>
                  <div class="image-separator">
                    <el-icon>
                      <Right />
                    </el-icon>
                  </div>
                  <div class="image-wrapper">
                    <div class="image-title">AI标注照片</div>
                    <ImagePreview v-if="form.aiPhotoDocumentUrl" :src="String(form.aiPhotoDocumentUrl)"
                      class="detail-image" />
                    <el-empty v-else description="暂无AI标注照片" />
                  </div>
                </div>
              </div>
            </div>

            <div class="info-section">
              <div class="section-header">
                <el-icon>
                  <Warning />
                </el-icon>
                <span>识别结果详情</span>
              </div>
              <div v-if="aiResults.length > 0">
                <el-collapse accordion>
                  <el-collapse-item v-for="(item, index) in aiResults" :key="index"
                    :title="`隐患 ${index + 1}: ${item.violation.slice(0, 50)}...`">
                    <div class="ai-result-card">
                      <div class="result-info">
                        <el-descriptions :column="2" border class="custom-descriptions">
                          <el-descriptions-item label="隐患描述" :span="2">
                            <div class="description-content">{{ item.violation }}</div>
                          </el-descriptions-item>
                          <el-descriptions-item label="违规等级">
                            <el-tag :type="item.level === '1' ? 'warning' : 'danger'" effect="dark" size="large">
                              {{ item.level === '1' ? '一般隐患' : '重大隐患' }}
                            </el-tag>
                          </el-descriptions-item>
                          <el-descriptions-item label="整改意见">
                            <div class="description-content">{{ item.measure }}</div>
                          </el-descriptions-item>
                          <el-descriptions-item label="违反条款" :span="2">
                            <div class="description-content">{{ item.regulation }}</div>
                          </el-descriptions-item>
                        </el-descriptions>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
              <el-empty v-else description="暂无AI识别结果" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="dialog.visible = false" round>关 闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 历史工单列表对话框 -->
    <el-dialog :title="'历史工单列表'" v-model="historyListDialog.visible" width="1500px" append-to-body>
      <el-tabs v-model="tab" @tab-click="changeTab">
        <el-tab-pane name="running" label="运行中"></el-tab-pane>
        <el-tab-pane name="finish" label="已完成"></el-tab-pane>
        <el-table v-loading="hostoryLoading" border :data="processInstanceList">
          <el-table-column align="center" type="index" label="序号" width="60"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" label="流程定义名称">
            <template #default="scope">
              <span>{{ scope.row.flowName }}v{{ scope.row.version }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="nodeName" label="任务名称"></el-table-column>
          <el-table-column align="center" prop="flowCode" label="流程定义编码"></el-table-column>
          <el-table-column align="center" prop="categoryName" label="流程分类"></el-table-column>
          <el-table-column align="center" prop="createByName" label="申请人"></el-table-column>
          <el-table-column align="center" prop="version" label="版本号" width="90">
            <template #default="scope"> v{{ scope.row.version }}.0</template>
          </el-table-column>
          <el-table-column v-if="tab === 'running'" align="center" prop="isSuspended" label="状态" min-width="70">
            <template #default="scope">
              <el-tag v-if="!scope.row.isSuspended" type="success">激活</el-tag>
              <el-tag v-else type="danger">挂起</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" label="流程状态" min-width="70">
            <template #default="scope">
              <dict-tag :options="wf_business_status" :value="scope.row.flowStatus"></dict-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createTime" label="启动时间" width="160"></el-table-column>
          <el-table-column v-if="tab === 'finish'" align="center" prop="updateTime" label="结束时间"
            width="160"></el-table-column>
          <el-table-column label="操作" align="center" :width="165">
            <template #default="scope">
              <div>
                <el-button link type="primary" icon="View" @click="handleViewProcess(scope.row)">查看流程</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="historyTotal > 0" v-model:page="historyQueryParams.pageNum"
          v-model:limit="historyQueryParams.pageSize" :total="historyTotal" @pagination="handleHistoryQuery" />
      </el-tabs>
      <!-- 审批记录 -->
      <approvalRecord ref="approvalRecordRef" />
    </el-dialog>
    <!-- 人工复检和派遣审查组件 -->
    <ManualDispatch ref="ManualDispatchRef"></ManualDispatch>
  </div>
</template>

<script setup name="Ai_haz_analysis_tasks" lang="ts">
import { pageByRunning, pageByFinish } from '@/api/workflow/instance';
import { AIProblem, getAi_haz_analysis_tasks } from '@/api/ai/ai_haz_analysis_tasks';
import { Ai_haz_analysis_tasksVO, Ai_haz_analysis_tasksQuery, Ai_haz_analysis_tasksForm } from '@/api/ai/ai_haz_analysis_tasks/types';
import { InfoFilled, Monitor, Tools, Warning, Picture, Right } from '@element-plus/icons-vue';
import ManualDispatch from '@/components/ManualDispatch/ManualDispatch.vue';
import ImagePreview from '@/components/ImagePreview/index.vue';
import ApprovalRecord from '@/components/Process/approvalRecord.vue';
const props = defineProps({
  deilplanId: {
    type: String,
    default: null
  }
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { ai_haz_analysis_tasks_status, hidden_danger_type, ai_haz_analysis_tasks_recheck_status, ai_haz_analysis_tasks_source_type, wf_business_status } = toRefs<any>(
  proxy?.useDict('ai_haz_analysis_tasks_status', 'hidden_danger_type', 'ai_haz_analysis_tasks_recheck_status', 'ai_haz_analysis_tasks_source_type', 'wf_business_status')
);

const ai_haz_analysis_tasksList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const searchName = ref('');

const queryFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const aiResults = ref<any[]>([]);

const initFormData = {
  taskId: undefined,
  projectId: undefined,
  projectName: undefined,
  itemName: undefined,
  dangerListType: undefined,
  parentName: undefined,
  expertUserId: undefined,
  uploadTime: undefined,
  photoDocumentUrl: undefined,
  aiPhotoDocumentUrl: undefined,
  gpsLocation: undefined,
  locationDescription: undefined,
  aiRecognitionRawResult: undefined,
  aiIdentifiedHazards: undefined,
  expertSelectedHazardTypes: undefined,
  expertManualInputDetails: undefined,
  relatedHazardousItemId: undefined,
  expertConfirmationTime: undefined,
  status: undefined,
  relatedWorkOrderId: undefined
};

const data = reactive({
  form: { ...initFormData },
  queryParams: {
    planId: null,
    pageNum: 1,
    pageSize: 10,
    projectName: undefined,
    itemName: undefined,
    type: undefined,
    status: undefined,
    params: {}
  },
  rules: {}
});

const { queryParams, form } = toRefs(data);

// 历史工单列表对话框的部分变量
const historyListDialog = reactive<DialogOption>({
  visible: false,
  title: ''
})
// tab切换的双向绑定的值
const tab = ref('running');
const hostoryLoading = ref(false);
// 历史工单列表的总条数
const historyTotal = ref(0);
// 历史工单表格数据
const processInstanceList = ref([]);
// 查询历史工单所需要传递的参数
const historyQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  aiTaskId: undefined
});
// 人工复检和派遣审查的变量部分
const ManualDispatchRef = ref<InstanceType<typeof ManualDispatch>>();
//审批记录组件
const approvalRecordRef = ref<InstanceType<typeof ApprovalRecord>>();

//查询已运行的方法
const getProcessInstanceRunningList = () => {
  hostoryLoading.value = true;
  pageByRunning(historyQueryParams.value).then((resp) => {
    processInstanceList.value = resp.rows;
    historyTotal.value = resp.total;
    hostoryLoading.value = false;
  });
};
// 查询已完成的方法
const getProcessInstanceFinishList = () => {
  hostoryLoading.value = true;
  pageByFinish(historyQueryParams.value).then((resp) => {
    processInstanceList.value = resp.rows;
    historyTotal.value = resp.total;
    hostoryLoading.value = false;
  });
};
// 查询历史工单列表的方法
const handleHistoryQuery = () => {
  if ('running' === tab.value) {
    getProcessInstanceRunningList();
  } else {
    getProcessInstanceFinishList();
  }
}
// 已运行和已完成的tab切换方法
const changeTab = async (val: any) => {
  processInstanceList.value = [];
  historyQueryParams.value.pageNum = 1;
  if ('running' === val.paneName) {
    getProcessInstanceRunningList();
  } else {
    getProcessInstanceFinishList();
  }
};
// 查看流程的方法
const handleViewProcess = (row: any) => {
  approvalRecordRef.value.init(row.businessId);
}
/** 查询智能隐患分析任务列表 */
const getList = async () => {
  loading.value = true;
  const res = await AIProblem({ ...queryParams.value, planId: props.deilplanId });
  ai_haz_analysis_tasksList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  // 根据选择的查询类型设置查询参数
  if (queryParams.value.type === 'project') {
    queryParams.value.projectName = searchName.value;
    queryParams.value.itemName = undefined;
  } else if (queryParams.value.type === 'item') {
    queryParams.value.itemName = searchName.value;
    queryParams.value.projectName = undefined;
  } else {
    // 如果没有选择查询类型，则清空相关字段
    queryParams.value.projectName = undefined;
    queryParams.value.itemName = undefined;
  }
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  searchName.value = '';
  queryFormRef.value?.resetFields();
  // 确保重置后清空所有查询条件
  queryParams.value.projectName = undefined;
  queryParams.value.itemName = undefined;
  queryParams.value.status = undefined;
  queryParams.value.type = undefined;
  handleQuery();
};
// 人工复检点击事件
const handleReinspection = (row: Ai_haz_analysis_tasksVO) => {
  if (ManualDispatchRef.value) {
    // 只有当AI分析完成并且有隐患时才能进行人工复检
    if (row.status == 'AI_ANALYSIS_COMPLETED') {
      // 调用子组件的方法，传递当前行数据
      ManualDispatchRef.value.handleManual(row);
    } else if (row.status == 'AI_ANALYSIS_NOT_CONSTRUCTION_SITE') {
      proxy?.$modal.msgWarning('当前不是工地现场，请重新上传照片');
    } else if (row.status == 'AI_ANALYSIS_NO_SAFETY_HAZARDS') {
      proxy?.$modal.msgWarning('当前没有安全隐患，无需人工复检');
    } else if (row.status == 'AI_ANALYSING' || row.status == 'PENDING_AI_ANALYSIS') {
      proxy?.$modal.msgWarning('AI分析中，请稍等');
    } else {
      proxy?.$modal.msgWarning('当前状态不支持人工复检');
    }
  }
};
// 历史工单点击事件
const handleHistoricalWorkOrder = (row: Ai_haz_analysis_tasksVO) => {
  // 这里可以添加历史工单的逻辑，例如跳转到历史工单页面
  tab.value = 'running'
  historyListDialog.visible = true;
  historyQueryParams.value.aiTaskId = row.taskId;
  getProcessInstanceRunningList();
};
/** 查看详情按钮操作 */
const handleDetail = async (row: Ai_haz_analysis_tasksVO) => {
  const res = await getAi_haz_analysis_tasks(row.taskId);
  Object.assign(form.value, res.data);
  try {
    if (form.value.aiRecognitionRawResult) {
      const aiResult = JSON.parse(form.value.aiRecognitionRawResult);

      // 只有当状态为有隐患时才显示违规列表
      if (form.value.status === 'AI_ANALYSIS_COMPLETED' && aiResult.violationList) {
        aiResults.value = aiResult.violationList.map((item: any) => ({
          ...item
        }));

        // 只有当aiResults有数据时才添加固定数据
        if (aiResults.value.length > 0) {
          aiResults.value.push({
            violation: '疑似现场消防设施配备不足',
            level: '1', // 1代表一般隐患
            measure: '按照要求配备齐消防设施',
            regulation: '建设工程施工现场消防安全技术规范(GB 50720-2011)'
          });
        }
      } else {
        aiResults.value = [];
      }
    } else {
      aiResults.value = [];
    }
  } catch (error) {
    console.error('解析AI识别结果失败:', error);
    aiResults.value = [];
  }
  dialog.visible = true;
};

// 监听type变化，当type改变时清空searchName
watch(
  () => queryParams.value.type,
  (newVal) => {
    if (newVal === '') {
      searchName.value = '';
    }
  }
);

onMounted(() => {
  getList();
});
</script>

<style scoped>
.ai-result-item {
  margin-bottom: 16px;
}

.detail-container {
  padding: 0;
}

.detail-tabs {
  --el-border-radius-base: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.info-section {
  margin-bottom: 20px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--el-color-primary-light-9);
  font-weight: bold;
  font-size: 16px;
  color: var(--el-color-primary);
  border-bottom: 1px solid var(--el-border-color-light);
}

.section-header .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.custom-descriptions {
  --el-descriptions-item-bordered-label-background: var(--el-fill-color-light);
}

.ai-result-card {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--el-bg-color);
}

.result-info {
  flex: 1;
  padding: 10px 0;
}

.description-content {
  line-height: 1.6;
  padding: 4px 0;
}

.image-comparison {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
}

.comparison-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.image-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.image-title {
  font-weight: bold;
  margin-bottom: 12px;
  color: var(--el-text-color-primary);
  font-size: 16px;
}

.detail-image {
  width: 100%;
  height: 400px;
  border-radius: 4px;
  transition: transform 0.3s;
}

.detail-image:hover {
  transform: scale(1.02);
}

.image-separator {
  margin: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--el-text-color-secondary);
}

.image-error .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.dialog-footer {
  text-align: center;
  margin-top: 16px;
}
</style>

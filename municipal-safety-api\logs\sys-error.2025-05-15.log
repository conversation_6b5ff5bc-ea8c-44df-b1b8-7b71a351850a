2025-05-15 08:49:14 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 08:50:18 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJwVkRSenJHVGYxTUY4cE5EMjJpNlBTWkZNek9DTEVvbiIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.InYM4vIdjy2u5idHDqet02c9WcScj4CthrWFPrYw8hk',无法访问系统资源
2025-05-15 08:53:58 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/new/1921129812304113665',发生未知异常.
java.lang.NullPointerException: Cannot invoke "org.dromara.projects.domain.vo.PrjHazardousItemsVo.getDangerId()" because "prjHazardousItemsVo" is null
	at org.dromara.ai.service.impl.AiHazAnalysisTasksServiceImpl.queryByIdNew(AiHazAnalysisTasksServiceImpl.java:102)
	at org.dromara.ai.controller.AppAiHazAnalysisTasksController.getAppInfo(AppAiHazAnalysisTasksController.java:109)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at org.dromara.ai.controller.AppAiHazAnalysisTasksController$$SpringCGLIB$$0.getAppInfo(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.RepeatableFilter.doFilter(RepeatableFilter.java:30)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.XssFilter.doFilter(XssFilter.java:38)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.encrypt.filter.CryptoFilter.doFilter(CryptoFilter.java:70)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-15 08:54:29 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/new/1921129812304113665',发生未知异常.
java.lang.NullPointerException: Cannot invoke "org.dromara.projects.domain.vo.PrjHazardousItemsVo.getDangerId()" because "prjHazardousItemsVo" is null
	at org.dromara.ai.service.impl.AiHazAnalysisTasksServiceImpl.queryByIdNew(AiHazAnalysisTasksServiceImpl.java:102)
	at org.dromara.ai.controller.AppAiHazAnalysisTasksController.getAppInfo(AppAiHazAnalysisTasksController.java:109)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at org.dromara.ai.controller.AppAiHazAnalysisTasksController$$SpringCGLIB$$0.getAppInfo(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.RepeatableFilter.doFilter(RepeatableFilter.java:30)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.XssFilter.doFilter(XssFilter.java:38)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.encrypt.filter.CryptoFilter.doFilter(CryptoFilter.java:70)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-15 08:56:39 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/new/1921129812304113665',发生未知异常.
java.lang.NullPointerException: Cannot invoke "org.dromara.projects.domain.vo.PrjHazardousItemsVo.getDangerId()" because "prjHazardousItemsVo" is null
	at org.dromara.ai.service.impl.AiHazAnalysisTasksServiceImpl.queryByIdNew(AiHazAnalysisTasksServiceImpl.java:102)
	at org.dromara.ai.controller.AppAiHazAnalysisTasksController.getAppInfo(AppAiHazAnalysisTasksController.java:109)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at org.dromara.ai.controller.AppAiHazAnalysisTasksController$$SpringCGLIB$$0.getAppInfo(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.RepeatableFilter.doFilter(RepeatableFilter.java:30)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.XssFilter.doFilter(XssFilter.java:38)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.encrypt.filter.CryptoFilter.doFilter(CryptoFilter.java:70)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-15 08:58:00 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/new/1921129812304113665',发生未知异常.
java.lang.NullPointerException: Cannot invoke "org.dromara.projects.domain.vo.PrjHazardousItemsVo.getDangerId()" because "prjHazardousItemsVo" is null
	at org.dromara.ai.service.impl.AiHazAnalysisTasksServiceImpl.queryByIdNew(AiHazAnalysisTasksServiceImpl.java:102)
	at org.dromara.ai.controller.AppAiHazAnalysisTasksController.getAppInfo(AppAiHazAnalysisTasksController.java:109)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at org.dromara.ai.controller.AppAiHazAnalysisTasksController$$SpringCGLIB$$0.getAppInfo(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.RepeatableFilter.doFilter(RepeatableFilter.java:30)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.XssFilter.doFilter(XssFilter.java:38)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.encrypt.filter.CryptoFilter.doFilter(CryptoFilter.java:70)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-15 09:00:38 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJwVkRSenJHVGYxTUY4cE5EMjJpNlBTWkZNek9DTEVvbiIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.InYM4vIdjy2u5idHDqet02c9WcScj4CthrWFPrYw8hk',无法访问系统资源
2025-05-15 09:02:42 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJwVkRSenJHVGYxTUY4cE5EMjJpNlBTWkZNek9DTEVvbiIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.InYM4vIdjy2u5idHDqet02c9WcScj4CthrWFPrYw8hk',无法访问系统资源
2025-05-15 09:02:59 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJwVkRSenJHVGYxTUY4cE5EMjJpNlBTWkZNek9DTEVvbiIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.InYM4vIdjy2u5idHDqet02c9WcScj4CthrWFPrYw8hk',无法访问系统资源
2025-05-15 09:03:02 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJwVkRSenJHVGYxTUY4cE5EMjJpNlBTWkZNek9DTEVvbiIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.InYM4vIdjy2u5idHDqet02c9WcScj4CthrWFPrYw8hk',无法访问系统资源
2025-05-15 09:03:05 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJwVkRSenJHVGYxTUY4cE5EMjJpNlBTWkZNek9DTEVvbiIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.InYM4vIdjy2u5idHDqet02c9WcScj4CthrWFPrYw8hk',无法访问系统资源
2025-05-15 09:03:07 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJwVkRSenJHVGYxTUY4cE5EMjJpNlBTWkZNek9DTEVvbiIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.InYM4vIdjy2u5idHDqet02c9WcScj4CthrWFPrYw8hk',无法访问系统资源
2025-05-15 09:03:09 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJwVkRSenJHVGYxTUY4cE5EMjJpNlBTWkZNek9DTEVvbiIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.InYM4vIdjy2u5idHDqet02c9WcScj4CthrWFPrYw8hk',无法访问系统资源
2025-05-15 09:03:17 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:03:22 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJwVkRSenJHVGYxTUY4cE5EMjJpNlBTWkZNek9DTEVvbiIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.InYM4vIdjy2u5idHDqet02c9WcScj4CthrWFPrYw8hk',无法访问系统资源
2025-05-15 09:03:29 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJwVkRSenJHVGYxTUY4cE5EMjJpNlBTWkZNek9DTEVvbiIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.InYM4vIdjy2u5idHDqet02c9WcScj4CthrWFPrYw8hk',无法访问系统资源
2025-05-15 09:05:36 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:13:42 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:14:39 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:28:06 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:29:03 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:31:56 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:35:44 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:36:18 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:37:27 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:37:36 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:37:52 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:37:59 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:38:16 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:38:29 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:38:38 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:38:50 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:39:01 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:39:45 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:39:50 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/system/user/getInfo',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJnWTJYeFY4dGI3eklmV0FRdXNucHFaVjdvNVdrV01lWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.N3Iw00HfJZ__DVI7FV_Qzm91hvyzNvNUC-Amu4Qt0p8',无法访问系统资源
2025-05-15 09:40:01 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求地址'/system/area/formatId',发生未知异常.
java.lang.IllegalArgumentException: 获取不到该地区
	at cn.hutool.core.lang.Assert.lambda$notNull$3(Assert.java:217)
	at cn.hutool.core.lang.Assert.notNull(Assert.java:197)
	at cn.hutool.core.lang.Assert.notNull(Assert.java:217)
	at org.dromara.system.controller.system.AreaController.formatId(AreaController.java:38)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at org.dromara.system.controller.system.AreaController$$SpringCGLIB$$0.formatId(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.RepeatableFilter.doFilter(RepeatableFilter.java:30)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.XssFilter.doFilter(XssFilter.java:38)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.encrypt.filter.CryptoFilter.doFilter(CryptoFilter.java:70)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-15 09:42:43 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:43:49 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:44:38 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:48:11 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:48:25 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:48:37 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:50:12 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:50:40 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:50:44 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:51:16 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:52:26 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:54:01 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 09:54:07 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:02:16 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:03:09 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:03:42 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:04:12 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/new/1921129812304113665',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJhNFF6a2tMNDVnTzVLMTVCdk1BZllqdDNDeHJGZ2JPRCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.GJ6Ise-k3UyiYFE2FpolMA2Ok1J-KKc-i59FseAC4lc',无法访问系统资源
2025-05-15 10:04:15 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/new/1921129812304113665',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJhNFF6a2tMNDVnTzVLMTVCdk1BZllqdDNDeHJGZ2JPRCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.GJ6Ise-k3UyiYFE2FpolMA2Ok1J-KKc-i59FseAC4lc',无法访问系统资源
2025-05-15 10:04:20 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:05:14 [XNIO-1 task-5] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:07:23 [XNIO-1 task-9] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求地址'/projects/prj_hazardous_items/aiDangerDetail/1921129812304113665',发生未知异常.
java.lang.NullPointerException: Cannot invoke "com.alibaba.fastjson2.JSONObject.getJSONArray(String)" because "data" is null
	at org.dromara.projects.service.impl.PrjHazardousItemsServiceImpl.getAiHazAnalysisTaskDetail(PrjHazardousItemsServiceImpl.java:129)
	at org.dromara.projects.controller.PrjHazardousItemsController.aiDetailVOR(PrjHazardousItemsController.java:146)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at org.dromara.projects.controller.PrjHazardousItemsController$$SpringCGLIB$$0.aiDetailVOR(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.RepeatableFilter.doFilter(RepeatableFilter.java:30)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.XssFilter.doFilter(XssFilter.java:38)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.encrypt.filter.CryptoFilter.doFilter(CryptoFilter.java:70)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-15 10:07:27 [XNIO-1 task-9] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求地址'/projects/prj_hazardous_items/aiDangerDetail/1921129812304113665',发生未知异常.
java.lang.NullPointerException: Cannot invoke "com.alibaba.fastjson2.JSONObject.getJSONArray(String)" because "data" is null
	at org.dromara.projects.service.impl.PrjHazardousItemsServiceImpl.getAiHazAnalysisTaskDetail(PrjHazardousItemsServiceImpl.java:129)
	at org.dromara.projects.controller.PrjHazardousItemsController.aiDetailVOR(PrjHazardousItemsController.java:146)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at org.dromara.projects.controller.PrjHazardousItemsController$$SpringCGLIB$$0.aiDetailVOR(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.RepeatableFilter.doFilter(RepeatableFilter.java:30)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.XssFilter.doFilter(XssFilter.java:38)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.encrypt.filter.CryptoFilter.doFilter(CryptoFilter.java:70)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-15 10:07:30 [XNIO-1 task-9] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求地址'/projects/prj_hazardous_items/aiDangerDetail/1921129812304113665',发生未知异常.
java.lang.NullPointerException: Cannot invoke "com.alibaba.fastjson2.JSONObject.getJSONArray(String)" because "data" is null
	at org.dromara.projects.service.impl.PrjHazardousItemsServiceImpl.getAiHazAnalysisTaskDetail(PrjHazardousItemsServiceImpl.java:129)
	at org.dromara.projects.controller.PrjHazardousItemsController.aiDetailVOR(PrjHazardousItemsController.java:146)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at org.dromara.projects.controller.PrjHazardousItemsController$$SpringCGLIB$$0.aiDetailVOR(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.RepeatableFilter.doFilter(RepeatableFilter.java:30)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.XssFilter.doFilter(XssFilter.java:38)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.encrypt.filter.CryptoFilter.doFilter(CryptoFilter.java:70)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-15 10:13:41 [XNIO-1 task-8] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:15:15 [XNIO-1 task-8] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:15:31 [XNIO-1 task-8] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:15:48 [XNIO-1 task-8] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:16:08 [XNIO-1 task-8] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:16:32 [XNIO-1 task-8] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:17:26 [XNIO-1 task-8] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:17:55 [XNIO-1 task-8] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:19:18 [XNIO-1 task-7] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:21:42 [XNIO-1 task-8] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:22:36 [XNIO-1 task-7] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:23:35 [XNIO-1 task-7] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:23:45 [XNIO-1 task-7] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:24:04 [XNIO-1 task-7] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:24:26 [XNIO-1 task-7] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:26:07 [XNIO-1 task-7] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:29:05 [XNIO-1 task-7] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:31:11 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:32:25 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:34:05 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:34:52 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:35:42 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:36:43 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:37:05 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:39:20 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:40:02 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:40:21 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:41:02 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:43:16 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:44:24 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:45:06 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:45:38 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:46:24 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:46:38 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 10:48:13 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/list',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJFTnhqVnJ6VXk3RzV4aWFCdkJoRVlFMmZmY1ZpanZYaCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.zkFSu9aOve6UvtLPiHkI-EpmeW_HnBudgcp7KyKi76s',无法访问系统资源
2025-05-15 11:10:51 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/list',认证失败'未能读取到有效 token',无法访问系统资源
2025-05-15 11:19:40 [XNIO-1 task-6] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求地址'/projects/prj_hazardous_items/aiDangerDetail/1921129812304113665',发生未知异常.
java.lang.NullPointerException: Cannot invoke "com.alibaba.fastjson2.JSONObject.getJSONArray(String)" because "data" is null
	at org.dromara.projects.service.impl.PrjHazardousItemsServiceImpl.getAiHazAnalysisTaskDetail(PrjHazardousItemsServiceImpl.java:129)
	at org.dromara.projects.controller.PrjHazardousItemsController.aiDetailVOR(PrjHazardousItemsController.java:151)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at org.dromara.projects.controller.PrjHazardousItemsController$$SpringCGLIB$$0.aiDetailVOR(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.RepeatableFilter.doFilter(RepeatableFilter.java:30)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.XssFilter.doFilter(XssFilter.java:38)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.encrypt.filter.CryptoFilter.doFilter(CryptoFilter.java:70)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-05-15 14:37:00 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAiAll',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJSQ21Ld3Y5Wk9lYzdNUjRTV1pOMTJKNjlrQjJSYWFGeCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.8U63fHrYV5AiJ3juAEpQgGwfgTjrQ-auRlrYhLJmErU',无法访问系统资源
2025-05-15 14:37:04 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJSQ21Ld3Y5Wk9lYzdNUjRTV1pOMTJKNjlrQjJSYWFGeCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.8U63fHrYV5AiJ3juAEpQgGwfgTjrQ-auRlrYhLJmErU',无法访问系统资源
2025-05-15 15:27:17 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/resource/oss/upload',认证失败'未能读取到有效 token',无法访问系统资源
2025-05-15 15:27:33 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/resource/oss/upload',认证失败'未能读取到有效 token',无法访问系统资源
2025-05-15 15:52:19 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 访问过于频繁，请稍候再试
2025-05-15 15:52:21 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 访问过于频繁，请稍候再试
2025-05-15 15:52:23 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 访问过于频繁，请稍候再试
2025-05-15 15:52:29 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 访问过于频繁，请稍候再试
2025-05-15 15:52:43 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 访问过于频繁，请稍候再试
2025-05-15 15:53:02 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 访问过于频繁，请稍候再试
2025-05-15 15:53:53 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 验证码错误
2025-05-15 16:07:34 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/upload',权限码校验失败'无此权限：ai:ai_haz_analysis_tasks:add'
2025-05-15 16:09:39 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - Validation failed for argument [0] in public org.dromara.common.core.domain.R<java.lang.Void> org.dromara.system.controller.system.SysDictDataController.edit(org.dromara.system.domain.bo.SysDictDataBo): [Field error in object 'sysDictDataBo' on field 'cssClass': rejected value [/* HTML:  */ .loader {   width: 50px;   aspect-ratio: 1;   border-radius: 50%;   border: 8px solid lightblue;   border-right-color: orange;   animation: l2 1s infinite linear; } @keyframes l2 {to{transform: rotate(1turn)}}]; codes [Size.sysDictDataBo.cssClass,Size.cssClass,Size.java.lang.String,Size]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [sysDictDataBo.cssClass,cssClass]; arguments []; default message [cssClass],100,0]; default message [样式属性长度不能超过100个字符]] 
2025-05-15 16:12:17 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 验证码已失效
2025-05-15 16:19:13 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAiAll',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 16:19:37 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 16:19:38 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_hazardous_items/app/list',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 16:19:39 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/list',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 16:19:41 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/list',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 16:38:39 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 未选择指定项目！
2025-05-15 17:11:41 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 访问过于频繁，请稍候再试
2025-05-15 17:13:31 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/list',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:14:48 [XNIO-1 task-4] ERROR o.d.a.s.i.AiHazAnalysisTasksServiceImpl - AI分析结果数据为空，任务ID: 1922943209207250945
2025-05-15 17:18:01 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:22:37 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:22:44 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:25:52 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:34:32 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:34:52 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:35:16 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:36:13 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:36:28 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:36:38 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:37:07 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:37:35 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:38:14 [XNIO-1 task-5] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:39:16 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:39:57 [XNIO-1 task-5] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/null',发生系统异常.
2025-05-15 17:40:00 [XNIO-1 task-5] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:40:01 [XNIO-1 task-5] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/null',发生系统异常.
2025-05-15 17:40:08 [XNIO-1 task-3] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/null',发生系统异常.
2025-05-15 17:40:31 [XNIO-1 task-3] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/null',发生系统异常.
2025-05-15 17:41:16 [XNIO-1 task-5] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/null',发生系统异常.
2025-05-15 17:41:19 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:41:23 [XNIO-1 task-3] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/null',发生系统异常.
2025-05-15 17:41:40 [XNIO-1 task-3] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/null',发生系统异常.
2025-05-15 17:41:42 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:41:43 [XNIO-1 task-3] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/null',发生系统异常.
2025-05-15 17:41:50 [XNIO-1 task-3] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/null',发生系统异常.
2025-05-15 17:42:15 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:42:16 [XNIO-1 task-3] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/null',发生系统异常.
2025-05-15 17:42:22 [XNIO-1 task-3] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/null',发生系统异常.
2025-05-15 17:43:56 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:44:09 [XNIO-1 task-5] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/null',发生系统异常.
2025-05-15 17:44:16 [XNIO-1 task-5] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/null',发生系统异常.
2025-05-15 17:44:45 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:44:49 [XNIO-1 task-3] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/null',发生系统异常.
2025-05-15 17:45:15 [XNIO-1 task-4] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:45:23 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:45:23 [XNIO-1 task-5] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求地址'/projects/prj_hazardous_items/app/1922184486981308418',连接中断
org.springframework.web.context.request.async.AsyncRequestNotUsableException: ServletOutputStream failed to write: 你的主机中的软件中止了一个已建立的连接。
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleHttpServletResponse.handleIOException(StandardServletAsyncWebRequest.java:346)
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.write(StandardServletAsyncWebRequest.java:404)
	at org.springframework.util.StreamUtils$NonClosingOutputStream.write(StreamUtils.java:263)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2261)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1202)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1063)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:486)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:126)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:345)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:208)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:136)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.RepeatableFilter.doFilter(RepeatableFilter.java:30)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.XssFilter.doFilter(XssFilter.java:38)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.encrypt.filter.CryptoFilter.doFilter(CryptoFilter.java:70)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at java.base/sun.nio.ch.SocketDispatcher.writev0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.writev(SocketDispatcher.java:58)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:217)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:153)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:563)
	at org.xnio.nio.NioSocketConduit.write(NioSocketConduit.java:162)
	at io.undertow.server.protocol.http.HttpResponseConduit.write(HttpResponseConduit.java:679)
	at org.xnio.conduits.AbstractStreamSinkConduit.write(AbstractStreamSinkConduit.java:55)
	at org.xnio.conduits.ConduitStreamSinkChannel.write(ConduitStreamSinkChannel.java:158)
	at io.undertow.channels.DetachableStreamSinkChannel.write(DetachableStreamSinkChannel.java:179)
	at io.undertow.server.HttpServerExchange$WriteDispatchChannel.write(HttpServerExchange.java:2171)
	at org.xnio.channels.Channels.writeBlocking(Channels.java:202)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.writeBlocking(ServletOutputStreamImpl.java:1002)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.writeTooLargeForBuffer(ServletOutputStreamImpl.java:228)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.write(ServletOutputStreamImpl.java:149)
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.write(StandardServletAsyncWebRequest.java:401)
	... 87 common frames omitted
2025-05-15 17:46:23 [XNIO-1 task-5] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 17:52:57 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/system/user/getInfo',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJQYUt0bmZoZEdFbU5QRmw2RnVGMzAzbVdIWEhSSEpEUiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.1MOCZ0K4sENy95VL7lXRRzKVlwsnQAuPo7bfA80SMDU',无法访问系统资源
2025-05-15 18:25:35 [XNIO-1 task-3] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJyNmlFd2tWd09iVG9nZXF2T3hSMmZqUnFSekZINlhKUyIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.ihhSLTl9x_E6t4rtyjVnOMubCL-UJl1uDayGYZ0Co8Q',无法访问系统资源
2025-05-15 18:29:39 [XNIO-1 task-3] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/undefined',发生系统异常.
2025-05-15 18:29:53 [XNIO-1 task-3] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/undefined',发生系统异常.
2025-05-15 18:30:28 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求参数类型不匹配'/projects/prj_hazardous_items/app/undefined',发生系统异常.
2025-05-15 18:35:12 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 18:35:18 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 18:36:20 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 18:37:21 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 18:38:36 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 18:40:20 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 18:43:02 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 18:43:09 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 18:43:37 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 18:43:44 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 18:45:00 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 18:45:47 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 18:46:17 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 18:47:33 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 18:47:46 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 19:38:29 [XNIO-1 task-9] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/projects/prj_projects/listAi',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiIyOTI1a0ZXdnJZVFVxU0pYYVFNRjJJUGJWQXEzWUhYTSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.PnlgYVw1vlLLWx45dmN5G_NMb5F-mTmCzwC5dZ3g5xg',无法访问系统资源
2025-05-15 19:38:31 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/upload',连接中断
org.springframework.web.context.request.async.AsyncRequestNotUsableException: ServletOutputStream failed to write: 你的主机中的软件中止了一个已建立的连接。
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleHttpServletResponse.handleIOException(StandardServletAsyncWebRequest.java:346)
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.write(StandardServletAsyncWebRequest.java:404)
	at org.springframework.util.StreamUtils$NonClosingOutputStream.write(StreamUtils.java:263)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2261)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1202)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1063)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:486)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:126)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:345)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:208)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:136)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.RepeatableFilter.doFilter(RepeatableFilter.java:32)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.XssFilter.doFilter(XssFilter.java:42)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.encrypt.filter.CryptoFilter.doFilter(CryptoFilter.java:70)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at java.base/sun.nio.ch.SocketDispatcher.writev0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.writev(SocketDispatcher.java:58)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:217)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:153)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:563)
	at org.xnio.nio.NioSocketConduit.write(NioSocketConduit.java:162)
	at io.undertow.server.protocol.http.HttpResponseConduit.processWrite(HttpResponseConduit.java:291)
	at io.undertow.server.protocol.http.HttpResponseConduit.write(HttpResponseConduit.java:666)
	at org.xnio.conduits.AbstractStreamSinkConduit.write(AbstractStreamSinkConduit.java:55)
	at org.xnio.conduits.ConduitStreamSinkChannel.write(ConduitStreamSinkChannel.java:158)
	at io.undertow.channels.DetachableStreamSinkChannel.write(DetachableStreamSinkChannel.java:179)
	at io.undertow.server.HttpServerExchange$WriteDispatchChannel.write(HttpServerExchange.java:2171)
	at org.xnio.channels.Channels.writeBlocking(Channels.java:202)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.writeBlocking(ServletOutputStreamImpl.java:1002)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.writeTooLargeForBuffer(ServletOutputStreamImpl.java:202)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.write(ServletOutputStreamImpl.java:149)
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.write(StandardServletAsyncWebRequest.java:401)
	... 87 common frames omitted
2025-05-15 19:38:31 [XNIO-1 task-3] ERROR o.d.c.w.h.GlobalExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/upload',连接中断
org.springframework.web.context.request.async.AsyncRequestNotUsableException: ServletOutputStream failed to write: 你的主机中的软件中止了一个已建立的连接。
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleHttpServletResponse.handleIOException(StandardServletAsyncWebRequest.java:346)
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.write(StandardServletAsyncWebRequest.java:404)
	at org.springframework.util.StreamUtils$NonClosingOutputStream.write(StreamUtils.java:263)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2261)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1202)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1063)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:486)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:126)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:345)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:208)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:136)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.RepeatableFilter.doFilter(RepeatableFilter.java:32)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.web.filter.XssFilter.doFilter(XssFilter.java:42)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.dromara.common.encrypt.filter.CryptoFilter.doFilter(CryptoFilter.java:70)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at java.base/sun.nio.ch.SocketDispatcher.writev0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.writev(SocketDispatcher.java:58)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:217)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:153)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:563)
	at org.xnio.nio.NioSocketConduit.write(NioSocketConduit.java:162)
	at io.undertow.server.protocol.http.HttpResponseConduit.processWrite(HttpResponseConduit.java:291)
	at io.undertow.server.protocol.http.HttpResponseConduit.write(HttpResponseConduit.java:666)
	at org.xnio.conduits.AbstractStreamSinkConduit.write(AbstractStreamSinkConduit.java:55)
	at org.xnio.conduits.ConduitStreamSinkChannel.write(ConduitStreamSinkChannel.java:158)
	at io.undertow.channels.DetachableStreamSinkChannel.write(DetachableStreamSinkChannel.java:179)
	at io.undertow.server.HttpServerExchange$WriteDispatchChannel.write(HttpServerExchange.java:2171)
	at org.xnio.channels.Channels.writeBlocking(Channels.java:202)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.writeBlocking(ServletOutputStreamImpl.java:1002)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.writeTooLargeForBuffer(ServletOutputStreamImpl.java:202)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.write(ServletOutputStreamImpl.java:149)
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.write(StandardServletAsyncWebRequest.java:401)
	... 87 common frames omitted
2025-05-15 19:47:41 [XNIO-1 task-2] ERROR o.d.c.w.h.GlobalExceptionHandler - 验证码已失效
2025-05-15 19:48:25 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/new/1922943209207250945',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJxeks1UGdPcEF4QmNPZHlqWXRndDJtT1Y4WVlYdDlxUCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.i1iB8NqebqQNrlcijG6yl7cTWnZlPAeT4OgysmhCLqA',无法访问系统资源
2025-05-15 19:48:29 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/app/ai/ai_haz_analysis_tasks/app/new/1922943209207250945',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJxeks1UGdPcEF4QmNPZHlqWXRndDJtT1Y4WVlYdDlxUCIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.i1iB8NqebqQNrlcijG6yl7cTWnZlPAeT4OgysmhCLqA',无法访问系统资源
2025-05-15 21:08:51 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysLoginService' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantServiceImpl' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysTenantServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantMapper' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysTenantMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1155)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1121)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1056)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.MunicipalSafetyApplication.main(MunicipalSafetyApplication.java:19)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysLoginService' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantServiceImpl' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysTenantServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantMapper' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysTenantMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1667)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysTenantServiceImpl' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysTenantServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantMapper' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysTenantMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1667)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysTenantMapper' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysTenantMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1436)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1667)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 47 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:546)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1667)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1527)
	... 59 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1812)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1609)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 72 common frames omitted
Caused by: com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.zaxxer.hikari.pool.HikariPool.throwPoolInitializationException(HikariPool.java:584)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:571)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator.createDataSource(HikariDataSourceCreator.java:85)
	at com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator.createDataSource(DefaultDataSourceCreator.java:97)
	at com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider.createDataSourceMap(AbstractDataSourceProvider.java:53)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:53)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1859)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	... 82 common frames omitted
Caused by: java.sql.SQLNonTransientConnectionException: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:102)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:81)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:55)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:65)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:911)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:830)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:420)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:238)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	... 91 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:52)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:95)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:140)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:156)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:79)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:142)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:851)
	... 100 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:144)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:53)
	... 102 common frames omitted
2025-05-15 21:46:49 [XNIO-1 task-2] ERROR o.d.c.s.h.SaTokenExceptionHandler - 请求地址'/system/user/getInfo',认证失败'token 已被冻结：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJscndRYW5FbkVabG5CQjJRVGl3NkE3RlRTZ1dSMHBiZyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.VoEgXChxiiUHG2i14ysRSniUo-c1EHNb0OufKRKmxjU',无法访问系统资源
2025-05-15 22:13:54 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysLoginService' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantServiceImpl' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysTenantServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantMapper' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysTenantMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1155)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1121)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1056)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.MunicipalSafetyApplication.main(MunicipalSafetyApplication.java:19)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysLoginService' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantServiceImpl' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysTenantServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantMapper' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysTenantMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1667)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysTenantServiceImpl' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysTenantServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantMapper' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysTenantMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1667)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysTenantMapper' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysTenantMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1436)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1667)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 47 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:546)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1667)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1527)
	... 59 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1812)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1609)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 72 common frames omitted
Caused by: com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.zaxxer.hikari.pool.HikariPool.throwPoolInitializationException(HikariPool.java:584)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:571)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator.createDataSource(HikariDataSourceCreator.java:85)
	at com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator.createDataSource(DefaultDataSourceCreator.java:97)
	at com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider.createDataSourceMap(AbstractDataSourceProvider.java:53)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:53)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1859)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	... 82 common frames omitted
Caused by: java.sql.SQLNonTransientConnectionException: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:102)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:81)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:55)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:65)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:911)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:830)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:420)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:238)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	... 91 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:52)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:95)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:140)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:156)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:592)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:516)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:411)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1331)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:155)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:851)
	... 100 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:58)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:72)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:54)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:36)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:586)
	... 105 common frames omitted
2025-05-15 22:50:13 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysLoginService' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantServiceImpl' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysTenantServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantMapper' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysTenantMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1155)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1121)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1056)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.MunicipalSafetyApplication.main(MunicipalSafetyApplication.java:19)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysLoginService' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantServiceImpl' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysTenantServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantMapper' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysTenantMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1667)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysTenantServiceImpl' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysTenantServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysTenantMapper' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysTenantMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1667)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysTenantMapper' defined in file [D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysTenantMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1436)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1667)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 47 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:546)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1667)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1527)
	... 59 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1812)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1609)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1555)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 72 common frames omitted
Caused by: com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.zaxxer.hikari.pool.HikariPool.throwPoolInitializationException(HikariPool.java:584)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:571)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator.createDataSource(HikariDataSourceCreator.java:85)
	at com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator.createDataSource(DefaultDataSourceCreator.java:97)
	at com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider.createDataSourceMap(AbstractDataSourceProvider.java:53)
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:53)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1859)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	... 82 common frames omitted
Caused by: java.sql.SQLNonTransientConnectionException: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:102)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:81)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:55)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:65)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:911)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:830)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:420)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:238)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	... 91 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:52)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:95)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:140)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:156)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:592)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:516)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:411)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1331)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:155)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:851)
	... 100 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:58)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:72)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:54)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:36)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:586)
	... 105 common frames omitted

import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DivisionVO, DivisionForm, DivisionQuery } from '@/api/system/division/types';

/**
 * 查询行政区划列表
 * @param query
 * @returns {*}
 */
export const listDivision = (query?: DivisionQuery): AxiosPromise<DivisionVO[]> => {
  return request({
    url: '/system/division/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询行政区划详细
 * @param divisionId
 */
export const getDivision = (divisionId: string | number): AxiosPromise<DivisionVO> => {
  return request({
    url: '/system/division/' + divisionId,
    method: 'get'
  });
};

/**
 * 新增行政区划
 * @param data
 */
export const addDivision = (data: DivisionForm) => {
  return request({
    url: '/system/division',
    method: 'post',
    data: data
  });
};

/**
 * 修改行政区划
 * @param data
 */
export const updateDivision = (data: DivisionForm) => {
  return request({
    url: '/system/division',
    method: 'put',
    data: data
  });
};

/**
 * 删除行政区划
 * @param divisionId
 */
export const delDivision = (divisionId: string | number | Array<string | number>) => {
  return request({
    url: '/system/division/' + divisionId,
    method: 'delete'
  });
};

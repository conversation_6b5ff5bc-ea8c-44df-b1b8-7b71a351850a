<template>
  <div class="p-2" style="height: calc(100vh - 155px);">
    <el-card shadow="never">
      <div style="display: flex; justify-content: space-between">
        <div>
          <el-button v-if="submitButtonShow" :loading="buttonLoading" type="info"
            @click="submitForm('draft')">暂存</el-button>
          <el-button v-if="submitButtonShow" :loading="buttonLoading" type="primary" @click="submitForm('submit')">提
            交</el-button>
          <el-button v-if="approvalButtonShow" :loading="buttonLoading" type="primary"
            @click="approvalVerifyOpen">审批</el-button>
          <el-button v-if="form && form.id" type="primary" @click="handleApprovalRecord">流程进度</el-button>
        </div>
        <div>
          <el-button style="float: right" @click="goBack()">返回</el-button>
        </div>
      </div>
    </el-card>
    <!-- 隐患清单 -->
    <el-row :gutter="10" style="width: 100%;height: 100%;padding: 10px;">
      <el-col :span="5" style="display: flex;flex-direction: column;align-items: flex-start;">
        <div style="margin-bottom: 10px;">
          <p style="text-align: start;color: #409EFF;margin: 0 0 16px;">分析前</p>
          <HeaderPrewiew :src="aiDetailData?.photoDocumentUrl" width="17vw" height="17vw"
            :preview-src-list="[aiDetailData?.photoDocumentUrl]">
          </HeaderPrewiew>
        </div>
        <div>
          <p style="text-align: start;color: #67C23A;margin: 10px 0 16px;">分析后</p>
          <HeaderPrewiew :src="aiDetailData?.aiPhotoDocumentUrl" width="17vw" height="17vw"
            :preview-src-list="[aiDetailData?.aiPhotoDocumentUrl]">
          </HeaderPrewiew>
        </div>
      </el-col>
      <el-col :span="10" style="display: flex;flex-direction: column;height: 100%;padding-top: 5px;">
        <div style="width: 100%;height: calc(100vh - 227px);overflow-y: auto;">
          <el-card style="width: 100%;margin-bottom: 20px;" v-for="(item, index) in aiDetailData?.violations"
            :key="index">
            <template #header>
              <div class="card-header">
                <div style="display: flex;align-items: center;">
                  <el-checkbox v-if="isPreview != 'view'" v-model="rgfjCheck[index]" />
                  <span style="display: block;padding-bottom: 3px;margin-left: 8px;">问题 {{ index + 1 }}</span>
                </div>
                <div style="display: flex;align-items: center;">
                  <span style="color: #409EFF;">危险级别：</span>
                  <dict-tag :options="hidden_danger_type" :value="item.level" />
                </div>
              </div>
            </template>
            <div style="display: flex;">
              <span style="color: #666;display: block;width: 80px;">隐患描述：</span>
              <span style="display: block;flex: 1;">{{ item.violation }}</span>
            </div>
            <div style="display: flex;margin: 15px 0;">
              <span style="color: #666;display: block;width: 80px;">违反条款：</span>
              <span style="display: block;flex: 1;">{{ item.regulation }}</span>
            </div>
            <div style="display: flex;">
              <span style="color: #666;display: block;width: 80px;">整改意见：</span>
              <span style="display: block;flex: 1;">{{ item.measure
              }}</span>
            </div>
          </el-card>
        </div>
      </el-col>
      <el-col :span="9" style="padding-left: 20px;height: 100%;padding-top: 5px;">
        <div style="width: 100%;height: calc(100vh - 227px);overflow-y: auto;padding-right: 20px;">
          <div>
            <div style="display: flex;align-items: center;">
              <label>整改复核</label>
              <span v-if="isPreview != 'view'"
                style="font-size: 15px;color: #F56C6C;margin-left: 10px;padding-bottom: 2px;">(注：请先选择左侧的问题)</span>
            </div>
            <el-checkbox-group v-model="selectCheckVal" style="margin-top: 20px;"
              :disabled="isPreview == 'view' ? true : false">
              <el-checkbox label="限期整改" :value="1" />
              <el-checkbox label="停工通知" :value="2" />
              <el-checkbox label="行政处罚" :value="3" />
              <el-checkbox label="其他" :value="4" />
            </el-checkbox-group>
            <div>
              <div style="display: flex;align-items: center;margin-top: 20px;">
                <span style="display: block;width: 80px;">整改时限</span>
                <el-input v-model="formCorrectData.timeLimit" style="width: 12vw;" type="number" :min="1"
                  placeholder="请输入时间" @change="() => {
                    if (formCorrectData.timeLimit <= 0) {
                      formCorrectData.timeLimit = 1;
                    }
                  }" :disabled="isPreview == 'view' ? true : false">
                  <template #append>
                    <el-select v-model="formCorrectData.timeType" style="width: 80px;"
                      :disabled="isPreview == 'view' ? true : false">
                      <el-option label="小时" :value="1" />
                      <el-option label="天" :value="2" />
                    </el-select>
                  </template>
                </el-input>
              </div>
            </div>
          </div>
          <div style="margin-top: 30px;">
            <ul style="list-style: none;padding: 0;margin: 0;">
              <li v-if="selectCheckVal.includes(1)" style="margin-bottom: 20px;">
                <div v-if="isPreview == 'view'" style="display: flex;margin-bottom: 10px;">
                  <label>限期整改内容</label>
                  <el-link type="primary" href="javascript:;" style="color: #409EFF;margin-left: 20px"
                    @click="handleFileView(formCorrectData.correctionsFile, null)">{{
                      fileNames.correctionsFileName
                    }}</el-link>
                </div>
                <div v-else style="display: flex;">
                  <label>限期整改内容</label>
                  <el-button type="primary" size="small" style="margin-left: 20px;margin-right: 15px;">下载模板</el-button>
                  <FileUpload @update:modelValue="handleDeadlineFile" :limit="1"
                    :modelValue="formCorrectData.correctionsFile" :isShowTip="false" :fileSize="20"
                    style="width: 60px;">
                    <el-button size="small" type="primary">上传</el-button>
                  </FileUpload>
                  <span
                    style="font-size: 15px;color: #F56C6C;margin-left: 10px;padding-bottom: 2px;">(注：请上传已盖章文件)</span>
                </div>
                <el-input v-model="formCorrectData.correctionsContent" :autosize="{ minRows: 3 }" type="textarea"
                  style="margin-top:10px;" :disabled="isPreview == 'view' ? true : false" />
              </li>
              <li v-if="selectCheckVal.includes(2)" style="margin-bottom: 20px;">
                <div v-if="isPreview == 'view'" style="display: flex;margin-bottom: 10px;">
                  <label>停工通知内容</label>
                  <el-link type="primary" href="javascript:;" style="color: #409EFF;margin-left: 20px"
                    @click="handleFileView(formCorrectData.suspensionFile, null)">{{
                      fileNames.suspensionFileName
                    }}</el-link>
                </div>
                <div v-else style="display: flex;">
                  <label>停工通知内容</label>
                  <el-button type="primary" size="small" style="margin-left: 20px;margin-right: 15px;">下载模板</el-button>
                  <FileUpload @update:modelValue="handleShutdownFile" :limit="1"
                    :modelValue="formCorrectData.suspensionFile" :isShowTip="false" :fileSize="20" style="width: 60px;">
                    <el-button size="small" type="primary">上传</el-button>
                  </FileUpload>
                  <span
                    style="font-size: 15px;color: #F56C6C;margin-left: 10px;padding-bottom: 2px;">(注：请上传已盖章文件)</span>
                </div>
                <el-input v-model="formCorrectData.suspensionContent" :autosize="{ minRows: 3 }" type="textarea"
                  style="margin-top: 10px;" :disabled="isPreview == 'view' ? true : false" />
              </li>
              <li v-if="selectCheckVal.includes(3)" style="margin-bottom: 20px;">
                <div v-if="isPreview == 'view'" style="display: flex;margin-bottom: 10px;">
                  <label>行政处罚内容</label>
                  <el-link type="primary" href="javascript:;" style="color: #409EFF;margin-left: 20px"
                    @click="handleFileView(formCorrectData.penaltyFile, null)">{{
                      fileNames.penaltyFileName
                    }}</el-link>
                </div>
                <div v-else style="display: flex;">
                  <label>行政处罚内容</label>
                  <el-button type="primary" size="small" style="margin-left: 20px;margin-right: 15px;">下载模板</el-button>
                  <FileUpload @update:modelValue="handlePunishFile" :limit="1" :modelValue="formCorrectData.penaltyFile"
                    :isShowTip="false" :fileSize="20" style="width: 60px;">
                    <el-button size="small" type="primary">上传</el-button>
                  </FileUpload>
                  <span
                    style="font-size: 15px;color: #F56C6C;margin-left: 10px;padding-bottom: 2px;">(注：请上传已盖章文件)</span>
                </div>
                <el-input v-model="formCorrectData.penaltyContent" :autosize="{ minRows: 3 }" type="textarea"
                  style="margin-top: 10px;" :disabled="isPreview == 'view' ? true : false" />
              </li>
              <li v-if="selectCheckVal.includes(4)" style="margin-bottom: 20px;">
                <div style="display: flex;">
                  <label>其他</label>
                </div>
                <el-table :data="OtherTableData" style="width: 100%;margin-top: 10px;">
                  <el-table-column type="index" label="序号" width="55" align="center" />
                  <el-table-column prop="name" label="名称" align="center" />
                  <el-table-column prop="fileName" label="文件" align="center">
                    <template #default="scope">
                      <el-link type="primary" href="javascript:;" style="color: #409EFF;"
                        @click="handleFileView(null, scope.row.url)">{{
                          scope.row.fileName
                        }}</el-link>
                    </template>
                  </el-table-column>
                </el-table>
              </li>
            </ul>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 提交组件 -->
    <submitVerify ref="submitVerifyRef" :task-variables="taskVariables" :isReadyFile="false" :isSelectBtnDisabled="true"
      @submit-callback="submitCallback" @beforeSubmit="beforeSubmit" />
    <!-- 审批记录 -->
    <approvalRecord ref="approvalRecordRef" />
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" :before-close="handleClose" width="500">
      <el-select v-model="flowCode" placeholder="Select" style="width: 240px">
        <el-option v-for="item in flowCodeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="submitFlow()"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Leave" lang="ts">
import { addLeave, updateLeave } from '@/api/workflow/leave';
import { LeaveForm, LeaveQuery, LeaveVO } from '@/api/workflow/leave/types';
import { startWorkFlow, getTaskVariables } from '@/api/workflow/task';
import { batSave, getPrj_hazardous_items_ai_detail } from '@/api/customFlow/api'
import { listByIds } from '@/api/system/oss/index'
import SubmitVerify from '@/components/Process/submitVerify.vue';
import ApprovalRecord from '@/components/Process/approvalRecord.vue';
import { AxiosResponse } from 'axios';
import { StartProcessBo } from '@/api/workflow/workflowCommon/types';
import HeaderPrewiew from '@/components/ImagePreview/index.vue';
import FileUpload from '@/components/FileUpload/index.vue'
import { getManualDetail } from '@/api/customFlow/api'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { hidden_danger_type } = toRefs<any>(proxy?.useDict('hidden_danger_type'));

const buttonLoading = ref(false);
const loading = ref(true);
const leaveTime = ref<Array<string>>([]);
// 存放隐患清单详情的数据
const aiDetailData = ref();
// 人工复检多选值
const rgfjCheck = ref([])
// 人工复检多选框的值
const selectCheckVal = ref([])
// 提交工单的表单参数
const formCorrectData = reactive({
  question: '', // 问题点id(多个根据顺序用,隔开拼接)
  timeLimit: 1, // 整改时限
  timeType: 1, // 时间单位
  correctionsFile: '', // 限期整改文件
  correctionsContent: '', // 限期整改内容
  suspensionFile: '', // 停工通知文件
  suspensionContent: '', // 停工通知内容
  penaltyFile: '', // 行政处罚文件
  penaltyContent: '', // 行政处罚内容
  elseFile: []        //其他文件
})
// 其他部分的数据
const OtherTableData = ref([])
// 存放整改文件的文件名
const fileNames = ref({
  correctionsFileName: '', // 限期整改文件
  suspensionFileName: '', // 停工通知文件
  penaltyFileName: '', // 行政处罚文件
})
// 判断是预览还是编辑状态
const isPreview = ref('')
// 单选框选中的值
const isSelected = ref([]);
const selectContent = ref([]);
const personComments = ref([]);
//路由参数
const routeParams = ref<Record<string, any>>({});
const flowCodeOptions = [
  {
    value: 'leave1',
    label: '请假申请-普通'
  },
  {
    value: 'leave2',
    label: '请假申请-排他网关'
  },
  {
    value: 'leave3',
    label: '请假申请-并行网关'
  },
  {
    value: 'leave4',
    label: '请假申请-会签'
  },
  {
    value: 'leave5',
    label: '请假申请-并行会签网关'
  },
  {
    value: 'leave6',
    label: '请假申请-排他并行会签'
  }
];

const flowCode = ref<string>('');

const dialogVisible = reactive<DialogOption>({
  visible: false,
  title: '流程定义'
});
//提交组件
const submitVerifyRef = ref<InstanceType<typeof SubmitVerify>>();
//审批记录组件
const approvalRecordRef = ref<InstanceType<typeof ApprovalRecord>>();

const leaveFormRef = ref<ElFormInstance>();

const submitFormData = ref<StartProcessBo>({
  businessId: '',
  flowCode: '',
  variables: {}
});
const taskVariables = ref<Record<string, any>>({});

const initFormData: LeaveForm = {
  id: undefined,
  leaveType: undefined,
  startDate: undefined,
  endDate: undefined,
  leaveDays: undefined,
  remark: undefined,
  status: 'waiting'
};
const data = reactive<PageData<LeaveForm, LeaveQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    startLeaveDays: undefined,
    endLeaveDays: undefined
  },
  rules: {
    id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
    leaveType: [{ required: true, message: '请假类型不能为空', trigger: 'blur' }],
    leaveTime: [{ required: true, message: '请假时间不能为空', trigger: 'blur' }],
    leaveDays: [{ required: true, message: '请假天数不能为空', trigger: 'blur' }]
  }
});

const handleClose = () => {
  dialogVisible.visible = false;
  flowCode.value = '';
  buttonLoading.value = false;
};
const { form, rules } = toRefs(data);

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  leaveTime.value = [];
  leaveFormRef.value?.resetFields();
};
const changeLeaveTime = () => {
  const startDate = new Date(leaveTime.value[0]).getTime();
  const endDate = new Date(leaveTime.value[1]).getTime();
  const diffInMilliseconds = endDate - startDate;
  form.value.leaveDays = Math.floor(diffInMilliseconds / (1000 * 60 * 60 * 24)) + 1;
};
// 使用ossId查询图片的url地址
const getImageUrl = async (ossId: string | number) => {
  const { data } = await listByIds(ossId);
  return data[0]?.url;
}
// 获取隐患详情数据
const getAiDetail = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(255, 255, 255, 0.8)',
  })
  isSelected.value = [];
  personComments.value = [];
  const res = await getPrj_hazardous_items_ai_detail(taskVariables.value.ai_task_id);
  if (res.code === 200) {
    aiDetailData.value = res.data;
    aiDetailData.value.photoDocumentUrl = await getImageUrl(aiDetailData.value.photoDocumentId);
    aiDetailData.value.aiPhotoDocumentUrl = await getImageUrl(aiDetailData.value.aiPhotoDocumentId);
    aiDetailData.value.violations.forEach(item => {
      isSelected.value.push(item.commentStatus == null ? 0 : item.commentStatus);
      personComments.value.push(item.personComments);
      if (routeParams.value.type == 'view') {
        item.disable = true;
      } else {
        item.disable = false;
      }
    });
  }
  loading.close();
}
// 获取整改符合的详情数据
const getManualDetailData = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(255, 255, 255, 0.8)',
  })
  const res = await getManualDetail(routeParams.value.id);
  if (res.code === 200) {
    Object.assign(formCorrectData, res.data)
    if (formCorrectData.correctionsFile) {
      selectCheckVal.value[0] = 1;
      const { data } = await listByIds(formCorrectData.correctionsFile);
      fileNames.value.correctionsFileName = data[0]?.originalName;
    }
    if (formCorrectData.suspensionFile) {
      selectCheckVal.value[1] = 2;
      const { data } = await listByIds(formCorrectData.suspensionFile);
      fileNames.value.suspensionFileName = data[0]?.originalName;
    }
    if (formCorrectData.penaltyFile) {
      selectCheckVal.value[2] = 3;
      const { data } = await listByIds(formCorrectData.penaltyFile);
      fileNames.value.penaltyFileName = data[0]?.originalName;
    }
    if (formCorrectData.elseFile.length > 0) {
      selectCheckVal.value[3] = 4;
      for (let i = 0; i < formCorrectData.elseFile.length; i++) {
        const { data } = await listByIds(formCorrectData.elseFile[i].fileId);
        OtherTableData.value.push({
          name: formCorrectData.elseFile[i].name,
          fileName: data[0]?.originalName,
          url: data[0]?.url,
          ossId: data[0]?.ossId,
        })
      }
    }
  }
  loading.close();
}
// 上传限期整改文件的回调函数
const handleDeadlineFile = (fileOssId: string) => {
  formCorrectData.correctionsFile = fileOssId;
}
// 上传停工通知文件的回调函数
const handleShutdownFile = (fileOssId: string) => {
  formCorrectData.suspensionFile = fileOssId;
}
// 上传行政处罚文件的回调函数
const handlePunishFile = (fileOssId: string) => {
  formCorrectData.penaltyFile = fileOssId;
}
// 获取任务变量数据
const getTaskVariablesData = async () => {
  const res = await getTaskVariables(routeParams.value.id);
  if (res.code === 200) {
    taskVariables.value = res.data;
  }
}
// 单选框选中的值改变时触发的事件
const selectChange = () => {
  const isSelectVal = isSelected.value.some(item => item == 1)
  if (isSelectVal) {
    taskVariables.value.real = 1;
  } else {
    taskVariables.value.real = 0;
  }
}
/** 暂存提交按钮 */
const submitForm = (status: string) => {
  if (leaveTime.value.length === 0) {
    proxy?.$modal.msgError('请假时间不能为空');
    return;
  }
  try {
    leaveFormRef.value?.validate(async (valid: boolean) => {
      form.value.startDate = leaveTime.value[0];
      form.value.endDate = leaveTime.value[1];
      if (valid) {
        buttonLoading.value = true;
        let res: AxiosResponse<LeaveVO>;
        if (form.value.id) {
          res = await updateLeave(form.value);
        } else {
          res = await addLeave(form.value);
        }
        form.value = res.data;
        if (status === 'draft') {
          buttonLoading.value = false;
          proxy?.$modal.msgSuccess('暂存成功');
          proxy.$tab.closePage(proxy.$route);
          proxy.$router.go(-1);
        } else {
          if ((form.value.status === 'draft' && (flowCode.value === '' || flowCode.value === null)) || routeParams.value.type === 'add') {
            flowCode.value = flowCodeOptions[0].value;
            dialogVisible.visible = true;
            return;
          }
          //说明启动过先随意穿个参数
          if (flowCode.value === '' || flowCode.value === null) {
            flowCode.value = 'xx';
          }
          await handleStartWorkFlow(res.data);
        }
      }
    });
  } finally {
    buttonLoading.value = false;
  }
};

const submitFlow = async () => {
  handleStartWorkFlow(form.value);
  dialogVisible.visible = false;
};
//提交申请
const handleStartWorkFlow = async (data: LeaveForm) => {
  try {
    submitFormData.value.flowCode = flowCode.value;
    submitFormData.value.businessId = data.id;
    //流程变量
    taskVariables.value = {
      leaveDays: data.leaveDays,
      userList: ['1', '3', '4']
    };
    submitFormData.value.variables = taskVariables.value;
    const resp = await startWorkFlow(submitFormData.value);
    if (submitVerifyRef.value) {
      buttonLoading.value = false;
      submitVerifyRef.value.openDialog(resp.data.taskId);
    }
  } finally {
    buttonLoading.value = false;
  }
};
//头部流程进度
const handleApprovalRecord = () => {
  approvalRecordRef.value.init(form.value.id);
};

//提交组件回调
const submitCallback = async () => {
  await proxy.$tab.closePage(proxy.$route);
  proxy.$router.go(-1);
};
// 提交前的通用回调函数
const beforeSubmit = async (fun) => {
  // 提交前的逻辑处理
  fun(true)
};
//头部返回
const goBack = () => {
  proxy.$tab.closePage(proxy.$route);
  proxy.$router.go(-1);
};
//头部审批
const approvalVerifyOpen = async () => {
  const isAllSelect = isSelected.value.every(item => item == 1);
  if (!isAllSelect) {
    submitVerifyRef.value.openDialog(routeParams.value.taskId);
  } else {
    proxy?.$modal.msgError('该项目清单已经提交审批了，无法再次提交');
  }
};
//校验提交按钮是否显示
const submitButtonShow = computed(() => {
  return (
    routeParams.value.type === 'add' ||
    (routeParams.value.type === 'update' &&
      form.value.status &&
      (form.value.status === 'draft' || form.value.status === 'cancel' || form.value.status === 'back'))
  );
});

//校验审批按钮是否显示
const approvalButtonShow = computed(() => {
  return routeParams.value.type === 'approval' && form.value.status && form.value.status === 'waiting';
});
const handleFileView = async (fileOssId: string, url: string) => {
  let urls = ''
  if (fileOssId) {
    const { data } = await listByIds(fileOssId);
    urls = data[0]?.url;
  } else {
    urls = url;
  }
  const botaUrl = btoa(urls)
  const url1 = `${import.meta.env.VITE_APP_VIEW_URL}/onlinePreview?url=${botaUrl}`
  // 证书预览
  window.open(url1, '_blank')
}
onMounted(() => {
  nextTick(async () => {
    routeParams.value = proxy.$route.query;
    reset();
    await getTaskVariablesData();
    loading.value = false;
    isPreview.value = routeParams.value.type == 'approval' ? 'view' : routeParams.value.type;
    form.value.id = routeParams.value.id;
    getAiDetail();
    getManualDetailData()
  });
});
</script>
<style lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>

package org.dromara.flow.domain.vo;

import lombok.Data;
import org.dromara.flow.domain.PrjHazardousItemsFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/21 14:41
 * @Description TODO
 * @Version 1.0
 */
@Data
public class SpecialistThreeVO {

    /**
     * 厅局意见
     */
    private String remark;

    /**
     * 所选人员
     */
    private List<SpecialistUserVO> checkUsers;

    /**
     * 附件列表
     */
    private List<PrjHazardousItemsFile> files;

    /**
     * 关联质监站工单业务id（flow_instance.business_id）
     */
    private String qualityTaskId;

    /**
     * 子流程id
     */
    private String childrenTaskId;
}

{"doc": " [项目管理] 列出项目内具体的危险性较大的分部分项工程Service业务层处理\n\n <AUTHOR>\n @date 2025-05-07\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询[项目管理] 列出项目内具体的危险性较大的分部分项工程\n\n @param itemId 主键\n @return [项目管理] 列出项目内具体的危险性较大的分部分项工程\n"}, {"name": "queryDetailById", "paramTypes": ["java.lang.Long"], "doc": " 查询分部分项详情信息\n\n @param itemId\n @return\n"}, {"name": "saveAiTaskAndResult", "paramTypes": ["org.dromara.ai.domain.dto.AiHazAnalysisResultResetDTO"], "doc": " 审核ai隐患任务\n\n @param dto 包含任务信息的数据传输对象\n @return 保存成功返回true，否则返回false\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return [项目管理] 列出项目内具体的危险性较大的分部分项工程分页列表\n"}, {"name": "setParentDangerName", "paramTypes": ["org.dromara.projects.domain.vo.PrjHazardousItemsVo", "java.util.Map"], "doc": " 赋值父类涉危工程名称\n\n @param itemsVo\n @param dangerListMap\n"}, {"name": "queryList", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsBo"], "doc": " 查询符合条件的[项目管理] 列出项目内具体的危险性较大的分部分项工程列表\n\n @param bo 查询条件\n @return [项目管理] 列出项目内具体的危险性较大的分部分项工程列表\n"}, {"name": "getLongDangerListMap", "paramTypes": [], "doc": " 获取所有危大清单列表集合\n\n @return\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsBo"], "doc": " 新增[项目管理] 列出项目内具体的危险性较大的分部分项工程\n\n @param bo [项目管理] 列出项目内具体的危险性较大的分部分项工程\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsBo"], "doc": " 修改[项目管理] 列出项目内具体的危险性较大的分部分项工程\n\n @param bo [项目管理] 列出项目内具体的危险性较大的分部分项工程\n @return 是否修改成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.projects.domain.PrjHazardousItems"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除[项目管理] 列出项目内具体的危险性较大的分部分项工程信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
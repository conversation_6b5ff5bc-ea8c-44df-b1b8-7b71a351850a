package org.dromara.special.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.special.domain.bo.SpecialOperationPersonnelBoToSpecialOperationPersonnelMapper;
import org.dromara.special.domain.vo.SpecialOperationPersonnelVo;
import org.dromara.special.domain.vo.SpecialOperationPersonnelVoToSpecialOperationPersonnelMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {SpecialOperationPersonnelVoToSpecialOperationPersonnelMapper.class,SpecialOperationPersonnelBoToSpecialOperationPersonnelMapper.class},
    imports = {}
)
public interface SpecialOperationPersonnelToSpecialOperationPersonnelVoMapper extends BaseMapper<SpecialOperationPersonnel, SpecialOperationPersonnelVo> {
}

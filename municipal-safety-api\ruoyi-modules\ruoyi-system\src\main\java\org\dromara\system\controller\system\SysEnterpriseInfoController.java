package org.dromara.system.controller.system;

import java.util.List;
import java.util.Objects;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.PageUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.security.PermitAll;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.constant.GlobalConstants;
import org.dromara.common.core.exception.user.CaptchaException;
import org.dromara.common.core.exception.user.CaptchaExpireException;
import org.dromara.common.core.utils.MessageUtils;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.event.LogininforEvent;
import org.dromara.common.mybatis.helper.DataPermissionHelper;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.web.core.TableDataInfo;
import org.dromara.common.web.utils.PageUtils;
import org.dromara.expert.domain.bo.ExpertBo;
import org.dromara.system.domain.SysEnterpriseInfo;
import org.dromara.system.domain.bo.DivisionBo;
import org.dromara.system.domain.vo.DivisionVo;
import org.dromara.system.domain.vo.EnterpriseNameAndId;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.service.ISysUserService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.system.service.ISysEnterpriseInfoService;

import static org.dromara.common.satoken.utils.LoginHelper.getUserId;

/**
 * 企业信息
 *
 * <AUTHOR> Li
 * @date 2025-05-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/enterpriseInfo")
public class SysEnterpriseInfoController extends BaseController {

    private final ISysEnterpriseInfoService sysEnterpriseInfoService;

    private final ISysUserService userService;

    /**
     * 查询企业信息列表
     */
    @SaCheckPermission("system:enterpriseInfo:list")
    @GetMapping("/list")
    public TableDataInfo list(SysEnterpriseInfo bo) {
        startPage();
        List<SysEnterpriseInfo> list = sysEnterpriseInfoService.pageList(bo);
        return getDataTable(list);
    }

    /**
     * 导出企业信息列表
     */
    @Log(title = "企业信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysEnterpriseInfo bo, HttpServletResponse response) {
        List<SysEnterpriseInfo> list = sysEnterpriseInfoService.pageList(bo);
        ExcelUtil.exportExcel(list, "企业信息", SysEnterpriseInfo.class, response);
    }

    /**
     * 获取企业信息详细信息
     *
     * @param enterpriseId 主键
     */
    @GetMapping("/{enterpriseId}")
    public R<SysEnterpriseInfo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long enterpriseId) {
        SysEnterpriseInfo info = sysEnterpriseInfoService.getOneInfo(enterpriseId);
        if (!Objects.isNull(info.getEnterpriseUserId())) {
            info.setEnterpriseName(userService.selectUserById(info.getEnterpriseUserId()).getNickName());
        }
        return R.ok(info);
    }

    /**
     * 新增企业信息
     */
    @Log(title = "企业信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@RequestBody SysEnterpriseInfo bo) {
        return sysEnterpriseInfoService.ins(bo);
    }

    /**
     * 新增企业信息  (web 注册)
     */
    @Log(title = "企业信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @SaIgnore
    @PostMapping("/webAdd")
    public R<Void> webAdd(@RequestBody SysEnterpriseInfo bo) {
        // 校验验证码
        validateCaptcha(bo.getEnterpriseName(), bo.getCode(), bo.getUuid());
        return sysEnterpriseInfoService.ins(bo);
    }


    /**
     * 修改企业信息
     */
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@RequestBody SysEnterpriseInfo bo) {
        bo.setUpdateBy(getUserId());
        bo.setUpdateTime(DateUtil.date());
        return toAjax(sysEnterpriseInfoService.updateById(bo));
    }

    /**
     * 删除企业信息
     *
     * @param enterpriseIds 主键串
     */
    @Log(title = "企业信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{enterpriseIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] enterpriseIds) {
        for (@NotEmpty(message = "主键不能为空") Long enterpriseId : enterpriseIds) {
            if (sysEnterpriseInfoService.getOneInfo(enterpriseId).getEnterpriseUserId() != null) {
                return R.fail("企业信息已被使用，无法删除");
            }
        }
        return toAjax(sysEnterpriseInfoService.removeBatchByIds(List.of(enterpriseIds)));
    }

    public void validateCaptcha(String username, String code, String uuid) {
        String verifyKey = GlobalConstants.CAPTCHA_CODE_KEY + StringUtils.blankToDefault(uuid, "");
        String captcha = RedisUtils.getCacheObject(verifyKey);
        RedisUtils.deleteObject(verifyKey);
        if (captcha == null) {
            recordLogininfor(null, username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"));
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha)) {
            recordLogininfor(null, username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error"));
            throw new CaptchaException();
        }
    }

    private void recordLogininfor(String tenantId, String username, String status, String message) {
        LogininforEvent logininforEvent = new LogininforEvent();
        logininforEvent.setTenantId(tenantId);
        logininforEvent.setUsername(username);
        logininforEvent.setStatus(status);
        logininforEvent.setMessage(message);
        logininforEvent.setRequest(ServletUtils.getRequest());
        SpringUtils.context().publishEvent(logininforEvent);
    }

    /**
     * 企业注册审核
     */
    @PostMapping("/audit")
    @Log(title = "企业注册审核", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    public R<Void> audit(@RequestBody SysEnterpriseInfo bo) {
        if (StringUtils.isEmpty(bo.getEnterpriseStatus())) {
            return R.fail("请选择审核状态");
        }
        return sysEnterpriseInfoService.audit(bo);
    }

    /**
     * 查询所有企业的名称和id
     * @return
     */
    @SaCheckPermission("system:enterpriseInfo:searchData")
    @GetMapping("/allForSearch")
    public R<List<EnterpriseNameAndId>> getSearchData() {
        return sysEnterpriseInfoService.getSearchData();
    }


    /**
     * 重置密码
     */
    @GetMapping("/resetPwd")
    public R<Void> resetPwd(ExpertBo bo) {
        SysUserVo user = userService.selectUserByUserName(bo.getIdCard());
        if (Objects.isNull(user)) {
            return R.fail("账号不存在");
        }
        int rows = DataPermissionHelper.ignore(() -> userService.resetUserPwd(user.getUserId(), BCrypt.hashpw("123456")));
        if (rows > 0) {
            return R.ok("密码已重置，初始密码为123456");
        }
        return R.fail("密码重置，请联系管理员");
    }
}

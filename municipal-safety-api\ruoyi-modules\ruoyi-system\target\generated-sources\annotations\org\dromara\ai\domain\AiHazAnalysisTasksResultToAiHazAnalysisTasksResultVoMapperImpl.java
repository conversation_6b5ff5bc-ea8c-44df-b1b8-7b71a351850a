package org.dromara.ai.domain;

import javax.annotation.processing.Generated;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksResultVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class AiHazAnalysisTasksResultToAiHazAnalysisTasksResultVoMapperImpl implements AiHazAnalysisTasksResultToAiHazAnalysisTasksResultVoMapper {

    @Override
    public AiHazAnalysisTasksResultVo convert(AiHazAnalysisTasksResult arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AiHazAnalysisTasksResultVo aiHazAnalysisTasksResultVo = new AiHazAnalysisTasksResultVo();

        aiHazAnalysisTasksResultVo.setResultId( arg0.getResultId() );
        aiHazAnalysisTasksResultVo.setTaskId( arg0.getTaskId() );
        aiHazAnalysisTasksResultVo.setViolation( arg0.getViolation() );
        aiHazAnalysisTasksResultVo.setRegulation( arg0.getRegulation() );
        aiHazAnalysisTasksResultVo.setCoordinate( arg0.getCoordinate() );
        aiHazAnalysisTasksResultVo.setLevel( arg0.getLevel() );
        aiHazAnalysisTasksResultVo.setMeasure( arg0.getMeasure() );

        return aiHazAnalysisTasksResultVo;
    }

    @Override
    public AiHazAnalysisTasksResultVo convert(AiHazAnalysisTasksResult arg0, AiHazAnalysisTasksResultVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setResultId( arg0.getResultId() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setViolation( arg0.getViolation() );
        arg1.setRegulation( arg0.getRegulation() );
        arg1.setCoordinate( arg0.getCoordinate() );
        arg1.setLevel( arg0.getLevel() );
        arg1.setMeasure( arg0.getMeasure() );

        return arg1;
    }
}

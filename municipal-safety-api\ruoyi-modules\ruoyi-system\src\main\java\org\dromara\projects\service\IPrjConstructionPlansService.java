package org.dromara.projects.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.projects.domain.bo.AnalyseResultBo;
import org.dromara.projects.domain.bo.PrjConstructionPlansBo;
import org.dromara.projects.domain.vo.PrjConstructionPlansVo;

import java.util.Collection;
import java.util.List;

/**
 * [项目管理] 存储危大工程专项施工方案信息及其审批状态Service接口
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface IPrjConstructionPlansService {

    /**
     * 查询[项目管理] 存储危大工程专项施工方案信息及其审批状态
     *
     * @param planId 主键
     * @return [项目管理] 存储危大工程专项施工方案信息及其审批状态
     */
    PrjConstructionPlansVo queryById(Long planId);

    /**
     * 分页查询[项目管理] 存储危大工程专项施工方案信息及其审批状态列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return [项目管理] 存储危大工程专项施工方案信息及其审批状态分页列表
     */
    TableDataInfo<PrjConstructionPlansVo> queryPageList(PrjConstructionPlansBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的[项目管理] 存储危大工程专项施工方案信息及其审批状态列表
     *
     * @param bo 查询条件
     * @return [项目管理] 存储危大工程专项施工方案信息及其审批状态列表
     */
    List<PrjConstructionPlansVo> queryList(PrjConstructionPlansBo bo);

    /**
     * 新增[项目管理] 存储危大工程专项施工方案信息及其审批状态
     *
     * @param bo [项目管理] 存储危大工程专项施工方案信息及其审批状态
     * @return 是否新增成功
     */
    Boolean insertByBo(PrjConstructionPlansBo bo);

    /**
     * 修改[项目管理] 存储危大工程专项施工方案信息及其审批状态
     *
     * @param bo [项目管理] 存储危大工程专项施工方案信息及其审批状态
     * @return 是否修改成功
     */
    Boolean updateByBo(PrjConstructionPlansBo bo);

    /**
     * 校验并批量删除[项目管理] 存储危大工程专项施工方案信息及其审批状态信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 接收文档分析回调接口
     * @param resultBo
     */
    void analyse(AnalyseResultBo resultBo);

    Boolean pushAiAnalyse(Long plansId);
}

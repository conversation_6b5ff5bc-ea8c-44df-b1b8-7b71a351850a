package org.dromara.system.domain.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import org.dromara.system.domain.DangerList;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * dangerList视图对象 t_danger_list
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DangerList.class)
public class DangerListVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "danger_id")
    private Long dangerId;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 父级id
     */
    @ExcelProperty(value = "父级id")
    private Long preId;

    /**
     * 危大类型
     */
    @ExcelProperty(value = "危大类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "danger_list_type")
    private Integer type;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}

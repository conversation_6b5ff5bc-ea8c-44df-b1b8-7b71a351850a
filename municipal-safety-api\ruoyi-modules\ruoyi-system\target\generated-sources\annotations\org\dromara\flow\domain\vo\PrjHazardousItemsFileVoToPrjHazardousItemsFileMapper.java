package org.dromara.flow.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.PrjHazardousItemsFile;
import org.dromara.flow.domain.PrjHazardousItemsFileToPrjHazardousItemsFileVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjHazardousItemsFileToPrjHazardousItemsFileVoMapper.class},
    imports = {}
)
public interface PrjHazardousItemsFileVoToPrjHazardousItemsFileMapper extends BaseMapper<PrjHazardousItemsFileVo, PrjHazardousItemsFile> {
}

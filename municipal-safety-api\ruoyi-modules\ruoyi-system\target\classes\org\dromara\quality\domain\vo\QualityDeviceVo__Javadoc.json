{"doc": " 设备管理视图对象 quality_device\n\n <AUTHOR>\n @date 2024-01-01\n", "fields": [{"name": "deviceId", "doc": " 设备ID\n"}, {"name": "deviceName", "doc": " 设备名称\n"}, {"name": "specification", "doc": " 规格型号\n"}, {"name": "deviceType", "doc": " 设备类型\n"}, {"name": "deviceCode", "doc": " 设备编号\n"}, {"name": "quantity", "doc": " 数量\n"}, {"name": "deviceImageOssId", "doc": " 设备图片OSS ID\n"}, {"name": "deviceImageUrl", "doc": " 设备图片URL\n"}, {"name": "deviceDescription", "doc": " 设备简介\n"}, {"name": "devicePurpose", "doc": " 设备用途\n"}, {"name": "usageInstructions", "doc": " 使用说明\n"}, {"name": "manualFileOssId", "doc": " 使用说明书OSS ID\n"}, {"name": "manualFileUrl", "doc": " 使用说明书URL\n"}, {"name": "manualFileName", "doc": " 使用说明书文件名\n"}, {"name": "status", "doc": " 设备状态\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "createBy", "doc": " 创建人\n"}, {"name": "createByName", "doc": " 创建人账号\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "updateBy", "doc": " 更新人\n"}, {"name": "updateByName", "doc": " 更新人账号\n"}], "enumConstants": [], "methods": [], "constructors": []}
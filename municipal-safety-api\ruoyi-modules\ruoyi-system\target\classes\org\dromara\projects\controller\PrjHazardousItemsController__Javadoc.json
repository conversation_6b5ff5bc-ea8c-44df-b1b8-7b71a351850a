{"doc": " [项目管理] 列出项目内具体的危险性较大的分部分项工程\n\n <AUTHOR>\n @date 2025-05-07\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表\n"}, {"name": "adminList", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表\n"}, {"name": "appList", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsAppBo"], "doc": " todo APP查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表\n"}, {"name": "export", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出[项目管理] 列出项目内具体的危险性较大的分部分项工程列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取[项目管理] 列出项目内具体的危险性较大的分部分项工程详细信息\n\n @param itemId 主键\n"}, {"name": "getInfoDetail", "paramTypes": ["java.lang.Long"], "doc": " 获取[项目管理] 列出项目内具体的危险性较大的分部分项工程详细信息\n\n @param itemId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsBo"], "doc": " 新增[项目管理] 列出项目内具体的危险性较大的分部分项工程\n"}, {"name": "edit", "paramTypes": ["org.dromara.projects.domain.bo.PrjHazardousItemsBo"], "doc": " 修改[项目管理] 列出项目内具体的危险性较大的分部分项工程\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除[项目管理] 列出项目内具体的危险性较大的分部分项工程\n\n @param itemIds 主键串\n"}, {"name": "aiDetailVOR", "paramTypes": ["java.lang.Long"], "doc": " ai隐患获取详情信息\n\n @param id\n @return\n"}, {"name": "saveAiTaskAndResult", "paramTypes": ["org.dromara.ai.domain.dto.AiHazAnalysisResultResetDTO"], "doc": " 隐患清单审核\n\n @param dto\n @return\n"}], "constructors": []}
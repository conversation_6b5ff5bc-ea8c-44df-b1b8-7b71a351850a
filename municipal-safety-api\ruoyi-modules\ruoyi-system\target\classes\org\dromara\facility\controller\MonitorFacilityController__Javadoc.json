{"doc": " 监测设备\n\n <AUTHOR>\n @date 2025-07-24\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.facility.domain.bo.MonitorFacilityBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询监测设备列表\n"}, {"name": "export", "paramTypes": ["org.dromara.facility.domain.bo.MonitorFacilityBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出监测设备列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取监测设备详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.facility.domain.bo.MonitorFacilityBo"], "doc": " 新增监测设备\n"}, {"name": "edit", "paramTypes": ["org.dromara.facility.domain.bo.MonitorFacilityBo"], "doc": " 修改监测设备\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除监测设备\n\n @param ids 主键串\n"}], "constructors": []}
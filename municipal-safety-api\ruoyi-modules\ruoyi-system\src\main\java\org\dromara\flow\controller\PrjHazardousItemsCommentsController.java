package org.dromara.flow.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.flow.domain.PrjHazardousItemsComments;
import org.dromara.flow.domain.vo.PrjHazardousItemsCommentsVo;
import org.dromara.flow.service.IPrjHazardousItemsCommentsService;
import org.springframework.web.bind.annotation.*;

/**
 * 质监站隐患清单整改
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/hazardousItemsComments")
public class PrjHazardousItemsCommentsController extends BaseController {

    private final IPrjHazardousItemsCommentsService prjHazardousItemsCommentsService;

    /**
     * 添加隐患清单
     *
     * @param comments
     * @return
     */
    @PostMapping("/add")
    public R<String> save(@RequestBody PrjHazardousItemsComments comments) {
        return R.ok("", prjHazardousItemsCommentsService.save(comments));
    }

    /**
     * 修改质监站隐患清单
     *
     * @param comments
     * @return
     */
    @PostMapping("/modify")
    public R<Void> update(@RequestBody PrjHazardousItemsComments comments) {
        return toAjax(prjHazardousItemsCommentsService.updateById(comments));
    }

    /**
     * 获取详情
     *
     * @param taskId
     * @return
     */
    @PostMapping("/detail/{taskId}")
    public R<PrjHazardousItemsCommentsVo> getDetail(@PathVariable String taskId) {
        return R.ok(prjHazardousItemsCommentsService.getDetail(taskId));
    }

    /**
     * 施工方上传报告用
     *
     * @param comments
     * @return
     */
    @PostMapping("/upBack")
    public R<Boolean> back(@RequestBody PrjHazardousItemsComments comments) {
        return R.ok(prjHazardousItemsCommentsService.consturctionUpBackFiles(comments));
    }
}

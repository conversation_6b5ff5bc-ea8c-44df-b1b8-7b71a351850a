{"doc": " 认证\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "login", "paramTypes": ["java.lang.String"], "doc": " 登录方法\n\n @param body 登录信息\n @return 结果\n"}, {"name": "appLogin", "paramTypes": ["java.lang.String"], "doc": " 登录方法\n\n @param body 登录信息\n @return 结果\n"}, {"name": "auth<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 获取跳转URL\n\n @param source 登录来源\n @return 结果\n"}, {"name": "socialCallback", "paramTypes": ["org.dromara.common.core.domain.model.SocialLoginBody"], "doc": " 前端回调绑定授权(需要token)\n\n @param loginBody 请求体\n @return 结果\n"}, {"name": "unlockSocial", "paramTypes": ["java.lang.Long"], "doc": " 取消授权(需要token)\n\n @param socialId socialId\n"}, {"name": "logout", "paramTypes": [], "doc": " 退出登录\n"}, {"name": "register", "paramTypes": ["org.dromara.common.core.domain.model.RegisterBody"], "doc": " 用户注册\n"}, {"name": "tenantList", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": " 登录页面租户下拉框\n\n @return 租户列表\n"}], "constructors": []}
package org.dromara.person.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.person.domain.bo.QualificationDictBo;
import org.dromara.person.domain.vo.QualificationDictVo;
import org.dromara.person.domain.QualificationDict;
import org.dromara.person.mapper.QualificationDictMapper;
import org.dromara.person.service.IQualificationDictService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 人员证书属性类型Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-05-10
 */
@RequiredArgsConstructor
@Service
public class QualificationDictServiceImpl implements IQualificationDictService {

    private final QualificationDictMapper baseMapper;

    /**
     * 查询人员证书属性类型
     *
     * @param id 主键
     * @return 人员证书属性类型
     */
    @Override
    public QualificationDictVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }


    /**
     * 查询符合条件的人员证书属性类型列表
     *
     * @param bo 查询条件
     * @return 人员证书属性类型列表
     */
    @Override
    public List<QualificationDictVo> queryList(QualificationDictBo bo) {
        LambdaQueryWrapper<QualificationDict> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QualificationDict> buildQueryWrapper(QualificationDictBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QualificationDict> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(QualificationDict::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), QualificationDict::getName, bo.getName());
        lqw.eq(bo.getPreId() != null, QualificationDict::getPreId, bo.getPreId());
        return lqw;
    }

    /**
     * 新增人员证书属性类型
     *
     * @param bo 人员证书属性类型
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(QualificationDictBo bo) {
        QualificationDict add = MapstructUtils.convert(bo, QualificationDict.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改人员证书属性类型
     *
     * @param bo 人员证书属性类型
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(QualificationDictBo bo) {
        QualificationDict update = MapstructUtils.convert(bo, QualificationDict.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QualificationDict entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除人员证书属性类型信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}

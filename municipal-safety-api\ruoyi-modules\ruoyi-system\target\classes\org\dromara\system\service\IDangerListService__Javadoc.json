{"doc": " dangerListService接口\n\n <AUTHOR>\n @date 2025-04-30\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询dangerList\n\n @param dangerId 主键\n @return dangerList\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.DangerListBo"], "doc": " 查询符合条件的dangerList列表\n\n @param bo 查询条件\n @return dangerList列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.DangerListBo"], "doc": " 新增dangerList\n\n @param bo dangerList\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.DangerListBo"], "doc": " 修改dangerList\n\n @param bo dangerList\n @return 是否修改成功\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除dangerList信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
package org.dromara.attendance.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.attendance.domain.MAttRecord;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 考勤记录业务对象 m_att_record
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MAttRecord.class, reverseConvertGenerate = false)
public class MAttRecordBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 规则id
     */
    private Long ruleId;


    /**
     * 项目人员id
     */
    private Long personId;

    /**
     * 人员岗位/角色
     */
    private String personType;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份Id
     */
    private String idNumber;

    /**
     * 实时人脸
     */
    private String realTimeFace;

    /**
     * 设备号
     */
    private String sn;

    /**
     * 打卡来源
     */
    private Integer source;

    /**
     * 自定义内容
     */
    private String content;

    /**
     * 考勤时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date attTime;

    /**
     * 考勤日期
     */
    private String attDate;

    /**
     * 打卡结果
     */
    private Integer attResult;

    /**
     * 第几次打卡
     */
    private Integer whichTime;
}

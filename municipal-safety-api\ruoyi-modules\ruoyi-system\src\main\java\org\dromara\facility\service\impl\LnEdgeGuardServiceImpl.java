package org.dromara.facility.service.impl;

import com.alibaba.fastjson2.JSON;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.facility.domain.bo.LnEdgeGuardBo;
import org.dromara.facility.domain.vo.LnEdgeGuardVo;
import org.dromara.facility.domain.LnEdgeGuard;
import org.dromara.facility.mapper.LnEdgeGuardMapper;
import org.dromara.facility.service.ILnEdgeGuardService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 绿能临边防护Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@RequiredArgsConstructor
@Service
public class LnEdgeGuardServiceImpl implements ILnEdgeGuardService {

    private final LnEdgeGuardMapper baseMapper;

    /**
     * 查询绿能临边防护
     *
     * @param id 主键
     * @return 绿能临边防护
     */
    @Override
    public LnEdgeGuardVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询绿能临边防护列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能临边防护分页列表
     */
    @Override
    public TableDataInfo<LnEdgeGuardVo> queryPageList(LnEdgeGuardBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LnEdgeGuard> lqw = buildQueryWrapper(bo);
        Page<LnEdgeGuardVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的绿能临边防护列表
     *
     * @param bo 查询条件
     * @return 绿能临边防护列表
     */
    @Override
    public List<LnEdgeGuardVo> queryList(LnEdgeGuardBo bo) {
        LambdaQueryWrapper<LnEdgeGuard> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LnEdgeGuard> buildQueryWrapper(LnEdgeGuardBo bo) {
        LambdaQueryWrapper<LnEdgeGuard> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(LnEdgeGuard::getCreateTime);
        lqw.eq(LnEdgeGuard::getDevNo, bo.getDevNo());
        return lqw;
    }

    /**
     * 新增绿能临边防护
     *
     * @param bo 绿能临边防护
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LnEdgeGuardBo bo) {
        LnEdgeGuard add = MapstructUtils.convert(bo, LnEdgeGuard.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改绿能临边防护
     *
     * @param bo 绿能临边防护
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LnEdgeGuardBo bo) {
        LnEdgeGuard update = MapstructUtils.convert(bo, LnEdgeGuard.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LnEdgeGuard entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除绿能临边防护信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public void insertByJson(String jsonString) {
        LnEdgeGuardBo bo = JSON.parseObject(jsonString, LnEdgeGuardBo.class);
        LnEdgeGuard add = MapstructUtils.convert(bo, LnEdgeGuard.class);
        add.setCreateTime(new Date());
        baseMapper.insert(add);
    }
}

import request from '@/utils/request';
import {AxiosPromise} from 'axios';
import {AttRecordForm, AttRecordQuery, AttRecordVO} from '@/api/';

/**
 * 查询考勤记录列表
 * @param query
 * @returns {*}
 */

export const listAttRecord = (query?: AttRecordQuery): AxiosPromise<AttRecordVO[]> => {
    return request({
        url: '/attendance/attRecord/list',
        method: 'get',
        params: query
    });
};

/**
 * 查询考勤记录详细
 * @param id
 */
export const getAttRecord = (id: string | number): AxiosPromise<AttRecordVO> => {
    return request({
        url: '/attendance/attRecord/' + id,
        method: 'get'
    });
};

/**
 * 新增考勤记录
 * @param data
 */
export const addAttRecord = (data: AttRecordForm) => {
    return request({
        url: '/attendance/attRecord',
        method: 'post',
        data: data
    });
};

/**
 * 修改考勤记录
 * @param data
 */
export const updateAttRecord = (data: AttRecordForm) => {
    return request({
        url: '/attendance/attRecord',
        method: 'put',
        data: data
    });
};

/**
 * 删除考勤记录
 * @param id
 */
export const delAttRecord = (id: string | number | Array<string | number>) => {
    return request({
        url: '/attendance/attRecord/' + id,
        method: 'delete'
    });
};

package org.dromara.projects.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/13 16:27
 * @Description TODO
 * @Version 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ItemsAiDetailVO {

    /**
     * 上传的照片文档ID
     */
    private Long photoDocumentId;

    /**
     * AI分析后返回的带标注的照片文档ID
     */
    private Long aiPhotoDocumentId;

    /**
     * 拍照位置文字描述
     */
    private String locationDescription;

    /**
     *  隐患描述列表
     */
    private List<ViolationVO> violations;
}

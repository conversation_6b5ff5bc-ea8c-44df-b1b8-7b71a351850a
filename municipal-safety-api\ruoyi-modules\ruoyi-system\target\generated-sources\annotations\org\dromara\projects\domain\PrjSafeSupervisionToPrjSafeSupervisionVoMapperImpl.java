package org.dromara.projects.domain;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.vo.PrjSafeSupervisionVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjSafeSupervisionToPrjSafeSupervisionVoMapperImpl implements PrjSafeSupervisionToPrjSafeSupervisionVoMapper {

    @Override
    public PrjSafeSupervisionVo convert(PrjSafeSupervision arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjSafeSupervisionVo prjSafeSupervisionVo = new PrjSafeSupervisionVo();

        prjSafeSupervisionVo.setSupervisionId( arg0.getSupervisionId() );
        prjSafeSupervisionVo.setUserName( arg0.getUserName() );
        prjSafeSupervisionVo.setUserPositionName( arg0.getUserPositionName() );
        prjSafeSupervisionVo.setFace( arg0.getFace() );
        prjSafeSupervisionVo.setCertificate( arg0.getCertificate() );
        prjSafeSupervisionVo.setOpenTaskId( arg0.getOpenTaskId() );

        return prjSafeSupervisionVo;
    }

    @Override
    public PrjSafeSupervisionVo convert(PrjSafeSupervision arg0, PrjSafeSupervisionVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSupervisionId( arg0.getSupervisionId() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setUserPositionName( arg0.getUserPositionName() );
        arg1.setFace( arg0.getFace() );
        arg1.setCertificate( arg0.getCertificate() );
        arg1.setOpenTaskId( arg0.getOpenTaskId() );

        return arg1;
    }
}

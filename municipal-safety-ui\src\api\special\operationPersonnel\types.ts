export interface OperationPersonnelVO {
  /**
   * ID
   */
  sopId: string | number;

  /**
   * 证书编号
   */
  certificateNumber: string;

  /**
   * 姓名
   */
  name: string;

  /**
   * 身份证号
   */
  idCard: string | number;

  /**
   * 性别
   */
  gender: string;

  /**
   * 出生日期
   */
  birthdate: string;

  /**
   * 操作类别
   */
  operationCategory: string;

  /**
   * 发证机关
   */
  issuer: string;

  /**
   * 初次领证日期
   */
  firstIssueDate: string;

  /**
   * 最近发证日期
   */
  lastIssueDate: string;

  /**
   * 有效期开始
   */
  validityStart: string | number;

  /**
   * 有效期截止
   */
  validityEnd: string | number;

  /**
   * 证书状态(有效,无效,挂失,注销)
   */
  status: string;

  /**
   * 电子证照链接
   */
  electronicLicenseUrl: string;

  /**
   * 电子证照文件ID
   */
  electronicLicenseId: string | number;

  /**
   * 电子证照文件IDUrl
   */
  electronicLicenseIdUrl: string;
}

export interface OperationPersonnelForm extends BaseEntity {
  /**
   * ID
   */
  sopId?: string | number;

  /**
   * 证书编号
   */
  certificateNumber?: string;

  /**
   * 姓名
   */
  name?: string;

  /**
   * 身份证号
   */
  idCard?: string | number;

  /**
   * 性别
   */
  gender?: string;

  /**
   * 出生日期
   */
  birthdate?: string;

  /**
   * 操作类别
   */
  operationCategory?: string;

  /**
   * 发证机关
   */
  issuer?: string;

  /**
   * 初次领证日期
   */
  firstIssueDate?: string;

  /**
   * 最近发证日期
   */
  lastIssueDate?: string;

  /**
   * 有效期开始
   */
  validityStart?: string | number;

  /**
   * 有效期截止
   */
  validityEnd?: string | number;

  /**
   * 证书状态(有效,无效,挂失,注销)
   */
  status?: string;

  /**
   * 电子证照链接
   */
  electronicLicenseUrl?: string;

  /**
   * 电子证照文件ID
   */
  electronicLicenseId?: string | number;

}

export interface OperationPersonnelQuery extends PageQuery {

  /**
   * 证书编号
   */
  certificateNumber?: string;

  /**
   * 姓名
   */
  name?: string;

  /**
   * 身份证号
   */
  idCard?: string | number;

  /**
   * 性别
   */
  gender?: string;

  /**
   * 出生日期
   */
  birthdate?: string;

  /**
   * 操作类别
   */
  operationCategory?: string;

  /**
   * 发证机关
   */
  issuer?: string;

  /**
   * 初次领证日期
   */
  firstIssueDate?: string;

  /**
   * 最近发证日期
   */
  lastIssueDate?: string;

  /**
   * 有效期开始
   */
  validityStart?: string | number;

  /**
   * 有效期截止
   */
  validityEnd?: string | number;

  /**
   * 证书状态(有效,无效,挂失,注销)
   */
  status?: string;

  /**
   * 电子证照链接
   */
  electronicLicenseUrl?: string;

  /**
   * 电子证照文件ID
   */
  electronicLicenseId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}




package org.dromara.expert.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 专家项目对象 z_project
 * @date 2025-05-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("z_project")
public class Project extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "project_id")
    private Long projectId;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目地点
     */
    private String location;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 状态
     */
    private String status;

    /**
     * 描述
     */
    private String description;

    /**
     * 0 正常 1 删除
     */
    @TableLogic(value = "0", delval = "1")
    private String delFlag;

    /**
     * 专家id
     */
    private Long expertId;


}

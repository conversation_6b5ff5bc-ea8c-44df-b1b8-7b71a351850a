package org.dromara.ai.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 智能隐患分析任务对象 ai_haz_analysis_tasks
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_haz_analysis_tasks")
public class AiHazAnalysisTasks extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分析任务ID
     */
    @TableId(value = "task_id")
    private Long taskId;

    /**
     * 关联项目ID
     */
    private Long projectId;

    /**
     * 关联的已知危大工程项ID
     */
    private Long itemId;

    /**
     * 预警来源类型 CAMERA[摄像头] APP[用户App上报]
     */
    private String sourceType;

    /**
     * 提交分析的专家用户ID
     */
    private Long expertUserId;

    /**
     * 照片上传时间
     */
    private Date uploadTime;

    /**
     * 上传的照片文档ID
     */
    private Long photoDocumentId;

    /**
     * 拍照时GPS坐标
     */
    private String gpsLocation;

    /**
     * 拍照位置文字描述
     */
    private String locationDescription;

    /**
     * AI分析后返回的带标注的照片文档ID
     */
    private Long aiPhotoDocumentId;

    /**
     * AI模型识别输出结果
     */
    private String aiRecognitionRawResult;


    /**
     * 任务状态
     */
    private String status;

    /**
     * 复检状态（PENDING_RECHECK[待复检]、FINISH_RECHECK[复检完成]）
     */
    private String recheckStatus;

    /**
     * 因此次分析发现问题而生成的工单ID
     */
    private Long relatedWorkOrderId;

    private String remark;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}

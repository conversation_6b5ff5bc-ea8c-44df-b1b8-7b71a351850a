{"doc": " 全局异常处理器\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleHttpRequestMethodNotSupported", "paramTypes": ["org.springframework.web.HttpRequestMethodNotSupportedException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 请求方式不支持\n"}, {"name": "handleServiceException", "paramTypes": ["org.dromara.common.core.exception.ServiceException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 业务异常\n"}, {"name": "handleNotLoginException", "paramTypes": ["org.dromara.common.core.exception.SseException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 认证失败\n"}, {"name": "handleServletException", "paramTypes": ["jakarta.servlet.ServletException", "jakarta.servlet.http.HttpServletRequest"], "doc": " servlet异常\n"}, {"name": "handleBaseException", "paramTypes": ["org.dromara.common.core.exception.base.BaseException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 业务异常\n"}, {"name": "handleMissingPathVariableException", "paramTypes": ["org.springframework.web.bind.MissingPathVariableException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 请求路径中缺少必需的路径变量\n"}, {"name": "handleMethodArgumentTypeMismatchException", "paramTypes": ["org.springframework.web.method.annotation.MethodArgumentTypeMismatchException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 请求参数类型不匹配\n"}, {"name": "handleNoHandlerFoundException", "paramTypes": ["org.springframework.web.servlet.NoHandlerFoundException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 找不到路由\n"}, {"name": "handleRuntimeException", "paramTypes": ["java.io.IOException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 拦截未知的运行时异常\n"}, {"name": "handleRuntimeException", "paramTypes": ["java.lang.RuntimeException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 拦截未知的运行时异常\n"}, {"name": "handleException", "paramTypes": ["java.lang.Exception", "jakarta.servlet.http.HttpServletRequest"], "doc": " 系统异常\n"}, {"name": "handleBindException", "paramTypes": ["org.springframework.validation.BindException"], "doc": " 自定义验证异常\n"}, {"name": "constraintViolationException", "paramTypes": ["jakarta.validation.ConstraintViolationException"], "doc": " 自定义验证异常\n"}, {"name": "handleMethodArgumentNotValidException", "paramTypes": ["org.springframework.web.bind.MethodArgumentNotValidException"], "doc": " 自定义验证异常\n"}], "constructors": []}
package org.dromara.person.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.person.domain.bo.QualificationDictBoToQualificationDictMapper;
import org.dromara.person.domain.vo.QualificationDictVo;
import org.dromara.person.domain.vo.QualificationDictVoToQualificationDictMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {QualificationDictVoToQualificationDictMapper.class,QualificationDictBoToQualificationDictMapper.class},
    imports = {}
)
public interface QualificationDictToQualificationDictVoMapper extends BaseMapper<QualificationDict, QualificationDictVo> {
}

package org.dromara.special.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.special.domain.SpecialOperationPersonnel;
import org.dromara.special.domain.sync.*;
import org.dromara.special.domain.vo.SpecialOperationPersonnelVo;
import org.dromara.special.mapper.SpecialOperationPersonnelMapper;
import org.dromara.special.service.ISpecialOperationPersonnelService;
import org.dromara.system.domain.SysDictData;
import org.dromara.system.domain.SysDictType;
import org.dromara.system.domain.vo.SysDictDataVo;
import org.dromara.system.domain.vo.SysDictTypeVo;
import org.dromara.system.mapper.SysDictDataMapper;
import org.dromara.system.service.ISysDeptService;
import org.dromara.system.service.ISysDictDataService;
import org.dromara.system.service.ISysDictTypeService;
import org.springframework.stereotype.Service;
import org.dromara.special.domain.bo.SpecialEquipmentBo;
import org.dromara.special.domain.vo.SpecialEquipmentVo;
import org.dromara.special.domain.SpecialEquipment;
import org.dromara.special.mapper.SpecialEquipmentMapper;
import org.dromara.special.service.ISpecialEquipmentService;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 特种设备Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-05-14
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SpecialEquipmentServiceImpl implements ISpecialEquipmentService {

    private final SpecialEquipmentMapper baseMapper;
    private final SpecialOperationPersonnelMapper specialOperationPersonnelMapper;
    private final SysDictDataMapper sysDictDataMapper;
    // 身份证正则表达式
    private static final Pattern ID_CARD_PATTERN = Pattern.compile(
        "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$"
    );
    /**
     * 查询特种设备
     *
     * @param equipmentId 主键
     * @return 特种设备
     */
    @Override
    public SpecialEquipmentVo queryById(Long equipmentId){
        return baseMapper.selectVoById(equipmentId);
    }

    /**
     * 分页查询特种设备列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 特种设备分页列表
     */
    @Override
    public TableDataInfo<SpecialEquipmentVo> queryPageList(SpecialEquipmentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SpecialEquipment> lqw = buildQueryWrapper(bo);
        Page<SpecialEquipmentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的特种设备列表
     *
     * @param bo 查询条件
     * @return 特种设备列表
     */
    @Override
    public List<SpecialEquipmentVo> queryList(SpecialEquipmentBo bo) {
        LambdaQueryWrapper<SpecialEquipment> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SpecialEquipment> buildQueryWrapper(SpecialEquipmentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SpecialEquipment> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getCertificateNumber()), SpecialEquipment::getCertificateNumber, bo.getCertificateNumber());
        lqw.like(StringUtils.isNotBlank(bo.getIssuer()), SpecialEquipment::getIssuer, bo.getIssuer());
        lqw.eq(bo.getIssueDate() != null, SpecialEquipment::getIssueDate, bo.getIssueDate());
        lqw.eq(bo.getUseRegistrationCertificate() != null, SpecialEquipment::getUseRegistrationCertificate, bo.getUseRegistrationCertificate());
        lqw.eq(StringUtils.isNotBlank(bo.getEquipmentCategory()), SpecialEquipment::getEquipmentCategory, bo.getEquipmentCategory());
        lqw.eq(StringUtils.isNotBlank(bo.getModelSpec()), SpecialEquipment::getModelSpec, bo.getModelSpec());
        lqw.eq(StringUtils.isNotBlank(bo.getFactoryNumber()), SpecialEquipment::getFactoryNumber, bo.getFactoryNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getRecordNumber()), SpecialEquipment::getRecordNumber, bo.getRecordNumber());
        lqw.like(StringUtils.isNotBlank(bo.getManufacturer()), SpecialEquipment::getManufacturer, bo.getManufacturer());
        lqw.like(StringUtils.isNotBlank(bo.getPropertyOwner()), SpecialEquipment::getPropertyOwner, bo.getPropertyOwner());
        lqw.like(StringUtils.isNotBlank(bo.getProjectName()), SpecialEquipment::getProjectName, bo.getProjectName());
        lqw.like(StringUtils.isNotBlank(bo.getProjectAddress()), SpecialEquipment::getProjectAddress, bo.getProjectAddress());
        lqw.eq(bo.getItemId() != null, SpecialEquipment::getItemId, bo.getItemId());
        lqw.eq(StringUtils.isNotBlank(bo.getUsageUnit()), SpecialEquipment::getUsageUnit, bo.getUsageUnit());
        lqw.eq(StringUtils.isNotBlank(bo.getMaintenanceUnit()), SpecialEquipment::getMaintenanceUnit, bo.getMaintenanceUnit());
        lqw.eq(StringUtils.isNotBlank(bo.getInstallationUnit()), SpecialEquipment::getInstallationUnit, bo.getInstallationUnit());
        lqw.eq(StringUtils.isNotBlank(bo.getInspectionUnit()), SpecialEquipment::getInspectionUnit, bo.getInspectionUnit());
        lqw.like(StringUtils.isNotBlank(bo.getProjectManager()), SpecialEquipment::getProjectManager, bo.getProjectManager());
        lqw.eq(bo.getInstallationDate() != null, SpecialEquipment::getInstallationDate, bo.getInstallationDate());
        lqw.eq(bo.getInspectionDate() != null, SpecialEquipment::getInspectionDate, bo.getInspectionDate());
        lqw.eq(bo.getEnterDate() != null, SpecialEquipment::getEnterDate, bo.getEnterDate());
        lqw.eq(bo.getExitDate() != null, SpecialEquipment::getExitDate, bo.getExitDate());
        lqw.like(StringUtils.isNotBlank(bo.getLocation()), SpecialEquipment::getLocation, bo.getLocation());
        lqw.eq(bo.getSopId() != null, SpecialEquipment::getSopId, bo.getSopId());
        lqw.eq(StringUtils.isNotBlank(bo.getRemarks()), SpecialEquipment::getRemarks, bo.getRemarks());
        lqw.eq(bo.getProjectId() != null, SpecialEquipment::getProjectId, bo.getProjectId());
        lqw.eq(StringUtils.isNotBlank(bo.getAuthority()),SpecialEquipment::getAuthority,bo.getAuthority());
        lqw.orderByDesc(SpecialEquipment::getCreateTime);
        return lqw;
    }

    /**
     * 新增特种设备
     *
     * @param bo 特种设备
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SpecialEquipmentBo bo) {
        SpecialEquipment add = MapstructUtils.convert(bo, SpecialEquipment.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setEquipmentId(add.getEquipmentId());
        }
        return flag;
    }

    /**
     * 修改特种设备
     *
     * @param bo 特种设备
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SpecialEquipmentBo bo) {
        SpecialEquipment update = MapstructUtils.convert(bo, SpecialEquipment.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SpecialEquipment entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除特种设备信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public R<Object> syncProject(SyncEntity syncEntity) {
        SyncDate date = SyncUtil.syncDate(syncEntity.getConstructionPermitNo());
        List<SysDictData> dictOperationList = sysDictDataMapper.selectList(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictType, "person_type"));
        List<SysDictData> dictEquipmentList = sysDictDataMapper.selectList(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictType, "special_equipment_type"));
        List<SysDictData> companyList = sysDictDataMapper.selectList(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictType, "enterprise_type"));
        switch (syncEntity.getType()){
            case "1": {
                // 同步特种人员信息
                ArrayList<SyncSpecialPersonnel> xgry = date.getXGRY();
                ArrayList<SpecialOperationPersonnel> specialPeopleList = new ArrayList<>();
                for (SyncSpecialPersonnel syncSpecialPersonnel : xgry) {
                    String personType = null;
                    // 人员类型判断
                    SysDictData dictData = dictOperationList.stream()
                        .filter(dict -> dict.getDictLabel() != null && dict.getDictLabel().equals(syncSpecialPersonnel.getPERSONTYPE()))
                        .findFirst()
                        .orElse(null);
                    if (Objects.isNull(dictData)) {
                        personType = "新增类型" + syncSpecialPersonnel.getPERSONTYPE();
                    } else {
                        personType = dictData.getDictValue();
                    }
                    if (Arrays.asList("4", "5", "11", "12").contains(personType)) {
                        String certificateNumber = syncSpecialPersonnel.getZSBH() != null ? syncSpecialPersonnel.getZSBH().replace("\t", "").replaceAll("\\s+", "") : null;
                        List<SpecialOperationPersonnel> specialOperationPersonnels = specialOperationPersonnelMapper.selectList(new LambdaQueryWrapper<SpecialOperationPersonnel>()
                            .eq(SpecialOperationPersonnel::getIdCard, syncSpecialPersonnel.getIDENTITYCARD())
                            .eq(SpecialOperationPersonnel::getCertificateNumber, certificateNumber)
                            .eq(SpecialOperationPersonnel::getProjectId, syncEntity.getProjectId()));
                        if (CollectionUtils.isEmpty(specialOperationPersonnels)) {
                            SpecialOperationPersonnel personnel = new SpecialOperationPersonnel();
                            personnel.setSopId(IdWorker.getId());
                            personnel.setProjectId(syncEntity.getProjectId());
                            personnel.setDelFlag("0");
                            personnel.setCertificateNumber(certificateNumber);
                            personnel.setName(syncSpecialPersonnel.getNAME());
                            personnel.setIdCard(syncSpecialPersonnel.getIDENTITYCARD());
                            if (!isValidIdCard(syncSpecialPersonnel.getIDENTITYCARD())) {
                                throw new IllegalArgumentException("身份证号格式不正确");
                            }
                            // 18位身份证倒数第二位数字，奇数为男，偶数为女
                            int genderCode = Integer.parseInt(syncSpecialPersonnel.getIDENTITYCARD().substring(16, 17));
                            String gender = genderCode % 2 == 0 ? "1" : "0";
                            personnel.setGender(gender);
                            String birthDateStr = syncSpecialPersonnel.getIDENTITYCARD().substring(6, 14);
                            Date birthDate = DateUtil.parse(birthDateStr, "yyyyMMdd");
                            personnel.setBirthdate(birthDate);
                            personnel.setCompanyName(syncSpecialPersonnel.getCORPNAME());
                            personnel.setCompanyCode(syncSpecialPersonnel.getCORPCODE());
                            //  单位类型判断
                            SysDictData companyType = companyList.stream()
                                .filter(dict -> dict.getDictLabel() != null && dict.getDictLabel().equals(syncSpecialPersonnel.getLBFL()))
                                .findFirst()
                                .orElse(null);
                            if (Objects.isNull(companyType)) {
                                personnel.setCompanyType("新增类型" + syncSpecialPersonnel.getLBFL());
                            } else {
                                personnel.setCompanyType(companyType.getDictValue());
                            }
                            personnel.setPersonType(personType);
                            personnel.setOperationCategory(dictData.getRemark());
                            specialPeopleList.add(personnel);
                        } else {
                            ObjectMapper objectMapper = new ObjectMapper();
                            String oldString = null;
                            String newString = null;
                            SpecialOperationPersonnel specialOperationPersonnelOne = specialOperationPersonnels.get(0);
                            try {
                                specialOperationPersonnelOne.setBirthdate(null);
                                oldString = objectMapper.writer().writeValueAsString(specialOperationPersonnelOne);
                            }catch (Exception e){
                                log.error("数据转换错误:{}",e.getMessage());
                            }
                            specialOperationPersonnelOne.setProjectId(syncEntity.getProjectId());
                            specialOperationPersonnelOne.setDelFlag("0");
                            specialOperationPersonnelOne.setCertificateNumber(certificateNumber);
                            specialOperationPersonnelOne.setName(syncSpecialPersonnel.getNAME());
                            specialOperationPersonnelOne.setIdCard(syncSpecialPersonnel.getIDENTITYCARD());
                            if (!isValidIdCard(syncSpecialPersonnel.getIDENTITYCARD())) {
                                throw new IllegalArgumentException("身份证号格式不正确");
                            }
                            // 18位身份证倒数第二位数字，奇数为男，偶数为女
                            int genderCode = Integer.parseInt(syncSpecialPersonnel.getIDENTITYCARD().substring(16, 17));
                            String gender = genderCode % 2 == 0 ? "1" : "0";
                            specialOperationPersonnelOne.setGender(gender);
                            String birthDateStr = syncSpecialPersonnel.getIDENTITYCARD().substring(6, 14);
                            Date birthDate = DateUtil.parse(birthDateStr, "yyyyMMdd");
                            specialOperationPersonnelOne.setBirthdate(birthDate);
                            specialOperationPersonnelOne.setCompanyName(syncSpecialPersonnel.getCORPNAME());
                            specialOperationPersonnelOne.setCompanyCode(syncSpecialPersonnel.getCORPCODE());
                            //  单位类型判断
                            SysDictData companyType = companyList.stream()
                                .filter(dict -> dict.getDictLabel() != null && dict.getDictLabel().equals(syncSpecialPersonnel.getLBFL()))
                                .findFirst()
                                .orElse(null);
                            if (Objects.isNull(companyType)) {
                                specialOperationPersonnelOne.setCompanyType("新增类型" + syncSpecialPersonnel.getLBFL());
                            } else {
                                specialOperationPersonnelOne.setCompanyType(companyType.getDictValue());
                            }
                            specialOperationPersonnelOne.setPersonType(personType);
                            specialOperationPersonnelOne.setOperationCategory(dictData.getRemark());
                            try {
                                specialOperationPersonnelOne.setBirthdate(null);
                                newString = objectMapper.writer().writeValueAsString(specialOperationPersonnelOne);
                            }catch (Exception e){
                                log.error("数据转换错误:{}",e.getMessage());
                            }
                            if (!oldString.equals(newString)){
                                specialPeopleList.add(specialOperationPersonnelOne);
                            }
                        }
                    }

                }
                List<SpecialOperationPersonnel> distinctSpecialPeopleList = specialPeopleList.stream()
                    .collect(Collectors.toMap(
                        SpecialOperationPersonnel::getCertificateNumber,
                        personnel -> personnel,
                        (existing, replacement) -> existing
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
                specialOperationPersonnelMapper.insertOrUpdateBatch(distinctSpecialPeopleList);
                return R.ok();
            }
            case "2": {
                //同步特种设备信息
                log.info("同步特种设备信息:{}", date.getQZJX());
                ArrayList<SyncSpecialEquipment> equipmentList = date.getQZJX();
                ArrayList<SpecialEquipment> specialEquipmentLists = new ArrayList<>();
                for (SyncSpecialEquipment syncSpecialEquipment : equipmentList) {
                    //查询信息  存在跟新  不存在添加
                    List<SpecialEquipment> specialEquipments = baseMapper.selectList(new LambdaQueryWrapper<SpecialEquipment>()
                        .eq(SpecialEquipment::getFactoryNumber, syncSpecialEquipment.getCCBH())
                        .eq(SpecialEquipment::getProjectId, syncEntity.getProjectId()));
                    if (CollectionUtils.isEmpty(specialEquipments)){
                        SpecialEquipment specialEquipment = new SpecialEquipment();
                        specialEquipment.setDelFlag("0");
                        specialEquipment.setProjectId(syncEntity.getProjectId());
                        specialEquipment.setConstructionPermitNum(syncSpecialEquipment.getCONSTRUCTIONPERMITNUM());
                        specialEquipment.setRecordNumber(syncSpecialEquipment.getSBBABH());
                        specialEquipment.setEquipmentName(syncSpecialEquipment.getSBMC());
                        specialEquipment.setModelSpec(syncSpecialEquipment.getGGXH());
                        specialEquipment.setManufacturer(syncSpecialEquipment.getSCCS());
                        specialEquipment.setManufacturerCode(syncSpecialEquipment.getSCCSTYSHXYDM());
                        specialEquipment.setFactoryNumber(syncSpecialEquipment.getCCBH());
                        specialEquipment.setFactoryDate(syncSpecialEquipment.getCCRQ());
                        specialEquipment.setProductionLicense(syncSpecialEquipment.getZZXKZH());
                        specialEquipment.setUseYears(syncSpecialEquipment.getSYNX());
                        specialEquipment.setPropertyOwner(syncSpecialEquipment.getCQDW());
                        specialEquipment.setPropertyOwnerCode(syncSpecialEquipment.getCQDWTYSHXYDM());
                        specialEquipment.setPropertyOwnerAddress(syncSpecialEquipment.getCQDWDZ());
                        specialEquipment.setLegalPerson(syncSpecialEquipment.getQYFRDB());
                        specialEquipment.setLegalPersonLicense(syncSpecialEquipment.getFRSFZH());
                        specialEquipment.setContacts(syncSpecialEquipment.getLXR());
                        specialEquipment.setContactsPhone(syncSpecialEquipment.getLXDH());
                        specialEquipment.setPrice(syncSpecialEquipment.getCCJG());
                        specialEquipment.setPurchaseDate(syncSpecialEquipment.getGZNY());
                        specialEquipment.setAuthority(syncSpecialEquipment.getSBBAJG());
                        specialEquipment.setAuthorityCode(syncSpecialEquipment.getFZJGTYSHDM());
                        specialEquipment.setLocationCity(syncSpecialEquipment.getJXSZDS());
                        specialEquipment.setLocationCounty(syncSpecialEquipment.getJXSZQX());
                        specialEquipment.setLocationArea(syncSpecialEquipment.getJXQY());
                        // 设备类型  字典值 需要匹配
                        SysDictData equipment = dictEquipmentList.stream()
                            .filter(dict -> dict.getDictLabel() != null && dict.getDictLabel().equals(syncSpecialEquipment.getSBLX()))
                            .findFirst()
                            .orElse(null);
                        if (Objects.isNull(equipment)){
                            specialEquipment.setEquipmentCategory("新类型未匹配"+syncSpecialEquipment.getSBLX());
                        }else {
                            specialEquipment.setEquipmentCategory(equipment.getDictValue());
                        }
                        specialEquipment.setTowerCraneWeight(syncSpecialEquipment.getEDQZL());
                        specialEquipment.setWeightTorque(syncSpecialEquipment.getEDQZLJ());
                        specialEquipment.setWeightLength(syncSpecialEquipment.getTSQZJQZCB());
                        specialEquipment.setWorkRange(syncSpecialEquipment.getZDGZFD());
                        specialEquipment.setWorkRangeWeight(syncSpecialEquipment.getTSQZJZDFDQZL());
                        specialEquipment.setImproveHeight(syncSpecialEquipment.getZDDLQSGD());
                        specialEquipment.setLiftingHeight(syncSpecialEquipment.getZDQSGD());
                        specialEquipment.setStructureNumber(syncSpecialEquipment.getZYJGJWYBH());
                        specialEquipment.setMaxHeight(syncSpecialEquipment.getTSQZJNAZZDGD());
                        specialEquipment.setStandardSectionSpecifications(syncSpecialEquipment.getZYJGJGG());
                        specialEquipment.setStrengthenSection(syncSpecialEquipment.getTAQZJJQJCS());
                        specialEquipment.setStandardSection(syncSpecialEquipment.getTSQZJBZJCS());
                        specialEquipment.setImproveSpeed(syncSpecialEquipment.getEDTSSD());
                        specialEquipment.setSafetyModel(syncSpecialEquipment.getFZAQQXH());
                        specialEquipment.setConstructionElevatorSize(syncSpecialEquipment.getJKCC());
                        specialEquipmentLists.add(specialEquipment);
                    }else {
                        ObjectMapper objectMapper = new ObjectMapper();
                        String oldString = null;
                        String newString = null;
                        SpecialEquipment specialEquipmentOne = specialEquipments.get(0);
                        try {
                            oldString = objectMapper.writer().writeValueAsString(specialEquipmentOne);
                        }catch (Exception e){
                            log.error("数据转换错误:{}",e.getMessage());
                        }
                        specialEquipmentOne.setDelFlag("0");
                        specialEquipmentOne.setProjectId(syncEntity.getProjectId());
                        specialEquipmentOne.setConstructionPermitNum(syncSpecialEquipment.getCONSTRUCTIONPERMITNUM());
                        specialEquipmentOne.setRecordNumber(syncSpecialEquipment.getSBBABH());
                        specialEquipmentOne.setEquipmentName(syncSpecialEquipment.getSBMC());
                        specialEquipmentOne.setModelSpec(syncSpecialEquipment.getGGXH());
                        specialEquipmentOne.setManufacturer(syncSpecialEquipment.getSCCS());
                        specialEquipmentOne.setManufacturerCode(syncSpecialEquipment.getSCCSTYSHXYDM());
                        specialEquipmentOne.setFactoryNumber(syncSpecialEquipment.getCCBH());
                        specialEquipmentOne.setFactoryDate(syncSpecialEquipment.getCCRQ());
                        specialEquipmentOne.setProductionLicense(syncSpecialEquipment.getZZXKZH());
                        specialEquipmentOne.setUseYears(syncSpecialEquipment.getSYNX());
                        specialEquipmentOne.setPropertyOwner(syncSpecialEquipment.getCQDW());
                        specialEquipmentOne.setPropertyOwnerCode(syncSpecialEquipment.getCQDWTYSHXYDM());
                        specialEquipmentOne.setPropertyOwnerAddress(syncSpecialEquipment.getCQDWDZ());
                        specialEquipmentOne.setLegalPerson(syncSpecialEquipment.getQYFRDB());
                        specialEquipmentOne.setLegalPersonLicense(syncSpecialEquipment.getFRSFZH());
                        specialEquipmentOne.setContacts(syncSpecialEquipment.getLXR());
                        specialEquipmentOne.setContactsPhone(syncSpecialEquipment.getLXDH());
                        specialEquipmentOne.setPrice(syncSpecialEquipment.getCCJG());
                        specialEquipmentOne.setPurchaseDate(syncSpecialEquipment.getGZNY());
                        specialEquipmentOne.setAuthority(syncSpecialEquipment.getSBBAJG());
                        specialEquipmentOne.setAuthorityCode(syncSpecialEquipment.getFZJGTYSHDM());
                        specialEquipmentOne.setLocationCity(syncSpecialEquipment.getJXSZDS());
                        specialEquipmentOne.setLocationCounty(syncSpecialEquipment.getJXSZQX());
                        specialEquipmentOne.setLocationArea(syncSpecialEquipment.getJXQY());
                        // 设备类型  字典值 需要匹配
                        SysDictData equipment = dictEquipmentList.stream()
                            .filter(dict -> dict.getDictLabel() != null && dict.getDictLabel().equals(syncSpecialEquipment.getSBLX()))
                            .findFirst()
                            .orElse(null);
                        if (Objects.isNull(equipment)){
                            specialEquipmentOne.setEquipmentCategory("新类型未匹配"+syncSpecialEquipment.getSBLX());
                        }else {
                            specialEquipmentOne.setEquipmentCategory(equipment.getDictValue());
                        }
                        specialEquipmentOne.setTowerCraneWeight(syncSpecialEquipment.getEDQZL());
                        specialEquipmentOne.setWeightTorque(syncSpecialEquipment.getEDQZLJ());
                        specialEquipmentOne.setWeightLength(syncSpecialEquipment.getTSQZJQZCB());
                        specialEquipmentOne.setWorkRange(syncSpecialEquipment.getZDGZFD());
                        specialEquipmentOne.setWorkRangeWeight(syncSpecialEquipment.getTSQZJZDFDQZL());
                        specialEquipmentOne.setImproveHeight(syncSpecialEquipment.getZDDLQSGD());
                        specialEquipmentOne.setLiftingHeight(syncSpecialEquipment.getZDQSGD());
                        specialEquipmentOne.setStructureNumber(syncSpecialEquipment.getZYJGJWYBH());
                        specialEquipmentOne.setMaxHeight(syncSpecialEquipment.getTSQZJNAZZDGD());
                        specialEquipmentOne.setStandardSectionSpecifications(syncSpecialEquipment.getZYJGJGG());
                        specialEquipmentOne.setStrengthenSection(syncSpecialEquipment.getTAQZJJQJCS());
                        specialEquipmentOne.setStandardSection(syncSpecialEquipment.getTSQZJBZJCS());
                        specialEquipmentOne.setImproveSpeed(syncSpecialEquipment.getEDTSSD());
                        specialEquipmentOne.setSafetyModel(syncSpecialEquipment.getFZAQQXH());
                        specialEquipmentOne.setConstructionElevatorSize(syncSpecialEquipment.getJKCC());
                        try {
                            newString = objectMapper.writer().writeValueAsString(specialEquipmentOne);
                        }catch (Exception e){
                            log.error("数据转换错误:{}",e.getMessage());
                        }
                        if (!oldString.equals(newString)){
                            specialEquipmentLists.add(specialEquipmentOne);
                        }
                    }
                }
                baseMapper.insertOrUpdateBatch(specialEquipmentLists);
                return R.ok();
            }
            default: return R.warn("同步失败");
        }
    }

    /**
     * 验证身份证号是否合法
     * @param idCard 身份证号
     * @return 是否合法
     */
    public boolean isValidIdCard(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            return false;
        }
        return ID_CARD_PATTERN.matcher(idCard).matches();
    }

    @Override
    public List<SpecialEquipmentVo> getAuthorityList() {
        return baseMapper.getAuthorityList();
    }
}

{"doc": " 通用流程附件对象 prj_hazardous_items_file\n\n <AUTHOR>\n @date 2025-06-18\n", "fields": [{"name": "itemFileId", "doc": " 附件id\n"}, {"name": "name", "doc": " 文件名称\n"}, {"name": "fileId", "doc": " 取sys_oss.oss_id,文件id\n"}, {"name": "taskId", "doc": " 业务id，取 雪花||UUID\n"}, {"name": "serviceType", "doc": " 服务类型，取字典表flow_service_type\n"}, {"name": "callFileId", "doc": " 取sys_oss.oss_id,反馈文件id（多个逗号隔开）\n"}, {"name": "delFlag", "doc": " 删除标志 (0代表存在 1代表删除)\n"}], "enumConstants": [], "methods": [], "constructors": []}
<template>
  <div class="p-2" style="height: calc(100vh - 155px);">
    <el-card shadow="never">
      <div style="display: flex; justify-content: space-between">
        <div>
          <el-button v-if="submitButtonShow" :loading="buttonLoading" type="info"
            @click="submitForm('draft')">暂存</el-button>
          <el-button v-if="submitButtonShow" :loading="buttonLoading" type="primary" @click="submitForm('submit')">提
            交</el-button>
          <el-button v-if="approvalButtonShow" :loading="buttonLoading" type="primary"
            @click="approvalVerifyOpen">审批</el-button>
          <el-button v-if="form && form.id" type="primary" @click="handleApprovalRecord">流程进度</el-button>
        </div>
        <div>
          <el-button style="float: right" @click="goBack()">返回</el-button>
        </div>
      </div>
    </el-card>
    <!-- 隐患清单 -->
    <el-row :gutter="0" style="width: 100%;height: 100%;padding: 15px;">
      <el-col :span="11" style="height: 100%;padding-right: 15px;">
        <div style="width: 100%;height: calc(100vh - 220px);overflow-y: auto;">
          <!-- <label>补充说明</label>
          <div style="margin-top: 20px;margin-bottom: 20px;">
            <el-input v-model="bcsmText" :rows="3" type="textarea" :disabled="true" class="bcsmInput" />
          </div> -->
          <div>
            <label>附件</label>
            <div style="margin-top: 25px;">
              <div>
                <div style="margin-bottom: 17px;">
                  <template v-for="item in userInfo" :key="item.expertId">
                    <el-tag type="primary" style="margin-right: 10px;">{{
                      item.nickName
                    }}</el-tag>
                  </template>
                </div>
                <el-table :data="formHallBureauData.files">
                  <el-table-column type="index" label="序号" width="55px" align="center" />
                  <el-table-column prop="name" label="名称" align="center" />
                  <el-table-column prop="url" label="文件" align="center">
                    <template #default="scope">
                      <el-button type="primary" size="small" @click="handlePreviewChange(scope.row.url)">预览</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div style="margin-top: 20px;">
                <span style="font-size: 15px;color: #333;">厅/局意见：</span>
                <span style="font-size: 15px;color: #606266;">{{ formHallBureauData.remark }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="13" style="padding-left: 15px;">
        <el-button type="primary" @click="handleManual">下发工单</el-button>
        <div v-if="leftEchoData?.version" style="margin-top: 15px;">
          <el-descriptions title="" :column="2" border>
            <el-descriptions-item label="流程定义名称" align="center">{{ leftEchoData.flowName }}v{{ leftEchoData.version
            }}</el-descriptions-item>
            <el-descriptions-item label="流程定义编码" align="center">{{ leftEchoData.flowCode }}</el-descriptions-item>
            <el-descriptions-item label="流程分类" align="center">{{ leftEchoData.categoryName }}</el-descriptions-item>
            <el-descriptions-item label="版本号" align="center">{{ Number(leftEchoData.version).toFixed(1)
            }}</el-descriptions-item>
            <el-descriptions-item label="任务名称" align="center">{{ leftEchoData.nodeName }}</el-descriptions-item>
            <el-descriptions-item label="督办单位" align="center">{{ leftEchoData.createByName }}</el-descriptions-item>
            <el-descriptions-item label="办理人" align="center">{{ leftEchoData.approveName }}</el-descriptions-item>
            <el-descriptions-item label="流程状态" align="center">
              <dict-tag :options="wf_business_status" :value="leftEchoData.flowStatus"></dict-tag>
            </el-descriptions-item>
            <el-descriptions-item label="任务状态" align="center">
              <dict-tag :options="wf_task_status" :value="leftEchoData.flowTaskStatus"></dict-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间" align="center">{{ leftEchoData.createTime }}</el-descriptions-item>
            <el-descriptions-item label="操作" label-align="center">
              <el-button type="primary" @click="handleViewProcess(leftEchoData.businessId)"
                style="width: 20%;margin-left: 35px;">查看流程</el-button>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-col>
    </el-row>
    <!-- 提交组件 -->
    <submitVerify ref="submitVerifyRef" :task-variables="taskVariables" :isReadyFile="false" :isSelectBtnDisabled="true"
      @submit-callback="submitCallback" @beforeSubmit="beforeSubmit" />
    <!-- 审批记录 -->
    <approvalRecord ref="approvalRecordRef" />
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" :before-close="handleClose" width="500">
      <el-select v-model="flowCode" placeholder="Select" style="width: 240px">
        <el-option v-for="item in flowCodeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="submitFlow()"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 人工复检弹框 -->
    <el-dialog :title="manualDialog.title" v-model="manualDialog.visible" append-to-body width="100%"
      style="height: 100%;" class="manualDialog" @close="handleManualClose">
      <el-row :gutter="10" style="width: 100%;height: 100%;">
        <el-col :span="5" style="display: flex;flex-direction: column;align-items: flex-start;">
          <div style="margin-bottom: 10px;">
            <p style="text-align: start;color: #409EFF;margin: 0 0 16px;">分析前</p>
            <HeaderPrewiew :src="aiDetailData?.photoDocumentUrl" width="18.3vw" height="18.3vw"
              :preview-src-list="[aiDetailData?.photoDocumentUrl]">
            </HeaderPrewiew>
          </div>
          <div>
            <p style="text-align: start;color: #67C23A;margin: 10px 0 16px;">分析后</p>
            <HeaderPrewiew :src="aiDetailData?.aiPhotoDocumentUrl" width="18.3vw" height="18.3vw"
              :preview-src-list="[aiDetailData?.aiPhotoDocumentUrl]">
            </HeaderPrewiew>
          </div>
        </el-col>
        <el-col ref="ref1" :span="10" style="display: flex;flex-direction: column;height: 100%;">
          <div style="width: 100%; height: calc(90vh - 50px);overflow-y: auto;">
            <el-card v-for="(item, index) in aiDetailData?.violations" :key="index"
              @dblclick="hiddenDangerDBClick(index, item)"
              :class="[rgfjCheck[index] ? 'active' : '', 'hiddenDangerCard']">
              <template #header>
                <div class="card-header">
                  <div style="display: flex;align-items: center;">
                    <el-checkbox v-model="rgfjCheck[index]" @change="handleCheckChange(index, item)" />
                    <span style="display: block;padding-bottom: 3px;margin-left: 8px;">问题 {{ index + 1 }}</span>
                  </div>
                  <div style="display: flex;align-items: center;">
                    <span style="color: #409EFF;">危险级别：</span>
                    <dict-tag :options="hidden_danger_type" :value="item.level" />
                  </div>
                </div>
              </template>
              <div style="display: flex;">
                <span style="color: #666;display: block;width: 70px;">隐患描述：</span>
                <span style="display: block;flex: 1;">{{ item.violation }}</span>
              </div>
              <div style="display: flex;margin: 15px 0;">
                <span style="color: #666;display: block;width: 70px;">违反条款：</span>
                <span style="display: block;flex: 1;">{{ item.regulation }}</span>
              </div>
              <div style="display: flex;">
                <span style="color: #666;display: block;width: 70px;">整改意见：</span>
                <span style="display: block;flex: 1;">{{ item.measure
                }}</span>
              </div>
            </el-card>
          </div>
        </el-col>
        <el-col :span="9" style="padding-left: 20px;height: 100%;">
          <div style="width: 100%;height: calc(90vh - 50px);overflow-y: auto;padding-right: 20px;">
            <div>
              <div style="display: flex;align-items: center;">
                <label>整改复核</label>
                <span style="font-size: 15px;color: #F56C6C;margin-left: 10px;padding-bottom: 2px;">(注：请先选择左侧的问题)</span>
              </div>
              <el-checkbox-group ref="ref2" v-model="selectCheckVal" style="margin-top: 20px;">
                <el-checkbox label="限期整改" :value="1" />
                <el-checkbox label="停工通知" :value="2" />
                <el-checkbox label="行政处罚" :value="3" />
                <el-checkbox label="其他" :value="4" />
              </el-checkbox-group>
              <div>
                <div style="display: flex;align-items: center;margin-top: 20px;">
                  <span style="display: block;width: 70px;">整改时限</span>
                  <el-input v-model="formCorrectData.timeLimit" style="width: 12vw;" type="number" :min="1"
                    placeholder="请输入时间" @change="() => {
                      if (formCorrectData.timeLimit <= 0) {
                        formCorrectData.timeLimit = 1;
                      }
                    }">
                    <template #append>
                      <el-select v-model="formCorrectData.timeType" style="width: 80px;">
                        <el-option label="小时" :value="1" />
                        <el-option label="天" :value="2" />
                      </el-select>
                    </template>
                  </el-input>
                </div>
              </div>
            </div>
            <div style="margin-top: 30px;">
              <ul style="list-style: none;padding: 0;margin: 0;">
                <li v-if="selectCheckVal.includes(1)" style="margin-bottom: 20px;">
                  <div style="display: flex;">
                    <label>限期整改内容</label>
                    <el-button ref="ref4" type="primary" size="small" style="margin-left: 20px;margin-right: 15px;"
                      @click="handleDeadlineTemplate">下载模板</el-button>
                    <FileUpload @update:modelValue="handleDeadlineFile" :limit="1"
                      :modelValue="formCorrectData.correctionsFile" :isShowTip="false" :fileSize="20">
                      <el-button ref="ref5" size="small" type="primary">上传</el-button>
                    </FileUpload>
                    <span
                      style="font-size: 15px;color: #F56C6C;margin-left: 10px;padding-bottom: 2px;margin-left: 20px;">(注：请上传已盖章文件)</span>
                  </div>
                  <el-input ref="ref3" v-model="formCorrectData.correctionsContent" :rows="18" type="textarea"
                    style="margin-top: 10px;" />
                </li>
                <li v-if="selectCheckVal.includes(2)" style="margin-bottom: 20px;">
                  <div style="display: flex;">
                    <label>停工通知内容</label>
                    <el-button type="primary" size="small" style="margin-left: 20px;margin-right: 15px;"
                      @click="handleSuspensionTemplate">下载模板</el-button>
                    <FileUpload @update:modelValue="handleShutdownFile" :limit="1"
                      :modelValue="formCorrectData.suspensionFile" :isShowTip="false" :fileSize="20">
                      <el-button size="small" type="primary">上传</el-button>
                    </FileUpload>
                    <span
                      style="font-size: 15px;color: #F56C6C;margin-left: 10px;padding-bottom: 2px;margin-left: 20px;">(注：请上传已盖章文件)</span>
                  </div>
                  <el-input v-model="formCorrectData.suspensionContent" :rows="18" type="textarea"
                    style="margin-top: 10px;" />
                </li>
                <li v-if="selectCheckVal.includes(3)" style="margin-bottom: 20px;">
                  <div style="display: flex;">
                    <label>行政处罚内容</label>
                    <el-button type="primary" size="small" style="margin-left: 20px;margin-right: 15px;"
                      @click="handlePenaltyTemplate">下载模板</el-button>
                    <FileUpload @update:modelValue="handlePunishFile" :limit="1"
                      :modelValue="formCorrectData.penaltyFile" :isShowTip="false" :fileSize="20">
                      <el-button size="small" type="primary">上传</el-button>
                    </FileUpload>
                    <span
                      style="font-size: 15px;color: #F56C6C;margin-left: 10px;padding-bottom: 2px;margin-left: 20px;">(注：请上传已盖章文件)</span>
                  </div>
                  <el-input v-model="formCorrectData.penaltyContent" :rows="18" type="textarea"
                    style="margin-top: 10px;" />
                </li>
                <li v-if="selectCheckVal.includes(4)" style="margin-bottom: 20px;">
                  <div style="display: flex;">
                    <label>其他</label>
                    <FileUpload @update:modelValue="handleOtherFile" :isShowTip="false" :fileSize="20"
                      style="margin-left: 20px;">
                      <el-button ref="ref5" size="small" type="primary">上传</el-button>
                    </FileUpload>
                  </div>
                  <el-table :data="formCorrectData.elseFile" style="width: 100%;margin-top: 10px;">
                    <el-table-column type="index" label="序号" width="55" align="center" />
                    <el-table-column prop="name" label="名称" align="center">
                      <template #default="scope">
                        <el-input v-model="scope.row.name" style="width: 150px;" />
                      </template>
                    </el-table-column>
                    <el-table-column prop="fileName" label="文件" align="center">
                      <template #default="scope">
                        <el-link type="primary" href="javascript:;" style="color: #409EFF;"
                          @click="handleFileView(scope.row.url)">{{
                            scope.row.fileName
                          }}</el-link>
                      </template>
                    </el-table-column>
                  </el-table>
                </li>
              </ul>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-tour v-model="tourOpen" @change="tourChange" @close="handleTourClose" :z-index="3001" close-icon="CircleClose"
        class="tourClass">
        <el-tour-step :target="ref1?.$el" title="选择问题点" description="双击选择左侧问题点，可多选" placement="right" />
        <el-tour-step :target="ref2?.$el" title="选择整改措施" description="选择整改措施，可多选" />
        <el-tour-step :target="ref3?.$el" title="编辑内容" description="编辑问题点内容" />
        <el-tour-step :target="ref4?.$el" title="下载模板" description="下载需要上传的文件模板" />
        <el-tour-step :target="ref5?.$el" title="上传文件" description="上传盖章后的文件" />
        <el-tour-step :target="ref6?.$el" title="提交" description="一键提交所有内容" />
      </el-tour>
      <template #footer>
        <div class="dialog-footer">
          <el-button ref="ref6" type="primary" @click="handleManualSubmit">提交工单</el-button>
          <el-button @click="manualDialog.visible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Leave" lang="ts">
import { getPrj_hazardous_items_ai_detail } from '@/api/projects/prj_hazardous_items/index'
import { addLeave, updateLeave } from '@/api/workflow/leave';
import { manualSubmit } from '@/api/customFlow/api'
import { startWorkOrder } from '@/api/projects/prj_construction_plans/api'
import { LeaveForm, LeaveQuery, LeaveVO } from '@/api/workflow/leave/types';
import { startWorkFlow, getTaskVariables } from '@/api/workflow/task';
import { listByIds } from '@/api/system/oss/index'
import SubmitVerify from '@/components/Process/submitVerify.vue';
import ApprovalRecord from '@/components/Process/approvalRecord.vue';
import { AxiosResponse } from 'axios';
import { StartProcessBo } from '@/api/workflow/workflowCommon/types';
import { addHazardousThirdEcho, addHazardousFourthSave, addHazardousFourthEcho } from '@/api/second/index'
import type { ButtonInstance } from 'element-plus'
import { useStorage } from '@vueuse/core';
import HeaderPrewiew from '@/components/ImagePreview/index.vue';
import FileUpload from '@/components/FileUpload/index.vue'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { hidden_danger_type, wf_business_status, wf_task_status } = toRefs<any>(proxy?.useDict('hidden_danger_type', 'wf_business_status', 'wf_task_status'));
const buttonLoading = ref(false);
const loading = ref(true);
const leaveTime = ref<Array<string>>([]);
// 提交工单的表单参数
const formHallBureauData = reactive({
  remark: '', // 意见说明
  taskId: '', // 任务id
  files: []
})
//路由参数
const routeParams = ref<Record<string, any>>({});
const userInfo = ref([]);
const flowCodeOptions = [
  {
    value: 'leave1',
    label: '请假申请-普通'
  },
  {
    value: 'leave2',
    label: '请假申请-排他网关'
  },
  {
    value: 'leave3',
    label: '请假申请-并行网关'
  },
  {
    value: 'leave4',
    label: '请假申请-会签'
  },
  {
    value: 'leave5',
    label: '请假申请-并行会签网关'
  },
  {
    value: 'leave6',
    label: '请假申请-排他并行会签'
  }
];

const flowCode = ref<string>('');

const dialogVisible = reactive<DialogOption>({
  visible: false,
  title: '流程定义'
});
//提交组件
const submitVerifyRef = ref<InstanceType<typeof SubmitVerify>>();
//审批记录组件
const approvalRecordRef = ref<InstanceType<typeof ApprovalRecord>>();

const leaveFormRef = ref<ElFormInstance>();

const submitFormData = ref<StartProcessBo>({
  businessId: '',
  flowCode: '',
  variables: {}
});
const taskVariables = ref<Record<string, any>>({});

const initFormData: LeaveForm = {
  id: undefined,
  leaveType: undefined,
  startDate: undefined,
  endDate: undefined,
  leaveDays: undefined,
  remark: undefined,
  status: 'waiting'
};
const data = reactive<PageData<LeaveForm, LeaveQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    startLeaveDays: undefined,
    endLeaveDays: undefined
  },
  rules: {
    id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
    leaveType: [{ required: true, message: '请假类型不能为空', trigger: 'blur' }],
    leaveTime: [{ required: true, message: '请假时间不能为空', trigger: 'blur' }],
    leaveDays: [{ required: true, message: '请假天数不能为空', trigger: 'blur' }]
  }
});


// 控制人工复检弹框显隐
const manualDialog = reactive({
  visible: false,
  title: '',
})
const ref1 = ref<ButtonInstance>()
const ref2 = ref<ButtonInstance>()
const ref3 = ref<ButtonInstance>()
const ref4 = ref<ButtonInstance>()
const ref5 = ref<ButtonInstance>()
const ref6 = ref<ButtonInstance>()
const tourOpen = ref(false)
const tourStorage = useStorage<null | string>('Tour', null);
// 人工复检多选值
const rgfjCheck = ref([])
// 人工复检多选框的值
const selectCheckVal = ref([])
// 临时存放选中的问题
const tempCheck = ref([])
// 存放隐患清单详情的数据
const aiDetailData = ref();
// 提交工单的表单参数
const formCorrectData = reactive({
  question: '', // 问题点id(多个根据顺序用,隔开拼接)
  timeLimit: 1, // 整改时限
  timeType: 1, // 时间单位
  correctionsFile: '', // 限期整改文件
  correctionsContent: '', // 限期整改内容
  suspensionFile: '', // 停工通知文件
  suspensionContent: '', // 停工通知内容
  penaltyFile: '', // 行政处罚文件
  penaltyContent: '', // 行政处罚内容
  elseFile: [],
  taskId: '', // 任务id
})
const taskId1 = ref('')
// 查询提交工单以后的回显数据的id
const qualityTaskId = ref('')
// 存放右侧回显的数据
const leftEchoData = ref()

const handleClose = () => {
  dialogVisible.visible = false;
  flowCode.value = '';
  buttonLoading.value = false;
};
const { form, rules } = toRefs(data);

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  leaveTime.value = [];
  leaveFormRef.value?.resetFields();
};
// 获取第二条的第三步右侧的厅局意见回显数据
const getThirdEcho2 = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(255, 255, 255, 0.8)',
  })
  const res = await addHazardousThirdEcho(routeParams.value.id)
  if (res.code === 200) {
    qualityTaskId.value = res.data.qualityTaskId
    res.data.checkUsers.forEach((item) => {
      userInfo.value.push({
        userId: item.userId,
        nickName: item.name
      })
    })
    for (let i = 0; i < res.data.files.length; i++) {
      const { data } = await listByIds(res.data.files[i].fileId);
      const dataObj = data[data.length - 1]
      formHallBureauData.files.push({
        name: res.data.files[i].name,   //文件名称
        fileId: res.data.files[i].fileId,  //取ossid
        taskId: res.data.files[i].taskId,   //业务id，取地址栏中的id
        url: dataObj.url    //文件地址
      })
    }
    formHallBureauData.taskId = res.data.files[0].taskId;
    formHallBureauData.remark = res.data.remark;
  }
  loading.close();
}
// 点击预览按钮预览文件点击事件
const handlePreviewChange = (urls: string) => {
  const botaUrl = btoa(urls)
  const url = `${import.meta.env.VITE_APP_VIEW_URL}/onlinePreview?url=${botaUrl}`
  // 文件预览
  window.open(url, '_blank')
}
// 获取任务变量数据
const getTaskVariablesData = async () => {
  const res = await getTaskVariables(form.value.id as string);
  if (res.code === 200) {
    taskVariables.value = res.data;
  }
}
/** 暂存提交按钮 */
const submitForm = (status: string) => {
  if (leaveTime.value.length === 0) {
    proxy?.$modal.msgError('请假时间不能为空');
    return;
  }
  try {
    leaveFormRef.value?.validate(async (valid: boolean) => {
      form.value.startDate = leaveTime.value[0];
      form.value.endDate = leaveTime.value[1];
      if (valid) {
        buttonLoading.value = true;
        let res: AxiosResponse<LeaveVO>;
        if (form.value.id) {
          res = await updateLeave(form.value);
        } else {
          res = await addLeave(form.value);
        }
        form.value = res.data;
        if (status === 'draft') {
          buttonLoading.value = false;
          proxy?.$modal.msgSuccess('暂存成功');
          proxy.$tab.closePage(proxy.$route);
          proxy.$router.go(-1);
        } else {
          if ((form.value.status === 'draft' && (flowCode.value === '' || flowCode.value === null)) || routeParams.value.type === 'add') {
            flowCode.value = flowCodeOptions[0].value;
            dialogVisible.visible = true;
            return;
          }
          //说明启动过先随意穿个参数
          if (flowCode.value === '' || flowCode.value === null) {
            flowCode.value = 'xx';
          }
          await handleStartWorkFlow(res.data);
        }
      }
    });
  } finally {
    buttonLoading.value = false;
  }
};

const submitFlow = async () => {
  handleStartWorkFlow(form.value);
  dialogVisible.visible = false;
};
//提交申请
const handleStartWorkFlow = async (data: LeaveForm) => {
  try {
    submitFormData.value.flowCode = flowCode.value;
    submitFormData.value.businessId = data.id;
    console.log("流程变量", data);

    //流程变量
    taskVariables.value = {
      leaveDays: data.leaveDays,
      userList: ['1', '3', '4']
    };
    submitFormData.value.variables = taskVariables.value;
    const resp = await startWorkFlow(submitFormData.value);
    if (submitVerifyRef.value) {
      buttonLoading.value = false;
      submitVerifyRef.value.openDialog(resp.data.taskId);
    }
  } finally {
    buttonLoading.value = false;
  }
};
//头部流程进度
const handleApprovalRecord = () => {
  approvalRecordRef.value.init(form.value.id);
};

//提交组件回调
const submitCallback = async () => {
  await proxy.$tab.closePage(proxy.$route);
  proxy.$router.go(-1);
};
// 提交前的通用回调函数
const beforeSubmit = async (fun) => {
  // 提交前的逻辑处理
  fun(true)
};
//头部返回
const goBack = () => {
  proxy.$tab.closePage(proxy.$route);
  proxy.$router.go(-1);
};
//头部审批
const approvalVerifyOpen = async () => {
  if (leftEchoData.value.flowStatus == 'finish') {
    submitVerifyRef.value.openDialog(routeParams.value.taskId);
  } else {
    proxy.$modal.msgError('子流程未完成，无法审批！');
  }
  // proxy?.$modal.msgError('该项目清单已经提交审批了，无法再次提交');
};
//校验提交按钮是否显示
const submitButtonShow = computed(() => {
  return (
    routeParams.value.type === 'add' ||
    (routeParams.value.type === 'update' &&
      form.value.status &&
      (form.value.status === 'draft' || form.value.status === 'cancel' || form.value.status === 'back'))
  );
});

//校验审批按钮是否显示
const approvalButtonShow = computed(() => {
  return routeParams.value.type === 'approval' && form.value.status && form.value.status === 'waiting';
});




// 使用ossId查询图片的url地址
const getImageUrl = async (ossId: string | number) => {
  const { data } = await listByIds(ossId);
  return data[0]?.url;
}
// 获取隐患详情数据
const getAiDetail = async (taskId: string) => {
  const res = await getPrj_hazardous_items_ai_detail(taskId);
  if (res.code === 200) {
    aiDetailData.value = res.data;
    aiDetailData.value.photoDocumentUrl = await getImageUrl(aiDetailData.value.photoDocumentId);
    aiDetailData.value.aiPhotoDocumentUrl = await getImageUrl(aiDetailData.value.aiPhotoDocumentId);
  }
}
// 关闭人工复检弹框的回调函数
const handleManualClose = () => {
  rgfjCheck.value.forEach((item: any, index: number) => { // 重置多选框的值;
    rgfjCheck.value[index] = false;
  })
  formCorrectData.timeLimit = 1;
  selectCheckVal.value = [];
  formCorrectData.correctionsContent = '';
  formCorrectData.suspensionContent = '';
  formCorrectData.penaltyContent = '';
  formCorrectData.correctionsFile = '';
  formCorrectData.suspensionFile = '';
  formCorrectData.penaltyFile = '';
  formCorrectData.question = '';
  formCorrectData.timeType = 1;
  formCorrectData.elseFile = [];
  tempCheck.value = [];
}
// 人工复检
const handleManual = () => {
  taskId1.value = routeParams.value.id;
  formCorrectData.taskId = routeParams.value.id;
  getAiDetail(taskVariables.value.ai_task_id);
  manualDialog.title = '下发工单';
  manualDialog.visible = true;
  if (tourStorage.value == 'isTour') {
    return;
  }
  setTimeout(() => {
    tourOpen.value = true
    tourStorage.value = 'isTour'
  }, 500);
}
// tour引导下一步事件方法
const tourChange = (val: number) => {
  if (val === 1) {
    selectCheckVal.value[0] = 1
    hiddenDangerDBClick(0, aiDetailData.value.violations[0])
  }
}
// tour关闭事件
const handleTourClose = () => {
  handleManualClose()
}
// 双击人工复检里面的问题框事件
const hiddenDangerDBClick = (index: number, row: any) => {
  rgfjCheck.value[index] = !rgfjCheck.value[index];
  handleCheckChange(index, row)
}
// 限期整改模板下载
const handleDeadlineTemplate = async () => {
  proxy?.download('/system/hazardousItemsComments/down/xqzg',
    { projectId: taskVariables.value.projectId, timeLimit: formCorrectData.timeLimit, timeType: formCorrectData.timeType, question: formCorrectData.correctionsContent },
    `corrections_${new Date().getTime()}.docx`)
}
// 停工通知模板下载
const handleSuspensionTemplate = async () => {
  proxy?.download('/system/hazardousItemsComments/down/tgzg',
    { projectId: taskVariables.value.projectId, timeLimit: formCorrectData.timeLimit, timeType: formCorrectData.timeType, question: formCorrectData.suspensionContent },
    `suspension_${new Date().getTime()}.docx`)
}
// 行政处罚模板下载
const handlePenaltyTemplate = async () => {
  proxy?.download('/system/hazardousItemsComments/down/xzcfjds',
    { projectId: taskVariables.value.projectId, timeLimit: formCorrectData.timeLimit, timeType: formCorrectData.timeType, question: formCorrectData.penaltyContent },
    `penalty_${new Date().getTime()}.docx`)
}
// 左侧数据回显请求
const handleLeftEcho = async () => {
  // 左侧回显提交保存以后的数据
  const res3 = await addHazardousFourthEcho(qualityTaskId.value)
  if (res3.code === 200) {
    leftEchoData.value = res3.data;
  }
}
// 查看流程的点击事件
const handleViewProcess = (businessId: string) => {
  approvalRecordRef.value.init(businessId);
}
// 人工发起工单
const handleManualSubmit = () => {
  console.log('其他文件列表', formCorrectData);
  const isRgfjCheck = rgfjCheck.value.some((item: any) => item)
  if (!isRgfjCheck) {
    proxy?.$modal.msgError('请选择左侧问题！');
    return;
  }
  if (selectCheckVal.value.length === 0) {
    proxy?.$modal.msgError('请选择整改复核下方的内容！');
    return;
  }
  if (!formCorrectData.correctionsFile && selectCheckVal.value.includes(1)) {
    proxy?.$modal.msgError('请上传限期整改文件！');
    return;
  }
  if (!formCorrectData.suspensionFile && selectCheckVal.value.includes(2)) {
    proxy?.$modal.msgError('请上传停工通知文件！');
    return;
  }
  if (!formCorrectData.penaltyFile && selectCheckVal.value.includes(3)) {
    proxy?.$modal.msgError('请上传行政处罚文件！');
    return;
  }
  if (formCorrectData.elseFile.length <= 0 && selectCheckVal.value.includes(4)) {
    proxy?.$modal.msgError('请上传其他文件！');
    return;
  }
  if (!selectCheckVal.value.includes(1)) {
    formCorrectData.correctionsContent = '';
  }
  if (!selectCheckVal.value.includes(2)) {
    formCorrectData.suspensionContent = '';
  }
  if (!selectCheckVal.value.includes(3)) {
    formCorrectData.penaltyContent = '';
  }
  if (!selectCheckVal.value.includes(4)) {
    formCorrectData.elseFile = [];
  }
  proxy?.$modal.confirm('是否确认发起工单？').then(async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '提交中，请稍等...',
      background: 'rgba(255, 255, 255, 0.8)',
    })
    const res = await manualSubmit(formCorrectData);
    if (res.code === 200) {
      let busId = res.data
      const res1 = await startWorkOrder(taskVariables.value.ai_task_id, busId);
      if (res1.code === 200) {
        // 保存人工复检返回的变量查到数据
        const res2 = await addHazardousFourthSave({ qualityTaskId: res1.data.processInstanceId, taskId: routeParams.value.id })
        if (res2.code === 200) {
          proxy?.$modal.msgSuccess('操作成功');
          const res4 = await addHazardousThirdEcho(routeParams.value.id)
          if (res4.code === 200) {
            qualityTaskId.value = res4.data.qualityTaskId
            handleLeftEcho()
          }
        }
      }
    }
    loading.close();
    manualDialog.visible = false;
  }).catch(() => { });
}
// 问题的多选事件
const handleCheckChange = (index: number, item: any) => {
  if (rgfjCheck.value[index]) {
    if (formCorrectData.question) {
      formCorrectData.question = formCorrectData.question + ',' + item.resultId;
    } else {
      formCorrectData.question = item.resultId;
    }
    tempCheck.value[index] = `问题 ${index + 1}\n隐患描述：${item.violation}\n违反条款：${item.regulation}\n整改意见：${item.measure}\n`
  }
  if (rgfjCheck.value[index]) {
    formCorrectData.correctionsContent = tempCheck.value.join('');
    formCorrectData.suspensionContent = tempCheck.value.join('');
    formCorrectData.penaltyContent = tempCheck.value.join('');
  } else {
    tempCheck.value[index] = '';
    formCorrectData.correctionsContent = tempCheck.value.join('');
    formCorrectData.suspensionContent = tempCheck.value.join('');
    formCorrectData.penaltyContent = tempCheck.value.join('');
  }
}
// 上传限期整改文件的回调函数
const handleDeadlineFile = (fileOssId: string) => {
  formCorrectData.correctionsFile = fileOssId;
}
// 上传停工通知文件的回调函数
const handleShutdownFile = (fileOssId: string) => {
  formCorrectData.suspensionFile = fileOssId;
}
// 上传行政处罚文件的回调函数
const handlePunishFile = (fileOssId: string) => {
  formCorrectData.penaltyFile = fileOssId;
}
// 上传其他文件的回调函数
const handleOtherFile = (fileOssId: string) => {
  formCorrectData.elseFile = []
  const fileOssIdArr = fileOssId.split(',');
  fileOssIdArr.forEach((item: string) => {
    formCorrectData.elseFile.push({
      name: '',       //名称
      fileName: '',   //文件名称
      fileId: item,     //文件id
      taskId: undefined,     //任务id,
      serviceType: 'quality_supervision_department'   //服务类型（取字典表flow_service_type）
    })
  })
  getOtherFileList(fileOssId);
}
// 获取其他文件列表
const getOtherFileList = async (fileOssId: string) => {
  if (!fileOssId) {
    formCorrectData.elseFile = [];
    return;
  }
  const res = await listByIds(fileOssId);
  if (res.code == 200) {
    formCorrectData.elseFile.forEach((item: any, index: number) => {
      item.name = res.data[index].originalName
      item.fileName = res.data[index].originalName
    });
  }
}
// 预览上传的其他文件
const handleFileView = (urls: any) => {
  const botaUrl = btoa(urls)
  const url = `${import.meta.env.VITE_APP_VIEW_URL}/onlinePreview?url=${botaUrl}`
  // 其他文件预览
  window.open(url, '_blank')
}
onMounted(() => {
  nextTick(async () => {
    routeParams.value = proxy.$route.query;
    reset();
    loading.value = false;
    form.value.id = routeParams.value.id;
    formHallBureauData.taskId = routeParams.value.id;
    await getTaskVariablesData();
    await getThirdEcho2()
    if (qualityTaskId.value) {
      handleLeftEcho()
    }
  });
});
</script>
<style lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bcsmInput {
  .el-textarea__inner {
    background-color: #fff !important;
  }
}

.expertRadio {
  flex-wrap: nowrap !important;

  .el-radio {
    margin-right: 15px !important;
    padding-bottom: 10px;
  }
}

.manualDialog {
  .el-dialog__body {
    overflow-y: hidden;
    max-height: calc(90vh - 50px) !important;
  }
}

.hiddenDangerCard {
  width: 100%;
  margin-bottom: 20px;
  cursor: pointer;

  &.active {
    background-color: #e8f2ff;
  }

  &:hover {
    background-color: #f2f7fd;
  }
}

.tourClass {
  .el-tour__close {
    font-size: 24px !important;
  }
}
</style>

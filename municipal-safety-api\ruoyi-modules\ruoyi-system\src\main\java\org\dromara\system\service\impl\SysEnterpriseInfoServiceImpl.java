package org.dromara.system.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.repository.AbstractRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.config.ThreadPoolConfig;
import org.dromara.common.core.config.properties.ThreadPoolProperties;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.projects.service.IPrjProjectsSyncService;
import org.dromara.projects.service.impl.PrjProjectsSyncServiceImpl;
import org.dromara.system.domain.CJDW;
import org.dromara.system.domain.SysDept;
import org.dromara.system.domain.SysEnterpriseInfo;
import org.dromara.system.domain.SysRole;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.domain.vo.EnterpriseNameAndId;
import org.dromara.system.mapper.CJDWMapper;
import org.dromara.system.mapper.SysDeptMapper;
import org.dromara.system.mapper.SysEnterpriseInfoMapper;
import org.dromara.system.mapper.SysRoleMapper;
import org.dromara.system.service.ISysEnterpriseInfoService;
import org.dromara.system.service.ISysUserService;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static org.dromara.common.satoken.utils.LoginHelper.getUserId;
import static org.dromara.common.satoken.utils.LoginHelper.login;

@RequiredArgsConstructor
@Service
@Slf4j
public class SysEnterpriseInfoServiceImpl extends ServiceImpl<SysEnterpriseInfoMapper, SysEnterpriseInfo> implements ISysEnterpriseInfoService {

    private final SysEnterpriseInfoMapper sysEnterpriseInfoMapper;
    private final SysDeptMapper sysDeptMapper;
    private final ISysUserService userService;
    private final SysRoleMapper roleMapper;
    private final CJDWMapper cjdwMapper;

    @Resource
    @Lazy
    private IPrjProjectsSyncService PrjProjectsSyncService;

    private ThreadPoolExecutor executor = new ThreadPoolExecutor(
        2,
        2,
        3000L,
        TimeUnit.SECONDS,
        new LinkedBlockingQueue<Runnable>(1024),
        new ThreadPoolExecutor.CallerRunsPolicy());


    @Override
    public List<SysEnterpriseInfo> pageList(SysEnterpriseInfo bo) {
        return sysEnterpriseInfoMapper.list(bo);
    }

    @Override
    @Transactional
    public R ins(SysEnterpriseInfo bo) {
        bo.setCreateBy(getUserId());
        bo.setCreateTime(DateUtil.date());
        bo.setDelFlag("0");
        bo.setEnterpriseStatus("0");
        //  通过 信用代码 查询 部门表是否存在
        List<SysDept> sysDepts = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
            .eq(SysDept::getDeptName, bo.getUnifiedSocialCreditCode()));
        if (CollectionUtil.isNotEmpty(sysDepts)) {
            return R.fail("当前机构已存在，无法添加");
        }
        int b = sysEnterpriseInfoMapper.insert(bo);
        if (b == 1) {
            return R.ok();
        } else {
            return R.fail();
        }
    }

    @Override
    @Transactional
    public R audit(SysEnterpriseInfo bo) {
        if (StringUtils.equals(bo.getEnterpriseStatus(), "2")) {
            bo.setEnterpriseTime(DateUtil.date());
            bo.setEnterpriseId(getUserId());
            int update = sysEnterpriseInfoMapper.updateById(bo);
            if (update == 1) {
                return R.ok();
            } else {
                return R.fail();
            }
        }
        SysEnterpriseInfo sysEnterpriseInfo = sysEnterpriseInfoMapper.selectById(bo.getEnterpriseId());
        // 添加 到部门表
        List<SysDept> sysDepts = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
            .eq(SysDept::getDeptCode, sysEnterpriseInfo.getUnifiedSocialCreditCode()));
        if (CollectionUtil.isEmpty(sysDepts)) {
            SysDept sysDept = new SysDept();
            sysDept.setParentId(200L);
            sysDept.setAncestors("0,200");
            sysDept.setDeptName(sysEnterpriseInfo.getEnterpriseName());
            sysDept.setDeptType(sysEnterpriseInfo.getEnterpriseType());
            sysDept.setDeptCode(sysEnterpriseInfo.getUnifiedSocialCreditCode());
            sysDept.setDeptCategory(sysEnterpriseInfo.getUnifiedSocialCreditCode());
            sysDept.setProvinceCode(sysEnterpriseInfo.getRegistrationRegionProvince());
            sysDept.setCityCode(sysEnterpriseInfo.getRegistrationRegionCity());
            sysDept.setDistrictCode(sysEnterpriseInfo.getRegistrationRegionArea());
            sysDept.setOrderNum(1);
            sysDept.setStatus("0");
            sysDept.setDelFlag("0");
            sysDept.setCreateBy(sysEnterpriseInfo.getCreateBy());
            sysDept.setCreateTime(sysEnterpriseInfo.getCreateTime());
            int insert = sysDeptMapper.insert(sysDept);
            if (insert != 1) {
                return R.fail("添加失败");
            }
            sysEnterpriseInfo.setDeptId(sysDept.getDeptId());
        }
        // 添加到用户表  组装用户信息
        SysUserBo user = new SysUserBo();
        user.setDeptId(sysEnterpriseInfo.getDeptId());
        user.setUserName(sysEnterpriseInfo.getUnifiedSocialCreditCode());
        user.setNickName(sysEnterpriseInfo.getEnterpriseName());
        user.setUserType("");
        user.setPhonenumber(sysEnterpriseInfo.getOfficePhone());
        user.setPassword(sysEnterpriseInfo.getOfficePhone());
        user.setStatus("0");
        user.setUserType("sys_user");
        if (userService.checkUserNameUnique(user)) {
            // 支持多企业类型的角色分配（逗号分隔格式）
            user.setRoleIds(getRoleIdsByEnterpriseTypes(sysEnterpriseInfo.getEnterpriseType()));
            user.setPassword(BCrypt.hashpw(user.getPassword()));
            int i = userService.insertUser(user);
            sysEnterpriseInfo.setUserId(user.getUserId());
        }
        // 保存公司信息
        sysEnterpriseInfo.setEnterpriseStatus(bo.getEnterpriseStatus());
        sysEnterpriseInfo.setEnterpriseTime(DateUtil.date());
        sysEnterpriseInfo.setEnterpriseUserId(getUserId());
        sysEnterpriseInfo.setEnterpriseReason(bo.getEnterpriseReason());
        int update = sysEnterpriseInfoMapper.updateById(sysEnterpriseInfo);
        if (update == 1) {
            try {
                // 同步 当前企业下的说有项目信息
                List<CJDW> cjdws = cjdwMapper.selectBuilderLicenceNum(sysEnterpriseInfo.getUnifiedSocialCreditCode());
                if (!CollectionUtil.isEmpty(cjdws)) {
                    cjdws.forEach(cjdw -> {
                        PrjProjectsSyncService.syncProjects(cjdw.getBuilderLicenceNum());
                    });
                }
                log.info("异步同步项目信息成功，统一社会信用代码: {}", sysEnterpriseInfo.getUnifiedSocialCreditCode());
            } catch (Exception e) {
                // 记录异常日志，避免影响主流程
                log.error("异步同步项目信息失败，统一社会信用代码: {}", sysEnterpriseInfo.getUnifiedSocialCreditCode(), e);
            }
            return R.ok();
        } else {
            throw new RuntimeException("保存失败");
        }
    }

    public void syncProjects(String unifiedSocialCreditCode){
        try {
            // 同步 当前企业下的说有项目信息
            List<CJDW> cjdws = cjdwMapper.selectBuilderLicenceNum(unifiedSocialCreditCode);
            if (!CollectionUtil.isEmpty(cjdws)) {
                cjdws.forEach(cjdw -> {
                    PrjProjectsSyncService.syncProjects(cjdw.getBuilderLicenceNum());
                });
            }
            log.info("异步同步项目信息成功，统一社会信用代码: {}", unifiedSocialCreditCode);
        } catch (Exception e) {
            // 记录异常日志，避免影响主流程
            log.error("异步同步项目信息失败，统一社会信用代码: {}", unifiedSocialCreditCode, e);
        }
    }

    public Long[] returnRoleIds(String key) {
        SysRole sysRole = roleMapper.selectOne(new LambdaQueryWrapper<SysRole>()
            .eq(SysRole::getRoleKey, key));
        if (sysRole == null) {
            throw new RuntimeException("角色不存在");
        }
        Long[] roleIds = new Long[1];
        roleIds[0] = sysRole.getRoleId();
        return roleIds;
    }

    /**
     * 根据企业类型获取角色ID数组（支持多类型，逗号分隔格式）
     */
    private Long[] getRoleIdsByEnterpriseTypes(String enterpriseTypes) {
        if (StringUtils.isEmpty(enterpriseTypes)) {
            return new Long[0];
        }

        Set<Long> roleIds = new HashSet<>();
        String[] types = enterpriseTypes.split(",");

        for (String type : types) {
            type = type.trim();
            try {
                SysRole role = roleMapper.selectOne(new LambdaQueryWrapper<SysRole>()
                    .eq(SysRole::getRoleKey, type));
                if (role != null) {
                    roleIds.add(role.getRoleId());
                }
            } catch (Exception e) {
                // 如果角色不存在，记录警告但不抛出异常
                System.err.println("获取角色失败：" + type + ", " + e.getMessage());
            }
        }

        // 如果没有找到任何角色，使用默认的单类型逻辑作为后备
        if (roleIds.isEmpty() && types.length == 1) {
            try {
                return returnRoleIds(types[0]);
            } catch (Exception e) {
                throw new RuntimeException("未找到匹配的角色：" + enterpriseTypes);
            }
        }

        return roleIds.toArray(new Long[0]);
    }

    @Override
    public SysEnterpriseInfo getOneInfo(Long enterpriseId) {
        return sysEnterpriseInfoMapper.getOneInfo(enterpriseId);
    }

    @Override
    public R<List<EnterpriseNameAndId>> getSearchData() {

        List<SysEnterpriseInfo> list = this.list();

        List<EnterpriseNameAndId> result = new ArrayList<>();

        for (SysEnterpriseInfo enterpriseInfo : list) {
            result.add(new EnterpriseNameAndId(enterpriseInfo.getEnterpriseId(), enterpriseInfo.getEnterpriseName()));
        }

        return R.ok(result);
    }
}

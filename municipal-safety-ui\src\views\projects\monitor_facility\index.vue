<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="设备编号" prop="devNo">
              <el-input v-model="queryParams.devNo" placeholder="请输入设备编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备类型" prop="deviceType">
              <el-select v-model="queryParams.deviceType" placeholder="请选择设备类型" clearable>
                <el-option v-for="dict in hefei_faiclity_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="生产厂家" prop="manufacturers">
              <el-select v-model="queryParams.manufacturers" placeholder="请选择生产厂家" clearable>
                <el-option v-for="dict in hefei_manufacturers" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item><br />
            <el-form-item label="项目名称" prop="projectId">
              <el-select v-model="queryParams.projectId" filterable placeholder="请选择项目" clearable
                @change="handleSelectSearch">
                <el-option v-for="item in projectSelectData" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="工程名称" prop="itemId">
              <el-select v-model="queryParams.itemId" filterable placeholder="请选择工程" clearable>
                <el-option v-for="item in prj_hazardous_itemsList1" :key="item.itemId" :label="item.itemName"
                  :value="item.itemId" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!-- v-hasPermi="['system:monitorFacility:add']" -->
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <!-- v-hasPermi="['system:monitorFacility:upd']" -->
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()">修改</el-button>
          </el-col>
          <!-- v-hasPermi="['system:monitorFacility:remove']" -->
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()">删除</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['system:monitorFacility:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="monitorFacilityList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="设备编号" align="center" prop="devNo" />
        <el-table-column label="设备类型" align="center" prop="deviceType">
          <template #default="scope">
            <dict-tag v-if="scope.row.manufacturers == 'hefeilvneng'" :options="hefei_faiclity_type"
              :value="scope.row.deviceType" />
            <dict-tag v-if="scope.row.manufacturers == 'hefeijiulei'" :options="hefei_jl_faiclity_type"
              :value="scope.row.deviceType" />
          </template>

        </el-table-column>
        <el-table-column label="生产厂家" align="center" prop="manufacturers">
          <template #default="scope">
            <dict-tag :options="hefei_manufacturers" :value="scope.row.manufacturers" />
          </template>
        </el-table-column>
        <el-table-column label="数据来源" align="center" prop="dataSources" />
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column label="工程名称" align="center" prop="itemName" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <!-- v-hasPermi="['system:monitorFacility:upd']" -->
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <!-- v-hasPermi="['system:monitorFacility:concrrent']" -->
            <el-tooltip content="实时数据" placement="top">
              <el-button link type="primary" icon="Search" @click="handleRealTime(scope.row)"></el-button>
            </el-tooltip>
            <!-- v-hasPermi="['system:monitorFacility:remove']" -->
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改监测设备对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="monitorFacilityFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="设备编号" prop="devNo">
          <el-input v-model="form.devNo" placeholder="请输入设备编号" />
        </el-form-item>
        <el-form-item label="生产厂家" prop="manufacturers">
          <el-select v-model="form.manufacturers" placeholder="请选择生产厂家" @change="handleSelectManufacturers">
            <el-option v-for="dict in hefei_manufacturers" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceType">
          <el-select v-if="isHeFeiJiuLei || form.manufacturers == 'hefeijiulei'" v-model="form.deviceType"
            placeholder="请选择设备类型">
            <el-option v-for="dict in hefei_jl_faiclity_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
          <el-select v-if="isHeFeiLvNeng || form.manufacturers == 'hefeilvneng'" v-model="form.deviceType"
            placeholder="请选择设备类型">
            <el-option v-for="dict in hefei_faiclity_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
          <el-select v-if="!isHeFeiJiuLei && !isHeFeiLvNeng" v-model="form.deviceType" placeholder="请选择设备类型">
            <el-option v-for="dict in []" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据来源" prop="dataSources">
          <el-input v-model="form.dataSources" placeholder="请输入数据来源" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectId">
          <el-select v-model="form.projectId" filterable placeholder="请选择项目" @change="handleSelectChange">
            <el-option v-for="item in projectSelectData" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="工程名称" prop="itemId">
          <el-select v-model="form.itemId" filterable placeholder="请选择工程">
            <el-option v-for="item in prj_hazardous_itemsList" :key="item.itemId" :label="item.itemName"
              :value="item.itemId" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 实时数据 -->
    <el-dialog :title="realTimeDialog.title" v-model="realTimeDialog.visible" width="80%" append-to-body>
      <DustReal v-if="realTimeDialog.visible && deviceType == 'jl_dust'" :devNo="devNo" />
      <EdgeGuard v-if="realTimeDialog.visible && deviceType == 'ln_edgeGuardReal'" :devNo="devNo" />
      <DumpPlat v-if="realTimeDialog.visible && deviceType == 'ln_dump'" :devNo="devNo" />
      <Spraying v-if="realTimeDialog.visible && deviceType == 'ln_spraying'" :devNo="devNo" />
      <Energy v-if="realTimeDialog.visible && deviceType == 'ln_energy'" :devNo="devNo" />
      <LnNut v-if="realTimeDialog.visible && deviceType == 'ln_nut'" :devNo="devNo" />
      <LnWater v-if="realTimeDialog.visible && deviceType == 'ln_water'" :devNo="devNo" />
      <Smoke v-if="realTimeDialog.visible && deviceType == 'ln_smoke'" :devNo="devNo" />
      <HighFormWorkReal v-if="realTimeDialog.visible && deviceType == 'ln_highFormWork'" :devNo="devNo" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="realTimeDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MonitorFacility" lang="ts">
import { listMonitorFacility, getMonitorFacility, delMonitorFacility, addMonitorFacility, updateMonitorFacility } from '@/api/projects/facility';
import { MonitorFacilityVO, MonitorFacilityQuery, MonitorFacilityForm } from '@/api/projects/facility/types';
import { get_prj_search_data } from '@/api/projects/prj_hazardous_items';
import { listPrj_hazardous_items } from '@/api/projects/prj_hazardous_items';
import { Prj_hazardous_itemsVO } from '@/api/projects/prj_hazardous_items/types';
import DustReal from './components/hjjc.vue';
import EdgeGuard from './components/lbfh.vue';
import DumpPlat from './components/xlpt.vue';
import Spraying from './components/znpl.vue';
import Energy from './components/zndb.vue';
import LnNut from './components/znlm.vue';
import LnWater from './components/znsb.vue';
import Smoke from './components/znyg.vue';
import HighFormWorkReal from './components/gzm.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { hefei_faiclity_type } = toRefs<any>(proxy?.useDict('hefei_faiclity_type'));
const { hefei_jl_faiclity_type } = toRefs<any>(proxy?.useDict('hefei_jl_faiclity_type'));
const { hefei_manufacturers } = toRefs<any>(proxy?.useDict('hefei_manufacturers'));

const monitorFacilityList = ref<MonitorFacilityVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const monitorFacilityFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: MonitorFacilityForm = {
  id: undefined,
  devNo: undefined,
  deviceType: undefined,
  manufacturers: undefined,
  dataSources: undefined,
  projectId: undefined,
  itemId: undefined,
  remark: undefined,
}
const data = reactive<PageData<MonitorFacilityForm, MonitorFacilityQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    devNo: undefined,
    deviceType: undefined,
    manufacturers: undefined,
    dataSources: undefined,
    projectId: undefined,
    itemId: undefined,
    params: {
    }
  },
  rules: {
    devNo: [
      { required: true, message: "设备编号不能为空", trigger: "blur" }
    ],
    deviceType: [
      { required: true, message: "设备类型不能为空", trigger: "change" }
    ],
    manufacturers: [
      { required: true, message: "生产厂家不能为空", trigger: "change" }
    ],
    projectId: [
      { required: true, message: "项目名称不能为空", trigger: "change" }
    ],
    itemId: [
      { required: true, message: "工程名称不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);
const deviceType = ref('');

const projectSelectData = ref([])
const prj_hazardous_itemsList = ref<Prj_hazardous_itemsVO[]>([]);
const prj_hazardous_itemsList1 = ref<Prj_hazardous_itemsVO[]>([]);
const devNo = ref('');
const realTimeDialog = reactive<DialogOption>({
  visible: false,
  title: '实时数据'
});
// 存放多选修改选中的数据
const multipleSelectionItem = ref<MonitorFacilityVO[]>([]);
// 判断显示合肥九磊的选项还是合肥绿能的选项
const isHeFeiJiuLei = ref(false);
const isHeFeiLvNeng = ref(false);

/** 查询监测设备列表 */
const getList = async () => {
  loading.value = true;
  const res = await listMonitorFacility(queryParams.value);
  monitorFacilityList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

//查询项目选择框数据
const getProjectSelectData = async () => {
  const { data } = await get_prj_search_data();
  projectSelectData.value = data
}
/** 查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表 */
const getGongChengList = async () => {
  const res = await listPrj_hazardous_items({ projectId: form.value.projectId, pageNum: 1, pageSize: 10000 });
  prj_hazardous_itemsList.value = res.rows;
}
const getGongChengSearchList = async () => {
  const res = await listPrj_hazardous_items({ projectId: queryParams.value.projectId, pageNum: 1, pageSize: 10000 });
  prj_hazardous_itemsList1.value = res.rows;
}
const handleSelectChange = () => {
  form.value.itemId = undefined;
  getGongChengList();
}
const handleSelectSearch = () => {
  queryParams.value.itemId = undefined;
  getGongChengSearchList();
}
// 选择厂家时的事件
const handleSelectManufacturers = () => {
  form.value.deviceType = undefined;
  if (form.value.manufacturers == 'hefeijiulei') {
    isHeFeiJiuLei.value = true;
    isHeFeiLvNeng.value = false;
  } else if (form.value.manufacturers == 'hefeilvneng') {
    isHeFeiJiuLei.value = false;
    isHeFeiLvNeng.value = true;
  } else {
    isHeFeiJiuLei.value = false;
    isHeFeiLvNeng.value = false;
  }
}
/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  monitorFacilityFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  prj_hazardous_itemsList1.value = [];
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: MonitorFacilityVO[]) => {
  ids.value = selection.map(item => item.id);
  multipleSelectionItem.value = selection;
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  isHeFeiJiuLei.value = false;
  isHeFeiLvNeng.value = false;
  prj_hazardous_itemsList.value = [];
  dialog.visible = true;
  dialog.title = "添加监测设备";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: MonitorFacilityVO) => {
  reset();
  if (row) {
    isHeFeiJiuLei.value = false;
    isHeFeiLvNeng.value = false;
    if (row.manufacturers == 'hefeijiulei') {
      isHeFeiJiuLei.value = true;
      isHeFeiLvNeng.value = false;
    } else if (row.manufacturers == 'hefeilvneng') {
      isHeFeiJiuLei.value = false;
      isHeFeiLvNeng.value = true;
    } else {
      isHeFeiJiuLei.value = false;
      isHeFeiLvNeng.value = false;
    }
  } else {
    isHeFeiJiuLei.value = false;
    isHeFeiLvNeng.value = false;
    if (multipleSelectionItem.value[0].manufacturers == 'hefeijiulei') {
      isHeFeiJiuLei.value = true;
      isHeFeiLvNeng.value = false;
    } else if (multipleSelectionItem.value[0].manufacturers == 'hefeilvneng') {
      isHeFeiJiuLei.value = false;
      isHeFeiLvNeng.value = true;
    } else {
      isHeFeiJiuLei.value = false;
      isHeFeiLvNeng.value = false;
    }
  }
  prj_hazardous_itemsList.value = [];
  const _id = row?.id || ids.value[0]
  const res = await getMonitorFacility(_id);
  Object.assign(form.value, res.data);
  getGongChengList();
  dialog.visible = true;
  dialog.title = "修改监测设备";
}
// 实时数据操作按钮
const handleRealTime = (row: MonitorFacilityVO) => {
  deviceType.value = row.deviceType;
  devNo.value = row.devNo;
  realTimeDialog.visible = true;
}

/** 提交按钮 */
const submitForm = () => {
  monitorFacilityFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateMonitorFacility(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addMonitorFacility(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: MonitorFacilityVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除监测设备编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delMonitorFacility(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('system/monitorFacility/export', {
    ...queryParams.value
  }, `monitorFacility_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
  getProjectSelectData();
});
</script>

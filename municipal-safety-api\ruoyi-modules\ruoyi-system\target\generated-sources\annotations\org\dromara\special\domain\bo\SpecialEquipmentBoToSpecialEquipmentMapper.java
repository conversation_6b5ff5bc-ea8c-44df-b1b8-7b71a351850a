package org.dromara.special.domain.bo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.special.domain.SpecialEquipment;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {},
    imports = {}
)
public interface SpecialEquipmentBoToSpecialEquipmentMapper extends BaseMapper<SpecialEquipmentBo, SpecialEquipment> {
}

<template>
  <div class="gdmap">
    <el-autocomplete id="searchInput" v-if="showInput" v-model="searchKeyword" :fetch-suggestions="querySearch"
      placeholder="搜索项目位置" @select="handleSelect" style="width: 100%;margin-bottom: 25px;"></el-autocomplete>
    <!-- 地图容器 -->
    <div id="container" ref="amap"></div>
    <div id="map_canvas" v-show="false"></div>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'
import AMapLoader from '@amap/amap-jsapi-loader'
window._AMapSecurityConfig = {
  // 设置安全密钥
  securityJsCode: '070affe284bf3dcc7587165195d4ea0b',

}
export default {
  name: 'gdmap',
  mounted () {
    this.BMap = window.BMap
    this.bdmap = new this.BMap.Map('map_canvas')
    this.lang = this.coordinate ? this.coordinate.lang : this.lang
    this.lat = this.coordinate ? this.coordinate.lat : this.lat
    // this.initAMap()
  },
  props: {
    coordinate: {
      type: Object,
      default: () => { }
    },
    showInput: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      // 确保已经加载Baidu Maps API
      BMap: null,
      bdmap: null,
      lang: '103.689287',
      lat: '36.110355',
      searchKeyword: '', // 新增：搜索关键词
      placeSearch: null, // 新增：PlaceSearch 实例
      autocomplete: null,
      map: null,
      isflag: true
    }
  },
  methods: {
    initAMap (flag) {
      if (flag) {
        this.isflag = false
      } else {
        this.isflag = true
      }
      const _this = this
      AMapLoader.load({
        key: 'e097fc20def1f0b57fd55e79b6a1e437', // 申请好的Web端开发者Key，首次调用 load 时必填
        version: '1.4.15', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: ['AMap.PlaceSearch', 'AMap.Autocomplete']  // 需要使用的的插件列表，如比例尺'AMap.Scale'等，如果是地图控件，必须再 map.addControl添加
      })
        .then((AMap) => {
          const map = new AMap.Map('container', {
            resizeEnable: true,
            // 设置地图容器id
            viewMode: '2d', // 默认2d地图模式
            zoom: 18, // 初始化地图级别
            zooms: [5, 15], // 地图缩放范围
            // 可以设置初始化当前位置
            center: [_this.lang, _this.lat] // 初始化地图位置 116.397428, 39.90923
          })
          const marker = new AMap.Marker({
            position: map.getCenter(), // 标记点的位置
            draggable: true, // 可拖动
          })
          map.add(marker)

          // 监听地图点击事件
          map.on('click', function (e) {
            const lat = e.lnglat.getLat()
            const lng = e.lnglat.getLng()
            // console.log(`Clicked location: Latitude: ${lat}, Longitude: ${lng}`)
            _this.updateCoordinates(lat, lng, null, 'click')
            // 更新标记点位置
            marker.setPosition(e.lnglat)
          })

          _this.map = map

          // 初始化 Autocomplete 和 PlaceSearch
          _this.initAutoCompleteAndSearch(AMap)

          // 添加控件
          AMap.plugin(
            [
              'AMap.ElasticMarker',
              'AMap.Scale',
              'AMap.ToolBar',
              'AMap.HawkEye',
              'AMap.MapType',
              'AMap.Geolocation',
              'AMap.Geocoder',
              'AMap.Autocomplete',
            ],
            () => {
              map.addControl(new AMap.ElasticMarker()) // map.addControl为地图添加对应的控件
              map.addControl(new AMap.Scale())
              map.addControl(new AMap.ToolBar())
              map.addControl(new AMap.HawkEye())
              map.addControl(new AMap.MapType())
              map.addControl(new AMap.Geolocation(
                {
                  enableHighAccuracy: true, // 是否使用高精度定位，默认:true
                  timeout: 10000,          // 超过10秒后停止定位，默认：无限大
                  buttonPosition: 'RB'     // 定位按钮的位置
                }
              ))
            }
          )

          _this.map = map

          if (_this.isflag) {
            // 确保已经加载Baidu Maps API
            // const BMap = window.BMap;
            // 创建一个定位对象
            var geolocation = new _this.BMap.Geolocation();
            // 启用SDK辅助定位
            geolocation.enableSDKLocation();
            // 打开loading....
            // _this.loading = true
            // 开始定位
            geolocation.getCurrentPosition(function (r) {
              if (this.getStatus() == BMAP_STATUS_SUCCESS) {
                // ElMessage.success("定位成功")
                const bdPoint = [r.point.lng, r.point.lat]
                // 调用API转换接口
                AMap.convertFrom(bdPoint, 'baidu', function (status, result) {
                  if (status === 'complete') {
                    _this.lang = result.locations[0].lng
                    _this.lat = result.locations[0].lat
                    _this.isflag = false
                    _this.initAMap(true)
                    _this.updateCoordinates(_this.lat, _this.lang)
                  } else {
                    // 转换失败的处理逻辑
                    console.error('转换失败', result);
                  }
                });
              } else {
                // ElMessage.error('定位失败')
                // 点击地图的某个位置调用地图搜索服务
                // _this.searchService()
              }
            }, { enableHighAccuracy: true, timeout: 500 })
          }
        })
        .catch((e) => {
          console.error(e)
        })
    },
    initAutoCompleteAndSearch (AMap) {
      const _this = this
      AMap.plugin(['AMap.Autocomplete', 'AMap.PlaceSearch'], function () {
        const autoOptions = {
          input: "searchInput",
          // city: '兰州'
        }
        _this.autocomplete = new AMap.Autocomplete(autoOptions)
        // console.log('Autocomplete created:', _this.autocomplete)

        _this.placeSearch = new AMap.PlaceSearch({
          // city: '兰州',
          map: _this.map,
          pageSize: 5, //每页显示的结果数量
          pageIndex: 1, //页码
          autoFitView: true
        })

        // console.log('PlaceSearch created:', _this.placeSearch)

        // 监听选中建议结果事件
        AMap.event.addListener(_this.autocomplete, 'select', function (e) {
          _this.placeSearch.search(e.poi.name)
        })
        // 监听搜索结果点击mark标记
        AMap.event.addListener(_this.placeSearch, 'markerClick', function (e) {
          const lat = e.data.location.lat
          const lng = e.data.location.lng
          _this.updateCoordinates(lat, lng)
        })
        // 监听搜索完成事件
        AMap.event.addListener(_this.placeSearch, 'complete', function (results) {
          if (results.info === 'OK') {
            results.poiList.pois.forEach(poi => {
              const marker = new AMap.Marker({
                position: poi.location,
                title: poi.name
              })
              marker.on('click', () => {
                const lat = poi.location.getLat()
                const lng = poi.location.getLng()
                _this.updateCoordinates(lat, lng)
              })
              _this.map.add(marker)
            })
          }
        })
      })
    },
    querySearch (queryString, cb) {
      const _this = this
      if (queryString) {
        _this.autocomplete.search(queryString, (status, result) => {
          if (status === 'complete' && result.tips) {
            const suggestions = result.tips.map(tip => ({
              value: typeof (tip.address) == 'string' ? tip.name + '(' + tip.district + ' - ' + tip.address + ')' : tip.name + '(' + tip.district + ')',
              address: tip.district,
              location: tip.location,
            }))
            // const addressValue = {
            //   value: result.tips[0].address
            // }
            const addressValue = { value: '' }
            for (let i = 0; i < result.tips.length; i++) {
              if (result.tips[i].location) {
                addressValue.value = result.tips[i].district
                this.updateCoordinates(result.tips[i].location.lat, result.tips[i].location.lng, addressValue)
                break
              }
            }
            // if (result.tips) {
            //   this.updateCoordinates(result.tips[0].location.lat, result.tips[0].location.lng, addressValue)
            // }
            if (addressValue.value !== '') {
              this.placeSearch.search(addressValue.value)
            }
            cb(suggestions)
          } else {
            cb([])
            _this.bdSearch(queryString, cb)
          }
        })
      } else {
        cb([])
      }
    },

    // 百度的检索地址的方法
    bdSearch (queryString, cb) {
      const _this = this
      let options = {
        pageSize: 5, // 设置每页显示10个结果
        onSearchComplete: function (results) {
          // 获取搜索结果
          if (localSearch.getStatus() == BMAP_STATUS_SUCCESS) {
            // 判断状态是否正确
            if (results?.getCurrentNumPois() == 0) {
              cb([])
              console.log('未搜索到结果')
            } else if (results?.qk) {
              const suggestions = results.qk.map(tip => ({
                value: tip.title + ' (' + tip.address + ')',
                address: tip.address,
                location: tip.point,
              }))

              const addressValue = {
                value: results.qk[0].address
              }
              _this.updateCoordinates(results.qk[0].point.lat, results.qk[0].point.lng, addressValue)
              _this.placeSearch.search(addressValue.value)
              cb(suggestions)
            }
          } else {
            cb([])
          }
        }
      };
      let localSearch = new _this.BMap.LocalSearch(_this.bdmap, options);
      localSearch.search(queryString);
    },
    handleSelect (item) {
      if (item.location) {
        const lat = item.location.lat
        const lng = item.location.lng
        this.updateCoordinates(lat, lng, item)
        this.placeSearch.search(item.value)
      }
    },

    handleSearch () {
      if (this.placeSearch && this.searchKeyword) {
        // console.log('Performing search with:', this.searchKeyword);
        this.placeSearch.search(this.searchKeyword);
      } else {
        // console.log('PlaceSearch not available or searchKeyword empty');
      }
    },
    updateCoordinates (lat, lng, IAddress = null, type) {
      let _this = this
      let address = ''
      let addressComponent = ''
      this.lat = lat
      this.lang = lng
      let geocoder = new AMap.Geocoder();
      if (IAddress && !Array.isArray(IAddress.value)) {
        address = IAddress.value
        //  根据经纬度转换成省市区
        geocoder.getAddress([lng, lat], function (status, result) {
          if (status === "complete" && result.regeocode) {
            addressComponent = result.regeocode.addressComponent
            _this.$emit('setLocation', { lat, lng, address, addressComponent })
            if (type == 'click') {
              _this.searchKeyword = address
            }
          }
        });
      } else {
        //点击地图上的位置，根据经纬度转换成详细地址
        geocoder.getAddress([lng, lat], function (status, result) {
          if (status === "complete" && result.regeocode) {
            addressComponent = result.regeocode.addressComponent
            address = result.regeocode.formattedAddress
            _this.$emit('setLocation', { lat, lng, address, addressComponent })
            if (type == 'click') {
              _this.searchKeyword = address
            }
          }
        });
      }
      // 将地图中心设置为新的坐标
      if (this.map) {
        this.map.setCenter([lng, lat])
      }
    },
  }
}
</script>

<style scoped>
#container {
  margin: 0px;
  border: 1px solid #b4bccc;
  border-radius: 4px;
  width: 100%;
  height: 300px;
}
</style>

package org.dromara.special.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.special.domain.SpecialEquipment;
import org.dromara.special.domain.SpecialEquipmentToSpecialEquipmentVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {SpecialEquipmentToSpecialEquipmentVoMapper.class},
    imports = {}
)
public interface SpecialEquipmentVoToSpecialEquipmentMapper extends BaseMapper<SpecialEquipmentVo, SpecialEquipment> {
}

{"doc": " Undertow 自定义配置\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "customize", "paramTypes": ["org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory"], "doc": " 自定义 Undertow 配置\n <p>\n 主要配置内容包括：\n 1. 配置 WebSocket 部署信息\n 2. 在虚拟线程模式下使用虚拟线程池\n 3. 禁用不安全的 HTTP 方法，如 CONNECT、TRACE、TRACK\n </p>\n\n @param factory Undertow 的 Web 服务器工厂\n"}], "constructors": []}
package org.dromara.special.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.translation.annotation.Translation;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.translation.constant.TransConstant;

import java.io.Serial;

/**
 * 特种作业人员信息对象 special_operation_personnel
 *
 * <AUTHOR> Li
 * @date 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("special_operation_personnel")
public class SpecialOperationPersonnel extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "sop_id", type = IdType.ASSIGN_ID)
    private Long sopId;

    /**
     * 证书编号
     */
    private String certificateNumber;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 性别
     */
    private String gender;

    /**
     * 出生日期
     */
    private Date birthdate;

    /**
     * 操作类别
     */
    private String operationCategory;

    /**
     * 发证机关
     */
    private String issuer;

    /**
     * 初次领证日期
     */
    private Date firstIssueDate;

    /**
     * 最近发证日期
     */
    private Date lastIssueDate;

    /**
     * 有效期开始
     */
    private Date validityStart;

    /**
     * 有效期截止
     */
    private Date validityEnd;

    /**
     * 证书状态(有效,无效,挂失,注销)
     */
    private String status;

    /**
     * 电子证照链接
     */
    private String electronicLicenseUrl;

    /**
     * 电子证照文件ID
     */
    private Long electronicLicenseId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

    /** 所属单位名称 */
    private String companyName;

    /** 单位统一社会信用代码 */
    private String companyCode;

    /**  单位的类别分类 */
    private String companyType;

    /**  人员类别 */
    private String personType;
}

package org.dromara.projects.domain;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.vo.PrjSafeUserVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:15+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjSafeUserToPrjSafeUserVoMapperImpl implements PrjSafeUserToPrjSafeUserVoMapper {

    @Override
    public PrjSafeUserVo convert(PrjSafeUser arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjSafeUserVo prjSafeUserVo = new PrjSafeUserVo();

        prjSafeUserVo.setSaleUserId( arg0.getSaleUserId() );
        prjSafeUserVo.setMobile( arg0.getMobile() );
        prjSafeUserVo.setUserName( arg0.getUserName() );
        prjSafeUserVo.setIdCard( arg0.getIdCard() );
        prjSafeUserVo.setPositionType( arg0.getPositionType() );
        prjSafeUserVo.setOpenTaskId( arg0.getOpenTaskId() );

        return prjSafeUserVo;
    }

    @Override
    public PrjSafeUserVo convert(PrjSafeUser arg0, PrjSafeUserVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSaleUserId( arg0.getSaleUserId() );
        arg1.setMobile( arg0.getMobile() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setPositionType( arg0.getPositionType() );
        arg1.setOpenTaskId( arg0.getOpenTaskId() );

        return arg1;
    }
}

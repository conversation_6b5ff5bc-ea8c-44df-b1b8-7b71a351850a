package org.dromara.person.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.person.domain.bo.SysQualificationBo;
import org.dromara.person.domain.vo.SysQualificationVo;
import org.dromara.person.service.ISysQualificationService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;

/**
 * 人员资格证书
 *
 * <AUTHOR> zu da
 * @date 2025-05-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/qualification")
public class SysQualificationController extends BaseController {

    private final ISysQualificationService sysQualificationService;

    /**
     * 查询人员资格证书列表
     */
    @SaCheckPermission("system:qualification:list")
    @GetMapping("/list")
    public TableDataInfo<SysQualificationVo> list(SysQualificationBo bo, PageQuery pageQuery) {
        return sysQualificationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出人员资格证书列表
     */
    @SaCheckPermission("system:qualification:export")
    @Log(title = "人员资格证书", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysQualificationBo bo, HttpServletResponse response) {
        List<SysQualificationVo> list = sysQualificationService.queryList(bo);
        ExcelUtil.exportExcel(list, "人员资格证书", SysQualificationVo.class, response);
    }

    /**
     * 获取人员资格证书详细信息
     *
     * @param qualificationId 主键
     */
    @SaCheckPermission("system:qualification:query")
    @GetMapping("/{qualificationId}")
    public R<SysQualificationVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long qualificationId) {
        return R.ok(sysQualificationService.queryById(qualificationId));
    }

    /**
     * 新增人员资格证书
     */
    @SaCheckPermission("system:qualification:add")
    @Log(title = "人员资格证书", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysQualificationBo bo) {
        return toAjax(sysQualificationService.insertByBo(bo));
    }

    /**
     * 修改人员资格证书
     */
    @SaCheckPermission("system:qualification:edit")
    @Log(title = "人员资格证书", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysQualificationBo bo) {
        return toAjax(sysQualificationService.updateByBo(bo));
    }

    /**
     * 删除人员资格证书
     *
     * @param qualificationIds 主键串
     */
    @SaCheckPermission("system:qualification:remove")
    @Log(title = "人员资格证书", businessType = BusinessType.DELETE)
    @DeleteMapping("/{qualificationIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] qualificationIds) {
        return toAjax(sysQualificationService.deleteWithValidByIds(List.of(qualificationIds), true));
    }
}

export interface DangerListVO {

  /**
   * 主键
   */
  dangerId:string;
  /**
   * 名称
   */
  name: string;

  /**
   * 父级id
   */
  preId: string | number;

  /**
   * 危大类型
   */
  type: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 子对象
   */
  children: DangerListVO[];
}

export interface DangerListForm extends BaseEntity {
  /**
   * 主键
   */
  dangerId?:string;
  /**
   * 名称
   */
  name?: string;

  /**
   * 父级id
   */
  preId?: string | number;

  /**
   * 危大类型
   */
  type?: number;

  /**
   * 备注
   */
  remark?: string;

}

export interface DangerListQuery {

  /**
   * 名称
   */
  name?: string;

  /**
   * 父级id
   */
  preId?: string | number;

  /**
   * 危大类型
   */
  type?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}

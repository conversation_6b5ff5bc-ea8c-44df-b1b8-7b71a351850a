package org.dromara.person.service;

import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.person.domain.bo.SysQualificationBo;
import org.dromara.person.domain.vo.SysQualificationVo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 人员资格证书Service接口
 *
 * <AUTHOR> zu da
 * @date 2025-05-10
 */
public interface ISysQualificationService {

    /**
     * 查询人员资格证书
     *
     * @param qualificationId 主键
     * @return 人员资格证书
     */
    SysQualificationVo queryById(Long qualificationId);

    /**
     * 分页查询人员资格证书列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 人员资格证书分页列表
     */
    TableDataInfo<SysQualificationVo> queryPageList(SysQualificationBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的人员资格证书列表
     *
     * @param bo 查询条件
     * @return 人员资格证书列表
     */
    List<SysQualificationVo> queryList(SysQualificationBo bo);

    /**
     * 新增人员资格证书
     *
     * @param bo 人员资格证书
     * @return 是否新增成功
     */
    Boolean insertByBo(SysQualificationBo bo);

    /**
     * 修改人员资格证书
     *
     * @param bo 人员资格证书
     * @return 是否修改成功
     */
    Boolean updateByBo(SysQualificationBo bo);

    /**
     * 校验并批量删除人员资格证书信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

{"doc": " SSE工具类\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendMessage", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 向指定的SSE会话发送消息\n\n @param userId  要发送消息的用户id\n @param message 要发送的消息内容\n"}, {"name": "sendMessage", "paramTypes": ["java.lang.String"], "doc": " 本机全用户会话发送消息\n\n @param message 要发送的消息内容\n"}, {"name": "publishMessage", "paramTypes": ["org.dromara.common.sse.dto.SseMessageDto"], "doc": " 发布SSE订阅消息\n\n @param sseMessageDto 要发布的SSE消息对象\n"}, {"name": "publishAll", "paramTypes": ["java.lang.String"], "doc": " 向所有的用户发布订阅的消息(群发)\n\n @param message 要发布的消息内容\n"}, {"name": "isEnable", "paramTypes": [], "doc": " 是否开启\n"}], "constructors": []}
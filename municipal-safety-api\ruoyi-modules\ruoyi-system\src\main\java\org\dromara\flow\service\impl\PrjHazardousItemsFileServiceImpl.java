package org.dromara.flow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.flow.domain.PrjHazardousItemsFile;
import org.dromara.flow.domain.bo.PrjHazardousItemsFileBo;
import org.dromara.flow.domain.vo.PrjHazardousItemsFileVo;
import org.dromara.flow.mapper.PrjHazardousItemsFileMapper;
import org.dromara.flow.service.IPrjHazardousItemsFileService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 通用流程附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@RequiredArgsConstructor
@Service
public class PrjHazardousItemsFileServiceImpl implements IPrjHazardousItemsFileService {

    private final PrjHazardousItemsFileMapper baseMapper;

    /**
     * 查询通用流程附件
     *
     * @param itemFileId 主键
     * @return 通用流程附件
     */
    @Override
    public PrjHazardousItemsFileVo queryById(Long itemFileId) {
        return baseMapper.selectVoById(itemFileId);
    }

    /**
     * 分页查询通用流程附件列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 通用流程附件分页列表
     */
    @Override
    public TableDataInfo<PrjHazardousItemsFileVo> queryPageList(PrjHazardousItemsFileBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PrjHazardousItemsFile> lqw = buildQueryWrapper(bo);
        Page<PrjHazardousItemsFileVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的通用流程附件列表
     *
     * @param bo 查询条件
     * @return 通用流程附件列表
     */
    @Override
    public List<PrjHazardousItemsFileVo> queryList(PrjHazardousItemsFileBo bo) {
        LambdaQueryWrapper<PrjHazardousItemsFile> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PrjHazardousItemsFile> buildQueryWrapper(PrjHazardousItemsFileBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PrjHazardousItemsFile> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PrjHazardousItemsFile::getItemFileId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), PrjHazardousItemsFile::getName, bo.getName());
        lqw.eq(bo.getFileId() != null, PrjHazardousItemsFile::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getTaskId()), PrjHazardousItemsFile::getTaskId, bo.getTaskId());
        lqw.eq(StringUtils.isNotBlank(bo.getServiceType()), PrjHazardousItemsFile::getServiceType, bo.getServiceType());
        lqw.eq(StringUtils.isNotBlank(bo.getCallFileId()), PrjHazardousItemsFile::getCallFileId, bo.getCallFileId());
        return lqw;
    }

    /**
     * 新增通用流程附件
     *
     * @param bo 通用流程附件
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PrjHazardousItemsFileBo bo) {
        PrjHazardousItemsFile add = MapstructUtils.convert(bo, PrjHazardousItemsFile.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemFileId(add.getItemFileId());
        }
        return flag;
    }

    /**
     * 修改通用流程附件
     *
     * @param bo 通用流程附件
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PrjHazardousItemsFileBo bo) {
        PrjHazardousItemsFile update = MapstructUtils.convert(bo, PrjHazardousItemsFile.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PrjHazardousItemsFile entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除通用流程附件信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 通过BOS批量更新项目危险物品文件信息
     *
     * @param bos 待更新的项目危险物品文件信息列表
     * @return
     */
    @Override
    public boolean updateByBos(List<PrjHazardousItemsFile> bos) {
        List<PrjHazardousItemsFile> list = new ArrayList<>();

        for (PrjHazardousItemsFile bo : bos) {
            PrjHazardousItemsFile file = new PrjHazardousItemsFile();
            file.setItemFileId(bo.getItemFileId());
            file.setCallFileId(bo.getCallFileId());
            list.add(file);
        }
        return this.baseMapper.updateBatchById(list);
    }
}

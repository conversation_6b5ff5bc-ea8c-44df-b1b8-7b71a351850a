package org.dromara.projects.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.PrjSafeInstallation;
import org.dromara.projects.domain.PrjSafeInstallationToPrjSafeInstallationVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjSafeInstallationToPrjSafeInstallationVoMapper.class},
    imports = {}
)
public interface PrjSafeInstallationVoToPrjSafeInstallationMapper extends BaseMapper<PrjSafeInstallationVo, PrjSafeInstallation> {
}

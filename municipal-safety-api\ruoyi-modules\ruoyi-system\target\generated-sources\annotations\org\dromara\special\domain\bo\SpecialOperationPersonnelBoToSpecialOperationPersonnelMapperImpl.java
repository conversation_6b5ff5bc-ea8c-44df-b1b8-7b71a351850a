package org.dromara.special.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.special.domain.SpecialOperationPersonnel;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:15+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class SpecialOperationPersonnelBoToSpecialOperationPersonnelMapperImpl implements SpecialOperationPersonnelBoToSpecialOperationPersonnelMapper {

    @Override
    public SpecialOperationPersonnel convert(SpecialOperationPersonnelBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SpecialOperationPersonnel specialOperationPersonnel = new SpecialOperationPersonnel();

        specialOperationPersonnel.setSearchValue( arg0.getSearchValue() );
        specialOperationPersonnel.setCreateDept( arg0.getCreateDept() );
        specialOperationPersonnel.setCreateBy( arg0.getCreateBy() );
        specialOperationPersonnel.setCreateTime( arg0.getCreateTime() );
        specialOperationPersonnel.setUpdateBy( arg0.getUpdateBy() );
        specialOperationPersonnel.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            specialOperationPersonnel.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        specialOperationPersonnel.setSopId( arg0.getSopId() );
        specialOperationPersonnel.setCertificateNumber( arg0.getCertificateNumber() );
        specialOperationPersonnel.setName( arg0.getName() );
        specialOperationPersonnel.setIdCard( arg0.getIdCard() );
        specialOperationPersonnel.setGender( arg0.getGender() );
        specialOperationPersonnel.setBirthdate( arg0.getBirthdate() );
        specialOperationPersonnel.setOperationCategory( arg0.getOperationCategory() );
        specialOperationPersonnel.setIssuer( arg0.getIssuer() );
        specialOperationPersonnel.setFirstIssueDate( arg0.getFirstIssueDate() );
        specialOperationPersonnel.setLastIssueDate( arg0.getLastIssueDate() );
        specialOperationPersonnel.setValidityStart( arg0.getValidityStart() );
        specialOperationPersonnel.setValidityEnd( arg0.getValidityEnd() );
        specialOperationPersonnel.setStatus( arg0.getStatus() );
        specialOperationPersonnel.setElectronicLicenseUrl( arg0.getElectronicLicenseUrl() );
        specialOperationPersonnel.setElectronicLicenseId( arg0.getElectronicLicenseId() );
        specialOperationPersonnel.setProjectId( arg0.getProjectId() );

        return specialOperationPersonnel;
    }

    @Override
    public SpecialOperationPersonnel convert(SpecialOperationPersonnelBo arg0, SpecialOperationPersonnel arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSopId( arg0.getSopId() );
        arg1.setCertificateNumber( arg0.getCertificateNumber() );
        arg1.setName( arg0.getName() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setGender( arg0.getGender() );
        arg1.setBirthdate( arg0.getBirthdate() );
        arg1.setOperationCategory( arg0.getOperationCategory() );
        arg1.setIssuer( arg0.getIssuer() );
        arg1.setFirstIssueDate( arg0.getFirstIssueDate() );
        arg1.setLastIssueDate( arg0.getLastIssueDate() );
        arg1.setValidityStart( arg0.getValidityStart() );
        arg1.setValidityEnd( arg0.getValidityEnd() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setElectronicLicenseUrl( arg0.getElectronicLicenseUrl() );
        arg1.setElectronicLicenseId( arg0.getElectronicLicenseId() );
        arg1.setProjectId( arg0.getProjectId() );

        return arg1;
    }
}

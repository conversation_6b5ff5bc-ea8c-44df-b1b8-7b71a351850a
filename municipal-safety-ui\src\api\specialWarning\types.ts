export interface HazardousItemsSpecialWarningVO {
  /**
   * 特殊预警id
   */
  warningId: string | number;

  /**
   * 流程业务id
   */
  taskId: string | number;

  /**
   * 预警原因（质监站可补充）
   */
  reason: string;

  /**
   * 预警类型(字典special_warning_type)
   */
  reasonType: string;
  /**
   * 项目id
   */
   projectId: string | number;

}
export interface HazardousItemsSpecialWarningForm extends BaseEntity {
  /**
   * 特殊预警id
   */
  warningId?: string | number;

  /**
   * 流程业务id
   */
  taskId?: string | number;

  /**
   * 预警原因（质监站可补充）
   */
  reason?: string;

  /**
   * 预警类型(字典special_warning_type)
   */
  reasonType?: string;

}

export interface HazardousItemsSpecialWarningQuery extends PageQuery {

  /**
   * 流程业务id
   */
  taskId?: string | number;

  /**
   * 预警原因（质监站可补充）
   */
  reason?: string;

  /**
   * 预警类型(字典special_warning_type)
   */
  reasonType?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

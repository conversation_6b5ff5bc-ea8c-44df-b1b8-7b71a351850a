package org.dromara.special.service;

import org.dromara.special.domain.vo.SpecialOperationPersonnelVo;
import org.dromara.special.domain.bo.SpecialOperationPersonnelBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 特种作业人员信息Service接口
 *
 * <AUTHOR> Li
 * @date 2025-05-13
 */
public interface ISpecialOperationPersonnelService {

    /**
     * 查询特种作业人员信息
     *
     * @param sopId 主键
     * @return 特种作业人员信息
     */
    SpecialOperationPersonnelVo queryById(Long sopId);

    /**
     * 分页查询特种作业人员信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 特种作业人员信息分页列表
     */
    TableDataInfo<SpecialOperationPersonnelVo> queryPageList(SpecialOperationPersonnelBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的特种作业人员信息列表
     *
     * @param bo 查询条件
     * @return 特种作业人员信息列表
     */
    List<SpecialOperationPersonnelVo> queryList(SpecialOperationPersonnelBo bo);

    /**
     * 新增特种作业人员信息
     *
     * @param bo 特种作业人员信息
     * @return 是否新增成功
     */
    Boolean insertByBo(SpecialOperationPersonnelBo bo);

    /**
     * 修改特种作业人员信息
     *
     * @param bo 特种作业人员信息
     * @return 是否修改成功
     */
    Boolean updateByBo(SpecialOperationPersonnelBo bo);

    /**
     * 校验并批量删除特种作业人员信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

package org.dromara.quality.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.quality.domain.bo.QualityMeasurementBo;
import org.dromara.quality.domain.vo.QualityMeasurementVo;
import org.dromara.quality.service.IQualityMeasurementService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 实测实量Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/quality/measurement")
public class QualityMeasurementController extends BaseController {

    private final IQualityMeasurementService qualityMeasurementService;

    /**
     * 查询实测实量列表
     */
    @SaCheckPermission("quality:measurement:list")
    @GetMapping("/list")
    public TableDataInfo<QualityMeasurementVo> list(@Validated QualityMeasurementBo bo, PageQuery pageQuery) {
        return qualityMeasurementService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出实测实量列表
     */
    @SaCheckPermission("quality:measurement:export")
    @Log(title = "实测实量", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated QualityMeasurementBo bo, HttpServletResponse response) {
        List<QualityMeasurementVo> list = qualityMeasurementService.queryList(bo);
        ExcelUtil.exportExcel(list, "实测实量", QualityMeasurementVo.class, response);
    }

    /**
     * 获取实测实量详细信息
     *
     * @param measurementId 测量ID
     */
    @SaCheckPermission("quality:measurement:query")
    @GetMapping("/{measurementId}")
    public R<QualityMeasurementVo> getInfo(@NotNull(message = "测量ID不能为空")
                                           @PathVariable("measurementId") Long measurementId) {
        return R.ok(qualityMeasurementService.queryById(measurementId));
    }

    /**
     * 新增实测实量
     */
    @SaCheckPermission("quality:measurement:add")
    @Log(title = "实测实量", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QualityMeasurementBo bo) {
        return toAjax(qualityMeasurementService.insertByBo(bo));
    }

    /**
     * 修改实测实量
     */
    @SaCheckPermission("quality:measurement:edit")
    @Log(title = "实测实量", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QualityMeasurementBo bo) {
        return toAjax(qualityMeasurementService.updateByBo(bo));
    }

    /**
     * 删除实测实量
     *
     * @param measurementIds 测量ID串
     */
    @SaCheckPermission("quality:measurement:remove")
    @Log(title = "实测实量", businessType = BusinessType.DELETE)
    @DeleteMapping("/{measurementIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] measurementIds) {
        return toAjax(qualityMeasurementService.deleteWithValidByIds(Arrays.asList(measurementIds), true));
    }

    /**
     * 推送测量信息
     *
     * @param measurementIds 测量ID串
     */
    @SaCheckPermission("quality:measurement:push")
    @Log(title = "实测实量", businessType = BusinessType.UPDATE)
    @PostMapping("/push/{measurementIds}")
    public R<Void> pushMeasurementInfo(@NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] measurementIds) {
        return toAjax(qualityMeasurementService.pushMeasurementInfo(Arrays.asList(measurementIds)));
    }

    /**
     * 标记隐患
     *
     */
    @SaCheckPermission("quality:measurement:mark")
    @Log(title = "实测实量", businessType = BusinessType.UPDATE)
    @PostMapping("/markHazard")
    public R<Void> markHazard(@RequestParam(value = "measurementId") Long measurementId, @RequestParam(value = "hazardDescription") String hazardDescription) {
        return toAjax(qualityMeasurementService.markHazard(measurementId, hazardDescription));
    }

    /**
     * 取消标记隐患
     *
     * @param measurementId 测量ID
     */
    @SaCheckPermission("quality:measurement:unmark")
    @Log(title = "实测实量", businessType = BusinessType.UPDATE)
    @PostMapping("/unmarkHazard/{measurementId}")
    public R<Void> unmarkHazard(@NotNull(message = "测量ID不能为空")
                                @PathVariable Long measurementId) {
        return toAjax(qualityMeasurementService.unmarkHazard(measurementId));
    }

    /**
     * 根据设备ID获取设备信息
     *
     * @param deviceId 设备ID
     */
    @SaCheckPermission("quality:measurement:query")
    @GetMapping("/getDeviceInfo/{deviceId}")
    public R getDeviceInfo(@NotNull(message = "设备ID不能为空")
                                 @PathVariable Long deviceId) {
        QualityMeasurementBo bo = new QualityMeasurementBo();
        qualityMeasurementService.fillDeviceInfo(deviceId, bo);
        return R.ok(bo);
    }

}

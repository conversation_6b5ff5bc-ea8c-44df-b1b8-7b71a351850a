<template>
  <div class="weather-container">
    <div class="weather-card" :class="{ 'loading': isLoading }">
      <!-- 搜索区域 -->
      <div class="search-box">
        <input v-model="searchQuery" type="text" placeholder="输入城市名称" @keyup.enter="fetchWeather" />
        <button @click="fetchWeather">
          <i class="fas fa-search"></i>
        </button>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-indicator">
        <i class="fas fa-spinner fa-spin"></i>
        <p>加载中...</p>
      </div>

      <!-- 天气信息 -->
      <div v-else-if="weatherData" class="weather-info">
        <!-- 当前位置和日期 -->
        <div class="location-date">
          <h2>{{ weatherData.name }}, {{ weatherData.sys.country }}</h2>
          <p>{{ currentDate }}</p>
        </div>

        <!-- 主要天气信息 -->
        <div class="main-weather">
          <div class="temperature">
            <img :src="`https://openweathermap.org/img/wn/${weatherData.weather[0].icon}@2x.png`" :alt="weatherData.weather[0].description" />
            <h1>{{ Math.round(weatherData.main.temp) }}°C</h1>
          </div>
          <p class="weather-description">{{ weatherData.weather[0].description }}</p>
        </div>

        <!-- 天气详情 -->
        <div class="weather-details">
          <div class="detail-item">
            <i class="fas fa-temperature-high"></i>
            <span>体感温度: {{ Math.round(weatherData.main.feels_like) }}°C</span>
          </div>
          <div class="detail-item">
            <i class="fas fa-tint"></i>
            <span>湿度: {{ weatherData.main.humidity }}%</span>
          </div>
          <div class="detail-item">
            <i class="fas fa-wind"></i>
            <span>风速: {{ weatherData.wind.speed }} m/s</span>
          </div>
          <div class="detail-item">
            <i class="fas fa-cloud"></i>
            <span>云量: {{ weatherData.clouds.all }}%</span>
          </div>
        </div>

        <!-- 天气预报 -->
        <div v-if="forecastData" class="forecast">
          <h3>5天预报</h3>
          <div class="forecast-list">
            <div v-for="(day, index) in forecastData.list" :key="index" class="forecast-item" v-if="index % 8 === 0">
              <p>{{ formatDate(day.dt) }}</p>
              <img :src="`https://openweathermap.org/img/wn/${day.weather[0].icon}.png`" :alt="day.weather[0].description" />
              <p>{{ Math.round(day.main.temp) }}°C</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="error" class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <p>{{ error }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// API 配置
const API_KEY = 'YOUR_OPENWEATHERMAP_API_KEY'; // 替换为你的API key
const BASE_URL = 'https://api.openweathermap.org/data/2.5';
const UNITS = 'metric'; // 使用摄氏度

// 响应式数据
const searchQuery = ref('北京');
const weatherData = ref(null);
const forecastData = ref(null);
const isLoading = ref(false);
const error = ref(null);

// 计算属性
const currentDate = computed(() => {
  if (!weatherData.value) return '';
  return new Date(weatherData.value.dt * 1000).toLocaleDateString('zh-CN', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
});

// 方法
const fetchWeather = async () => {
  if (!searchQuery.value.trim()) return;

  isLoading.value = true;
  error.value = null;

  try {
    // 获取当前天气
    const weatherResponse = await fetch(`${BASE_URL}/weather?q=${searchQuery.value}&units=${UNITS}&appid=${API_KEY}&lang=zh_cn`);

    if (!weatherResponse.ok) {
      throw new Error('城市未找到，请检查名称是否正确');
    }

    weatherData.value = await weatherResponse.json();

    // 获取天气预报
    const forecastResponse = await fetch(`${BASE_URL}/forecast?q=${searchQuery.value}&units=${UNITS}&appid=${API_KEY}&lang=zh_cn`);

    if (forecastResponse.ok) {
      forecastData.value = await forecastResponse.json();
    }
  } catch (err) {
    error.value = err.message;
    weatherData.value = null;
    forecastData.value = null;
  } finally {
    isLoading.value = false;
  }
};

const formatDate = (timestamp) => {
  const date = new Date(timestamp * 1000);
  return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
};

// 生命周期钩子
onMounted(() => {
  fetchWeather();
});
</script>

<style scoped>
.weather-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  font-family: 'Arial', sans-serif;
}

.weather-card {
  width: 100%;
  max-width: 500px;
  background: linear-gradient(135deg, #72b5f7, #4a90e2);
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  color: white;
  transition: all 0.3s ease;
}

.weather-card.loading {
  opacity: 0.8;
}

.search-box {
  display: flex;
  margin-bottom: 20px;
}

.search-box input {
  flex: 1;
  padding: 12px 15px;
  border: none;
  border-radius: 30px 0 0 30px;
  outline: none;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.search-box input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-box button {
  padding: 0 20px;
  border: none;
  border-radius: 0 30px 30px 0;
  background: rgba(255, 255, 255, 0.3);
  color: white;
  cursor: pointer;
  transition: background 0.3s;
}

.search-box button:hover {
  background: rgba(255, 255, 255, 0.4);
}

.loading-indicator {
  text-align: center;
  padding: 40px 0;
}

.loading-indicator i {
  font-size: 30px;
  margin-bottom: 10px;
}

.location-date {
  text-align: center;
  margin-bottom: 20px;
}

.location-date h2 {
  margin: 0;
  font-size: 24px;
}

.location-date p {
  margin: 5px 0 0;
  opacity: 0.8;
}

.main-weather {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.temperature {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.temperature h1 {
  font-size: 48px;
  margin: 0;
  font-weight: 300;
}

.temperature img {
  width: 80px;
  height: 80px;
  margin-right: 10px;
}

.weather-description {
  margin: 0;
  font-size: 18px;
  text-transform: capitalize;
}

.weather-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 25px;
}

.detail-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 10px 15px;
  border-radius: 10px;
}

.detail-item i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.forecast {
  margin-top: 20px;
}

.forecast h3 {
  margin-bottom: 15px;
  font-size: 18px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 10px;
}

.forecast-list {
  display: flex;
  justify-content: space-between;
  overflow-x: auto;
  padding-bottom: 10px;
}

.forecast-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
  padding: 0 10px;
}

.forecast-item img {
  width: 40px;
  height: 40px;
  margin: 5px 0;
}

.error-message {
  text-align: center;
  padding: 20px;
  color: #ffcccb;
}

.error-message i {
  font-size: 30px;
  margin-bottom: 10px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .weather-card {
    padding: 15px;
  }

  .temperature h1 {
    font-size: 36px;
  }

  .weather-details {
    grid-template-columns: 1fr;
  }
}
</style>

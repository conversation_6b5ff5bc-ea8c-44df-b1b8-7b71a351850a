package org.dromara.projects.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 项目人员关联表对象 prj_personnel
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prj_personnel")
public class PrjPersonnel extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 项目人员关联ID
     */
    @TableId(value = "project_personnel_id")
    private Long projectPersonnelId;

    /**
     * 项目ID (逻辑外键至 prj_projects.project_id)
     */
    private Long projectId;

    /**
     * 人员表id
     */
    private Long personId;

    /**
     * 用户ID (逻辑外键至 sys_users.user_id)
     */
    private Long userId;

    /**
     * 该人员在项目中的所属单位ID (逻辑外键至 sys_organizations.org_id)
     */
    private Long orgId;

    /**
     * 在本项目中的具体角色/岗位（取字典值）
     */
    private String roleOnProject;

    /**
     * 是否特种作业人员 (0:否, 1:是)
     */
    private Long isSpecialOps;

    /**
     * 进入项目日期
     */
    private Date startDateOnProject;

    /**
     * 离开项目日期
     */
    private Date endDateOnProject;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;


}

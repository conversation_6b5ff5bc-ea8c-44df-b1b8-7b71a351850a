import request from '@/utils/request';
export const getlist = (params) => {
  return request({
    url: '/system/division/list',
    method: 'get',
    params
  });
};
// 获取危大清类型
export const getItemNum = (params) => {
  return request({
    url: 'dataView/getItemNum',
    method: 'get',
    params
  });
};
// 获取危大清清单
export const getDangerNum = (params) => {
  return request({
    url: '/dataView/getDangerNum',
    method: 'get',
    params
  });
};
// 获取危大清项目
export const getProItemLis = (params) => {
  return request({
    url: '/dataView/getProItemList',
    method: 'get',
    params
  });
};
// 获取左侧统计表
export const getYearItemNum = (params) => {
  return request({
    url: '/dataView/getYearItemNum',
    method: 'get',
    params
  });
};
// 获取右侧统计表
export const getAreaItemNum = (params) => {
  return request({
    url: '/dataView/getAreaItemNum',
    method: 'get',
    params
  });
};

2025-05-19 08:58:34 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-19 08:58:35 [main] INFO  o.dromara.MunicipalSafetyApplication - Starting MunicipalSafetyApplication using Java 17.0.15 with PID 9392 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-19 08:58:35 [main] INFO  o.dromara.MunicipalSafetyApplication - The following 1 profile is active: "prod"
2025-05-19 08:58:40 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-19 08:58:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-19 09:01:32 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-19 09:01:32 [main] INFO  o.dromara.MunicipalSafetyApplication - Starting MunicipalSafetyApplication using Java 17.0.15 with PID 20948 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-19 09:01:32 [main] INFO  o.dromara.MunicipalSafetyApplication - The following 1 profile is active: "dev"
2025-05-19 09:01:37 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-19 09:01:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-19 09:01:38 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-19 09:01:38 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5643052d
2025-05-19 09:01:38 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-19 09:01:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-19 09:01:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-19 09:01:41 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-19 09:01:41 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-19 09:01:42 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-19 09:01:42 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-19 09:01:43 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-19 09:01:50 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-19 09:01:51 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-19 09:01:51 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-19 09:01:51 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-19 09:01:51 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-19 09:01:51 [main] INFO  o.dromara.MunicipalSafetyApplication - Started MunicipalSafetyApplication in 20.227 seconds (process running for 21.169)
2025-05-19 09:01:51 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-19 09:01:51 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-19 09:01:52 [RMI TCP Connection(14)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-19 09:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-05-19 09:01:55 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-05-19 09:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[10]毫秒
2025-05-19 09:01:55 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[325]毫秒
2025-05-19 09:01:55 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-19 09:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-19 09:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-05-19 09:01:55 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:124.152.7.131:'
2025-05-19 09:01:56 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[266]毫秒
2025-05-19 09:02:15 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin ","password":"admin123","rememberMe":false,"uuid":"264b7ae4a4124e61a4a9caa8dea3ecac","code":"4029","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-05-19 09:02:15 [XNIO-1 task-3] INFO  o.d.w.s.impl.PasswordAuthStrategy - 登录用户：admin  不存在.
2025-05-19 09:02:15 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[136]毫秒
2025-05-19 09:02:15 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-19 09:02:15 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:124.152.7.131:'
2025-05-19 09:02:15 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[19]毫秒
2025-05-19 09:02:41 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-19 09:02:41 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[1]毫秒
2025-05-19 09:02:41 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-19 09:02:41 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/auth/code:124.152.7.131:'
2025-05-19 09:02:41 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[8]毫秒
2025-05-19 09:02:56 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"8f06d7b0b7994bc8ba92336afa98878b","code":"9399","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-05-19 09:02:57 [schedule-pool-1] INFO  o.d.s.s.i.SysLogininforServiceImpl - [124.152.7.131]中国|甘肃省|兰州市|联通[admin][Success][登录成功]
2025-05-19 09:02:57 [XNIO-1 task-3] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzbllnaEk0MnowamE1WUNZNGdHWHFQbE5Qb0h3RGgxaCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.5YaRdf_T4Wt534ykgUYgm87PqnmqXByBf89kl-aV128
2025-05-19 09:02:57 [XNIO-1 task-3] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJsY21HQUVqUTdmTm5VbXBYUjZRdFpwZXpNS0JKWWpFMyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.jgA-NO5DjAsFipODNOt5xSCvEi5hVJ1c9gvN2ANqTnI
2025-05-19 09:02:57 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[333]毫秒
2025-05-19 09:02:57 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-19 09:02:58 [XNIO-1 task-3] INFO  o.d.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-05-19 09:02:58 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[803]毫秒
2025-05-19 09:02:58 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-19 09:02:58 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[115]毫秒
2025-05-19 09:03:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-05-19 09:03:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[20]毫秒
2025-05-19 09:03:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/prj_projects_status],无参数
2025-05-19 09:03:00 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/educational_level_code],无参数
2025-05-19 09:03:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/prj_projects_status],耗时:[2]毫秒
2025-05-19 09:03:00 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/educational_level_code],耗时:[5]毫秒
2025-05-19 09:03:00 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/certificate_level],无参数
2025-05-19 09:03:00 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/certificate_level],耗时:[2]毫秒
2025-05-19 09:03:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_dept_type],无参数
2025-05-19 09:03:00 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/politics_status],无参数
2025-05-19 09:03:00 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/politics_status],耗时:[1]毫秒
2025-05-19 09:03:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_dept_type],耗时:[1]毫秒
2025-05-19 09:03:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/personnel_position],无参数
2025-05-19 09:03:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/personnel_position],耗时:[1]毫秒
2025-05-19 09:03:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/enterprise_type],无参数
2025-05-19 09:03:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/enterprise_type],耗时:[2]毫秒
2025-05-19 09:03:00 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/area/tree],无参数
2025-05-19 09:03:00 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/list],参数类型[param],参数:[{"params[dateRange]":[""],"pageSize":["10"],"pageNum":["1"]}]
2025-05-19 09:03:00 [XNIO-1 task-2] INFO  o.d.common.core.utils.area.AreaUtils - 启动加载 AreaUtils 成功，耗时 (21) 毫秒
2025-05-19 09:03:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-19 09:03:00 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-05-19 09:03:00 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzbllnaEk0MnowamE1WUNZNGdHWHFQbE5Qb0h3RGgxaCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.5YaRdf_T4Wt534ykgUYgm87PqnmqXByBf89kl-aV128"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:03:00 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/area/tree],耗时:[100]毫秒
2025-05-19 09:03:01 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/list],耗时:[209]毫秒
2025-05-19 09:03:02 [schedule-pool-2] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-19 09:03:02 [redisson-3-2] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-19 09:04:05 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzbllnaEk0MnowamE1WUNZNGdHWHFQbE5Qb0h3RGgxaCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.5YaRdf_T4Wt534ykgUYgm87PqnmqXByBf89kl-aV128"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:05:09 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzbllnaEk0MnowamE1WUNZNGdHWHFQbE5Qb0h3RGgxaCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.5YaRdf_T4Wt534ykgUYgm87PqnmqXByBf89kl-aV128"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:06:12 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzbllnaEk0MnowamE1WUNZNGdHWHFQbE5Qb0h3RGgxaCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.5YaRdf_T4Wt534ykgUYgm87PqnmqXByBf89kl-aV128"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:06:47 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/1924259686117462018],无参数
2025-05-19 09:07:04 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/1924259686117462018],耗时:[16771]毫秒
2025-05-19 09:07:15 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzbllnaEk0MnowamE1WUNZNGdHWHFQbE5Qb0h3RGgxaCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.5YaRdf_T4Wt534ykgUYgm87PqnmqXByBf89kl-aV128"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:35:39 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-19 09:35:39 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[72]毫秒
2025-05-19 09:35:39 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-19 09:35:39 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[37]毫秒
2025-05-19 09:35:40 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/politics_status],无参数
2025-05-19 09:35:40 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/politics_status],耗时:[2]毫秒
2025-05-19 09:35:40 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/certificate_level],无参数
2025-05-19 09:35:40 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/educational_level_code],无参数
2025-05-19 09:35:40 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/prj_projects_status],无参数
2025-05-19 09:35:40 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-05-19 09:35:40 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_dept_type],无参数
2025-05-19 09:35:40 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/certificate_level],耗时:[7]毫秒
2025-05-19 09:35:40 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/educational_level_code],耗时:[9]毫秒
2025-05-19 09:35:40 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/prj_projects_status],耗时:[10]毫秒
2025-05-19 09:35:40 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[10]毫秒
2025-05-19 09:35:40 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_dept_type],耗时:[16]毫秒
2025-05-19 09:35:41 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/personnel_position],无参数
2025-05-19 09:35:41 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/personnel_position],耗时:[2]毫秒
2025-05-19 09:35:41 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/enterprise_type],无参数
2025-05-19 09:35:41 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/area/tree],无参数
2025-05-19 09:35:41 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/enterprise_type],耗时:[2]毫秒
2025-05-19 09:35:41 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-19 09:35:41 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-05-19 09:35:41 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/list],参数类型[param],参数:[{"params[dateRange]":[""],"pageSize":["10"],"pageNum":["1"]}]
2025-05-19 09:35:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzbllnaEk0MnowamE1WUNZNGdHWHFQbE5Qb0h3RGgxaCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.5YaRdf_T4Wt534ykgUYgm87PqnmqXByBf89kl-aV128"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:35:41 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/area/tree],耗时:[46]毫秒
2025-05-19 09:35:41 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/list],耗时:[84]毫秒
2025-05-19 09:36:22 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/1921481309755801601],无参数
2025-05-19 09:36:22 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/1921481309755801601],耗时:[72]毫秒
2025-05-19 09:36:26 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /attendance/attRecord/getPersonAtt/1921866659637698561/2025-05-19],无参数
2025-05-19 09:36:26 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /attendance/attRecord/getPersonAttByMonth/1921866659637698561/2025-05],无参数
2025-05-19 09:36:26 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /attendance/attRecord/getPersonAtt/1921866659637698561/2025-05-19],耗时:[120]毫秒
2025-05-19 09:36:26 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /attendance/attRecord/getPersonAttByMonth/1921866659637698561/2025-05],耗时:[170]毫秒
2025-05-19 09:36:44 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzbllnaEk0MnowamE1WUNZNGdHWHFQbE5Qb0h3RGgxaCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.5YaRdf_T4Wt534ykgUYgm87PqnmqXByBf89kl-aV128"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:37:14 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/1924259686117462018],无参数
2025-05-19 09:37:19 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/1924259686117462018],耗时:[5629]毫秒
2025-05-19 09:37:48 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzbllnaEk0MnowamE1WUNZNGdHWHFQbE5Qb0h3RGgxaCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.5YaRdf_T4Wt534ykgUYgm87PqnmqXByBf89kl-aV128"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:37:53 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/1921481309755801601],无参数
2025-05-19 09:37:53 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/1921481309755801601],耗时:[68]毫秒
2025-05-19 09:38:02 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/1924259686117462018],无参数
2025-05-19 09:38:02 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/1924259686117462018],耗时:[67]毫秒
2025-05-19 09:39:41 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-05-19 09:39:41 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[1]毫秒
2025-05-19 09:39:41 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-05-19 09:39:41 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[0]毫秒
2025-05-19 09:39:42 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-19 09:39:42 [XNIO-1 task-6] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:124.152.7.131:'
2025-05-19 09:39:42 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[16]毫秒
2025-05-19 09:39:42 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-19 09:39:42 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-05-19 09:39:53 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"4dc165f9c3184dd7b73accb376b3daa7","code":"9954","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-05-19 09:39:53 [schedule-pool-1] INFO  o.d.s.s.i.SysLogininforServiceImpl - [124.152.7.131]中国|甘肃省|兰州市|联通[admin][Success][登录成功]
2025-05-19 09:39:53 [XNIO-1 task-6] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJDN2c0WHNtUlo5d2lJaVlqdlhoRDBMOUVzNjZKbVF5VCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.LbDuvQ6NLdUn8IMyLyVjHBBiwvP-S3XOeDEHFD00M5o
2025-05-19 09:39:53 [XNIO-1 task-6] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJrNnQySkxrbUxUcW9YUDh4OXhsbjYwMDM3ZEY1WjRjMiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.3CQfbfKgcryoy6kk9HDhLYLu5524_77xL2dOxqAwgOg
2025-05-19 09:39:53 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[238]毫秒
2025-05-19 09:39:53 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-05-19 09:39:53 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[48]毫秒
2025-05-19 09:39:53 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-05-19 09:39:53 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[30]毫秒
2025-05-19 09:39:55 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-05-19 09:39:55 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[1]毫秒
2025-05-19 09:39:55 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/prj_projects_status],无参数
2025-05-19 09:39:55 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_dept_type],无参数
2025-05-19 09:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/educational_level_code],无参数
2025-05-19 09:39:55 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/certificate_level],无参数
2025-05-19 09:39:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/politics_status],无参数
2025-05-19 09:39:55 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/prj_projects_status],耗时:[10]毫秒
2025-05-19 09:39:55 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_dept_type],耗时:[8]毫秒
2025-05-19 09:39:55 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/certificate_level],耗时:[8]毫秒
2025-05-19 09:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/educational_level_code],耗时:[9]毫秒
2025-05-19 09:39:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/politics_status],耗时:[9]毫秒
2025-05-19 09:39:56 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/personnel_position],无参数
2025-05-19 09:39:56 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/personnel_position],耗时:[2]毫秒
2025-05-19 09:39:56 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-19 09:39:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/enterprise_type],无参数
2025-05-19 09:39:56 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[1]毫秒
2025-05-19 09:39:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/enterprise_type],耗时:[1]毫秒
2025-05-19 09:39:56 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/area/tree],无参数
2025-05-19 09:39:56 [XNIO-1 task-6] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJDN2c0WHNtUlo5d2lJaVlqdlhoRDBMOUVzNjZKbVF5VCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.LbDuvQ6NLdUn8IMyLyVjHBBiwvP-S3XOeDEHFD00M5o"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:39:56 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/list],参数类型[param],参数:[{"params[dateRange]":[""],"pageSize":["10"],"pageNum":["1"]}]
2025-05-19 09:39:56 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/area/tree],耗时:[46]毫秒
2025-05-19 09:39:56 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/list],耗时:[86]毫秒
2025-05-19 09:39:58 [schedule-pool-2] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-19 09:39:58 [redisson-3-3] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-19 09:39:58 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzbllnaEk0MnowamE1WUNZNGdHWHFQbE5Qb0h3RGgxaCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.5YaRdf_T4Wt534ykgUYgm87PqnmqXByBf89kl-aV128"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:39:58 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[5]毫秒
2025-05-19 09:39:58 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/1924259686117462018],无参数
2025-05-19 09:39:58 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/1924259686117462018],耗时:[65]毫秒
2025-05-19 09:40:11 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-05-19 09:40:11 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[2]毫秒
2025-05-19 09:40:11 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-05-19 09:40:11 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[0]毫秒
2025-05-19 09:40:11 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-19 09:40:11 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-05-19 09:40:11 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-05-19 09:40:11 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:124.152.7.131:'
2025-05-19 09:40:11 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[10]毫秒
2025-05-19 09:40:13 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/enterprise_type],无参数
2025-05-19 09:40:13 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/enterprise_type],耗时:[1]毫秒
2025-05-19 09:40:13 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-05-19 09:40:13 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/auth/code:124.152.7.131:'
2025-05-19 09:40:13 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[14]毫秒
2025-05-19 09:40:13 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/division/getProvinceList],无参数
2025-05-19 09:40:13 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/division/getProvinceList],耗时:[63]毫秒
2025-05-19 09:40:39 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/1921481309755801601],无参数
2025-05-19 09:40:39 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/1921481309755801601],耗时:[68]毫秒
2025-05-19 09:40:41 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /attendance/attRecord/getPersonAtt/1921866659637698561/2025-05-19],无参数
2025-05-19 09:40:42 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /attendance/attRecord/getPersonAttByMonth/1921866659637698561/2025-05],无参数
2025-05-19 09:40:42 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /attendance/attRecord/getPersonAtt/1921866659637698561/2025-05-19],耗时:[66]毫秒
2025-05-19 09:40:42 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /attendance/attRecord/getPersonAttByMonth/1921866659637698561/2025-05],耗时:[137]毫秒
2025-05-19 09:41:02 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJDN2c0WHNtUlo5d2lJaVlqdlhoRDBMOUVzNjZKbVF5VCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.LbDuvQ6NLdUn8IMyLyVjHBBiwvP-S3XOeDEHFD00M5o"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:41:12 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /attendance/attRecord/getPersonAtt/1921866659637698561/2025-05-15],无参数
2025-05-19 09:41:12 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /attendance/attRecord/getPersonAtt/1921866659637698561/2025-05-15],耗时:[248]毫秒
2025-05-19 09:42:05 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJDN2c0WHNtUlo5d2lJaVlqdlhoRDBMOUVzNjZKbVF5VCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.LbDuvQ6NLdUn8IMyLyVjHBBiwvP-S3XOeDEHFD00M5o"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:43:08 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJDN2c0WHNtUlo5d2lJaVlqdlhoRDBMOUVzNjZKbVF5VCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.LbDuvQ6NLdUn8IMyLyVjHBBiwvP-S3XOeDEHFD00M5o"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:44:12 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJDN2c0WHNtUlo5d2lJaVlqdlhoRDBMOUVzNjZKbVF5VCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.LbDuvQ6NLdUn8IMyLyVjHBBiwvP-S3XOeDEHFD00M5o"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:45:15 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJDN2c0WHNtUlo5d2lJaVlqdlhoRDBMOUVzNjZKbVF5VCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.LbDuvQ6NLdUn8IMyLyVjHBBiwvP-S3XOeDEHFD00M5o"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:46:18 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJDN2c0WHNtUlo5d2lJaVlqdlhoRDBMOUVzNjZKbVF5VCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuaUv-W6nCIsImRlcHRDYXRlZ29yeSI6IiJ9.LbDuvQ6NLdUn8IMyLyVjHBBiwvP-S3XOeDEHFD00M5o"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-05-19 09:46:20 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/list],参数类型[param],参数:[{"params[dateRange]":[""],"pageSize":["10"],"pageNum":["1"]}]
2025-05-19 09:46:20 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/area/tree],无参数
2025-05-19 09:46:20 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/list],耗时:[81]毫秒
2025-05-19 09:46:20 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/area/tree],耗时:[54]毫秒
2025-05-19 09:46:50 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/list],参数类型[param],参数:[{"params[dateRange]":[""],"pageSize":["10"],"pageNum":["1"]}]
2025-05-19 09:46:51 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/area/tree],无参数
2025-05-19 09:46:51 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/area/tree],耗时:[40]毫秒
2025-05-19 09:46:51 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/list],耗时:[83]毫秒
2025-05-19 09:47:01 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/area/tree],无参数
2025-05-19 09:47:01 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/list],参数类型[param],参数:[{"params[dateRange]":[""],"pageSize":["10"],"pageNum":["1"]}]
2025-05-19 09:47:01 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/area/tree],耗时:[42]毫秒
2025-05-19 09:47:02 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/list],耗时:[80]毫秒

<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="区划代码" prop="divisionCode">
              <el-input v-model="queryParams.divisionCode" placeholder="请输入区划代码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="区划名称" prop="divisionName">
              <el-input v-model="queryParams.divisionName" placeholder="请输入区划名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="区划级别" prop="level">
              <el-select v-model="queryParams.level" placeholder="请选择区划级别" clearable>
                <el-option v-for="dict in level" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd()" v-hasPermi="['system:division:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="handleToggleExpandAll">展开/折叠</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>
      <el-table
        ref="divisionTableRef"
        v-loading="loading"
        :data="divisionList"
        row-key="divisionCode"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column label="区划代码" prop="divisionCode" />
        <el-table-column label="区划名称" align="center" prop="divisionName" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:division:edit']" />
            </el-tooltip>
            <el-tooltip content="新增" placement="top">
              <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['system:division:add']" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:division:remove']" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 添加或修改行政区划对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="divisionFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="区划代码" prop="divisionCode">
          <el-input v-model="form.divisionCode" placeholder="请输入区划代码" />
        </el-form-item>
        <el-form-item label="区划名称" prop="divisionName">
          <el-input v-model="form.divisionName" placeholder="请输入区划名称" />
        </el-form-item>
        <el-form-item label="区划级别" prop="level">
          <el-select v-model="form.level" placeholder="请选择区划级别">
            <el-option
              v-for="dict in level"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="父级区划代码" prop="parentCode">
          <el-tree-select
            v-model="form.parentCode"
            :data="divisionOptions"
            :props="{ value: 'divisionCode', label: 'divisionName', children: 'children' }"
            value-key="divisionCode"
            placeholder="请选择父级区划代码"
            check-strictly
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Division" lang="ts">
import { listDivision, getDivision, delDivision, addDivision, updateDivision } from "@/api/system/division";
import { DivisionVO, DivisionQuery, DivisionForm } from '@/api/system/division/types';

type DivisionOption = {
  divisionCode: number;
  divisionName: string;
  children?: DivisionOption[];
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance;;

const { level } = toRefs<any>(proxy?.useDict('level'));

const divisionList = ref<DivisionVO[]>([]);
const divisionOptions = ref<DivisionOption[]>([]);
const buttonLoading = ref(false);
const showSearch = ref(false);
const isExpandAll = ref(true);
const loading = ref(false);

const queryFormRef = ref<ElFormInstance>();
const divisionFormRef = ref<ElFormInstance>();
const divisionTableRef = ref<ElTableInstance>()

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
});


const initFormData: DivisionForm = {
    divisionCode: undefined,
    divisionName: undefined,
    level: undefined,
    parentCode: undefined,
}

const data = reactive<PageData<DivisionForm, DivisionQuery>>({
  form: {...initFormData},
  queryParams: {
    divisionCode: undefined,
    divisionName: undefined,
    level: undefined,
    parentCode: undefined,
    params: {
    }
  },
  rules: {
    divisionCode: [
      { required: true, message: "区划代码不能为空", trigger: "blur" }
    ],
    divisionName: [
      { required: true, message: "区划名称不能为空", trigger: "blur" }
    ],
    level: [
      { required: true, message: "区划级别不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询行政区划列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDivision(queryParams.value);
  const data = proxy?.handleTree<DivisionVO>(res.data, "divisionCode", "parentCode");
  if (data) {
    divisionList.value = data;
    loading.value = false;
  }
}

/** 查询行政区划下拉树结构 */
const getTreeselect = async () => {
  const res = await listDivision();
  divisionOptions.value = [];
  const data: DivisionOption = { divisionCode: 0, divisionName: '顶级节点', children: [] };
  data.children = proxy?.handleTree<DivisionOption>(res.data, "divisionCode", "parentCode");
  divisionOptions.value.push(data);
}

// 取消按钮
const cancel = () => {
  reset();
  dialog.visible = false;
}

// 表单重置
const reset = () => {
  form.value = {...initFormData}
  divisionFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 新增按钮操作 */
const handleAdd = (row?: DivisionVO) => {
  reset();
  getTreeselect();
  if (row != null && row.divisionCode) {
    form.value.parentCode = row.divisionCode;
  } else {
    form.value.parentCode = 0;
  }
  dialog.visible = true;
  dialog.title = "添加行政区划";
}

/** 展开/折叠操作 */
const handleToggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value;
  toggleExpandAll(divisionList.value, isExpandAll.value)
}

/** 展开/折叠操作 */
const toggleExpandAll = (data: DivisionVO[], status: boolean) => {
  data.forEach((item) => {
    divisionTableRef.value?.toggleRowExpansion(item, status)
    if (item.children && item.children.length > 0) toggleExpandAll(item.children, status)
  })
}

/** 修改按钮操作 */
const handleUpdate = async (row: DivisionVO) => {
  reset();
  await getTreeselect();
  if (row != null) {
    form.value.parentCode = row.parentCode;
  }
  const res = await getDivision(row.divisionId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改行政区划";
}

/** 提交按钮 */
const submitForm = () => {
  divisionFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.divisionId) {
        await updateDivision(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addDivision(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row: DivisionVO) => {
  await proxy?.$modal.confirm('是否确认删除行政区划编号为"' + row.divisionId + '"的数据项？');
  loading.value = true;
  await delDivision(row.divisionId).finally(() => loading.value = false);
  await getList();
  proxy?.$modal.msgSuccess("删除成功");
}

onMounted(() => {
  getList();
});
</script>

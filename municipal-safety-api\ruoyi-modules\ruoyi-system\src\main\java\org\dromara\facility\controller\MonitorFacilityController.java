package org.dromara.facility.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.facility.domain.vo.MonitorFacilityVo;
import org.dromara.facility.domain.bo.MonitorFacilityBo;
import org.dromara.facility.service.IMonitorFacilityService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 监测设备
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/monitorFacility")
public class MonitorFacilityController extends BaseController {

    private final IMonitorFacilityService monitorFacilityService;

    /**
     * 查询监测设备列表
     */
    @GetMapping("/list")
    public TableDataInfo<MonitorFacilityVo> list(MonitorFacilityBo bo, PageQuery pageQuery) {
        return monitorFacilityService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出监测设备列表
     */
    @Log(title = "监测设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MonitorFacilityBo bo, HttpServletResponse response) {
        List<MonitorFacilityVo> list = monitorFacilityService.queryList(bo);
        ExcelUtil.exportExcel(list, "监测设备", MonitorFacilityVo.class, response);
    }

    /**
     * 获取监测设备详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<MonitorFacilityVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long id) {
        return R.ok(monitorFacilityService.queryById(id));
    }

    /**
     * 新增监测设备
     */
    @Log(title = "监测设备", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MonitorFacilityBo bo) {
        return toAjax(monitorFacilityService.insertByBo(bo));
    }

    /**
     * 修改监测设备
     */
    @Log(title = "监测设备", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MonitorFacilityBo bo) {
        return toAjax(monitorFacilityService.updateByBo(bo));
    }

    /**
     * 删除监测设备
     *
     * @param ids 主键串
     */
    @Log(title = "监测设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(monitorFacilityService.deleteWithValidByIds(List.of(ids), true));
    }
}

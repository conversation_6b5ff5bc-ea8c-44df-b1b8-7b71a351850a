package org.dromara.ai.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.ai.domain.AiHazAnalysisTasks;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 智能隐患分析任务业务对象 ai_haz_analysis_tasks
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiHazAnalysisTasks.class, reverseConvertGenerate = false)
public class AiHazAnalysisTasksBo extends BaseEntity {

    /**
     * 分析任务ID
     */
    @NotNull(message = "分析任务ID不能为空", groups = {EditGroup.class})
    private Long taskId;

    /**
     * 关联项目ID
     */
    @NotNull(message = "关联项目ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long projectId;

    /**
     * 预警来源类型 CAMERA[摄像头] APP[用户App上报]
     */
    private String sourceType;

    /**
     * 提交分析的专家用户ID
     */
    @NotNull(message = "提交分析的专家用户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long expertUserId;

    /**
     * 照片上传时间
     */
    private Date uploadTime;

    /**
     * 上传的照片文档ID
     */
    private Long photoDocumentId;

    /**
     * 拍照时GPS坐标
     */
    private String gpsLocation;

    /**
     * 拍照位置文字描述
     */
    private String locationDescription;

    /**
     * AI模型识别输出结果
     */
    private String aiRecognitionRawResult;

    /**
     * AI分析后返回的带标注的照片文档ID
     */
    private Long aiPhotoDocumentId;

    /**
     * 关联的已知危大工程项ID
     */
    private Long itemId;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 复检状态（PENDING_RECHECK[待复检]、FINISH_RECHECK[复检完成]）
     */
    private String recheckStatus;

    /**
     * 因此次分析发现问题而生成的工单ID
     */
    private Long relatedWorkOrderId;

    private String remark;

}

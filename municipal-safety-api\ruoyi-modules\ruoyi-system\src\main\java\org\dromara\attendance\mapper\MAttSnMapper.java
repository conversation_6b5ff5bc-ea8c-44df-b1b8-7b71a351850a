package org.dromara.attendance.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.attendance.domain.MAttSn;
import org.dromara.attendance.domain.bo.MAttSnBo;
import org.dromara.attendance.domain.vo.MAttSnVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 考勤设备Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface MAttSnMapper extends BaseMapperPlus<MAttSn, MAttSnVo> {

    List<MAttSnVo> selectMAttSnList(MAttSnBo bo);

    MAttSnVo selectMAttSnBySn(@Param("sn") String sn);

    List<MAttSnVo> selectMAttSnByProjectId(@Param("projectId") Long projectId);
}

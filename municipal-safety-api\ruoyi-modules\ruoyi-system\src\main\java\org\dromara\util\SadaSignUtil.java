package org.dromara.util;

import cn.hutool.core.codec.Base64;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.io.*;

/**
 * <AUTHOR>
 * @date 2025/7/29 16:35
 * @Description TODO
 * @Version 1.0
 */
@Component
public class SadaSignUtil {

    private ScriptEngine engine;

    @PostConstruct
    public void init() throws FileNotFoundException, ScriptException {
        engine = new ScriptEngineManager().getEngineByName("graal.js");
        InputStream resource = getClass().getResourceAsStream("/word/sadaOpen.js");
        engine.eval(new InputStreamReader(resource));
    }

    public String getUrl(String uniqueId) {
        //执行js代码
        try {
            //是否可调用方法
            if (engine instanceof Invocable) {
                Invocable in = (Invocable) engine;
                //执行js函数
                String result = (String) in.invokeFunction("signatureUrl", uniqueId);

                String between = StringUtils.substringBetween(result, "gs_uid?sign=", "&openParty=");

                return result.replace(between, Base64.encode(between));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}

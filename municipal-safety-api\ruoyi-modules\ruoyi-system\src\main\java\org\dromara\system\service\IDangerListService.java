package org.dromara.system.service;

import org.dromara.system.domain.vo.DangerListVo;
import org.dromara.system.domain.bo.DangerListBo;

import java.util.Collection;
import java.util.List;

/**
 * dangerListService接口
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
public interface IDangerListService {

    /**
     * 查询dangerList
     *
     * @param dangerId 主键
     * @return dangerList
     */
    DangerListVo queryById(Long dangerId);


    /**
     * 查询符合条件的dangerList列表
     *
     * @param bo 查询条件
     * @return dangerList列表
     */
    List<DangerListVo> queryList(DangerListBo bo);

    /**
     * 新增dangerList
     *
     * @param bo dangerList
     * @return 是否新增成功
     */
    Boolean insertByBo(DangerListBo bo);

    /**
     * 修改dangerList
     *
     * @param bo dangerList
     * @return 是否修改成功
     */
    Boolean updateByBo(DangerListBo bo);

    /**
     * 校验并批量删除dangerList信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

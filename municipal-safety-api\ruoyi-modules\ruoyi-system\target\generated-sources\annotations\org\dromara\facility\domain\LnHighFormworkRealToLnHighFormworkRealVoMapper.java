package org.dromara.facility.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.bo.LnHighFormworkRealBoToLnHighFormworkRealMapper;
import org.dromara.facility.domain.vo.LnHighFormworkRealVo;
import org.dromara.facility.domain.vo.LnHighFormworkRealVoToLnHighFormworkRealMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {LnHighFormworkRealVoToLnHighFormworkRealMapper.class,LnHighFormworkRealBoToLnHighFormworkRealMapper.class},
    imports = {}
)
public interface LnHighFormworkRealToLnHighFormworkRealVoMapper extends BaseMapper<LnHighFormworkReal, LnHighFormworkRealVo> {
}

package org.dromara.flow.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 特殊预警对象 prj_hazardous_items_special_warning
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prj_hazardous_items_special_warning")
public class PrjHazardousItemsSpecialWarning extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 特殊预警id
     */
    @TableId(value = "warning_id")
    private Long warningId;

    /**
     * 流程业务id
     */
    private String taskId;

    /**
     * 预警原因（质监站可补充）
     */
    private String reason;

    /**
     * 预警类型(字典special_warning_type)
     */
    private String reasonType;

    /**
     * 删除标志 (字典: 0[存在], 1[删除])
     */
    @TableLogic
    private String delFlag;
}

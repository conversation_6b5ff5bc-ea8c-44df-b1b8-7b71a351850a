{"doc": " 扬尘数据Service接口\n\n <AUTHOR>\n @date 2025-07-24\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询扬尘数据\n\n @param id 主键\n @return 扬尘数据\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.facility.domain.bo.JlDustRealBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询扬尘数据列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 扬尘数据分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.facility.domain.bo.JlDustRealBo"], "doc": " 查询符合条件的扬尘数据列表\n\n @param bo 查询条件\n @return 扬尘数据列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.facility.domain.bo.JlDustRealBo"], "doc": " 新增扬尘数据\n\n @param bo 扬尘数据\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.facility.domain.bo.JlDustRealBo"], "doc": " 修改扬尘数据\n\n @param bo 扬尘数据\n @return 是否修改成功\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除扬尘数据信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
package org.dromara.facility.domain.bo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson2.annotation.JSONField;
import org.dromara.facility.domain.LnEnergy;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 绿能用电监测业务对象 ln_energy
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LnEnergy.class, reverseConvertGenerate = false)
public class LnEnergyBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * A相电压 0.1V
     */
    private Long ua;

    /**
     * B相电压 0.1V
     */
    private Long ub;

    /**
     * C相电压 0.1V
     */
    private Long uc;

    /**
     * A相电流 0.1V
     */
    private Long ia;

    /**
     * B相电流 0.1V
     */
    private Long ib;

    /**
     * C相电流 0.1V
     */
    private Long ic;

    /**
     * 漏电流 0.1V
     */
    private Long il;

    /**
     * A相温度 0.1°
     */
    private Long ta;

    /**
     * B相温度 0.1°
     */
    private Long tb;

    /**
     * C相温度 0.1°
     */
    private Long tc;

    /**
     * N相温度 0.1°
     */
    private Long tn;

    /**
     * 采集时间
     */
    @JSONField(format = DatePattern.NORM_DATETIME_PATTERN)
    private Date recordTime;

    /**
     * 设备编号
     */
    private String deviceNo;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

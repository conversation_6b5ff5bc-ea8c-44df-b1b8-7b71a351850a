<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="dustRealList">
      <el-table-column :width="commonWidth" label="扬尘设备唯一编号" width="140" align="center" prop="mn" />
      <el-table-column :width="commonWidth" label="设备时间" align="center" prop="datatime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.datatime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="噪音平均值" align="center" prop="b03Avg" />
      <el-table-column :width="commonWidth" label="噪音传感器状态" align="center" prop="b03Flag">
        <template v-slot="scope">
          {{ statusConvert(scope.row.b03Flag) }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="pm2.5" align="center" prop="pm25Avg">
        <template v-slot="scope"> {{ scope.row.pm25Avg }}μg/m³ </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="pm2.5传感器状态" align="center" prop="pm25Flag">
        <template v-slot="scope">
          {{ statusConvert(scope.row.pm25Flag) }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="pm10" align="center" prop="pm10Avg">
        <template v-slot="scope"> {{ scope.row.pm10Avg }}μg/m³ </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="pm10传感器状态" align="center" prop="pm10Flag">
        <template v-slot="scope">
          {{ statusConvert(scope.row.pm10Flag) }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="风速" align="center" prop="w02Avg">
        <template v-slot="scope"> {{ scope.row.w02Avg }}米/秒 </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="风速传感器状态" align="center" prop="w02Flag">
        <template v-slot="scope">
          {{ statusConvert(scope.row.w02Flag) }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="风向" align="center" prop="w01Avg" />
      <el-table-column :width="commonWidth" label="风向传感器状态" align="center" prop="w01Flag">
        <template v-slot="scope">
          {{ statusConvert(scope.row.w01Flag) }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="温度" align="center" prop="t01Avg" />
      <el-table-column :width="commonWidth" label="温度传感器状态" align="center" prop="t01Flag">
        <template v-slot="scope">
          {{ statusConvert(scope.row.t01Flag) }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="湿度" align="center" prop="h01Avg" />
      <el-table-column :width="commonWidth" label="湿度传感器状态" align="center" prop="h01Flag">
        <template v-slot="scope">
          {{ statusConvert(scope.row.h01Flag) }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="总悬浮微粒" align="center" prop="tspAvg" />
      <el-table-column :width="commonWidth" label="总悬浮微粒传感器状态" align="center" prop="tspFlag">
        <template v-slot="scope">
          {{ statusConvert(scope.row.tspFlag) }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="臭氧" align="center" prop="o3Avg" />
      <el-table-column :width="commonWidth" label="臭氧状态" align="center" prop="o3Flag">
        <template v-slot="scope">
          {{ statusConvert(scope.row.o3Flag) }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="一氧化碳" align="center" prop="coAvg" />
      <el-table-column :width="commonWidth" label="一氧化碳传感器状态" align="center" prop="coFlag">
        <template v-slot="scope">
          {{ statusConvert(scope.row.coFlag) }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="二氧化硫" align="center" prop="so2Avg">
        <template v-slot="scope"> {{ scope.row.so2Avg }}μg/m³ </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="二氧化硫传感器状态" align="center" prop="so2Flag">
        <template v-slot="scope">
          {{ statusConvert(scope.row.so2Flag) }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="二氧化氮" align="center" prop="no2Avg">
        <template v-slot="scope"> {{ scope.row.no2Avg }}μg/m³ </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="二氧化氮传感器状态" align="center" prop="no2Flag">
        <template v-slot="scope">
          {{ statusConvert(scope.row.no2Flag) }}
        </template>
      </el-table-column>
      <el-table-column :width="commonWidth" label="大气压" align="center" prop="a01006Rtd" />
      <el-table-column :width="commonWidth" label="大气压传感器状态" align="center" prop="a01006Flag">
        <template v-slot="scope">
          {{ statusConvert(scope.row.a01006Flag) }}
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script>
import { listHjjc } from "@/api/projects/facility/index";
export default {
  name: "DustReal",
  props: {
    devNo: {
      type: String,
      default: "",
    },
  },
  data () {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 扬尘数据表格数据
      dustRealList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        devNo: null
      },
      commonWidth: "140",
      stype: 41,
    };
  },
  mounted () {
    this.getList();
  },
  methods: {
    statusConvert (val) {
      return val == "N" ? "正常" : "异常";
    },
    /** 查询扬尘数据列表 */
    async getList () {
      this.dustRealList = [];
      this.loading = true;
      this.queryParams.devNo = this.devNo;
      const res = await listHjjc(this.queryParams);
      this.dustRealList = res.rows;
      this.total = res.total;
      this.loading = false;
    },
  },
};
</script>

{"doc": " 监控管理Service业务层处理\n\n <AUTHOR>\n @date 2025-05-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询监控管理\n\n @param monitoId 主键\n @return 监控管理\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.monito.domain.bo.DeviceMonitoBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询监控管理列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 监控管理分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.monito.domain.bo.DeviceMonitoBo"], "doc": " 查询符合条件的监控管理列表\n\n @param bo 查询条件\n @return 监控管理列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.monito.domain.bo.DeviceMonitoBo"], "doc": " 新增监控管理\n\n @param bo 监控管理\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.monito.domain.bo.DeviceMonitoBo"], "doc": " 修改监控管理\n\n @param bo 监控管理\n @return 是否修改成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.monito.domain.DeviceMonito"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除监控管理信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
{"doc": " 社交登录配置\n\n <AUTHOR>\n", "fields": [{"name": "clientId", "doc": " 应用 ID\n"}, {"name": "clientSecret", "doc": " 应用密钥\n"}, {"name": "redirectUri", "doc": " 回调地址\n"}, {"name": "unionId", "doc": " 是否获取unionId\n"}, {"name": "codingGroupName", "doc": " Coding 企业名称\n"}, {"name": "alipayPublicKey", "doc": " 支付宝公钥\n"}, {"name": "agentId", "doc": " 企业微信应用ID\n"}, {"name": "stackOverflowKey", "doc": " stackoverflow api key\n"}, {"name": "deviceId", "doc": " 设备ID\n"}, {"name": "clientOsType", "doc": " 客户端系统类型\n"}, {"name": "serverUrl", "doc": " maxkey 服务器地址\n"}, {"name": "scopes", "doc": " 请求范围\n"}], "enumConstants": [], "methods": [], "constructors": []}
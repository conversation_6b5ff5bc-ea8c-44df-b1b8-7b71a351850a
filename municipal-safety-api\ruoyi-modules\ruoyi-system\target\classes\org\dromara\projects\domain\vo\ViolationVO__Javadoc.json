{"doc": " <AUTHOR>\n @date 2025/5/13 16:48\n @Description TODO\n @Version 1.0\n", "fields": [{"name": "resultId", "doc": " 问题id\n"}, {"name": "violation", "doc": " 隐患描述\n"}, {"name": "regulation", "doc": "  参考条款\n"}, {"name": "level", "doc": " 级别\n"}, {"name": "measure", "doc": "  整改意见\n"}, {"name": "commentStatus", "doc": " 预警是否真实 1是 0否\n"}, {"name": "personComments", "doc": " 质监站整改意见\n"}, {"name": "abarbeitungImg", "doc": " 已整改图片\n"}, {"name": "noAbarbeitung", "doc": " 未整改图片\n"}, {"name": "abarbeitungComments", "doc": " 整改说明\n"}, {"name": "commentsId", "doc": " 整改问题id\n"}], "enumConstants": [], "methods": [], "constructors": []}
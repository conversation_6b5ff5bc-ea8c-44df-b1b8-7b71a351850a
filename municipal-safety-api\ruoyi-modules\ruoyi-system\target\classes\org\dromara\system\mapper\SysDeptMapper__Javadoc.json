{"doc": " 部门管理 数据层\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectDeptList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 查询部门管理数据\n\n @param queryWrapper 查询条件\n @return 部门信息集合\n"}, {"name": "selectPageDeptList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 分页查询部门管理数据\n\n @param queryWrapper 查询条件\n @return 部门信息集合\n"}, {"name": "countDeptById", "paramTypes": ["java.lang.Long"], "doc": " 统计指定部门ID的部门数量\n\n @param deptId 部门ID\n @return 该部门ID的部门数量\n"}, {"name": "selectListByParentId", "paramTypes": ["java.lang.Long"], "doc": " 根据父部门ID查询其所有子部门的列表\n\n @param parentId 父部门ID\n @return 包含子部门的列表\n"}, {"name": "selectDeptListByRoleId", "paramTypes": ["java.lang.Long", "boolean"], "doc": " 根据角色ID查询部门树信息\n\n @param roleId            角色ID\n @param deptCheckStrictly 部门树选择项是否关联显示\n @return 选中部门列表\n"}], "constructors": []}
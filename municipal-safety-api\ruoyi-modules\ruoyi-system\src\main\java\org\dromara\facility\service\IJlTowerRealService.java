package org.dromara.facility.service;

import org.dromara.facility.domain.vo.JlTowerRealVo;
import org.dromara.facility.domain.bo.JlTowerRealBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

/**
 * 塔机实时数据Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IJlTowerRealService extends BaseFacilityHandle {

    /**
     * 查询塔机实时数据
     *
     * @param id 主键
     * @return 塔机实时数据
     */
    JlTowerRealVo queryById(Long id);

    /**
     * 分页查询塔机实时数据列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 塔机实时数据分页列表
     */
    TableDataInfo<JlTowerRealVo> queryPageList(JlTowerRealBo bo, PageQuery pageQuery);

    /**
     * 新增塔机实时数据
     *
     * @param bo 塔机实时数据
     * @return 是否新增成功
     */
    Boolean insertByBo(JlTowerRealBo bo);

}

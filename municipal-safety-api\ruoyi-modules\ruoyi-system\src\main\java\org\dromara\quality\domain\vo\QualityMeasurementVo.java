package org.dromara.quality.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.quality.domain.QualityMeasurement;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 实测实量视图对象 quality_measurement
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = QualityMeasurement.class)
public class QualityMeasurementVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 测量ID
     */
    @ExcelProperty(value = "测量ID")
    private Long measurementId;

    /**
     * 测量时间
     */
    @ExcelProperty(value = "测量时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date measurementTime;

    /**
     * 测量事项
     */
    @ExcelProperty(value = "测量事项")
    private String measurementItem;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String deviceCode;

    /**
     * 测量结果
     */
    @ExcelProperty(value = "测量结果")
    private String measurementResult;

    /**
     * 是否合规
     */
    @ExcelProperty(value = "是否合规", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "quality_compliant")
    private String isCompliant;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String projectName;

    /**
     * 测量位置
     */
    @ExcelProperty(value = "测量位置")
    private String measurementLocation;

    /**
     * 标准值
     */
    @ExcelProperty(value = "标准值")
    private String standardValue;

    /**
     * 偏差值
     */
    @ExcelProperty(value = "偏差值")
    private String deviationValue;

    /**
     * 测量人员
     */
    @ExcelProperty(value = "测量人员")
    private String measurementPerson;

    /**
     * 是否标记隐患
     */
    @ExcelProperty(value = "是否标记隐患", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String isHazardMarked;

    /**
     * 隐患描述
     */
    @ExcelProperty(value = "隐患描述")
    private String hazardDescription;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "quality_measurement_status")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private Long createBy;

    /**
     * 创建人账号
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    @ExcelProperty(value = "创建人账号")
    private String createByName;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private Long updateBy;

    /**
     * 更新人账号
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "updateBy")
    @ExcelProperty(value = "更新人账号")
    private String updateByName;

}

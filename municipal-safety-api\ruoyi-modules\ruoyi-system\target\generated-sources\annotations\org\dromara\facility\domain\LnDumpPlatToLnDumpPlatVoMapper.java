package org.dromara.facility.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.bo.LnDumpPlatBoToLnDumpPlatMapper;
import org.dromara.facility.domain.vo.LnDumpPlatVo;
import org.dromara.facility.domain.vo.LnDumpPlatVoToLnDumpPlatMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {LnDumpPlatVoToLnDumpPlatMapper.class,LnDumpPlatBoToLnDumpPlatMapper.class},
    imports = {}
)
public interface LnDumpPlatToLnDumpPlatVoMapper extends BaseMapper<LnDumpPlat, LnDumpPlatVo> {
}

{"doc": " attPersonService接口\n\n <AUTHOR>\n @date 2025-06-10\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询attPerson\n\n @param id 主键\n @return attPerson\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.attendance.domain.bo.MAttPersonBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询attPerson列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return attPerson分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.attendance.domain.bo.MAttPersonBo"], "doc": " 查询符合条件的attPerson列表\n\n @param bo 查询条件\n @return attPerson列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.attendance.domain.bo.MAttPersonBo"], "doc": " 新增attPerson\n\n @param bo attPerson\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.attendance.domain.bo.MAttPersonBo"], "doc": " 修改attPerson\n\n @param bo attPerson\n @return 是否修改成功\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除attPerson信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
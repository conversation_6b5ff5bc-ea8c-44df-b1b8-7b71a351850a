{"doc": " 质监站隐患清单整改\n\n <AUTHOR>\n @date 2025-05-28\n", "fields": [], "enumConstants": [], "methods": [{"name": "save", "paramTypes": ["org.dromara.flow.domain.PrjHazardousItemsComments"], "doc": " 添加隐患清单\n\n @param comments\n @return\n"}, {"name": "update", "paramTypes": ["org.dromara.flow.domain.PrjHazardousItemsComments"], "doc": " 修改质监站隐患清单\n\n @param comments\n @return\n"}, {"name": "getDetail", "paramTypes": ["java.lang.String"], "doc": " 获取详情\n\n @param taskId\n @return\n"}, {"name": "back", "paramTypes": ["org.dromara.flow.domain.PrjHazardousItemsComments"], "doc": " 施工方上传报告用\n\n @param comments\n @return\n"}], "constructors": []}
{"doc": " 流程设计器-获取办理人权限设置列表\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getHandlerType", "paramTypes": [], "doc": " 获取办理人权限设置列表tabs页签\n\n @return tabs页签\n"}, {"name": "getHandlerSelect", "paramTypes": ["org.dromara.warm.flow.ui.dto.HandlerQuery"], "doc": " 获取办理列表, 同时构建左侧部门树状结构\n\n @param query 查询条件\n @return HandlerSelectVo\n"}, {"name": "fetchTaskAssigneeData", "paramTypes": ["org.dromara.workflow.common.enums.TaskAssigneeEnum", "org.dromara.common.core.domain.model.TaskAssigneeBody"], "doc": " 根据任务办理类型查询对应的数据\n"}, {"name": "fetchDeptData", "paramTypes": ["org.dromara.workflow.common.enums.TaskAssigneeEnum"], "doc": " 根据任务办理类型获取部门数据\n"}, {"name": "buildDeptTree", "paramTypes": ["java.util.List"], "doc": " 构建部门树状结构\n"}, {"name": "buildHandlerData", "paramTypes": ["org.dromara.common.core.domain.dto.TaskAssigneeDTO", "org.dromara.workflow.common.enums.TaskAssigneeEnum"], "doc": " 构建任务办理人数据\n"}, {"name": "fetchUsersByStorageId", "paramTypes": ["java.lang.String"], "doc": " 根据存储标识符（storageId）解析分配类型和ID，并获取对应的用户列表\n\n @param storageId 包含分配类型和ID的字符串（例如 \"user:123\" 或 \"role:456\"）\n @return 与分配类型和ID匹配的用户列表，如果格式无效则返回空列表\n"}, {"name": "getUsersByType", "paramTypes": ["org.dromara.workflow.common.enums.TaskAssigneeEnum", "java.util.List"], "doc": " 根据指定的任务分配类型（TaskAssigneeEnum）和 ID 列表，获取对应的用户信息列表\n\n @param type 任务分配类型，表示用户、角色、部门或其他（TaskAssigneeEnum 枚举值）\n @param ids  与指定分配类型关联的 ID 列表（例如用户ID、角色ID、部门ID等）\n @return 返回包含用户信息的列表。如果类型为用户（USER），则通过用户ID列表查询；\n 如果类型为角色（ROLE），则通过角色ID列表查询；\n 如果类型为部门（DEPT），则通过部门ID列表查询；\n 如果类型为岗位（POST）或无法识别的类型，则返回空列表\n"}], "constructors": []}
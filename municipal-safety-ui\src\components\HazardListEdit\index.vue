<template>
  <div class="HazardListEdit">
    <!-- 隐患清单的详情 -->
    <el-dialog :title="hiddenDangerDialog.title" v-model="hiddenDangerDialog.visible" append-to-body width="1200px"
      @close="handleCloseChange">
      <div>
        <el-row :gutter="10" style="padding:15px;box-shadow: 0px 0px 5px rgba(0, 0, 0, .12);height: 626px;">
          <el-col :span="7"
            style="display: flex;flex-direction: column; justify-content:center;align-items: flex-start;padding-left: 20px;">
            <div style="margin-bottom: 10px;">
              <p style="text-align: start;color: #409EFF;margin: 0 0 16px;">分析前</p>
              <HeaderPrewiew :src="aiDetailData?.photoDocumentUrl" :width="250" :height="250"
                :preview-src-list="[aiDetailData?.photoDocumentUrl]">
              </HeaderPrewiew>
            </div>
            <div>
              <p style="text-align: start;color: #67C23A;margin: 10px 0 16px;">分析后</p>
              <HeaderPrewiew :isShowToolbar="true" :src="aiDetailData?.aiPhotoDocumentUrl" :width="250" :height="250"
                :preview-src-list="[aiDetailData?.aiPhotoDocumentUrl]" @OriginalPhotoChange="OriginalPhotoChange"
                @screenShotChange="screenShotChange">
              </HeaderPrewiew>
            </div>
          </el-col>
          <el-col :span="17" style="display: flex;flex-direction: column;padding-top: 29px;height: 100%;">
            <div style="width: 100%; height: 100%;overflow-y: auto;">
              <el-card style="width: 100%;margin-bottom: 15px;" v-for="(item, index) in hazardListEditData?.violations"
                :key="index">
                <template #header>
                  <div class="card-header">
                    <span>问题 {{ index + 1 }}</span>
                    <div style="display: flex;align-items: baseline;">
                      <el-button type="danger" size="small" style="margin-right: 30px;"
                        @click="handleDeleteClick(index)">删除</el-button>
                      <div style="display: flex;align-items: center;">
                        <span style="color: #409EFF;display: block;">危险级别：</span>
                        <el-select style="width: 100px;" v-model="hazardListEditData.violations[index].level"
                          placeholder="请选择项目状态">
                          <el-option v-for="dict in hidden_danger_type" :key="dict.value" :label="dict.label"
                            :value="dict.value" />
                        </el-select>
                      </div>
                    </div>
                  </div>
                </template>
                <div style="display: flex;">
                  <span style="color: #666;display: block;width: 80px;">隐患描述：</span>
                  <el-input v-model="hazardListEditData.violations[index].violation" style="width: 100%"
                    :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" placeholder="请输入隐患描述" />
                </div>
                <div style="display: flex;margin: 15px 0;">
                  <span style="color: #666;display: block;width: 80px;">违反条款：</span>
                  <el-input v-model="hazardListEditData.violations[index].regulation" style="width: 100%"
                    :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" placeholder="请输入违反条款" />
                </div>
                <div style="display: flex;">
                  <span style="color: #666;display: block;width: 80px;">整改意见：</span>
                  <el-input v-model="hazardListEditData.violations[index].measure" style="width: 100%"
                    :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" placeholder="请输入整改意见" />
                </div>
              </el-card>
              <el-button :icon="Plus" style="width: 100%;;margin-bottom: 10px;" size="large"
                @click="handleAddHazard"></el-button>
            </div>
          </el-col>
        </el-row>
        <!-- 自定义截图组件 -->
        <ScreenShot :isEnableScreenshotStatus="isEnableScreenshotStatus" @isScreenShotStatus="isScreenShotStatus"
          @annotation="handleAnnotation">
        </ScreenShot>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmitHazard">提交</el-button>
          <el-button @click="hiddenDangerDialog.visible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import HeaderPrewiew from '@/components/ImagePreview/index.vue';
import { getPrj_hazardous_items_ai_detail, getPrj_hazardous_items_detail } from '@/api/projects/prj_hazardous_items/index'
import { listDangerList } from '@/api/system/dangerList';
import { DangerListVO } from '@/api/system/dangerList/types';
import { listByIds } from '@/api/system/oss/index'
import ScreenShot from '@/components/ScreenShot/index.vue'
import { Plus } from '@element-plus/icons-vue'
import { submitAnnotationData } from '@/api/projects//hazard_list_edit/index'
import { hazard_list_edit } from '@/api/projects/hazard_list_edit/types'

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { hidden_danger_type } = toRefs<any>(proxy?.useDict('hidden_danger_type'));

const emit = defineEmits(['update:isShowModel', 'hazardSubmit'])
const props = defineProps({
  isShowModel: {
    type: Boolean,
    default: false
  },
  taskId: {
    type: String,
    default: ''
  },
  itemId: {
    type: String,
    default: ''
  }
})
// 存放隐患清单详情的数据
const aiDetailData = ref();
// 详情信息的变量数据
const prjDetailData = ref();
const dangerTitle = ref();
const dangerListList = ref();
// 隐患清单的详情的弹框显隐
const hiddenDangerDialog = reactive({
  visible: false, // 控制对话框的显示隐藏
  title: '', // 对话框的标题
})
// 是否启用自定义截图
const isEnableScreenshotStatus = ref<boolean>(false);
// 临时存放AI图片的地址路径
const tempAiPhotoDocumentUrl = ref();
// 存放隐患清单要修改的数据
const hazardListEditData = ref<hazard_list_edit>({
  taskId: '',
  aiPhotoDocumentId: '',
  photoDocumentId: '',
  violations: []
});

watch(() => props.isShowModel, (newVal) => {
  hiddenDangerDialog.visible = newVal;
  if (props.itemId) {
    getItemsDetail(props.itemId);
    getAiDetail(props.taskId);
  }
})
// 获取隐患详情数据
const getAiDetail = async (taskId: string) => {
  const res = await getPrj_hazardous_items_ai_detail(taskId);
  if (res.code === 200) {
    hazardListEditData.value.violations = [];
    aiDetailData.value = res.data;
    hazardListEditData.value.taskId = taskId;
    hazardListEditData.value.aiPhotoDocumentId = aiDetailData.value.aiPhotoDocumentId;
    hazardListEditData.value.photoDocumentId = aiDetailData.value.photoDocumentId;
    aiDetailData.value.violations.forEach((item: any) => {
      hazardListEditData.value.violations.push({
        level: item.level,
        violation: item.violation,
        regulation: item.regulation,
        measure: item.measure,
      })
    })
    aiDetailData.value.photoDocumentUrl = await getImageUrl(aiDetailData.value.photoDocumentId);
    aiDetailData.value.aiPhotoDocumentUrl = await getImageUrl(aiDetailData.value.aiPhotoDocumentId);
  }
}
/** 查询dangerList列表 */
const getDangerList = async (dangerListType: string | number) => {
  const res = await listDangerList();
  dangerListList.value = [];
  const data = proxy?.handleTree<DangerListVO>(res.data, 'dangerId', 'preId');
  if (data) {
    for (let i = 0; i < data.length; i++) {
      if (data[i].type == dangerListType) {
        for (let m = 0; m < data[i].children.length; m++) {
          for (let n = 0; n < data[i].children[m].children.length; n++) {
            if ((prjDetailData.value.dangerId as string).includes(data[i].children[m].children[n].dangerId)) {
              dangerTitle.value = data[i].children[m].name
              dangerListList.value.push(data[i].children[m].children[n]);
            }
          }
        }
      }
    }
  }
};
const getItemsDetail = async (itemId: string) => {
  const res = await getPrj_hazardous_items_detail(itemId);
  if (res.code === 200) {
    prjDetailData.value = res.data;
    getDangerList(prjDetailData.value.dangerListType);
    for (let index = 0; index < prjDetailData.value.aiListVOS?.length; index++) {
      if (prjDetailData.value.aiListVOS[index].aiPhotoDocumentId != null) {
        prjDetailData.value.aiListVOS[index].imgUrl = await getImageUrl(prjDetailData.value.aiListVOS[index].aiPhotoDocumentId);
      } else {
        prjDetailData.value.aiListVOS[index].imgUrl = ""
      }
    }
  }
}
// 使用ossId查询图片的url地址
const getImageUrl = async (ossId: string | number) => {
  const { data } = await listByIds(ossId);
  return data[0]?.url;
}
// 替换原图的自定义回调事件
const OriginalPhotoChange = (val: boolean) => {
  if (val) {
    tempAiPhotoDocumentUrl.value = aiDetailData.value.aiPhotoDocumentUrl;
    aiDetailData.value.aiPhotoDocumentUrl = aiDetailData.value.photoDocumentUrl;
  } else {
    aiDetailData.value.aiPhotoDocumentUrl = tempAiPhotoDocumentUrl.value;
  }
}
// 预览大图组件的自定义回调函数
const screenShotChange = (val: boolean) => {
  isEnableScreenshotStatus.value = val;
}
// 截图组件的自定义回调函数
const isScreenShotStatus = (val: boolean) => {
  isEnableScreenshotStatus.value = val;
}
// 关闭弹框
const handleCloseChange = () => {
  emit('update:isShowModel', false)
}
// 截图的自定义事件
const handleAnnotation = (val: { fileName: string, ossId: string, url: string }) => {
  aiDetailData.value.aiPhotoDocumentUrl = val.url;
  hazardListEditData.value.aiPhotoDocumentId = val.ossId;
  hazardListEditData.value.photoDocumentId = hazardListEditData.value.photoDocumentId;
}
// 点击右侧底部的新增来新增标注的问题
const handleAddHazard = () => {
  proxy?.$modal.confirm('请先在图片上标注，然后再添加标注的问题').then(() => {
    hazardListEditData.value.violations.push({
      level: '1',
      violation: '',
      regulation: '',
      measure: '',
    })
  }).catch(() => { });
}
// 删除右侧的问题
const handleDeleteClick = (index: number) => {
  proxy?.$modal.confirm('是否确定删除该问题？').then(() => {
    hazardListEditData.value.violations.splice(index, 1);
  }).catch(() => { });
}
// 提交
const handleSubmitHazard = () => {
  const isEmpty = hazardListEditData.value.violations.every(item => item.level && item.violation && item.regulation && item.measure)
  if (isEmpty) {
    proxy?.$modal.confirm('是否确定提交？').then(async () => {
      const res = await submitAnnotationData(hazardListEditData.value)
      if (res.code == 200) {
        proxy?.$modal.msgSuccess('提交成功')
        emit('hazardSubmit', props.itemId)
      }
      hiddenDangerDialog.visible = false;
    }).catch(() => { });
  } else {
    proxy?.$modal.msgError('描述、条款、意见不能为空')
  }
}
</script>

<style lang="scss" scoped></style>
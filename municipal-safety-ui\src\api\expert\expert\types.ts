export interface ExpertVO {
  // 专家id
  expertId?: string;
  roleInMeeting?: string;   // 会议中的角色
  isAttendingExpert?: string;  // 是否评审专家
  // 专家头像
  avatar?: string;
  /**
   * 名称
   */
  name: string;

  /**
   * 身份证件号
   */
  idCard: string | number;

  /**
   * 性别
   */
  sex: string;

  /**
   * 工作单位
   */
  workUnit: string;

  /**
   * 电话
   */
  phone: string;

  /**
   * 简介
   */
  introduce: string;

  /**
   * 职称
   */
  title: string;
  /**
   * 所在省
   */
  province: string;
  // 省份编码
  divisionCode: string;
  /**
   * 省份名称
   */
  divisionName: string;
  /**
   * 省份等级
   */
  level: string;
  // 父级编码
  parentCode: string;
  /**
   * 所在市
   */
  city: string;

  /**
   * 所在区
   */
  area: string;
  provinceName: string;
  cityName: string;
  areaName: string;
  // 专家所在地
  expertArea: string;
  /**
   * 专业
   */
  major: string;

  /**
   * 行业
   */
  industry: string;
  /**
   * 专家类型
   */
  type: string;
}

// 参与项目的字段类型
interface ProjectField {
  projectId?: string;
  name: string; // 项目名称,
  location: string,
  status: string,
  startDate: string | number,
  endDate: string | number,
  description: string,
}
// 专家领域类型字段类型
interface ExpertField {
  fieldId?: string;
  name: string;
  description: string;
}

export interface ExpertForm extends BaseEntity {
  avatar?: string;
  // 专家id
  expertId?: string;
  /**
   * 名称
   */
  name?: string;

  /**
   * 身份证件号
   */
  idCard?: string | number;

  /**
   * 性别
   */
  sex?: string;

  /**
   * 工作单位
   */
  workUnit?: string;

  /**
   * 电话
   */
  phone?: string;

  /**
   * 简介
   */
  introduce?: string;

  /**
   * 职称
   */
  title?: string;

  /**
   * 所在省
   */
  province?: string;

  /**
   * 所在市
   */
  city?: string;

  /**
   * 所在区
   */
  area?: string;

  /**
   * 专业
   */
  major?: string;

  /**
   * 行业
   */
  industry?: string;
  /**
   * 专家类型
   */
  type: string;

  // 参与项目
  expertProjectList: ProjectField[];
  /**
   * 专家领域
   */
  expertFieldList: ExpertField[]
}

export interface ExpertQuery extends PageQuery {
  roleInMeeting?: string;   // 会议中的角色
  isAttendingExpert?: string;  // 是否评审专家
  /**
   * 名称
   */
  name?: string;

  /**
   * 身份证件号
   */
  idCard?: string | number;

  /**
   * 性别
   */
  sex?: string;

  /**
   * 工作单位
   */
  workUnit?: string;

  /**
   * 电话
   */
  phone?: string;

  /**
   * 简介
   */
  introduce?: string;

  /**
   * 职称
   */
  title?: string;

  /**
   * 所在省
   */
  province?: string;

  /**
   * 所在市
   */
  city?: string;

  /**
   * 所在区
   */
  area?: string;

  /**
   * 专业
   */
  major?: string;

  /**
   * 行业
   */
  industry?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}




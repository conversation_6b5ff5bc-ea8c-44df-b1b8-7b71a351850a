<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="危大工程名称" prop="itemName" label-width="96px">
              <el-input v-model="queryParams.itemName" placeholder="请输入危大工程名称/描述" clearable
                @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="危大类型" prop="dangerListType">
              <el-select v-model="queryParams.dangerListType" placeholder="请选择危大类型" clearable>
                <el-option v-for="dict in danger_list_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['projects:prj_hazardous_items:add']">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              v-hasPermi="['projects:prj_hazardous_items:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['projects:prj_hazardous_items:remove']">删除</el-button>
          </el-col> -->
          <!-- <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['projects:prj_hazardous_items:export']">导出</el-button>
          </el-col> -->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Close" @click="handleClose">关闭</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="prj_hazardous_itemsList" @selection-change="handleSelectionChange"
        show-overflow-tooltip class="expertTable5">
        <el-table-column type="index" label="序号" width="55" align="center" />
        <el-table-column label="项目名称" align="center" prop="projectName" min-width="200px" />
        <el-table-column label="涉危工程" align="center" prop="parentName" min-width="200px" />
        <el-table-column label="危大工程名称" align="center" prop="itemName" min-width="130px">
          <template #default="scope">
            <el-button text type="primary" @click="handleDetail(scope.row)">{{ scope.row.itemName }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="具体范围" align="center" prop="scopeDetails" min-width="130px" />
        <el-table-column label="危大类型" align="center" prop="dangerListType">
          <template #default="scope">
            <dict-tag :options="danger_list_type" :value="scope.row.dangerListType" />
          </template>
        </el-table-column>
        <el-table-column label="计划开工日期" align="center" prop="startDate" min-width="110px">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="计划竣工日期" align="center" prop="plannedEndDate" min-width="110px">
          <template #default="scope">
            <span>{{ parseTime(scope.row.plannedEndDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="实际竣工日期" align="center" prop="actualEndDate" min-width="130px">
          <template #default="scope">
            <span>{{ parseTime(scope.row.actualEndDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实际开工日期" align="center" prop="actualStartDate" min-width="130px">
          <template #default="scope">
            <span>{{ parseTime(scope.row.actualStartDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="状态" align="center" prop="status" min-width="80px">
          <template #default="scope">
            <dict-tag :options="project_item_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="110px">
          <template #default="scope">
            <el-tooltip content="监测设备" placement="top">
              <el-button link type="primary" icon="SetUp" @click="handleTestEquipment(scope.row)"
                v-hasPermi="['projects:prj_hazardous_items:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['projects:prj_hazardous_items:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
                v-hasPermi="['projects:prj_hazardous_items:detail']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['projects:prj_hazardous_items:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改[项目管理] 列出项目内具体的危险性较大的分部分项工程对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="60%" append-to-body>
      <el-tabs v-model="activeName" @tab-click="handleTabSchemeClick">
        <el-tab-pane label="工程基本信息" name="first">
          <el-form ref="prj_hazardous_itemsFormRef" :model="form" :rules="rules">
            <el-row :gutter="10">
              <el-col :span="11">
                <el-form-item label="危大工程名称" prop="itemName">
                  <el-input v-model="form.itemName" placeholder="请输入危大工程名称" />
                </el-form-item>
              </el-col>
              <el-col :span="13">
                <el-form-item label="危大类型" prop="dangerListType" label-width="106px">
                  <el-select v-model="form.dangerListType" placeholder="请选择危大类型" @change="selectTypeChange">
                    <el-option v-for="dict in danger_list_type" :key="dict.value" :label="dict.label"
                      :value="parseInt(dict.value)"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="11">
                <el-form-item label="涉危工程" prop="dangerGcVal" label-width="105px">
                  <el-select v-model="form.dangerGcVal" placeholder="请选择涉危工程" @change="selectGcChange"
                    :fit-input-width="true">
                    <el-option v-for="item in dangerListList" :key="item.dangerId" :label="item.name"
                      :value="item.dangerId" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="13">
                <el-form-item label="涉危工程类型" prop="dangerGcTypeVal" label-width="106px">
                  <el-select v-model="form.dangerGcTypeVal" multiple placeholder="请选择涉危工程类型" :fit-input-width="true"
                    clearable @change="selectGcTypeChange">
                    <el-option v-for="item in dangerListType" :key="item.dangerId" :label="item.name"
                      :value="item.dangerId" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="具体范围详情" prop="scopeDetails" label-width="105px">
                  <el-input v-model="form.scopeDetails" type="textarea" :rows="5" placeholder="请输入内容" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="11">
                <el-form-item label="计划开工日期" prop="startDate" label-width="105px">
                  <el-date-picker clearable v-model="form.startDate" type="date" value-format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择计划开工日期" :disabled-date="startPickerOptions" style="width: 100%;">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="13">
                <el-form-item label="计划竣工日期" prop="plannedEndDate" label-width="105px">
                  <el-date-picker clearable v-model="form.plannedEndDate" type="date" value-format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择计划竣工日期" :disabled-date="endPickerOptions" style="width: 100%;">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="11">
                <el-form-item label="实际开工日期" prop="actualStartDate" label-width="105px">
                  <el-date-picker clearable v-model="form.actualStartDate" type="date"
                    value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择实际开工日期" :disabled-date="startPickerOptions1"
                    style="width: 100%;">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="13">
                <el-form-item label="实际竣工日期" prop="actualEndDate" label-width="105px">
                  <el-date-picker clearable v-model="form.actualEndDate" type="date" value-format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择实际竣工日期" :disabled-date="endPickerOptions1" style="width: 100%;">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="专项施工方案" name="second" :disabled="isTabDisabled">
          <el-card shadow="never">
            <template #header>
              <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                  <el-button type="primary" plain icon="Plus" @click="handleSchemeAdd"
                    v-hasPermi="['projects:prj_hazardous_items:add']">新增</el-button>
                </el-col>
                <right-toolbar @queryTable="getSchemeList"></right-toolbar>
              </el-row>
            </template>

            <el-table v-loading="loading1" :data="plansList" show-overflow-tooltip class="expertTable5">
              <el-table-column type="index" label="序号" width="55" align="center" />
              <el-table-column label="施工方案名称" align="center" prop="planName" />
              <el-table-column label="施工方案" align="center" prop="planDocumentId">
                <template #default="scope">
                  <el-button size="small" type="text" icon="Download" @click="schemeDownload(scope.row)">下载</el-button>
                </template>
              </el-table-column>
              <el-table-column label="施工方案审批表" align="center" prop="approvalFormDocId">
                <template #default="scope">
                  <el-button size="small" type="text" icon="Download"
                    @click="approvalFormDownload(scope.row)">下载</el-button>
                </template>
              </el-table-column>
              <el-table-column label="AI校验状态" align="center" prop="reviewStatus">
                <template #default="scope">
                  <dict-tag :options="plan_ai_check_status" :value="scope.row.reviewStatus" />
                </template>
              </el-table-column>
              <el-table-column label="AI分析结果" align="center" prop="aiDefectWarningDetails"
                min-width="180px"></el-table-column>
              <el-table-column v-if="dangerListTypeVal == 2" label="专家论证结论" align="center" prop="conclusion"
                min-width="100px">
                <template #default="scope">
                  <dict-tag :options="expert_conclusion" :value="scope.row.conclusion" />
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" min-width="70px">
                <template #default="scope">
                  <el-tooltip content="非超危大的工程无需专家论证" placement="top">
                    <el-button link type="primary" icon="User" :disabled="dangerListTypeVal == 2 ? false : true"
                      @click="handleExpertReview(scope.row)" />
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="plansTotal > 0" :total="plansTotal" v-model:page="plansQueryParams.pageNum"
              v-model:limit="plansQueryParams.pageSize" @pagination="getSchemeList" />
          </el-card>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="activeName == 'first'" type="primary" @click="submitForm">下一步</el-button>
          <el-button v-else @click="dialog.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 项目工程管理详情弹框 -->
    <prj_hazardous_items_detail :itemId="itemId" :isShowModel="isShowModel" @update:isShowModel="handleModelDetail">
    </prj_hazardous_items_detail>
    <!-- 专项施工方案新增dialog弹框 -->
    <el-dialog :title="schemeDialog.title" v-model="schemeDialog.visible" append-to-body width="40%">
      <el-form ref="schemeFormRef" :model="plansForm" :rules="plansRules">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="施工方案名称" prop="planName">
              <el-input v-model="plansForm.planName" placeholder="请输入施工方案名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="施工方案审批表" prop="approvalFormDocId">
              <FileUpload @update:modelValue="handleFile1" :limit="1" :modelValue="plansForm.approvalFormDocId"
                :isShowTip="false" :fileSize="50">
              </FileUpload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="15">
            <el-form-item label="施工方案" prop="planDocumentId" label-width="105px">
              <FileUpload :fileType="['pdf']" @update:modelValue="handleFile" :limit="1"
                :modelValue="plansForm.planDocumentId" :isShowTip="false" :fileSize="50">
              </FileUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading1" type="primary" @click="submitSchemeForm">确 定</el-button>
          <el-button @click="schemeDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 专家评审弹框-->
    <ExpertReview :isExpertShowModel="isExpertShowModel" :row="row1"
      @update:isExpertShowModel="handleModelExpertReview" />
  </div>
</template>

<script setup name="Prj_hazardous_items" lang="ts">
import { listPrj_hazardous_items, getPrj_hazardous_items, delPrj_hazardous_items, addPrj_hazardous_items, updatePrj_hazardous_items } from '@/api/projects/prj_hazardous_items';
import { Prj_hazardous_itemsVO, Prj_hazardous_itemsQuery, Prj_hazardous_itemsForm } from '@/api/projects/prj_hazardous_items/types';
import { listDangerList } from '@/api/system/dangerList';
import { DangerListVO } from '@/api/system/dangerList/types';
import type { TabsPaneContext } from 'element-plus'
import prj_hazardous_items_detail from '@/components/prj_hazardous_items_detail/index.vue'
import { listConstructionPlans, addConstructionPlans } from '@/api/projects/prj_construction_plans/api'
import { ConstructionPlansQuery, ConstructionPlansVO, ConstructionPlansForm } from '@/api/projects/prj_construction_plans/types'
import FileUpload from '@/components/FileUpload/index.vue'
import ExpertReview from '@/components/ExpertReview/index.vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { danger_list_type, project_item_status, plan_ai_check_status, expert_conclusion } = toRefs<any>(proxy?.useDict('danger_list_type', 'project_item_status', 'plan_ai_check_status', 'expert_conclusion'));

const route = useRoute();
const prj_hazardous_itemsList = ref<Prj_hazardous_itemsVO[]>([]);
const buttonLoading = ref(false);
const buttonLoading1 = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const itemId = ref<string>('');
const isShowModel = ref<boolean>(false);

const dangerList = ref<DangerListVO[]>([]);
const dangerListList = ref([]);
const dangerListType = ref([]);
const queryFormRef = ref<ElFormInstance>();
const prj_hazardous_itemsFormRef = ref<ElFormInstance>();
const schemeFormRef = ref<ElFormInstance>();
const isExpertShowModel = ref<boolean>(false);
const row1 = ref<{ itemId: string | number, planId: string | number }>({
  itemId: '',
  planId: ''
});

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const activeName = ref('first')

const initFormData: Prj_hazardous_itemsForm = {
  dangerGcVal: undefined,
  dangerGcTypeVal: undefined,
  itemId: undefined,
  projectId: '',
  dangerId: undefined,
  itemName: undefined,
  scopeDetails: undefined,
  dangerListType: undefined,
  startDate: '',
  plannedEndDate: '',
  actualEndDate: '',
  actualStartDate: '',
  status: undefined,
}
const data = reactive<PageData<Prj_hazardous_itemsForm, Prj_hazardous_itemsQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: '',
    dangerId: undefined,
    itemName: undefined,
    scopeDetails: undefined,
    dangerListType: undefined,
    startDate: '',
    plannedEndDate: '',
    actualEndDate: '',
    actualStartDate: '',
    status: undefined,
    params: {
    }
  },
  rules: {
    itemName: [
      { required: true, message: "危大工程名称不能为空", trigger: "blur" }
    ],
    dangerListType: [
      { required: true, message: "危大类型不能为空", trigger: "change" }
    ],
    dangerGcVal: [
      { required: true, message: "涉危工程不能为空", trigger: "change" }
    ],
    dangerGcTypeVal: [
      { required: true, message: "涉危工程类型不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

const schemeDialog = reactive<DialogOption>({
  visible: false,
  title: ''
})
const plansQueryParams = ref<ConstructionPlansQuery>({
  pageNum: 1,
  pageSize: 10,
  itemId: undefined,
  planName: undefined,
  planVersion: undefined,
  reviewStatus: undefined,
  params: {}
})
// 新增专项施工方案表单提交数据
const plansForm = ref<ConstructionPlansForm>({
  planId: undefined,
  itemId: undefined,
  planName: undefined,
  planVersion: undefined,
  planDocumentId: undefined,
  approvalFormDocId: undefined
})
const plansRules = ref({
  planName: [
    { required: true, message: "名称不能为空", trigger: "blur" }
  ],
  approvalFormDocId: [
    { required: true, message: "施工方案审批表不能为空", trigger: "change" }
  ],
  planDocumentId: [
    { required: true, message: "施工方案不能为空", trigger: "change" }
  ],
})
// 存放专项施工方案列表数据
const plansList = ref<ConstructionPlansVO[]>([])
// 施工方案列表总条数
const plansTotal = ref(0)
// 控制专项施工方案tab页签是否可以点击
const isTabDisabled = ref(true)
const loading1 = ref(false)
// 存放工程项目id
const projectItemId = ref<string | number>('')
// 存放危大超危大的标识值
const dangerListTypeVal = ref<string | number>()
// 判断计划开工开始日期的范围
const startPickerOptions = (date: Date) => {
  if (form.value.plannedEndDate) {
    return date.getTime() > new Date(form.value.plannedEndDate).getTime();
  }
}
// 判断计划结束日期的范围
const endPickerOptions = (date: Date) => {
  if (form.value.startDate) {
    return date.getTime() < new Date(form.value.startDate).getTime();
  }
}
// 判断实际开工开始日期的范围
const startPickerOptions1 = (date: Date) => {
  if (form.value.actualEndDate) {
    return date.getTime() > new Date(form.value.actualEndDate).getTime();
  }
}
// 判断实际结束日期的范围
const endPickerOptions1 = (date: Date) => {
  if (form.value.actualStartDate) {
    return date.getTime() < new Date(form.value.actualStartDate).getTime();
  }
}
/** 查询dangerList列表 */
const getDangerList = async () => {
  const res = await listDangerList(queryParams.value);
  const data = proxy?.handleTree<DangerListVO>(res.data, 'dangerId', 'preId');
  if (data) {
    dangerList.value = data;
  }
};
/** 查询[项目管理] 列出项目内具体的危险性较大的分部分项工程列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPrj_hazardous_items(queryParams.value);
  prj_hazardous_itemsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  prj_hazardous_itemsFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  if (route.params.id) {
    form.value.projectId = (route.params.id as string | number);
    queryParams.value.projectId = (route.params.id as string | number);
  }
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: Prj_hazardous_itemsVO[]) => {
  ids.value = selection.map(item => item.itemId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  schemeReset();
  isTabDisabled.value = true;
  activeName.value = 'first';
  dialog.visible = true;
  dialog.title = "添加分部分项工程";
  if (route.params.id) {
    form.value.projectId = (route.params.id as string | number);
  }
}
// 检测设备按钮跳转事件
const handleTestEquipment = (row: Prj_hazardous_itemsVO) => {
  proxy.$router.push({
    path: '/prj/prj_monitor_facility',
    query: {
      id: row.itemId,
    }
  });
}
/** 修改按钮操作 */
const handleUpdate = async (row?: Prj_hazardous_itemsVO) => {
  dangerListTypeVal.value = row?.dangerListType;
  isTabDisabled.value = false;
  reset();
  projectItemId.value = row.itemId;
  plansQueryParams.value.itemId = row.itemId;
  plansForm.value.itemId = row.itemId;
  getSchemeList();
  if (route.params.id) {
    form.value.projectId = (route.params.id as string | number);
  }
  const _itemId = row?.itemId || ids.value[0]
  const res = await getPrj_hazardous_items(_itemId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改分部分项工程";
  await selectTypeChange(form.value.dangerListType);
  for (let m = 0; m < dangerListList.value.length; m++) {
    for (let n = 0; n < dangerListList.value[m].children.length; n++) {
      if ((form.value.dangerId as string).includes(dangerListList.value[m].children[n].dangerId)) {
        form.value.dangerGcVal = dangerListList.value[m].dangerId;
      }
    }
  }
  await selectGcChange(form.value.dangerGcVal);
  // 修改时的回显
  form.value.dangerGcTypeVal = (form.value.dangerId as string).split(',');
}

/** 提交按钮 */
const submitForm = () => {
  prj_hazardous_itemsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.itemId) {
        const res = await updatePrj_hazardous_items(form.value).finally(() => buttonLoading.value = false);
        if (res.code == 200) {
          activeName.value = 'second';
          await getList();
        }
      } else {
        const res = await addPrj_hazardous_items(form.value).finally(() => buttonLoading.value = false);
        if (res.code == 200) {
          activeName.value = 'second';
          isTabDisabled.value = false;
          projectItemId.value = res.data;
          plansForm.value.itemId = res.data;
          await getList();
        }
      }
      buttonLoading.value = false;
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: Prj_hazardous_itemsVO) => {
  const _itemIds = row?.itemId || ids.value;
  await proxy?.$modal.confirm(`是否确认删除该分部分项工程名称为(${row.itemName})?`).finally(() => loading.value = false);
  await delPrj_hazardous_items(_itemIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('projects/prj_hazardous_items/export', {
    ...queryParams.value
  }, `prj_hazardous_items_${new Date().getTime()}.xlsx`)
}
/** 返回按钮操作 */
const handleClose = () => {
  const obj: RouteLocationNormalized = {
    fullPath: '',
    hash: '',
    matched: [],
    meta: undefined,
    name: undefined,
    params: undefined,
    query: undefined,
    redirectedFrom: undefined,
    path: '/prj/prj_projects'
  };
  proxy?.$tab.closeOpenPage(obj);
};
const selectTypeChange = (id: number) => {
  form.value.dangerGcVal = '';
  form.value.dangerGcTypeVal = [];
  dangerList.value.forEach(item => {
    if (item.type == id) {
      dangerListList.value = item.children;
    }
  })
}
const selectGcChange = (id: number | string) => {
  form.value.dangerGcTypeVal = [];
  dangerListList.value.forEach(item => {
    if (item.dangerId == id) {
      dangerListType.value = item.children;
    }
  })
}
const selectGcTypeChange = (idArr: string[]) => {
  form.value.dangerId = idArr.join(',')
}
// 点击工程名称或详情的事件
const handleDetail = (row: Prj_hazardous_itemsVO) => {
  itemId.value = row.itemId as string;
  isShowModel.value = true;
}
const handleModelDetail = (val: boolean) => {
  isShowModel.value = val;
  itemId.value = '';
}
// 查询专项施工方案列表数据
const getSchemeList = async () => {
  loading1.value = true;
  const res = await listConstructionPlans(plansQueryParams.value);
  plansList.value = res.rows;
  plansTotal.value = res.total;
  loading1.value = false;
}
// 新增施工方案
const handleSchemeAdd = () => {
  schemeReset();
  schemeDialog.visible = true;
  schemeDialog.title = '新增专项施工方案';
}
// 下载施工方案
const schemeDownload = (row: ConstructionPlansVO) => {
  proxy?.$download.oss(row.planDocumentId);
}
// 下载施工方案审批表
const approvalFormDownload = (row: ConstructionPlansVO) => {
  proxy?.$download.oss(row.approvalFormDocId);
}
// 点击tab切换
const handleTabSchemeClick = (tab: TabsPaneContext, event: Event) => {
  if (tab.paneName == 'second') {
    getSchemeList();
  }
}
// 上传施工方案回调函数
const handleFile = (fileOssId: string) => {
  plansForm.value.planDocumentId = fileOssId;
}
// 上传施工方案审批表回调函数
const handleFile1 = (fileOssId: string) => {
  plansForm.value.approvalFormDocId = fileOssId;
}
// 提交新增专项施工方案表单
const submitSchemeForm = async () => {
  schemeFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading1.value = true;
      const res = await addConstructionPlans(plansForm.value);
      if (res.code == 200) {
        plansList.value = res.data
        proxy?.$modal.msgSuccess("操作成功");
        buttonLoading1.value = false;
        schemeDialog.visible = false;
        await getSchemeList();
        schemeReset();
      }
    }
  })
}
// 专家评审
const handleExpertReview = (row: ConstructionPlansVO) => {
  row1.value.itemId = row.itemId;
  row1.value.planId = row.planId;
  isExpertShowModel.value = true;
}
const schemeReset = () => {
  schemeFormRef.value?.resetFields();
  plansForm.value.itemId = projectItemId.value;
}
const handleModelExpertReview = (val: boolean) => {
  isExpertShowModel.value = val;
}
// 监听activeName的变化
watch(() => activeName.value, (newVal, oldVal) => {
  if (newVal == 'second') {
    getSchemeList();
  }
})
// 监听专家评审的弹框关闭与否
watch(() => isExpertShowModel.value, (newVal) => {
  if (newVal == false) {
    getSchemeList();
  }
})
onMounted(() => {
  // 接收动态路由的参数
  if (route.params.id) {
    queryParams.value.projectId = (route.params.id as string | number);
    getList();
  }
  getDangerList();
});
</script>
<style lang="scss">
.expertTable5 {

  .el-popper,
  .is-dark {
    max-width: 300px;
  }
}
</style>

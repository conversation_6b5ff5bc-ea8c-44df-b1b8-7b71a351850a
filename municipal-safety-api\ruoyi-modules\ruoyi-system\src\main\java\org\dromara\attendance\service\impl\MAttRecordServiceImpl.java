package org.dromara.attendance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.dromara.attendance.domain.MAttRecord;
import org.dromara.attendance.domain.bo.MAttRecordBo;
import org.dromara.attendance.domain.vo.CalendarDayVo;
import org.dromara.attendance.domain.vo.MAttRecordVo;
import org.dromara.attendance.domain.vo.MAttRuleVo;
import org.dromara.attendance.domain.vo.TimeSlot;
import org.dromara.attendance.mapper.MAttRecordMapper;
import org.dromara.attendance.mapper.MAttRuleMapper;
import org.dromara.attendance.service.IMAttRecordService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.service.DictService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.SysEnterpriseInfo;
import org.dromara.system.mapper.SysEnterpriseInfoMapper;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.dev33.satoken.SaManager.log;

/**
 * 考勤记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@RequiredArgsConstructor
@Service
public class MAttRecordServiceImpl implements IMAttRecordService {

    @Resource
    private MAttRecordMapper baseMapper;
    @Resource
    private DictService dictService;
    @Resource
    private MAttRuleMapper mAttRuleMapper;
    @Resource
    private SysEnterpriseInfoMapper sysEnterpriseInfoMapper;

    /**
     * 查询考勤记录
     *
     * @param id 主键
     * @return 考勤记录
     */
    @Override
    public MAttRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询考勤记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 考勤记录分页列表
     */
    @Override
    public TableDataInfo<MAttRecordVo> queryPageList(MAttRecordBo bo, PageQuery pageQuery) {
        SysEnterpriseInfo sysEnterpriseInfo = sysEnterpriseInfoMapper.selectById(LoginHelper.getEnterpriseId());
        bo.setCreateDept(LoginHelper.isSuperAdmin() ? null : sysEnterpriseInfo.getDeptId());
        LambdaQueryWrapper<MAttRecord> lqw = buildQueryWrapper(bo);
        Page<MAttRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的考勤记录列表
     *
     * @param bo 查询条件
     * @return 考勤记录列表
     */
    @Override
    public List<MAttRecordVo> queryList(MAttRecordBo bo) {
        LambdaQueryWrapper<MAttRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MAttRecord> buildQueryWrapper(MAttRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MAttRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(MAttRecord::getAttTime);
        lqw.eq(bo.getRuleId() != null, MAttRecord::getRuleId, bo.getRuleId());
        lqw.eq(bo.getPersonId() != null, MAttRecord::getPersonId, bo.getPersonId());
        lqw.eq(StringUtils.isNotBlank(bo.getPersonType()), MAttRecord::getPersonType, bo.getPersonType());
        lqw.eq(StringUtils.isNotBlank(bo.getRealName()), MAttRecord::getRealName, bo.getRealName());
        lqw.eq(StringUtils.isNotBlank(bo.getIdNumber()), MAttRecord::getIdNumber, bo.getIdNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getRealTimeFace()), MAttRecord::getRealTimeFace, bo.getRealTimeFace());
        lqw.eq(StringUtils.isNotBlank(bo.getSn()), MAttRecord::getSn, bo.getSn());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), MAttRecord::getContent, bo.getContent());
        lqw.eq(bo.getSource() != null, MAttRecord::getSource, bo.getSource());
        lqw.eq(bo.getAttTime() != null, MAttRecord::getAttTime, bo.getAttTime());
        lqw.eq(bo.getAttDate() != null, MAttRecord::getAttDate, bo.getAttDate());
        lqw.eq(bo.getAttResult() != null, MAttRecord::getAttResult, bo.getAttResult());
        lqw.eq(bo.getCreateDept() != null, MAttRecord::getCreateDept, bo.getCreateDept());
        return lqw;
    }

    /**
     * 新增考勤记录
     *
     * @param bo 考勤记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MAttRecordBo bo) {
        MAttRecord add = MapstructUtils.convert(bo, MAttRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改考勤记录
     *
     * @param bo 考勤记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MAttRecordBo bo) {
        MAttRecord update = MapstructUtils.convert(bo, MAttRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MAttRecord entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除考勤记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public MAttRecordVo selectMAttRuleByAttDate(String format, int i, Long personId) {
        return baseMapper.selectMAttRuleByAttDate(format, i, personId);
    }

    @Override
    public void insertMAttRecord(MAttRecordBo mAttRecordBo) {
        baseMapper.insertMAttRecord(mAttRecordBo);
    }

    @Override
    public List<MAttRecordVo> selectMAttRecordByPersonIdAndAttDate(Long personId, String attDate) {
        List<MAttRecordVo> mAttRecordVos = baseMapper.selectMAttRecordByPersonIdAndAttDate(personId, attDate);
        Set<Long> ruleIds = mAttRecordVos.isEmpty() ?
            Collections.emptySet() : mAttRecordVos.stream().map(MAttRecordVo::getRuleId).collect(Collectors.toSet());
        for (MAttRecordVo mAttRecordVo : mAttRecordVos) {
            mAttRecordVo.setMAttRules(mAttRuleMapper.selectVoByIds(new ArrayList<>(ruleIds)));
            mAttRecordVo.setPersonType(dictService.getDictLabel("personnel_position", mAttRecordVo.getPersonType()));
            mAttRecordVo.setAttResultName(dictService.getDictLabel("att_result", String.valueOf(mAttRecordVo.getAttResult())));
        }
        return mAttRecordVos;
    }

    @Override
    public List<MAttRecordVo> selectMAttRecordByPersonId(Long personId) {
        return baseMapper.selectMAttRecordByPersonId(personId);
    }

    /**
     * 获取考勤日历（时段打卡优化版）
     *
     * @param personId 人员ID
     * @param month    月份(yyyy-MM)
     * @return 考勤日历列表
     */
    public List<CalendarDayVo> getAttendanceCalendar(Long personId, String month) {
        // 1. 解析月份
        YearMonth targetMonth = parseYearMonth(month);
        LocalDate startDate = targetMonth.atDay(1);
        LocalDate endDate = targetMonth.atEndOfMonth();

        // 2. 获取考勤记录并按日期分组
        List<MAttRecordVo> records = baseMapper.selectMAttRecordByPersonId(personId);
        Map<String, List<MAttRecordVo>> recordMap = records.stream()
            .collect(Collectors.groupingBy(MAttRecordVo::getAttDate));

        // 3. 获取相关考勤规则（即使records为空也尝试获取）
        Set<Long> ruleIds = records.isEmpty() ?
            Collections.emptySet() : records.stream().map(MAttRecordVo::getRuleId).collect(Collectors.toSet());

        List<MAttRuleVo> rules = ruleIds.isEmpty() ?
            Collections.emptyList() : mAttRuleMapper.selectVoByIds(new ArrayList<>(ruleIds));

        // 4. 生成日历
        List<CalendarDayVo> calendar = new ArrayList<>();
        LocalDate currentDate = startDate;

        while (!currentDate.isAfter(endDate)) {
            CalendarDayVo dayVo = buildBaseDayVo(currentDate, targetMonth);

            // 获取当天考勤记录
            String dateStr = currentDate.format(DateTimeFormatter.ISO_DATE);
            List<MAttRecordVo> dayRecords = recordMap.getOrDefault(dateStr, Collections.emptyList());
            dayVo.setAttendances(dayRecords);

            // 计算考勤状态（优化版）
            calculateOptimizedAttendanceStatus(dayVo, dayRecords, rules);

            calendar.add(dayVo);
            currentDate = currentDate.plusDays(1);
        }

        return calendar;
    }

    /**
     * 优化版考勤状态计算
     */
    private void calculateOptimizedAttendanceStatus(CalendarDayVo dayVo, List<MAttRecordVo> attendances,
                                                    List<MAttRuleVo> rules) {
        if (attendances.isEmpty()) {
            setAbsentStatus(dayVo);
            return;
        }

        // 获取适用的考勤规则
        Optional<MAttRuleVo> ruleOpt = findApplicableRule(attendances, rules);
        if (!ruleOpt.isPresent()) {
            setNormalStatus(dayVo, "无适用规则，默认正常");
            return;
        }

        MAttRuleVo rule = ruleOpt.get();
        List<TimeSlot> timeSlots = parseTimeSlots(rule.getCheckTime());

        // 检查每个时段是否有对应的有效打卡记录
        boolean allTimeSlotsValid = true;
        for (TimeSlot slot : timeSlots) {
            boolean hasValidPunch = attendances.stream()
                .filter(r -> r.getWhichTime() == slot.getNum())
                .anyMatch(r -> r.getAttResult() == 0);

            if (!hasValidPunch) {
                allTimeSlotsValid = false;
                break;
            }
        }

        if (timeSlots.isEmpty()) {
            setNormalStatus(dayVo, "无打卡要求");
        } else if (allTimeSlotsValid) {
            setNormalStatus(dayVo, "正常");
        } else {
            setAbnormalStatus(dayVo, "缺卡");
        }
    }

    /**
     * 解析打卡时段
     */
    private List<TimeSlot> parseTimeSlots(String checkTime) {
        List<TimeSlot> slots = new ArrayList<>();
        try {
            if (checkTime != null && checkTime.startsWith("[") && checkTime.endsWith("]")) {
                JSONArray jsonArray = new JSONArray(checkTime);
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject obj = jsonArray.getJSONObject(i);
                    slots.add(new TimeSlot(
                        obj.getInt("num"),
                        obj.getString("startTime"),
                        obj.getString("endTime")
                    ));
                }
            }
        } catch (Exception e) {
            log.error("解析打卡时段出错: {}", checkTime, e);
        }
        return slots;
    }

    // 其他辅助方法保持不变...
    private CalendarDayVo buildBaseDayVo(LocalDate date, YearMonth targetMonth) {
        CalendarDayVo dayVo = new CalendarDayVo();
        dayVo.setDate(date.format(DateTimeFormatter.ISO_DATE));
        dayVo.setDayOfMonth(date.getDayOfMonth());
        dayVo.setCurrentMonth(date.getMonth() == targetMonth.getMonth());
        dayVo.setWorkDay(true);
        return dayVo;
    }

    private Optional<MAttRuleVo> findApplicableRule(List<MAttRecordVo> attendances,
                                                    List<MAttRuleVo> rules) {
        if (attendances.isEmpty() || rules.isEmpty()) {
            return Optional.empty();
        }
        Long ruleId = attendances.get(0).getRuleId();
        return rules.stream()
            .filter(r -> r.getId().equals(ruleId))
            .findFirst();
    }

    private void setNormalStatus(CalendarDayVo dayVo, String description) {
        dayVo.setAttStatus(0);
        dayVo.setStatusDetail("NORMAL");
        dayVo.setStatusDescription(description);
    }

    private void setAbnormalStatus(CalendarDayVo dayVo, String reason) {
        dayVo.setAttStatus(1);
        dayVo.setStatusDetail("ABNORMAL");
        dayVo.setStatusDescription(reason);
    }

    private void setAbsentStatus(CalendarDayVo dayVo) {
        dayVo.setAttStatus(2);
        dayVo.setStatusDetail("ABSENT");
        dayVo.setStatusDescription("缺勤");
    }

    private YearMonth parseYearMonth(String monthStr) {
        try {
            return YearMonth.parse(monthStr, DateTimeFormatter.ofPattern("yyyy-MM"));
        } catch (Exception e) {
            throw new ServiceException("月份格式不正确，请使用yyyy-MM格式");
        }
    }

}

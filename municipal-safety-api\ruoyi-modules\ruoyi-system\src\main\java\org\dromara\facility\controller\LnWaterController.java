package org.dromara.facility.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.facility.domain.vo.LnWaterVo;
import org.dromara.facility.domain.bo.LnWaterBo;
import org.dromara.facility.service.ILnWaterService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 绿能水表
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/lnWater")
public class LnWaterController extends BaseController {

    private final ILnWaterService lnWaterService;

    /**
     * 查询绿能水表列表
     */
    @GetMapping("/list")
    public TableDataInfo<LnWaterVo> list(LnWaterBo bo, PageQuery pageQuery) {
        return lnWaterService.queryPageList(bo, pageQuery);
    }
}

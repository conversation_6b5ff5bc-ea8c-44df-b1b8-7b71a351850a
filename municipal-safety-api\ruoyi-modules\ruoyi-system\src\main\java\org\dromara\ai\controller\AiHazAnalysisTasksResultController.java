package org.dromara.ai.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.ai.domain.bo.AiHazAnalysisTasksResultBo;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksResultVo;
import org.dromara.ai.service.IAiHazAnalysisTasksResultService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 隐患AI分析结果
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/ai_haz_analysis_tasks_result")
public class AiHazAnalysisTasksResultController extends BaseController {

    private final IAiHazAnalysisTasksResultService aiHazAnalysisTasksResultService;

    /**
     * 查询隐患AI分析结果列表
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks_result:list")
    @GetMapping("/list")
    public TableDataInfo<AiHazAnalysisTasksResultVo> list(AiHazAnalysisTasksResultBo bo, PageQuery pageQuery) {
        return aiHazAnalysisTasksResultService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出隐患AI分析结果列表
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks_result:export")
    @Log(title = "隐患AI分析结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiHazAnalysisTasksResultBo bo, HttpServletResponse response) {
        List<AiHazAnalysisTasksResultVo> list = aiHazAnalysisTasksResultService.queryList(bo);
        ExcelUtil.exportExcel(list, "隐患AI分析结果", AiHazAnalysisTasksResultVo.class, response);
    }

    /**
     * 获取隐患AI分析结果详细信息
     *
     * @param resultId 主键
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks_result:query")
    @GetMapping("/{resultId}")
    public R<AiHazAnalysisTasksResultVo> getInfo(@NotNull(message = "主键不能为空")
                                                 @PathVariable Long resultId) {
        return R.ok(aiHazAnalysisTasksResultService.queryById(resultId));
    }

    /**
     * 新增隐患AI分析结果
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks_result:add")
    @Log(title = "隐患AI分析结果", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiHazAnalysisTasksResultBo bo) {
        return toAjax(aiHazAnalysisTasksResultService.insertByBo(bo));
    }

    /**
     * 修改隐患AI分析结果
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks_result:edit")
    @Log(title = "隐患AI分析结果", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiHazAnalysisTasksResultBo bo) {
        return toAjax(aiHazAnalysisTasksResultService.updateByBo(bo));
    }

    /**
     * 删除隐患AI分析结果
     *
     * @param resultIds 主键串
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks_result:remove")
    @Log(title = "隐患AI分析结果", businessType = BusinessType.DELETE)
    @DeleteMapping("/{resultIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] resultIds) {
        return toAjax(aiHazAnalysisTasksResultService.deleteWithValidByIds(List.of(resultIds), true));
    }
}

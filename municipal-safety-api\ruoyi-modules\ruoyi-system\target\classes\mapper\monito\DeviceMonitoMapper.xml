<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.monito.mapper.DeviceMonitoMapper">

    <select id="baseMapper" parameterType="org.dromara.monito.domain.bo.DeviceMonitoBo" resultType="org.dromara.monito.domain.vo.DeviceMonitoVo">
        SELECT
        dm.monito_id,
        dm.project_id,
        dm.item_id,
        dm.device_name,
        dm.device_type,
        dm.device_code,
        dm.device_status,
        dm.enable_snapshot,
        dm.snapshot_time,
        dm.remarks,
        dm.del_flag,
        dm.create_dept,
        dm.create_by,
        dm.create_time,
        dm.update_by,
        dm.update_time,
        dm.channel_no,
        pp.project_name as projectName,
        phi.item_name as itemName
        FROM device_monito  dm
        left join prj_projects  pp on pp.project_id = dm.project_id
        left join prj_hazardous_items phi on phi.item_id = dm.item_id
        <where>
            dm.del_flag='0'
            <if test="deviceName != null and deviceName != ''">
                and dm.device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test=" deviceType != null and deviceType != ''">
                and dm.device_type = #{deviceType}
            </if>
            <if test=" deviceCode != null and deviceCode != ''">
                and dm.device_code = #{deviceCode}
            </if>
            <if test=" deviceStatus != null and deviceStatus != ''">
                and dm.device_status = #{deviceStatus}
            </if>
            <if test=" enableSnapshot != null and enableSnapshot != ''">
                and dm.enable_snapshot = #{enableSnapshot}
            </if>
            <if test="projectName != null and projectName != ''">
                and  pp.project_name like concat('%', #{projectName}, '%')
            </if>
            <if test="itemName != null and itemName != ''">
                and tdl.name like concat('%', #{itemName}, '%')
            </if>
            <if test="projectId != null and projectId != ''">
                and dm.project_id = #{projectId}
            </if>
            <if test="itemId != null and itemId != ''">
                and dm.item_id = #{itemId}
            </if>
        </where>
        ORDER BY monito_id ASC
    </select>


    <select id="getById" parameterType="long" resultType="org.dromara.monito.domain.vo.DeviceMonitoVo">
        SELECT
        dm.monito_id,
        dm.project_id,
        dm.item_id,
        dm.device_name,
        dm.device_type,
        dm.device_code,
        dm.device_status,
        dm.enable_snapshot,
        dm.snapshot_time,
        dm.remarks,
        dm.del_flag,
        dm.create_dept,
        dm.create_by,
        dm.create_time,
        dm.update_by,
        dm.update_time,
        dm.channel_no,
        pp.project_name as projectName,
        phi.item_name as itemName
        FROM device_monito  dm
        left join prj_projects  pp on pp.project_id = dm.project_id
        left join prj_hazardous_items phi on phi.item_id = dm.item_id
        where  dm.del_flag='0' and  dm.monito_id = #{monitoId}
    </select>

    <select id="getDeviceMonitoTreeInfo" resultType="java.util.Map" parameterType="org.dromara.monito.domain.bo.DeviceMonitoBo">
        select m.monito_id monitoId,
               m.device_name name,
               m.project_id projectId,
               m.item_id itemId,
               m.device_code deviceCode,
               IF(m.channel_no IS NULL, 1, m.channel_no) channelNo ,
               p.project_name projectName,
               p.province_name provinceName,
               p.city_name cityName,
               IF(p.city_name = '嘉峪关市', '嘉峪关市', p.district_name) AS districtName,
               i.item_name itemName,
            LEFT(i.danger_id,19) dangerId,
            d.name dangerName,
            pd.name pdName
        from device_monito m
            inner join prj_projects p on p.project_id = m.project_id
            inner join prj_hazardous_items i on i.item_id = m.item_id
            inner join t_danger_list d on d.danger_id = LEFT(i.danger_id,19)
            inner join t_danger_list pd on pd.danger_id = d.pre_id
        <where>
            m.del_flag = '0' and p.del_flag = '0' and i.del_flag = '0' and m.device_type = '1'
            <if test="projectName != null and projectName != ''">
                and p.project_name like concat('%', #{projectName}, '%')
            </if>
            <if test="itemName != null and itemName != ''">
                and i.item_name like concat('%', #{itemName}, '%')
            </if>
            <if test="provinceName != null and provinceName != ''">
                and p.project_name like concat('%', #{provinceName}, '%')
            </if>
            <if test="cityName != null and cityName != ''">
                and p.city_name like concat('%', #{cityName}, '%')
            </if>
            <if test="districtName != null and districtName != ''">
                and p.district_name like concat('%', #{districtName}, '%')
            </if>
            <if test="projectId != null and projectId != ''">
                and m.project_id = #{projectId}
            </if>
            <if test="itemId != null and itemId != ''">
                and m.item_id = #{itemId}
            </if>
        </where>
    </select>



    <select id="getItemNum" resultType="java.util.Map" parameterType="org.dromara.dp.domain.bo.DataViewBo">
        select
        d.dict_label itemsName,
        CAST(d.dict_value AS CHAR) itemsType,
        IFNULL(t.itemsNum, 0) AS itemsNum
        from sys_dict_data d
        left join
        (
        select
        i.danger_list_type itemsType,
        count(*) itemsNum
        from prj_hazardous_items i
        inner join prj_projects p on i.project_id = p.project_id
        where i.del_flag = '0' and p.del_flag = '0'
        <if test="areaCode != null and areaCode != ''">
            and (p.province_code = #{areaCode} or p.city_code = #{areaCode} or p.district_code = #{areaCode})
        </if>
        group by i.danger_list_type
        ) t
        on d.dict_value = t.itemsType
        where d.dict_type = 'danger_list_type'
    </select>

    <select id="getDangerNum" resultType="java.util.Map" parameterType="org.dromara.dp.domain.bo.DataViewBo">
        (
            select
                t.pdName as itemsName,
                count(*) itemsNum,
                sum(t.dfjNum) dfjNum,
                sum(t.yfjNum) yfjNum
            from
                (
                    select
                        pd.name pdName,
                        (select count(*) from ai_haz_analysis_tasks where item_id = i.item_id and recheck_status = 'PENDING_RECHECK') dfjNum,
                        (select count(*) from ai_haz_analysis_tasks where item_id = i.item_id and recheck_status = 'FINISH_RECHECK') yfjNum,
                        i.*
                    from prj_hazardous_items i
                             inner join prj_projects p on i.project_id = p.project_id
                             inner join t_danger_list d on d.danger_id = LEFT(i.danger_id,19)
                    inner join t_danger_list pd on pd.danger_id = d.pre_id
            where i.del_flag = '0' and p.del_flag = '0'
        <if test="areaCode != null and areaCode != ''">
            and (p.province_code = #{areaCode} or p.city_code = #{areaCode} or p.district_code = #{areaCode})
        </if>
            <if test="itemsType != null and itemsType != ''">
                and i.danger_list_type  = #{itemsType}
            </if>
        ) t group by t.pdName
)
UNION
(
	select name itemsName,0 as itemsNum,0 as dfjNum,0 as yfjNum from t_danger_list where pre_id
    in
    (
        select danger_id from t_danger_list
        where
        pre_id = 0
        <if test="itemsType != null and itemsType != ''">
            and type  = #{itemsType}
        </if>
    )
	and name not in
	(
		select
		DISTINCT pd.name
		from prj_hazardous_items i
		inner join prj_projects p on i.project_id = p.project_id
		inner join t_danger_list d on d.danger_id = LEFT(i.danger_id,19)
		inner join t_danger_list pd on pd.danger_id = d.pre_id
		where i.del_flag = '0' and p.del_flag = '0'
        <if test="areaCode != null and areaCode != ''">
            and (p.province_code = #{areaCode} or p.city_code = #{areaCode} or p.district_code = #{areaCode})
        </if>
        <if test="itemsType != null and itemsType != ''">
            and i.danger_list_type  = #{itemsType}
        </if>
	)
)
    </select>
    <select id="getProItemList" resultType="java.util.Map" parameterType="org.dromara.dp.domain.bo.DataViewBo">
        select
        CONCAT('/prj/prj_hazardous_items/',p.project_id)  url,
        <![CDATA[
       CONCAT('<strong>',p.project_name,'</strong><br/>','施工许可证编号: ',construction_permit_no,'<br/>所在地区：',p.province_name,' ',p.city_name,' ',p.district_name,'<br/>','项目位置：',p.location_detail,'<br/>','预算投资总额：',p.budget_total,
	' 万元','<br/>','占地面积：',p.site_area,' 平方米','<br/>','计划工期：',p.start_date,' 至 ',p.planned_end_date,'<br/>') info,
        ]]>
        p.*
        from prj_hazardous_items i
        inner join prj_projects p on i.project_id = p.project_id
        inner join t_danger_list d on d.danger_id = LEFT(i.danger_id,19)
        inner join t_danger_list pd on pd.danger_id = d.pre_id
        where i.del_flag = '0' and p.del_flag = '0'
        <if test="areaCode != null and areaCode != ''">
            and (p.province_code = #{areaCode} or p.city_code = #{areaCode} or p.district_code = #{areaCode})
        </if>
        <if test="itemsType != null and itemsType != ''">
            and i.danger_list_type  = #{itemsType}
        </if>
        <if test="dangerName != null and dangerName != ''">
            and pd.name = #{dangerName}
        </if>
    </select>

    <select id="getYearItemNum" parameterType="org.dromara.dp.domain.bo.DataViewBo" resultType="java.lang.Integer">
        select count(*)
        from prj_hazardous_items i
                 inner join prj_projects p on i.project_id = p.project_id
        where i.del_flag = '0' and p.del_flag = '0'
        <if test="areaCode != null and areaCode != ''">
            and (p.province_code = #{areaCode} or p.city_code = #{areaCode} or p.district_code = #{areaCode})
        </if>
        <if test="itemsType != null and itemsType != ''">
            and i.danger_list_type  = #{itemsType}
        </if>
        <if test="startDate != null and startDate != ''">
            and YEAR (p.start_date) =  #{startDate}
        </if>
    </select>


    <select id="getAreaItemNum" parameterType="org.dromara.dp.domain.bo.DataViewBo" resultType="java.lang.Integer">
        select
        count(*)
        from prj_hazardous_items i
        inner join prj_projects p on i.project_id = p.project_id
        where i.del_flag = '0' and p.del_flag = '0'
        <if test="itemsType != null and itemsType != ''">
            and i.danger_list_type = #{itemsType}
        </if>
        <if test="level != null  and level != '' and '1'.toString() == level">
            and p.province_code = #{areaCode}
        </if>
        <if test="level != null  and level != '' and '2'.toString() == level">
            and p.city_code = #{areaCode}
        </if>
        <if test="level != null  and level != '' and '3'.toString() == level">
            and p.district_code = #{areaCode}
        </if>
    </select>
</mapper>

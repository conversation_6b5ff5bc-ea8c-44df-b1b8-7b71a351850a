package org.dromara.plan.domain.bo;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.plan.domain.PrjPatrolPlan;

/**
 * 巡检计划业务对象 prj_patrol_plan
 *
 * <AUTHOR> Li
 * @date 2025-06-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrjPatrolPlan.class, reverseConvertGenerate = false)
public class PrjPatrolPlanBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long planId;

    /**
     * 计划名称
     */
    @NotNull(message = "计划名称", groups = { AddGroup.class })
    private String planName;

    /**
     * 计划开始时间
     */
    @NotNull(message = "计划开始时间不能为空", groups = { AddGroup.class })
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @NotNull(message = "计划结束时间不能为空", groups = { AddGroup.class })
    private Date endTime;

    /**
     * 监督机构id
     */
    @NotNull(message = "参与机构不能为空", groups = { AddGroup.class })
    private String deptIds;

    /**
     * 检查项目id
     */
    @NotNull(message = "检查项目不能为空", groups = { AddGroup.class })
    private String projectIds;

    /**
     * 专家ids
     */
    @NotNull(message = "专家不能为空", groups = { AddGroup.class })
    private String expertIds;

    /**
     *  单个专家id
     */
    private Long expert;

    /**
     *  单个机构
     */
    private Long deptId;

    /**
     * 备注
     */
    @NotNull(message = "简介不能为空", groups = { AddGroup.class })
    private String remarks;

}

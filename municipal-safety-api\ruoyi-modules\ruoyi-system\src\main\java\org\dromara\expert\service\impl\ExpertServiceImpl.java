package org.dromara.expert.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.base.BaseException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.expert.domain.Field;
import org.dromara.expert.domain.Project;
import org.dromara.expert.service.IFieldService;
import org.dromara.expert.service.IProjectService;
import org.dromara.system.domain.Division;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.domain.vo.DivisionVo;
import org.dromara.system.mapper.DivisionMapper;
import org.dromara.system.service.ISysUserService;
import org.springframework.stereotype.Service;
import org.dromara.expert.domain.bo.ExpertBo;
import org.dromara.expert.domain.vo.ExpertVo;
import org.dromara.expert.domain.Expert;
import org.dromara.expert.mapper.ExpertMapper;
import org.dromara.expert.service.IExpertService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.Objects;

/**
 *  专家主Service业务层处理
 * @date 2025-05-03
 */
@RequiredArgsConstructor
@Service
public class ExpertServiceImpl implements IExpertService {

    private final ExpertMapper baseMapper;

    private final DivisionMapper divisionMapper;

    private final IFieldService fieldService;

    private final IProjectService projectService;
    private final ISysUserService userService;


    /**
     * 查询 专家主
     *
     * @param expertId 主键
     * @return  专家主
     */
    @Override
    public ExpertVo queryById(Long expertId){
        ExpertVo record = baseMapper.selectVoById(expertId);
        if (!Objects.isNull( record)){
            // 行政区划
            DivisionVo province = divisionMapper.selectVoOne(new LambdaQueryWrapper<Division>().eq(Division::getDivisionCode, record.getProvince()));
            record.setProvinceName(province.getDivisionName());
            DivisionVo city = divisionMapper.selectVoOne(new LambdaQueryWrapper<Division>().eq(Division::getDivisionCode, record.getCity()));
            record.setCityName(city.getDivisionName());
            if (StringUtils.isNotBlank(record.getArea())){
                DivisionVo area = divisionMapper.selectVoOne(new LambdaQueryWrapper<Division>().eq(Division::getDivisionCode, record.getArea()));
                record.setAreaName(area.getDivisionName());
            }
            // 领域 项目
            List<Field> fieldList = fieldService.list(new LambdaQueryWrapper<Field>().eq(Field::getExpertId, record.getExpertId()));
            record.setExpertFieldList(fieldList);
            List<Project> projectList = projectService.list(new LambdaQueryWrapper<Project>().eq(Project::getExpertId, record.getExpertId()));
            record.setExpertProjectList(projectList);
        }
        return record;
    }

    /**
     * 分页查询 专家主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return  专家主分页列表
     */
    @Override
    public TableDataInfo<ExpertVo> queryPageList(ExpertBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Expert> lqw = buildQueryWrapper(bo);
        Page<ExpertVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        for (ExpertVo record : result.getRecords()) {
            // 行政区划
            DivisionVo province = divisionMapper.selectVoOne(new LambdaQueryWrapper<Division>().eq(Division::getDivisionCode, record.getProvince()));
            record.setProvinceName(province.getDivisionName());
            DivisionVo city = divisionMapper.selectVoOne(new LambdaQueryWrapper<Division>().eq(Division::getDivisionCode, record.getCity()));
            record.setCityName(city.getDivisionName());
            if (StringUtils.isNotEmpty(record.getArea())){
                DivisionVo area = divisionMapper.selectVoOne(new LambdaQueryWrapper<Division>().eq(Division::getDivisionCode, record.getArea()));
                record.setAreaName(area.getDivisionName());
            }else {
                record.setAreaName("");
            }
            // 领域 项目
            List<Field> fieldList = fieldService.list(new LambdaQueryWrapper<Field>().eq(Field::getExpertId, record.getExpertId()));
            record.setExpertFieldList(fieldList);
            List<Project> projectList = projectService.list(new LambdaQueryWrapper<Project>().eq(Project::getExpertId, record.getExpertId()));
            record.setExpertProjectList(projectList);
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的 专家主列表
     *
     * @param bo 查询条件
     * @return  专家主列表
     */
    @Override
    public List<ExpertVo> queryList(ExpertBo bo) {
        LambdaQueryWrapper<Expert> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Expert> buildQueryWrapper(ExpertBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Expert> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Expert::getExpertId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), Expert::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getIdCard()), Expert::getIdCard, bo.getIdCard());
        lqw.eq(StringUtils.isNotBlank(bo.getSex()), Expert::getSex, bo.getSex());
        lqw.eq(StringUtils.isNotBlank(bo.getWorkUnit()), Expert::getWorkUnit, bo.getWorkUnit());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), Expert::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getIntroduce()), Expert::getIntroduce, bo.getIntroduce());
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), Expert::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getProvince()), Expert::getProvince, bo.getProvince());
        lqw.eq(StringUtils.isNotBlank(bo.getCity()), Expert::getCity, bo.getCity());
        lqw.eq(StringUtils.isNotBlank(bo.getArea()), Expert::getArea, bo.getArea());
        lqw.eq(StringUtils.isNotBlank(bo.getMajor()), Expert::getMajor, bo.getMajor());
        lqw.eq(StringUtils.isNotBlank(bo.getIndustry()), Expert::getIndustry, bo.getIndustry());
        lqw.orderByDesc(Expert::getCreateTime);
        return lqw;
    }

    /**
     * 新增 专家
     *
     * @param bo  专家
     * @return 是否新增成功
     */
    @Override
    @Transactional
    public R insertByBo(ExpertBo bo) {
        bo.setDelFlag("0");
        Expert add = MapstructUtils.convert(bo, Expert.class);
        validEntityBeforeSave(add);
        // 组装用户信息
        SysUserBo user = new SysUserBo();
        user.setUserName(add.getIdCard());
        user.setNickName(add.getName());
        user.setPhonenumber(add.getPhone());
        user.setPassword(BCrypt.hashpw(add.getPhone()));
        user.setStatus("0");
        user.setUserType("sys_user");
        Long [] roleIds = {1920391944816410626L};
        user.setRoleIds(roleIds);
        // 校验
        if (!userService.checkUserNameUnique(user)) {
            return R.fail("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user)) {
            return R.fail("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
            return R.fail("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        int i = userService.insertUser(user);
        if (i < 0){
            throw new BaseException("添加用户异常");
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setExpertId(add.getExpertId());
        }else {
            throw new BaseException("添加用户异常");
        }
        for (Field fieldBo : bo.getExpertFieldList()) {
            fieldBo.setDelFlag("0");
            fieldBo.setExpertId(add.getExpertId());
        }
        for (Project projectBo : bo.getExpertProjectList()) {
            projectBo.setDelFlag("0");
            projectBo.setExpertId(add.getExpertId());
        }
        fieldService.saveBatch(bo.getExpertFieldList());
        projectService.saveBatch(bo.getExpertProjectList());
        return R.ok();
    }

    /**
     * 修改 专家组
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ExpertBo bo) {
        Expert update = MapstructUtils.convert(bo, Expert.class);
        validEntityBeforeSave(update);
        for (Field fieldBo : bo.getExpertFieldList()) {
            if (fieldBo.getFieldId() == null) {
                fieldBo.setDelFlag("0");
                fieldBo.setExpertId(update.getExpertId());
            }
        }
        for (Project projectBo : bo.getExpertProjectList()) {
            if (projectBo.getProjectId() == null){
                projectBo.setDelFlag("0");
                projectBo.setExpertId(update.getExpertId());
            }
        }
        fieldService.saveOrUpdateBatch(bo.getExpertFieldList());
        projectService.saveOrUpdateBatch(bo.getExpertProjectList());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Expert entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除 专家主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}

package org.dromara.projects.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.projects.domain.PrjSafeUser;

import java.io.Serial;
import java.io.Serializable;


/**
 * 【安拆任务】项目人员视图对象 prj_safe_user
 *
 * <AUTHOR> Li
 * @date 2025-08-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjSafeUser.class)
public class PrjSafeUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long saleUserId;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码 ")
    private String mobile;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String userName;

    /**
     * 身份证号
     */
    @ExcelProperty(value = "身份证号")
    private String idCard;

    /**
     * 岗位类型
     * 项目经
     * 理:ProjectManager
     * 安全总监:SafetyDirector
     * 安全经
     * 理:SafetyManager
     * 安全员:SafetyOfficer
     * 机管员:MachineKeeper
     */
    @ExcelProperty(value = "岗位类型")
    private String positionType;

    /**
     * 关联安拆prj_safe_task.open_task_id
     */
    private Long openTaskId;
}

package org.dromara.ai.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.ai.domain.AiHazAnalysisTasksResult;
import org.dromara.ai.domain.AiHazAnalysisTasksResultToAiHazAnalysisTasksResultVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {AiHazAnalysisTasksResultToAiHazAnalysisTasksResultVoMapper.class},
    imports = {}
)
public interface AiHazAnalysisTasksResultVoToAiHazAnalysisTasksResultMapper extends BaseMapper<AiHazAnalysisTasksResultVo, AiHazAnalysisTasksResult> {
}

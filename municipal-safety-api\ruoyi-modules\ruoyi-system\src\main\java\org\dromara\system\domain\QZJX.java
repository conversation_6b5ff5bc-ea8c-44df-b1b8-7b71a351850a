package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qzjx")
public class QZJX extends BaseEntity {

    @TableId(value = "id")
    private Integer id;
    private String constructionPermitNum;     // 施工许可证编号
    private String sbbabh;                   // 设备备案编号
    private String sblx;                     // 设备类型
    private String sbmc;                     // 设备名称
    private String ggxh;                     // 规格型号
    private String sccs;                     // 生产厂商
    private String sccstyshxydm;             // 生产厂商统一社会信用代码
    private String ccbh;                     // 出厂编号
    private Date ccrq;                       // 出厂日期
    private String zzxkzh;                   // 特种设备生产许可证编号
    private String synx;                     // 使用年限
    private String cqdw;                     // 产权单位
    private String cqdwtyshxydm;             // 产权单位统一社会信用代码
    private String cqdwdz;                   // 产权单位地址
    private String qyfrdb;                   // 企业法人代表
    private String frsfzh;                   // 法定代表人身份证件号
    private String lxr;                      // 联系人
    private String lxdh;                     // 联系电话
    private BigDecimal gzjg;                 // 出厂价格（万元）
    private Date gzny;                       // 购置年月
    private String sbbajg;                   // 设备备案机关
    private String fzjgtryshdm;              // 发证机关代码
    private String jxszds;                   // 机械所在地市
    private String jxszqx;                   // 机械所在区县
    private String jxqy;                     // 机械区域
    private BigDecimal edqzl;                // 额定起重量（T）
    private BigDecimal edqzlj;               // 额定起重力矩（T·M）
    private BigDecimal qzcb;                 // 起重臂长度（M）
    private BigDecimal zdgzfd;               // 最大工作幅度（M）
    private BigDecimal zdfdedqzl;            // 最大幅度额定起重量（T）
    private BigDecimal zddlqsgd;             // 独起升高度（M）
    private BigDecimal zdqsgd;               // 最大起升高度（M）
    private String zyjgjwybh;                // 主要结构件唯一编号
    private BigDecimal nazzdgd;              // 拟安装最大高度（m）
    private String zyjgjgg;                  // 标准节主要结构件规格
    private String jqjcs;                    // 加强节参数（长×宽×高）mm
    private String bzjcs;                    // 标准节参数（长×宽×高）mm
    private String sgsjjytlx;                // 施工升降机用途类型
    private String ddjzgl;                   // 电动机总功率（kW）
    private BigDecimal edtssd;               // 额定提升速度（m/min）
    private String fzaqqxh;                  // 防坠安全器型号
    private String jkcc;                     // 运载装置（吊笼）净空尺寸（长×宽×高）m
    private String sblb;                     // 设备类别
    private String msqzjkd;                  // 门式起重机跨度（m）
    @TableLogic
    private String delFlag;  //删除标志（0代表存在 1代表删除）
}

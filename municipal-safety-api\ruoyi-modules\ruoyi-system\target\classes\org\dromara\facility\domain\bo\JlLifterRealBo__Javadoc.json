{"doc": " 升降机实时数据业务对象 jl_lifter_real\n\n <AUTHOR>\n @date 2025-07-24\n", "fields": [{"name": "id", "doc": " 主键\n"}, {"name": "devNo", "doc": " 设备编号\n"}, {"name": "tcNo", "doc": " 吊笼编号\n"}, {"name": "lifterRight", "doc": " 0 是左   1是右\n"}, {"name": "tcRightStr", "doc": " 吊笼编号和左右笼\n"}, {"name": "date", "doc": " 时间\n"}, {"name": "weight", "doc": " 实时起重量\n"}, {"name": "weightPercent", "doc": " 重量百分比\n"}, {"name": "person", "doc": " 实时人数\n"}, {"name": "height", "doc": " 实时高度\n"}, {"name": "heightPercent", "doc": " 高度百分比\n"}, {"name": "speed", "doc": " 实时速度\n"}, {"name": "speedDir", "doc": " 速度方向 0停止，1上，2下\n"}, {"name": "slant1", "doc": " 实时倾斜度\n"}, {"name": "slant1Percent", "doc": " 倾斜百分比\n"}, {"name": "slant2", "doc": " 实时倾斜度\n"}, {"name": "slant2Percent", "doc": " 倾斜百分比\n"}, {"name": "driver<PERSON><PERSON>", "doc": " 驾驶员身份认证结果 00为未认证 01为已认证\n"}, {"name": "frontDoor", "doc": " 前门状态 数值1代表开启，0带便关闭\n"}, {"name": "backD<PERSON>", "doc": " 后门状态 数值1代表开启，0带便关闭\n"}, {"name": "doorLock", "doc": " 门锁异常指示 0无异常1有异常\n"}, {"name": "systemStatusWeight", "doc": " 重量\n"}, {"name": "systemStatusHeight", "doc": " 高度限位\n"}, {"name": "systemStatusSpeed", "doc": " 超速\n"}, {"name": "systemS<PERSON><PERSON><PERSON>erson", "doc": " 人数\n"}, {"name": "systemStatusSlant", "doc": " 倾斜\n"}, {"name": "systemStatusFrontDoor", "doc": " 前门锁状态：数字0正常,数值1异常\n"}, {"name": "systemStatusBackDoor", "doc": " 后门锁状态：数字0正常,数值1异常\n"}, {"name": "systemStatusWindSpeed", "doc": " 风速，0表示正常，1表示预警，2表示报警\n"}, {"name": "systemStatusUpperLimit", "doc": " 上限位，0表示正常，1表示报警\n"}, {"name": "systemStatusFallingProtector", "doc": " 防坠器，0表示正常，1表示报警\n"}, {"name": "windSpeed", "doc": " 实时风速\n"}, {"name": "currentFloor", "doc": " 当前楼层\n"}, {"name": "uncovered", "doc": " 未戴安全帽的人数\n"}, {"name": "createTime", "doc": " 创建时间\n"}], "enumConstants": [], "methods": [], "constructors": []}
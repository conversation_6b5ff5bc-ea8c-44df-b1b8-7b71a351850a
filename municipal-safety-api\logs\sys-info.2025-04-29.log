2025-04-29 18:48:58 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-04-29 18:48:58 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 27904 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-29 18:48:58 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-04-29 18:49:03 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 18:49:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-04-29 18:49:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-04-29 18:49:04 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@334ac669
2025-04-29 18:49:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-04-29 18:49:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-04-29 18:49:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-04-29 18:49:06 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-04-29 18:49:06 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-04-29 18:49:06 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-04-29 18:49:06 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-04-29 18:49:07 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-29 18:54:35 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-04-29 18:54:40 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 18:54:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-04-29 18:54:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-04-29 18:54:41 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7da40bf4
2025-04-29 18:54:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-04-29 18:54:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-04-29 18:54:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-04-29 18:54:43 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-04-29 18:54:43 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-04-29 18:54:43 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-04-29 18:54:43 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-04-29 18:54:45 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-29 18:58:41 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-04-29 18:58:46 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 18:58:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-04-29 18:58:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-04-29 18:58:47 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@********
2025-04-29 18:58:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-04-29 18:58:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-04-29 18:58:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-04-29 18:58:49 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-04-29 18:58:49 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-04-29 18:58:49 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-04-29 18:58:49 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-04-29 18:58:50 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-29 19:47:28 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-04-29 19:47:33 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 19:47:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-04-29 19:47:33 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-04-29 19:47:34 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@257da215
2025-04-29 19:47:34 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-04-29 19:47:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-04-29 19:47:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-04-29 19:47:35 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-04-29 19:47:36 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-04-29 19:47:36 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-04-29 19:47:36 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-04-29 19:47:37 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>
package org.dromara.facility.domain;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.vo.JlLifterRealVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class JlLifterRealToJlLifterRealVoMapperImpl implements JlLifterRealToJlLifterRealVoMapper {

    @Override
    public JlLifterRealVo convert(JlLifterReal arg0) {
        if ( arg0 == null ) {
            return null;
        }

        JlLifterRealVo jlLifterRealVo = new JlLifterRealVo();

        jlLifterRealVo.setId( arg0.getId() );
        jlLifterRealVo.setDevNo( arg0.getDevNo() );
        jlLifterRealVo.setTcNo( arg0.getTcNo() );
        jlLifterRealVo.setLifterRight( arg0.getLifterRight() );
        jlLifterRealVo.setTcRightStr( arg0.getTcRightStr() );
        jlLifterRealVo.setDate( arg0.getDate() );
        jlLifterRealVo.setWeight( arg0.getWeight() );
        jlLifterRealVo.setWeightPercent( arg0.getWeightPercent() );
        jlLifterRealVo.setPerson( arg0.getPerson() );
        jlLifterRealVo.setHeight( arg0.getHeight() );
        jlLifterRealVo.setHeightPercent( arg0.getHeightPercent() );
        jlLifterRealVo.setSpeed( arg0.getSpeed() );
        jlLifterRealVo.setSpeedDir( arg0.getSpeedDir() );
        jlLifterRealVo.setSlant1( arg0.getSlant1() );
        jlLifterRealVo.setSlant1Percent( arg0.getSlant1Percent() );
        jlLifterRealVo.setSlant2( arg0.getSlant2() );
        jlLifterRealVo.setSlant2Percent( arg0.getSlant2Percent() );
        jlLifterRealVo.setDriverAuth( arg0.getDriverAuth() );
        jlLifterRealVo.setFrontDoor( arg0.getFrontDoor() );
        jlLifterRealVo.setBackDoor( arg0.getBackDoor() );
        jlLifterRealVo.setDoorLock( arg0.getDoorLock() );
        jlLifterRealVo.setSystemStatusWeight( arg0.getSystemStatusWeight() );
        jlLifterRealVo.setSystemStatusHeight( arg0.getSystemStatusHeight() );
        jlLifterRealVo.setSystemStatusSpeed( arg0.getSystemStatusSpeed() );
        jlLifterRealVo.setSystemStatusPerson( arg0.getSystemStatusPerson() );
        jlLifterRealVo.setSystemStatusSlant( arg0.getSystemStatusSlant() );
        jlLifterRealVo.setSystemStatusFrontDoor( arg0.getSystemStatusFrontDoor() );
        jlLifterRealVo.setSystemStatusBackDoor( arg0.getSystemStatusBackDoor() );
        jlLifterRealVo.setSystemStatusWindSpeed( arg0.getSystemStatusWindSpeed() );
        jlLifterRealVo.setSystemStatusUpperLimit( arg0.getSystemStatusUpperLimit() );
        jlLifterRealVo.setSystemStatusFallingProtector( arg0.getSystemStatusFallingProtector() );
        jlLifterRealVo.setWindSpeed( arg0.getWindSpeed() );
        jlLifterRealVo.setCurrentFloor( arg0.getCurrentFloor() );
        jlLifterRealVo.setUncovered( arg0.getUncovered() );
        jlLifterRealVo.setCreateTime( arg0.getCreateTime() );

        return jlLifterRealVo;
    }

    @Override
    public JlLifterRealVo convert(JlLifterReal arg0, JlLifterRealVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setTcNo( arg0.getTcNo() );
        arg1.setLifterRight( arg0.getLifterRight() );
        arg1.setTcRightStr( arg0.getTcRightStr() );
        arg1.setDate( arg0.getDate() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setWeightPercent( arg0.getWeightPercent() );
        arg1.setPerson( arg0.getPerson() );
        arg1.setHeight( arg0.getHeight() );
        arg1.setHeightPercent( arg0.getHeightPercent() );
        arg1.setSpeed( arg0.getSpeed() );
        arg1.setSpeedDir( arg0.getSpeedDir() );
        arg1.setSlant1( arg0.getSlant1() );
        arg1.setSlant1Percent( arg0.getSlant1Percent() );
        arg1.setSlant2( arg0.getSlant2() );
        arg1.setSlant2Percent( arg0.getSlant2Percent() );
        arg1.setDriverAuth( arg0.getDriverAuth() );
        arg1.setFrontDoor( arg0.getFrontDoor() );
        arg1.setBackDoor( arg0.getBackDoor() );
        arg1.setDoorLock( arg0.getDoorLock() );
        arg1.setSystemStatusWeight( arg0.getSystemStatusWeight() );
        arg1.setSystemStatusHeight( arg0.getSystemStatusHeight() );
        arg1.setSystemStatusSpeed( arg0.getSystemStatusSpeed() );
        arg1.setSystemStatusPerson( arg0.getSystemStatusPerson() );
        arg1.setSystemStatusSlant( arg0.getSystemStatusSlant() );
        arg1.setSystemStatusFrontDoor( arg0.getSystemStatusFrontDoor() );
        arg1.setSystemStatusBackDoor( arg0.getSystemStatusBackDoor() );
        arg1.setSystemStatusWindSpeed( arg0.getSystemStatusWindSpeed() );
        arg1.setSystemStatusUpperLimit( arg0.getSystemStatusUpperLimit() );
        arg1.setSystemStatusFallingProtector( arg0.getSystemStatusFallingProtector() );
        arg1.setWindSpeed( arg0.getWindSpeed() );
        arg1.setCurrentFloor( arg0.getCurrentFloor() );
        arg1.setUncovered( arg0.getUncovered() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}

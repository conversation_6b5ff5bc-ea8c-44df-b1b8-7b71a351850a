package org.dromara.flow.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.flow.domain.PrjHazardousItemsSpecialWarning;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsSpecialWarningVoToPrjHazardousItemsSpecialWarningMapperImpl implements PrjHazardousItemsSpecialWarningVoToPrjHazardousItemsSpecialWarningMapper {

    @Override
    public PrjHazardousItemsSpecialWarning convert(PrjHazardousItemsSpecialWarningVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsSpecialWarning prjHazardousItemsSpecialWarning = new PrjHazardousItemsSpecialWarning();

        prjHazardousItemsSpecialWarning.setCreateTime( arg0.getCreateTime() );
        prjHazardousItemsSpecialWarning.setWarningId( arg0.getWarningId() );
        prjHazardousItemsSpecialWarning.setTaskId( arg0.getTaskId() );
        prjHazardousItemsSpecialWarning.setReason( arg0.getReason() );
        prjHazardousItemsSpecialWarning.setReasonType( arg0.getReasonType() );

        return prjHazardousItemsSpecialWarning;
    }

    @Override
    public PrjHazardousItemsSpecialWarning convert(PrjHazardousItemsSpecialWarningVo arg0, PrjHazardousItemsSpecialWarning arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setWarningId( arg0.getWarningId() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setReason( arg0.getReason() );
        arg1.setReasonType( arg0.getReasonType() );

        return arg1;
    }
}

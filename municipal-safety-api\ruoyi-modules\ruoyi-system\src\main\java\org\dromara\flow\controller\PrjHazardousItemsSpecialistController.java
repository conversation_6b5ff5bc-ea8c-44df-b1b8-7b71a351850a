package org.dromara.flow.controller;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.flow.domain.PrjHazardousItemsSpecialist;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistBo;
import org.dromara.flow.domain.dto.SpecialistThreeDTO;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistVo;
import org.dromara.flow.domain.vo.SpecialistThreeVO;
import org.dromara.flow.service.IPrjHazardousItemsSpecialistService;
import org.dromara.warm.flow.orm.entity.FlowInstance;
import org.dromara.workflow.domain.vo.FlowHisTaskVo;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;

/**
 * 省厅自动工单
 *
 * <AUTHOR> Li
 * @date 2025-06-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/hazardousItemsSpecialist")
public class PrjHazardousItemsSpecialistController extends BaseController {

    private final IPrjHazardousItemsSpecialistService prjHazardousItemsSpecialistService;

    /**
     * 查询省厅自动工单列表
     */
    @SaCheckPermission("system:hazardousItemsSpecialist:list")
    @GetMapping("/list")
    public TableDataInfo<PrjHazardousItemsSpecialistVo> list(PrjHazardousItemsSpecialistBo bo, PageQuery pageQuery) {
        return prjHazardousItemsSpecialistService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出省厅自动工单列表
     */
    @SaCheckPermission("system:hazardousItemsSpecialist:export")
    @Log(title = "省厅自动工单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PrjHazardousItemsSpecialistBo bo, HttpServletResponse response) {
        List<PrjHazardousItemsSpecialistVo> list = prjHazardousItemsSpecialistService.queryList(bo);
        ExcelUtil.exportExcel(list, "省厅自动工单", PrjHazardousItemsSpecialistVo.class, response);
    }

    /**
     * 获取省厅自动工单详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:hazardousItemsSpecialist:query")
    @GetMapping("/{id}")
    public R<PrjHazardousItemsSpecialistVo> getInfo(@NotNull(message = "主键不能为空")
                                                    @PathVariable Long id) {
        return R.ok(prjHazardousItemsSpecialistService.queryById(id));
    }

    /**
     * 新增省厅自动工单
     */
    @SaCheckPermission("system:hazardousItemsSpecialist:add")
    @Log(title = "省厅自动工单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PrjHazardousItemsSpecialistBo bo) {
        return toAjax(prjHazardousItemsSpecialistService.insertByBo(bo));
    }

    /**
     * 修改省厅自动工单
     */
    @SaCheckPermission("system:hazardousItemsSpecialist:edit")
    @Log(title = "省厅自动工单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PrjHazardousItemsSpecialistBo bo) {
        return toAjax(prjHazardousItemsSpecialistService.updateByBo(bo));
    }

    /**
     * 删除省厅自动工单
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:hazardousItemsSpecialist:remove")
    @Log(title = "省厅自动工单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(prjHazardousItemsSpecialistService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取省厅自动工单详细信息
     */
    @GetMapping("/detail/{taskId}")
    public R<PrjHazardousItemsSpecialistVo> getDetail(@PathVariable String taskId) {
        return R.ok(prjHazardousItemsSpecialistService.getDetail(taskId));
    }

    /**
     * 存储厅局决策
     *
     * @param dto
     * @return
     */
    @PostMapping("/saveThree")
    public R<Void> saveThree(@RequestBody SpecialistThreeDTO dto) {
        return toAjax(prjHazardousItemsSpecialistService.saveThree(dto));
    }

    /**
     * 存储厅局决策
     *
     * @param specialist
     * @return
     */
    @PostMapping("/saveThree2")
    public R<Void> saveThree2(@RequestBody PrjHazardousItemsSpecialist specialist) {
        return toAjax(prjHazardousItemsSpecialistService.saveThree2(specialist));
    }

    /**
     * 存储厅局决策
     *
     * @param taskId
     * @return
     */
    @GetMapping("/threeDetail/{taskId}")
    public R<SpecialistThreeVO> threeDetail(@PathVariable String taskId) {
        return R.ok(prjHazardousItemsSpecialistService.threeDetail(taskId));
    }

    @GetMapping("/threeInstance/{taskId}")
    public R<FlowHisTaskVo> getInstance(@PathVariable String taskId){
        return R.ok(prjHazardousItemsSpecialistService.getInstance(taskId));
    }
}

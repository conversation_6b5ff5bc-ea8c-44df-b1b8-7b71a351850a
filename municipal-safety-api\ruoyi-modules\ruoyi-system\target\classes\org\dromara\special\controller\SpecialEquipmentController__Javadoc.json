{"doc": " 特种设备\n\n <AUTHOR>\n @date 2025-05-14\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.special.domain.bo.SpecialEquipmentBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询特种设备列表\n"}, {"name": "export", "paramTypes": ["org.dromara.special.domain.bo.SpecialEquipmentBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出特种设备列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取特种设备详细信息\n\n @param equipmentId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.special.domain.bo.SpecialEquipmentBo"], "doc": " 新增特种设备\n"}, {"name": "edit", "paramTypes": ["org.dromara.special.domain.bo.SpecialEquipmentBo"], "doc": " 修改特种设备\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除特种设备\n\n @param equipmentIds 主键串\n"}, {"name": "syncProject", "paramTypes": ["org.dromara.special.domain.sync.SyncEntity"], "doc": " 同步信息到特种设备或特种人员信息\n @param syncEntity 同步实体\n"}], "constructors": []}
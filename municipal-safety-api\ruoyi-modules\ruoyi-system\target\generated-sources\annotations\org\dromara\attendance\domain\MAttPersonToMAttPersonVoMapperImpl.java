package org.dromara.attendance.domain;

import javax.annotation.processing.Generated;
import org.dromara.attendance.domain.vo.MAttPersonVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class MAttPersonToMAttPersonVoMapperImpl implements MAttPersonToMAttPersonVoMapper {

    @Override
    public MAttPersonVo convert(MAttPerson arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MAttPersonVo mAttPersonVo = new MAttPersonVo();

        mAttPersonVo.setId( arg0.getId() );
        mAttPersonVo.setPersonId( arg0.getPersonId() );
        mAttPersonVo.setSnId( arg0.getSnId() );

        return mAttPersonVo;
    }

    @Override
    public MAttPersonVo convert(MAttPerson arg0, MAttPersonVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setPersonId( arg0.getPersonId() );
        arg1.setSnId( arg0.getSnId() );

        return arg1;
    }
}

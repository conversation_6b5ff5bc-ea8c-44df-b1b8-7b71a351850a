<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.attendance.mapper.MAttRuleMapper">

    <resultMap type="org.dromara.attendance.domain.vo.MAttRuleVo" id="MAttRuleResult"></resultMap>

    <select id="selectMattRuleList" resultMap="MAttRuleResult">
        select a.*, p.project_name
        from m_att_rule a
        left join prj_projects p on p.project_id = a.project_id
        <where>
            <if test="projectId != null ">and a.project_id = #{projectId}</if>
            <if test="personType != null ">and a.person_type = #{personType}</if>
            <if test="fieldCheck != null ">and a.field_check = #{fieldCheck}</if>
            <if test="createDept != null ">and a.create_dept = #{createDept}</if>
        </where>
        order by a.create_time desc
    </select>

    <select id="selectMAttRuleByRoleIdAndByProjectId" resultMap="MAttRuleResult">
        select a.*
        from m_att_rule a
        where a.person_type like concat('%', #{personType}, '%') and a.project_id = #{projectId}
    </select>

    <select id="selectMAttRuleByProjectId" resultMap="MAttRuleResult">
        select a.*
        from m_att_rule a
        where a.project_id = #{projectId}
    </select>
</mapper>

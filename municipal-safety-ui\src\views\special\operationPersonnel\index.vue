<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="项目名称" prop="projectId">
              <el-select v-model="queryParams.projectId" filterable placeholder="请选择项目" style="width: 240px" clearable
                @change="changePro">
                <el-option v-for="item in projectSelectData" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="人员姓名" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="queryParams.idCard" placeholder="请输入身份证号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="人员性别" prop="gender">
              <el-select v-model="queryParams.gender" placeholder="请选择性别" clearable>
                <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="证书编号" prop="certificateNumber">
              <el-input v-model="queryParams.certificateNumber" placeholder="请输入证书编号" clearable
                @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="操作类别" prop="operationCategory">
              <el-select v-model="queryParams.operationCategory" placeholder="请选择操作类别" clearable>
                <el-option v-for="dict in special_operation_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="发证机关" prop="issuer">
              <el-input v-model="queryParams.issuer" placeholder="请输入发证机关" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证书状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择证书状态" clearable>
                <el-option v-for="dict in special_operation_license_status" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header v-if="route.params.id">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['special:operationPersonnel:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              v-hasPermi="['special:operationPersonnel:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['special:operationPersonnel:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['special:operationPersonnel:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="operationPersonnelList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column label="人员姓名" align="center" prop="name" />
        <el-table-column label="身份证号" align="center" prop="idCard" />
        <el-table-column label="人员性别" align="center" prop="gender">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
          </template>
        </el-table-column>
        <el-table-column label="出生日期" align="center" prop="birthdate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.birthdate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="证书编号" align="center" prop="certificateNumber" />
        <el-table-column label="操作类别" align="center" prop="operationCategory">
          <template #default="scope">
            <dict-tag :options="special_operation_type" :value="scope.row.operationCategory" />
          </template>
        </el-table-column>
        <el-table-column label="发证机关" align="center" prop="issuer" />
        <el-table-column label="证书状态" align="center" prop="status" width="100px">
          <template #default="scope">
            <dict-tag :options="special_operation_license_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <div v-if="route.params.id">
              <el-tooltip content="修改" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                  v-hasPermi="['special:operationPersonnel:edit']"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                  v-hasPermi="['special:operationPersonnel:remove']"></el-button>
              </el-tooltip>
            </div>

            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="Tickets" @click="handleDetail(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改特种作业人员信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="1200px" append-to-body>
      <el-form ref="operationPersonnelFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="证书编号" prop="certificateNumber">
          <el-input v-model="form.certificateNumber" placeholder="请输入证书编号" />
        </el-form-item>
        <el-form-item label="人员姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="人员性别" prop="gender">
          <el-select v-model="form.gender" placeholder="请选择性别">
            <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="出生日期" prop="birthdate">
          <el-date-picker clearable v-model="form.birthdate" type="date" value-format="YYYY-MM-DD HH:mm:ss "
            placeholder="请选择出生日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="操作类别" prop="operationCategory">
          <el-select v-model="form.operationCategory" placeholder="请选择操作类别">
            <el-option v-for="dict in special_operation_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发证机关" prop="issuer">
          <el-input v-model="form.issuer" placeholder="请输入发证机关" />
        </el-form-item>
        <el-form-item label="初次领证日期" prop="firstIssueDate">
          <el-date-picker clearable v-model="form.firstIssueDate" type="date" value-format="YYYY-MM-DD HH:mm:ss "
            placeholder="请选择初次领证日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="最近发证日期" prop="lastIssueDate">
          <el-date-picker clearable v-model="form.lastIssueDate" type="date" value-format="YYYY-MM-DD HH:mm:ss "
            placeholder="请选择最近发证日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="有效期开始" prop="validityStart">
          <el-date-picker clearable v-model="form.validityStart" type="date" value-format="YYYY-MM-DD HH:mm:ss "
            placeholder="请选择有效期开始">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="有效期截止" prop="validityEnd">
          <el-date-picker clearable v-model="form.validityEnd" type="date" value-format="YYYY-MM-DD HH:mm:ss "
            placeholder="请选择有效期截止">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="项目名称" prop="projectId" label-width="129px">
          <el-input v-model="form.projectName" placeholder="请选择项目" readonly>
            <template #append>
              <el-button-group>
                <el-button @click="selectProjectChange">选择</el-button>
              </el-button-group>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="证书状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in special_operation_license_status" :key="dict.value" :value="dict.value">{{
              dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="电子证照链接" prop="electronicLicenseUrl">
          <el-input v-model="form.electronicLicenseUrl" placeholder="请输入电子证照链接" />
        </el-form-item>

        <el-form-item label="电子证照文件" prop="electronicLicenseId">
          <image-upload v-model="form.electronicLicenseId" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog v-model="isDetail" title="详情" width="60vw" @close="handleDetailClose">
      <el-descriptions class="margin-top" :column="2" border v-if="detailInfo">
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">人员姓名</div>
          </template>
          {{ detailInfo.name }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">身份证号码</div>
          </template>
          {{ detailInfo.idCard }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">人员性别</div>
          </template>
          <dict-tag :options="sys_user_sex" :value="detailInfo.gender" />
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">出生日期</div>
          </template>
          {{ formatBirthdate(detailInfo.birthdate) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">证书编号</div>
          </template>
          {{ detailInfo.certificateNumber }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">操作类别</div>
          </template>
          <div v-for="dict in special_operation_type" :key="dict.value">
            <span v-if="dict.value == detailInfo.operationCategory">{{ dict.label }}</span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">发证机关</div>
          </template>
          {{ detailInfo.issuer }}
        </el-descriptions-item>

        <el-descriptions-item>
          <template #label>
            <div class="cell-item">初次领证日期</div>
          </template>
          {{ formatBirthdate(detailInfo.firstIssueDate) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">最近发证日期</div>
          </template>
          {{ formatBirthdate(detailInfo.lastIssueDate) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">证书有效期</div>
          </template>
          {{ formatBirthdate(detailInfo.validityStart) }}至{{ formatBirthdate(detailInfo.validityEnd) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">证书状态</div>
          </template>
          <div v-for="dict in special_operation_license_status" :key="dict.value">
            <span v-if="dict.value == detailInfo.status">{{ dict.label }}</span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">营业执照</div>
          </template>
          <el-image style="width: 100px; height: 100px" :src="list" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2"
            :preview-src-list="[list]" show-progress :initial-index="4" fit="cover" />
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="isDetail = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 导入监控管理对话框 -->
    <Project :isShowModel="isShowModel" @update:isShowModel="isShowModelChange"
      @selectionProjectData="handleSelectionProject" />
  </div>
</template>

<script setup name="OperationPersonnel" lang="ts">
import {
  listOperationPersonnel,
  getOperationPersonnel,
  delOperationPersonnel,
  addOperationPersonnel,
  updateOperationPersonnel
} from '@/api/special/operationPersonnel';
import { OperationPersonnelVO, OperationPersonnelQuery, OperationPersonnelForm } from '@/api/special/operationPersonnel/types';
import { listByIds } from '@/api/system/oss';
import Project from '@/components/Project/index.vue';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { special_operation_type, sys_user_sex, special_operation_license_status } = toRefs<any>(
  proxy?.useDict('special_operation_type', 'sys_user_sex', 'special_operation_license_status')
);
import { useRoute } from 'vue-router';
import { get_prj_search_data, getItemList } from '@/api/projects/prj_hazardous_items';

const route = useRoute();
const operationPersonnelList = ref<OperationPersonnelVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const operationPersonnelFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData = {
  sopId: undefined,
  certificateNumber: undefined,
  name: undefined,
  idCard: undefined,
  gender: undefined,
  birthdate: undefined,
  operationCategory: undefined,
  issuer: undefined,
  firstIssueDate: undefined,
  lastIssueDate: undefined,
  validityStart: undefined,
  validityEnd: undefined,
  status: undefined,
  electronicLicenseUrl: undefined,
  electronicLicenseId: undefined,
  projectId: undefined,
  projectName: undefined
};
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    certificateNumber: undefined,
    name: undefined,
    idCard: undefined,
    gender: undefined,
    birthdate: undefined,
    operationCategory: undefined,
    issuer: undefined,
    firstIssueDate: undefined,
    lastIssueDate: undefined,
    validityStart: undefined,
    validityEnd: undefined,
    projectId: undefined,
    projectName: undefined,
    status: undefined,
    electronicLicenseUrl: undefined,
    electronicLicenseId: undefined,
    params: {}
  },
  rules: {
    projectId: [{ required: true, message: '请选择项目', trigger: 'change' }],
    sopId: [{ required: true, message: 'ID不能为空', trigger: 'blur' }],
    certificateNumber: [{ required: true, message: '证书编号不能为空', trigger: 'blur' }],
    name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
    idCard: [{ required: true, message: '身份证号不能为空', trigger: 'blur' }],
    gender: [{ required: true, message: '性别不能为空', trigger: 'change' }],
    birthdate: [{ required: true, message: '出生日期不能为空', trigger: 'blur' }],
    operationCategory: [{ required: true, message: '操作类别不能为空', trigger: 'change' }],
    issuer: [{ required: true, message: '发证机关不能为空', trigger: 'blur' }],
    firstIssueDate: [{ required: true, message: '初次领证日期不能为空', trigger: 'blur' }],
    lastIssueDate: [{ required: true, message: '最近发证日期不能为空', trigger: 'blur' }],
    validityStart: [{ required: true, message: '有效期开始不能为空', trigger: 'blur' }],
    validityEnd: [{ required: true, message: '有效期截止不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '证书状态(有效,无效,挂失,注销)不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);
const isShowModel = ref(false);
// 详情
let isDetail = ref(false);
let detailInfo = ref({
  sopId: null,
  certificateNumber: null,
  name: null,
  idCard: null,
  gender: null,
  birthdate: null,
  operationCategory: null,
  issuer: null,
  firstIssueDate: null,
  lastIssueDate: null,
  validityStart: null,
  validityEnd: null,
  status: null,
  electronicLicenseId: null
});
const getItemData = ref([]);
const projectSelectData = ref([]);

const list = ref(null);
//查询项目选择框数据
const getProjectSelectData = async () => {
  const { data } = await get_prj_search_data();
  projectSelectData.value = data;
};
const changePro = async (val) => {
  getItemList(val).then((res) => {
    getItemData.value = res.data;
  });
};
const handleSelectionProject = (data: { projectId: string; projectName: string }) => {
  form.value.projectId = data.projectId;
  form.value.projectName = data.projectName;
  // itemId.value = data.projectId;
  console.log('data.projectId', data.projectId, 'data.projectName', data.projectName, 'data.projectId', data.projectId);

  isShowModel.value = false;
};
const isShowModelChange = (val: boolean) => {
  isShowModel.value = val;
};
// 项目选择
const selectProjectChange = () => {
  isShowModel.value = true;
};
const handleDetail = async (row) => {
  detailInfo.value = row;
  if (row.electronicLicenseId) {
    listByIds(row.electronicLicenseId).then((res) => {
      list.value = res.data[0].url;
    });
  }
  // 弹窗
  isDetail.value = true;
};
// 关闭详情弹框的方法
const handleDetailClose = () => {
  detailInfo.value = {
    sopId: null,
    certificateNumber: null,
    name: null,
    idCard: null,
    gender: null,
    birthdate: null,
    operationCategory: null,
    issuer: null,
    firstIssueDate: null,
    lastIssueDate: null,
    validityStart: null,
    validityEnd: null,
    status: null,
    electronicLicenseId: null
  };
  list.value = null;
  isDetail.value = false;
};
/** 查询特种作业人员信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listOperationPersonnel(queryParams.value);
  operationPersonnelList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  operationPersonnelFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: OperationPersonnelVO[]) => {
  ids.value = selection.map((item) => item.sopId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加特种作业人员信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: OperationPersonnelVO) => {
  reset();
  const _sopId = row?.sopId || ids.value[0];
  const res = await getOperationPersonnel(_sopId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改特种作业人员信息';
};

/** 提交按钮 */
const submitForm = () => {
  operationPersonnelFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.sopId) {
        await updateOperationPersonnel(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addOperationPersonnel(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: OperationPersonnelVO) => {
  const _sopIds = row?.sopId || ids.value;
  await proxy?.$modal.confirm('是否确认删除特种作业人员信息编号为"' + _sopIds + '"的数据项？').finally(() => (loading.value = false));
  await delOperationPersonnel(_sopIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'special/operationPersonnel/export',
    {
      ...queryParams.value
    },
    `operationPersonnel_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
  getProjectSelectData();
});
const formatBirthdate = (birthdate) => {
  if (typeof birthdate !== 'string') return '';
  // 去除前后空白后按空白分割
  const parts = birthdate.trim().split(/\s+/);
  return parts[0];
};
</script>

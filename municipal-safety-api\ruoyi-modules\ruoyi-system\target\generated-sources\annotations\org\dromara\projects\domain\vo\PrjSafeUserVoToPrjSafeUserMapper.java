package org.dromara.projects.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.PrjSafeUser;
import org.dromara.projects.domain.PrjSafeUserToPrjSafeUserVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjSafeUserToPrjSafeUserVoMapper.class},
    imports = {}
)
public interface PrjSafeUserVoToPrjSafeUserMapper extends BaseMapper<PrjSafeUserVo, PrjSafeUser> {
}

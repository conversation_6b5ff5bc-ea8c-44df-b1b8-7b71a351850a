package org.dromara.facility.domain.bo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.LnHighFormworkReal;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {},
    imports = {}
)
public interface LnHighFormworkRealBoToLnHighFormworkRealMapper extends BaseMapper<LnHighFormworkRealBo, LnHighFormworkReal> {
}

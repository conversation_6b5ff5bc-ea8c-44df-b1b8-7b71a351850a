package org.dromara.projects.domain.vo;

import lombok.Data;
import org.dromara.expert.domain.Expert;
import org.dromara.projects.domain.PrjExpertReviewParticipants;
import org.dromara.projects.domain.PrjExpertReviews;
import org.dromara.system.domain.SysUser;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/5 14:54
 * @Description TODO
 * @Version 1.0
 */
@Data
public class PrjExpertDetailVo {

    /**
     * 专家列表
     */
    private List<ExpertExtendVo> expertUser;

    /**
     * 会议实体
     */
    private PrjExpertReviews prjExpertReviews;

    public PrjExpertDetailVo() {
    }

    public PrjExpertDetailVo(PrjExpertReviews prjExpertReviews
        , List<PrjExpertReviewParticipants> prjExpertReviewParticipants
        , List<Expert> experts
        , List<SysUser> sysUsers) {

        this.prjExpertReviews = prjExpertReviews;
        this.expertUser = new ArrayList<>();

        Map<String, Expert> expertMap = experts.stream().collect(Collectors.toMap(Expert::getIdCard, t -> t));

        Map<Long, SysUser> sysUserMap = sysUsers.stream().collect(Collectors.toMap(SysUser::getUserId, t -> t));

        for (PrjExpertReviewParticipants participant : prjExpertReviewParticipants) {
            SysUser user = sysUserMap.get(participant.getUserId());

            Expert expert = expertMap.get(user.getUserName());

            ExpertExtendVo expertUser = new ExpertExtendVo();
            BeanUtils.copyProperties(expert, expertUser);
            expertUser.setUserId(Long.valueOf(expert.getIdCard()));
            expertUser.setRoleInMeeting(participant.getRoleInMeeting());
            expertUser.setIsAttendingExpert(participant.getIsAttendingExpert());
            this.expertUser.add(expertUser);
        }
    }
}

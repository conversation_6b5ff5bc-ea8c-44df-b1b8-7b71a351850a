package org.dromara.ai.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.ai.domain.bo.AiHazAnalysisTasksBo;
import org.dromara.ai.domain.dto.AiHazAnalysisTasksDto;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksVo;
import org.dromara.ai.service.IAiHazAnalysisTasksService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.dto.StartProcessReturnDTO;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.helper.DataPermissionHelper;
import org.dromara.common.web.core.BaseController;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistBo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 智能隐患分析任务
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/ai_haz_analysis_tasks")
public class AiHazAnalysisTasksController extends BaseController {

    private final IAiHazAnalysisTasksService aiHazAnalysisTasksService;

    /**
     * 查询智能隐患分析任务列表
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks:list")
    @GetMapping("/list")
    public TableDataInfo<AiHazAnalysisTasksVo> list(AiHazAnalysisTasksDto dto, PageQuery pageQuery) {
        return DataPermissionHelper.ignore(() -> aiHazAnalysisTasksService.queryNewPageList(dto, pageQuery));
    }

    /**
     * 导出智能隐患分析任务列表
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks:export")
    @Log(title = "智能隐患分析任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiHazAnalysisTasksBo bo, HttpServletResponse response) {
        List<AiHazAnalysisTasksVo> list = aiHazAnalysisTasksService.queryList(bo);
        ExcelUtil.exportExcel(list, "智能隐患分析任务", AiHazAnalysisTasksVo.class, response);
    }

    /**
     * 获取智能隐患分析任务详细信息
     *
     * @param taskId 主键
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks:query")
    @GetMapping("/{taskId}")
    public R<AiHazAnalysisTasksVo> getInfo(@NotNull(message = "主键不能为空")
                                           @PathVariable Long taskId) {
        return R.ok(aiHazAnalysisTasksService.queryByIdNew(taskId));
    }

    /**
     * 新增智能隐患分析任务
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks:add")
    @Log(title = "智能隐患分析任务", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiHazAnalysisTasksBo bo) {
        return toAjax(aiHazAnalysisTasksService.insertByBo(bo));
    }

    /**
     * 修改智能隐患分析任务
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks:edit")
    @Log(title = "智能隐患分析任务", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiHazAnalysisTasksBo bo) {
        return toAjax(aiHazAnalysisTasksService.updateByBo(bo));
    }

    /**
     * 删除智能隐患分析任务
     *
     * @param taskIds 主键串
     */
    @SaCheckPermission("ai:ai_haz_analysis_tasks:remove")
    @Log(title = "智能隐患分析任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] taskIds) {
        return toAjax(aiHazAnalysisTasksService.deleteWithValidByIds(List.of(taskIds), true));
    }

    /**
     * 推送工单
     *
     * @param taskId
     * @return
     */
    @RepeatSubmit(interval = 2000)
    @PostMapping("/pushWorkOrder/{taskId}/{busId}")
    public R<StartProcessReturnDTO> testConnection(@PathVariable Long taskId, @PathVariable String busId) {
        return R.ok(aiHazAnalysisTasksService.pushWorkOrder(taskId, busId));
    }

    /**
     * 推送工单
     *
     * @return
     */
    @RepeatSubmit(interval = 2000)
    @PostMapping("/pushWorkOrder2")
    public R<StartProcessReturnDTO> testConnection2(@RequestBody PrjHazardousItemsSpecialistBo bo) {
        return R.ok(aiHazAnalysisTasksService.pushWorkOrder2(bo));
    }
}

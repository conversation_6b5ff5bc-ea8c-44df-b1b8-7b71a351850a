package org.dromara.facility.service;

import org.dromara.facility.domain.vo.LnEnergyVo;
import org.dromara.facility.domain.bo.LnEnergyBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 绿能用电监测Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
public interface ILnEnergyService extends BaseFacilityHandle{

    /**
     * 查询绿能用电监测
     *
     * @param id 主键
     * @return 绿能用电监测
     */
    LnEnergyVo queryById(Long id);

    /**
     * 分页查询绿能用电监测列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能用电监测分页列表
     */
    TableDataInfo<LnEnergyVo> queryPageList(LnEnergyBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的绿能用电监测列表
     *
     * @param bo 查询条件
     * @return 绿能用电监测列表
     */
    List<LnEnergyVo> queryList(LnEnergyBo bo);

    /**
     * 新增绿能用电监测
     *
     * @param bo 绿能用电监测
     * @return 是否新增成功
     */
    Boolean insertByBo(LnEnergyBo bo);

    /**
     * 修改绿能用电监测
     *
     * @param bo 绿能用电监测
     * @return 是否修改成功
     */
    Boolean updateByBo(LnEnergyBo bo);

    /**
     * 校验并批量删除绿能用电监测信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}

package org.dromara.ai.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.dromara.ai.domain.AiHazAnalysisTasks;
import org.dromara.ai.domain.dto.AiHazAnalysisTasksDto;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksVo;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 智能隐患分析任务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
public interface AiHazAnalysisTasksMapper extends BaseMapperPlus<AiHazAnalysisTasks, AiHazAnalysisTasksVo> {

    /**
     * APP端分页查询隐患列表
     *
     * @param page 分页参数
     * @param dto 查询条件
     * @return 分页结果
     */
    /**
     * 分页查询项目列表（带部门名称）
     */
    @DataPermission({
        @DataColumn(key = "deptNameZf", value = "p.supervising_qs_org_id"),
        @DataColumn(key = "projectId", value = "p.project_id")
    })
    IPage<AiHazAnalysisTasksVo> selectAppPageList(IPage<AiHazAnalysisTasks> page, @Param("dto") AiHazAnalysisTasksDto dto);


    /**
     * 分页查询AI隐患问题
     */
    IPage<AiHazAnalysisTasksVo> selectAIPageList(IPage<AiHazAnalysisTasks> page, @Param("dto") AiHazAnalysisTasksDto dto);

}

{"doc": " 测试单表Service接口\n\n <AUTHOR>\n @date 2021-07-26\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询单个\n\n @return\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询列表\n"}, {"name": "customPageList", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 自定义分页查询\n"}, {"name": "queryList", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo"], "doc": " 查询列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo"], "doc": " 根据新增业务对象插入测试单表\n\n @param bo 测试单表新增业务对象\n @return\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo"], "doc": " 根据编辑业务对象修改测试单表\n\n @param bo 测试单表编辑业务对象\n @return\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并删除数据\n\n @param ids     主键集合\n @param isValid 是否校验,true-删除前校验,false-不校验\n @return\n"}, {"name": "saveBatch", "paramTypes": ["java.util.List"], "doc": " 批量保存\n"}], "constructors": []}
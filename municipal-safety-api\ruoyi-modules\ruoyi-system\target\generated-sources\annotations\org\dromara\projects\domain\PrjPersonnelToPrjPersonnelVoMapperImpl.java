package org.dromara.projects.domain;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.vo.PrjPersonnelVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjPersonnelToPrjPersonnelVoMapperImpl implements PrjPersonnelToPrjPersonnelVoMapper {

    @Override
    public PrjPersonnelVo convert(PrjPersonnel arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjPersonnelVo prjPersonnelVo = new PrjPersonnelVo();

        prjPersonnelVo.setProjectPersonnelId( arg0.getProjectPersonnelId() );
        prjPersonnelVo.setProjectId( arg0.getProjectId() );
        prjPersonnelVo.setPersonId( arg0.getPersonId() );
        prjPersonnelVo.setUserId( arg0.getUserId() );
        prjPersonnelVo.setOrgId( arg0.getOrgId() );
        prjPersonnelVo.setRoleOnProject( arg0.getRoleOnProject() );
        prjPersonnelVo.setIsSpecialOps( arg0.getIsSpecialOps() );
        prjPersonnelVo.setStartDateOnProject( arg0.getStartDateOnProject() );
        prjPersonnelVo.setEndDateOnProject( arg0.getEndDateOnProject() );

        return prjPersonnelVo;
    }

    @Override
    public PrjPersonnelVo convert(PrjPersonnel arg0, PrjPersonnelVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setProjectPersonnelId( arg0.getProjectPersonnelId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setPersonId( arg0.getPersonId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOrgId( arg0.getOrgId() );
        arg1.setRoleOnProject( arg0.getRoleOnProject() );
        arg1.setIsSpecialOps( arg0.getIsSpecialOps() );
        arg1.setStartDateOnProject( arg0.getStartDateOnProject() );
        arg1.setEndDateOnProject( arg0.getEndDateOnProject() );

        return arg1;
    }
}

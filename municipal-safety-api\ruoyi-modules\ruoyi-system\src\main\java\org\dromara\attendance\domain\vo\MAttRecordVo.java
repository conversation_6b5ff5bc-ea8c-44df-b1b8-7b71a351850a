package org.dromara.attendance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.attendance.domain.MAttRecord;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 考勤记录视图对象 m_att_record
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MAttRecord.class)
public class MAttRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 规则id
     */
    private Long ruleId;

    /**
     * 项目人员id
     */
    private Long personId;

    /**
     * 人员岗位/角色
     */
    private String personType;

    /**
     * 真实姓名
     */
    @ExcelProperty(value = "真实姓名")
    private String realName;

    /**
     * 身份Id
     */
    @ExcelProperty(value = "身份Id")
    private String idNumber;

    /**
     * 实时人脸
     */
    @ExcelProperty(value = "实时人脸")
    private String realTimeFace;

    /**
     * 设备号
     */
    @ExcelProperty(value = "设备号")
    private String sn;

    /**
     * 打卡来源
     */
    @ExcelProperty(value = "打卡来源")
    private Integer source;

    /**
     * 自定义内容
     */
    @ExcelProperty(value = "自定义内容")
    private String content;

    /**
     * 考勤时间
     */
    @ExcelProperty(value = "考勤时间")
    private Date attTime;

    /**
     * 考勤日期
     */
    @ExcelProperty(value = "考勤日期")
    private String attDate;

    /**
     * 打卡结果
     */
    @ExcelProperty(value = "打卡结果")
    private Integer attResult;

    /**
     * 第几次打卡
     */
    @ExcelProperty(value = "第几次打卡")
    private Integer whichTime;

    /**
     * 设备名称
     */
    private String snName;

    /**
     * 规则列表
     */
    private List<MAttRuleVo> mAttRules;

    /**
     * 考勤结果
     */
    private String attResultName;

}

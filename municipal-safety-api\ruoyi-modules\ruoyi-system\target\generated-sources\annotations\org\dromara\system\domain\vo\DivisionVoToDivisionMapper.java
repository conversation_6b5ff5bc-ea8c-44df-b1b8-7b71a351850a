package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.PrjProjectsToPrjProjectsVoMapper;
import org.dromara.projects.domain.vo.PrjProjectsVoToPrjProjectsMapper;
import org.dromara.system.domain.Division;
import org.dromara.system.domain.DivisionToDivisionVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjProjectsVoToPrjProjectsMapper.class,PrjProjectsToPrjProjectsVoMapper.class,DivisionToDivisionVoMapper.class,DivisionToDivisionVoMapper.class},
    imports = {}
)
public interface DivisionVoToDivisionMapper extends BaseMapper<DivisionVo, Division> {
}

package org.dromara.facility.service.impl;

import com.alibaba.fastjson2.JSON;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.facility.domain.bo.LnWaterBo;
import org.dromara.facility.domain.vo.LnWaterVo;
import org.dromara.facility.domain.LnWater;
import org.dromara.facility.mapper.LnWaterMapper;
import org.dromara.facility.service.ILnWaterService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 绿能水表Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@RequiredArgsConstructor
@Service
public class LnWaterServiceImpl implements ILnWaterService {

    private final LnWaterMapper baseMapper;

    /**
     * 查询绿能水表
     *
     * @param id 主键
     * @return 绿能水表
     */
    @Override
    public LnWaterVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询绿能水表列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能水表分页列表
     */
    @Override
    public TableDataInfo<LnWaterVo> queryPageList(LnWaterBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LnWater> lqw = buildQueryWrapper(bo);
        Page<LnWaterVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的绿能水表列表
     *
     * @param bo 查询条件
     * @return 绿能水表列表
     */
    @Override
    public List<LnWaterVo> queryList(LnWaterBo bo) {
        LambdaQueryWrapper<LnWater> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LnWater> buildQueryWrapper(LnWaterBo bo) {
        LambdaQueryWrapper<LnWater> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(LnWater::getCreateTime);
        lqw.eq(LnWater::getDevNo, bo.getDevNo());
        return lqw;
    }

    /**
     * 新增绿能水表
     *
     * @param bo 绿能水表
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LnWaterBo bo) {
        LnWater add = MapstructUtils.convert(bo, LnWater.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public void insertByJson(String jsonString) {
        LnWater add = MapstructUtils.convert(JSON.parseObject(jsonString, LnWaterBo.class), LnWater.class);
        assert add != null;
        add.setCreateTime(new Date());
        baseMapper.insert(add);
    }

    /**
     * 修改绿能水表
     *
     * @param bo 绿能水表
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LnWaterBo bo) {
        LnWater update = MapstructUtils.convert(bo, LnWater.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LnWater entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除绿能水表信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

}

<template>
  <div class="p-2">
    <!-- 搜索区域 -->
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="设备名称" prop="deviceName">
              <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="规格型号" prop="specification">
              <el-input v-model="queryParams.specification" placeholder="请输入规格型号" clearable
                @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备编号" prop="deviceCode">
              <el-input v-model="queryParams.deviceCode" placeholder="请输入设备编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="设备状态" clearable>
                <el-option label="正常" value="0" />
                <el-option label="停用" value="1" />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间" style="width: 308px">
              <el-date-picker v-model="dateRange" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
                range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <!-- 主内容区域 -->
    <el-card shadow="hover">
      <template #header>
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button v-has-permi="['quality:device:add']" type="primary" plain icon="Plus" @click="handleAdd"> 新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-has-permi="['quality:device:edit']" type="success" plain :disabled="single" icon="Edit"
              @click="handleUpdate()">
              修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-has-permi="['quality:device:remove']" type="danger" plain :disabled="multiple" icon="Delete"
              @click="handleDelete()">
              删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-has-permi="['quality:device:export']" type="warning" plain icon="Download"
              @click="handleExport"> 导出 </el-button>
          </el-col>
          <right-toolbar v-model:show-search="showSearch" :columns="columns" :search="true" @query-table="getList" />
        </el-row>
      </template>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column v-if="columns[0].visible" key="deviceId" label="设备ID" align="center" prop="deviceId"
          width="80" />
        <el-table-column v-if="columns[1].visible" key="deviceName" label="设备名称" align="center" prop="deviceName"
          :show-overflow-tooltip="true" />
        <el-table-column v-if="columns[2].visible" key="specification" label="规格型号" align="center" prop="specification"
          :show-overflow-tooltip="true" />
        <el-table-column v-if="columns[3].visible" key="deviceCode" label="设备编号" align="center" prop="deviceCode"
          width="120" />
        <el-table-column v-if="columns[4].visible" key="quantity" label="数量" align="center" prop="quantity"
          width="80" />
        <el-table-column v-if="columns[5].visible" key="deviceImageUrl" label="设备图片" align="center" width="100">
          <template #default="scope">
            <el-image v-if="scope.row.deviceImageUrl" :src="scope.row.deviceImageUrl"
              :preview-src-list="[scope.row.deviceImageUrl]" fit="cover"
              style="width: 60px; height: 60px; border-radius: 6px" preview-teleported />
            <span v-else class="text-gray-400">暂无图片</span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[6].visible" key="devicePurpose" label="设备用途" align="center" prop="devicePurpose"
          :show-overflow-tooltip="true" />
        <el-table-column v-if="columns[7].visible" key="status" label="状态" align="center" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
              {{ scope.row.status === '0' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[8].visible" label="创建时间" align="center" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[9].visible" label="创建人" align="center" prop="createByName" width="100" />

        <el-table-column label="操作" fixed="right" width="280" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="查看详情" placement="top">
              <el-button v-has-permi="['quality:device:query']" link type="primary" icon="View"
                @click="handleDetail(scope.row)" />
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-has-permi="['quality:device:edit']" link type="primary" icon="Edit"
                @click="handleUpdate(scope.row)" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-has-permi="['quality:device:remove']" link type="primary" icon="Delete"
                @click="handleDelete(scope.row)" />
            </el-tooltip>
            <el-tooltip content="上传图片" placement="top">
              <el-button v-has-permi="['quality:device:upload']" link type="primary" icon="Picture"
                @click="handleUploadImage(scope.row)" />
            </el-tooltip>
            <el-tooltip content="上传说明书" placement="top">
              <el-button v-has-permi="['quality:device:upload']" link type="primary" icon="Document"
                @click="handleUploadManual(scope.row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
        :total="total" @pagination="getList" />
    </el-card>

    <!-- 添加或修改设备对话框 -->
    <el-dialog ref="formDialogRef" v-model="dialog.visible" :title="dialog.title" width="800px" append-to-body
      @close="closeDialog">
      <el-form ref="deviceFormRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="deviceName">
              <el-input v-model="form.deviceName" placeholder="请输入设备名称" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格型号" prop="specification">
              <el-input v-model="form.specification" placeholder="请输入规格型号" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备类型" prop="deviceType">
              <el-select v-model="form.deviceType" placeholder="请选择设备类型" clearable>
                <el-option v-for="dict in quality_device_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备编号" prop="deviceCode">
              <el-input v-model="form.deviceCode" placeholder="请输入设备编号" maxlength="30">
                <template #append>
                  <el-button @click="generateCode">生成</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="数量" prop="quantity">
              <el-input-number v-model="form.quantity" :min="1" :max="9999" controls-position="right"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备用途" prop="devicePurpose">
              <el-input v-model="form.devicePurpose" placeholder="请输入设备用途" maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio value="0">正常</el-radio>
                <el-radio value="1">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="设备简介" prop="deviceDescription">
              <el-input v-model="form.deviceDescription" type="textarea" placeholder="请输入设备简介" :rows="3" maxlength="500"
                show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="使用说明" prop="usageInstructions">
              <el-input v-model="form.usageInstructions" type="textarea" placeholder="请输入使用说明" :rows="3"
                maxlength="1000" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 设备详情对话框 -->
    <el-dialog v-model="detailDialog.visible" title="设备详情" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="设备名称">{{ detailData.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="规格型号">{{ detailData.specification }}</el-descriptions-item>
        <el-descriptions-item label="设备编号">{{ detailData.deviceCode }}</el-descriptions-item>
        <el-descriptions-item label="数量">{{ detailData.quantity }}</el-descriptions-item>
        <el-descriptions-item label="设备用途">{{ detailData.devicePurpose }}</el-descriptions-item>
        <el-descriptions-item label="设备状态">
          <el-tag :type="detailData.status === '0' ? 'success' : 'danger'">
            {{ detailData.status === '0' ? '正常' : '停用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="设备图片" :span="2">
          <el-image v-if="detailData.deviceImageUrl" :src="detailData.deviceImageUrl"
            :preview-src-list="[detailData.deviceImageUrl]" fit="cover"
            style="width: 200px; height: 150px; border-radius: 6px" preview-teleported />
          <span v-else class="text-gray-400">暂无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="设备简介" :span="2">{{ detailData.deviceDescription || '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="使用说明" :span="2">{{ detailData.usageInstructions || '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="使用说明书" :span="2">
          <el-link v-if="detailData.manualFileUrl" :href="detailData.manualFileUrl" target="_blank" type="primary">
            {{ detailData.manualFileName || '下载说明书' }}
          </el-link>
          <span v-else class="text-gray-400">暂无说明书</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detailData.createTime }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ detailData.createByName }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 上传图片对话框 -->
    <el-dialog v-model="uploadImageDialog.visible" title="上传设备图片" width="500px" append-to-body>
      <el-upload ref="uploadImageRef" :action="uploadImageUrl" :headers="uploadHeaders" :file-list="imageFileList"
        :on-success="handleImageUploadSuccess" :on-error="handleUploadError" :before-upload="beforeImageUpload"
        list-type="picture-card" :data="{ deviceId: currentUploadDevice.deviceId }" :limit="1" accept="image/*">
        <el-icon>
          <Plus />
        </el-icon>
        <template #tip>
          <div class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="uploadImageDialog.visible = false">取 消</el-button>
          <el-button type="primary" @click="confirmImageUpload">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 上传说明书对话框 -->
    <el-dialog v-model="uploadManualDialog.visible" title="上传使用说明书" width="500px" append-to-body>
      <el-upload ref="uploadManualRef" :action="uploadManualUrl" :headers="uploadHeaders" :file-list="manualFileList"
        :on-success="handleManualUploadSuccess" :on-error="handleUploadError" :before-upload="beforeManualUpload"
        :data="{ deviceId: currentUploadDevice.deviceId }" :limit="1" accept=".pdf,.doc,.docx">
        <el-button type="primary">点击上传</el-button>
        <template #tip>
          <div class="el-upload__tip">只能上传pdf/doc/docx文件，且不超过10MB</div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="uploadManualDialog.visible = false">取 消</el-button>
          <el-button type="primary" @click="confirmManualUpload">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Device" lang="ts">
import {
  listDevice,
  getDevice,
  delDevice,
  addDevice,
  updateDevice,
  exportDevice,
  generateDeviceCode,
  checkDeviceCodeUnique,
  uploadDeviceImage,
  uploadManual
} from '@/api/quality/device';
import type { DeviceVO, DeviceForm, DeviceQuery } from '@/api/quality/device/types';
import { globalHeaders } from '@/utils/request';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { quality_device_type } = toRefs<any>(proxy?.useDict('quality_device_type'));

const deviceList = ref<DeviceVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref<[DateModelType, DateModelType]>(['', '']);

// 列显示控制
const columns = ref([
  { key: 0, label: `设备ID`, visible: true },
  { key: 1, label: `设备名称`, visible: true },
  { key: 2, label: `规格型号`, visible: true },
  { key: 3, label: `设备编号`, visible: true },
  { key: 4, label: `数量`, visible: true },
  { key: 5, label: `设备图片`, visible: true },
  { key: 6, label: `设备用途`, visible: true },
  { key: 7, label: `状态`, visible: true },
  { key: 8, label: `创建时间`, visible: true },
  { key: 9, label: `创建人`, visible: true }
]);

// 查询参数
const queryParams = ref<DeviceQuery>({
  pageNum: 1,
  pageSize: 10,
  deviceName: '',
  specification: '',
  deviceCode: '',
  status: ''
});

// 表单参数
const form = ref<DeviceForm>({
  deviceName: '',
  specification: '',
  deviceCode: '',
  deviceType: '',
  quantity: 1,
  devicePurpose: '',
  deviceDescription: '',
  usageInstructions: '',
  status: '0'
});

// 表单校验
const rules = reactive({
  deviceName: [{ required: true, message: '设备名称不能为空', trigger: 'blur' }],
  specification: [{ required: true, message: '规格型号不能为空', trigger: 'blur' }],
  deviceType: [{ required: true, message: '设备类型不能为空', trigger: 'blur' }],
  deviceCode: [
    { required: true, message: '设备编号不能为空', trigger: 'blur' },
    {
      validator: async (rule: any, value: string, callback: any) => {
        if (value && form.value.deviceId === undefined) {
          try {
            const res = await checkDeviceCodeUnique(value);
            if (!res.data.unique) {
              callback(new Error('设备编号已存在'));
            } else {
              callback();
            }
          } catch (error) {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  quantity: [{ required: true, message: '数量不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '设备状态不能为空', trigger: 'change' }]
});

// 对话框
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const detailDialog = reactive({
  visible: false
});

const uploadImageDialog = reactive({
  visible: false
});

const uploadManualDialog = reactive({
  visible: false
});

// 详情数据
const detailData = ref<DeviceVO>({} as DeviceVO);

// 上传相关
const uploadImageUrl = ref(import.meta.env.VITE_APP_BASE_API + '/quality/device/uploadImage');
const uploadManualUrl = ref(import.meta.env.VITE_APP_BASE_API + '/quality/device/uploadManual');
const uploadHeaders = globalHeaders();
const imageFileList = ref([]);
const manualFileList = ref([]);
const currentUploadDevice = ref<DeviceVO>({} as DeviceVO);

const queryFormRef = ref<ElFormInstance>();
const deviceFormRef = ref<ElFormInstance>();
const uploadImageRef = ref<ElUploadInstance>();
const uploadManualRef = ref<ElUploadInstance>();

/** 查询设备列表 */
const getList = async () => {
  loading.value = true;
  try {
    const res = await listDevice(proxy?.addDateRange(queryParams.value, dateRange.value));
    deviceList.value = res.rows;
    total.value = res.total;
  } finally {
    loading.value = false;
  }
};

/** 取消按钮 */
const closeDialog = () => {
  dialog.visible = false;
  reset();
};

/** 表单重置 */
const reset = () => {
  form.value = {
    deviceName: '',
    specification: '',
    deviceCode: '',
    quantity: 1,
    devicePurpose: '',
    deviceDescription: '',
    usageInstructions: '',
    status: '0'
  };
  deviceFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: DeviceVO[]) => {
  ids.value = selection.map((item) => item.deviceId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加设备';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: DeviceVO) => {
  reset();
  const deviceId = row?.deviceId || ids.value[0];
  try {
    const res = await getDevice(deviceId);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = '修改设备';
  } catch (error) {
    proxy?.$modal.msgError('获取设备信息失败');
  }
};

/** 提交按钮 */
const submitForm = () => {
  deviceFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (form.value.deviceId) {
          await updateDevice(form.value);
          proxy?.$modal.msgSuccess('修改成功');
        } else {
          await addDevice(form.value);
          proxy?.$modal.msgSuccess('新增成功');
        }
        dialog.visible = false;
        await getList();
      } catch (error) {
        proxy?.$modal.msgError('操作失败');
      }
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: DeviceVO) => {
  const deviceIds = row?.deviceId || ids.value;
  try {
    await proxy?.$modal.confirm('是否确认删除设备编号为"' + deviceIds + '"的数据项？');
    await delDevice(deviceIds);
    await getList();
    proxy?.$modal.msgSuccess('删除成功');
  } catch (error) {
    proxy?.$modal.msgError('删除失败');
  }
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'quality/device/export',
    {
      ...queryParams.value
    },
    `device_${new Date().getTime()}.xlsx`
  );
};

/** 查看详情 */
const handleDetail = async (row: DeviceVO) => {
  try {
    const res = await getDevice(row.deviceId);
    detailData.value = res.data;
    detailDialog.visible = true;
  } catch (error) {
    proxy?.$modal.msgError('获取设备详情失败');
  }
};

/** 生成设备编号 */
const generateCode = async () => {
  try {
    const res = await generateDeviceCode(form.value.deviceType);
    form.value.deviceCode = res.data;
  } catch (error) {
    proxy?.$modal.msgError('生成设备编号失败');
  }
};

/** 上传设备图片 */
const handleUploadImage = (row: DeviceVO) => {
  currentUploadDevice.value = row;
  imageFileList.value = [];
  uploadImageDialog.visible = true;
};

/** 上传使用说明书 */
const handleUploadManual = (row: DeviceVO) => {
  currentUploadDevice.value = row;
  manualFileList.value = [];
  uploadManualDialog.visible = true;
};

/** 图片上传前校验 */
const beforeImageUpload = (file: any) => {
  const isImage = file.type.indexOf('image/') === 0;
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    proxy?.$modal.msgError('只能上传图片文件!');
    return false;
  }
  if (!isLt2M) {
    proxy?.$modal.msgError('上传图片大小不能超过 2MB!');
    return false;
  }
  return true;
};

/** 说明书上传前校验 */
const beforeManualUpload = (file: any) => {
  const isValidType = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(
    file.type
  );
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isValidType) {
    proxy?.$modal.msgError('只能上传PDF、DOC、DOCX格式的文件!');
    return false;
  }
  if (!isLt10M) {
    proxy?.$modal.msgError('上传文件大小不能超过 10MB!');
    return false;
  }
  return true;
};

/** 图片上传成功回调 */
const handleImageUploadSuccess = (response: any) => {
  if (response.code === 200) {
    proxy?.$modal.msgSuccess('图片上传成功');
  } else {
    proxy?.$modal.msgError(response.msg || '图片上传失败');
  }
};

/** 说明书上传成功回调 */
const handleManualUploadSuccess = (response: any) => {
  if (response.code === 200) {
    proxy?.$modal.msgSuccess('说明书上传成功');
  } else {
    proxy?.$modal.msgError(response.msg || '说明书上传失败');
  }
};

/** 上传失败回调 */
const handleUploadError = () => {
  proxy?.$modal.msgError('上传失败');
};

/** 确认图片上传 */
const confirmImageUpload = () => {
  uploadImageDialog.visible = false;
  getList();
};

/** 确认说明书上传 */
const confirmManualUpload = () => {
  uploadManualDialog.visible = false;
  getList();
};

onMounted(() => {
  getList();
});
</script>

<style scoped>
.el-upload__tip {
  color: #606266;
  font-size: 12px;
  margin-top: 7px;
}
</style>

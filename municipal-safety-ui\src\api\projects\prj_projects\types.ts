export interface Prj_projectsVO {
  /**
   * 项目ID
   */
  projectId: string | number;

  /**
   * 项目名称
   */
  projectName: string;

  /**
   * 项目编码/标识
   */
  projectCode: string;

  /**
   * 工程概况 (对应附件一.1)
   */
  projectOverview: string;

  /**
   * 施工许可证编号 (对应附件一.2)
   */
  constructionPermitNo: string;

  /**
   * 施工许可证扫描件文档ID (逻辑外键至 sys_documents.document_id)
   */
  constructionPermitDocId: string | number;

  /**
   * 项目位置（经纬度）
   */
  lola: string;

  /**
   * 省/直辖市编码
   */
  provinceCode: string;

  /**
   * 省/直辖市名称
   */
  provinceName: string;

  /**
   * 市编码
   */
  cityCode: string;

  /**
   * 市名称
   */
  cityName: string;

  /**
   * 区/县编码
   */
  districtCode: string;

  /**
   * 区/县名称
   */
  districtName: string;

  /**
   * 乡镇/街道编码 (可选)
   */
  countyCode: string;

  /**
   * 乡镇/街道名称 (可选)
   */
  countyName: string;

  /**
   * 详细地址
   */
  locationDetail: string;

  /**
   * 项目状态
   */
  status: string;

  /**
   * 计划开工日期
   */
  startDate: string;

  /**
   * 计划竣工日期
   */
  plannedEndDate: string;

  /**
   * 实际开工日期
   */
  actualStartDate: string;

  /**
   * 实际竣工日期
   */
  actualEndDate: string;

  /**
   * 建设单位ID (逻辑外键至 sys_organizations.org_id)
   */
  clientOrgId: string | number;

  /**
   * 施工总包单位ID (逻辑外键至 sys_organizations.org_id)
   */
  constructionOrgId: string | number;

  /**
   * 监理单位ID (逻辑外键至 sys_organizations.org_id)
   */
  supervisionOrgId: string | number;

  /**
   * 设计单位ID (逻辑外键至 sys_organizations.org_id)
   */
  designOrgId: string | number;

  /**
   * 勘察单位ID (逻辑外键至 sys_organizations.org_id)
   */
  surveyOrgId: string | number;

  /**
   * 安拆单位ID (逻辑外键至 sys_organizations.org_id)
   */
  installationDismantlingOrgId?: string | number;

  /**
   * 维保单位ID (逻辑外键至 sys_organizations.org_id)
   */
  maintenanceOrgId?: string | number;

  /**
   * 专业分包单位ID列表 (JSON数组，元素为 sys_organizations.org_id)
   */
  subcontractorOrgIds: string | number;

  /**
   * 施工单位项目负责人ID (逻辑外键至 sys_users.user_id)
   */
  projectManagerUserId: string | number;

  /**
   * 监理单位总监ID (逻辑外键至 sys_users.user_id)
   */
  supervisionChiefEngUserId: string | number;

  /**
   * 危大工程安全防护文明施工措施费财务凭证文档ID (对应附件三.1)
   */
  safetyMeasuresFeeDocId: string | number;

  /**
   * 占地面积(平方米)
   */
  siteArea?: number;

  /**
   * 预算投资总额(万元)
   */
  budgetTotal?: number;

}

export interface Prj_projectsForm extends BaseEntity {
  /**
   * 项目ID
   */
  projectId?: string | number;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 项目编码/标识
   */
  projectCode?: string;

  /**
   * 工程概况 (对应附件一.1)
   */
  projectOverview?: string;

  /**
   * 施工许可证编号 (对应附件一.2)
   */
  constructionPermitNo?: string;

  /**
   * 施工许可证扫描件文档ID (逻辑外键至 sys_documents.document_id)
   */
  constructionPermitDocId?: string | number;

  /**
   * 项目位置（经纬度）
   */
  lola?: string;

  /**
   * 省/直辖市编码
   */
  provinceCode?: string;

  /**
   * 省/直辖市名称
   */
  provinceName?: string;

  /**
   * 市编码
   */
  cityCode?: string;

  /**
   * 市名称
   */
  cityName?: string;

  /**
   * 区/县编码
   */
  districtCode?: string;

  /**
   * 区/县名称
   */
  districtName?: string;

  /**
   * 乡镇/街道编码 (可选)
   */
  countyCode?: string;

  /**
   * 乡镇/街道名称 (可选)
   */
  countyName?: string;

  /**
   * 详细地址
   */
  locationDetail?: string;

  /**
   * 项目状态
   */
  status?: string;

  /**
   * 计划开工日期
   */
  startDate?: string;

  /**
   * 计划竣工日期
   */
  plannedEndDate?: string;

  /**
   * 实际开工日期
   */
  actualStartDate?: string;

  /**
   * 实际竣工日期
   */
  actualEndDate?: string;

  /**
   * 建设单位ID (逻辑外键至 sys_organizations.org_id)
   */
  clientOrgId?: string | number;

  /**
   * 建设单位名称
   */
  clientOrgName?: string;

  /**
   * 施工总包单位ID (逻辑外键至 sys_organizations.org_id)
   */
  constructionOrgId?: string | number;

  /**
   * 施工总包单位名称
   */
  constructionOrgName?: string;

  /**
   * 监理单位ID (逻辑外键至 sys_organizations.org_id)
   */
  supervisionOrgId?: string | number;

  /**
   * 监理单位名称
   */
  supervisionOrgName?: string;

  /**
   * 设计单位ID (逻辑外键至 sys_organizations.org_id)
   */
  designOrgId?: string | number;

  /**
   * 设计单位名称
   */
  designOrgName?: string;

  /**
   * 勘察单位ID (逻辑外键至 sys_organizations.org_id)
   */
  surveyOrgId?: string | number;

  /**
   * 勘察单位名称
   */
  surveyOrgName?: string;

  /**
   * 安拆单位ID (逻辑外键至 sys_organizations.org_id)
   */
  installationDismantlingOrgId?: string | number;

  /**
   * 安拆单位名称
   */
  installationDismantlingOrgName?: string;

  /**
   * 维保单位ID (逻辑外键至 sys_organizations.org_id)
   */
  maintenanceOrgId?: string | number;

  /**
   * 维保单位名称
   */
  maintenanceOrgName?: string;

  /**
   * 专业分包单位ID列表 (JSON数组，元素为 sys_organizations.org_id)
   */
  subcontractorOrgIds?: string | number;

  /**
   * 施工单位项目负责人ID (逻辑外键至 sys_users.user_id)
   */
  projectManagerUserId?: string | number;

  /**
   * 监理单位总监ID (逻辑外键至 sys_users.user_id)
   */
  supervisionChiefEngUserId?: string | number;

  /**
   * 危大工程安全防护文明施工措施费财务凭证文档ID (对应附件三.1)
   */
  safetyMeasuresFeeDocId?: string | number;

  /**
   * 占地面积(平方米)
   */
  siteArea?: number;

  /**
   * 预算投资总额(万元)
   */
  budgetTotal?: number;

  // 人员
  personIds?:string | number;
  // 五方公司信息
  enterpriseList?:any;
  // 人员信息
  personnelList?:any;

  /**
   * 项目所属质监站机构ID(逻辑外键至 sys_organizations.org_id)
   */
  supervisingQsOrgId?: string | number;

  /**
   * 项目所属质监站机构单位名称
   */
  supervisingQsOrgName?: string;
}

export interface Prj_projectsQuery extends PageQuery {

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 项目编码/标识
   */
  projectCode?: string;

  /**
   * 施工许可证编号
   */
  constructionPermitNo?: string;

  /**
   * 省/直辖市编码
   */
  provinceCode?: string;

  /**
   * 市编码
   */
  cityCode?: string;

  /**
   * 区/县编码
   */
  districtCode?: string;

  /**
   * 项目状态
   */
  status?: string;

  /**
   * 参建单位名称（模糊）
   */
  participateOrgName?: string;

  /**
   * 其他查询参数
   */
  params?: {
    dateRange?: any[];
  };
}




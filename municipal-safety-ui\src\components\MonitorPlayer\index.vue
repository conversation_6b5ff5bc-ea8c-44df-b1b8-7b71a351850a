<script lang="ts" setup>
import EZUIKit from "ezuikit-js";
import { onMounted } from "vue";

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const props = defineProps({
    url: {
        type: String,
        default: "ezopen://open.ys7.com/F19883308/1.hd.live",
    },
    accessToken: {
        type: String,
        default: "at.ajcs28a30mn97pfd9yfuge204f0if9kw-83a9s2obup-118mbr5-j3c6d8tkt",
    },
    width: {
        type: Number,
        default: 600,
    },
    height: {
        type: Number,
        default: 400,
    }
});

const emit = defineEmits(["capturePicture"]);

let player: any;

const destroy = () => {
    const destroyPromise = player.destroy();
    destroyPromise.then((data: any) => {
        console.log("promise 获取 数据", data);
    });
    player = null!;
};

const init = () => {
    if (player) {
        destroy();
    }
    console.group("mounted 组件挂载完毕状态===============》");
    // fetch("https://open.ys7.com/jssdk/ezopen/demo/token")
    //   .then((response) => response.json())
    //   .then((res) => {
    //     var accessToken = res.data.accessToken;
    player = new EZUIKit.EZUIKitPlayer({
        id: "video-container", // 视频容器ID
        accessToken: props.accessToken,
        url: props.url,
        // simple: 极简版; pcLive: pc直播; pcRec: pc回放; mobileLive: 移动端直播; mobileRec: 移动端回放;security: 安防版; voice: 语音版;
        template: "pcLive",
        plugin: ["talk"], // 加载插件，talk-对讲
        width: props.width,
        height: props.height,
        // language: "en", // zh | en
        handleError: (err: any) => {
            console.error("handleError", err);
        },
        // staticPath: "/ezuikit_static", // 如果想使用本地静态资源，请复制根目录下ezuikit_static 到当前目录下， 然后设置该值
        env: {
            // https://open.ys7.com/help/1772?h=domain
            // domain默认是 https://open.ys7.com, 如果是私有化部署或海外的环境，请配置对应的domain
            // The default domain is https://open.ys7.com If it is a private deployment or overseas (outside of China) environment, please configure the corresponding domain
            domain: "https://open.ys7.com",
        },
        // staticPath: "https://openstatic.ys7.com/ezuikit_js/v8.1.9/ezuikit_static",
        // 日志打印设置
        loggerOptions: {
            // player.setLoggerOptions(options)
            level: "INFO", // INFO LOG  WARN  ERROR
            name: "ezuikit",
            showTime: true,
        },
        // 视频流的信息回调类型
        /**
         * 打开流信息回调，监听 streamInfoCB 事件
         * 0 : 每次都回调
         * 1 : 只回调一次
         * 注意：会影响性能
         * 默认值 1
         */
        streamInfoCBType: 1,
        download: false, // 是否支持下载
    });
    player.eventEmitter.on(
        EZUIKit.EZUIKitPlayer.EVENTS.videoInfo,
        (info: any) => {
            console.log("videoinfo", info);
        },
    );

    player.eventEmitter.on(
        EZUIKit.EZUIKitPlayer.EVENTS.audioInfo,
        (info: any) => {
            console.log("audioInfo", info);
        },
    );

    // 首帧渲染成功
    // first frame display
    player.eventEmitter.on(
        EZUIKit.EZUIKitPlayer.EVENTS.firstFrameDisplay,
        () => {
            console.log("firstFrameDisplay ");
        },
    );
    player.eventEmitter.on(
        EZUIKit.EZUIKitPlayer.EVENTS.streamInfoCB,
        (info: any) => {
            console.log("streamInfoCB ", info);
        },
    );
    // 监听截图事件
    player.eventEmitter.on(EZUIKit.EZUIKitPlayer.EVENTS.capturePicture, (info: any) => {
        emit("capturePicture", info.data.base64);
    });
    window.player = player;
    // });
};

onMounted(() => {
    init();
});
onUnmounted(() => {
    destroy();
})
</script>

<template>
    <div class="hello-ezuikit-js">
        <div>
            <div id="video-container"></div>
        </div>
    </div>
</template>
<style>
.hello-ezuikit-js {
    .header-controls {
        opacity: 0 !important;
    }

    #video-container-wrap {
        width: 100% !important;
        height: 60vh !important;

        #video-container {
            width: 100% !important;
            height: 100% !important;
        }
    }
}
</style>

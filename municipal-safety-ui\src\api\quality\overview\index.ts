import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { 
  QualityOverview, 
  ChartDataQuery, 
  ChartDataResponse,
  MeasurementStatistics
} from '../measurement/types';
import { DeviceStatistics } from '../device/types';

/**
 * 获取质量管理概览数据
 */
export const getQualityOverview = (): AxiosPromise<QualityOverview> => {
  return request({
    url: '/quality/overview',
    method: 'get'
  });
};

/**
 * 获取设备统计数据
 */
export const getDeviceStatistics = (): AxiosPromise<DeviceStatistics> => {
  return request({
    url: '/quality/overview/device-statistics',
    method: 'get'
  });
};

/**
 * 获取测量统计数据
 */
export const getMeasurementStatistics = (): AxiosPromise<MeasurementStatistics> => {
  return request({
    url: '/quality/overview/measurement-statistics',
    method: 'get'
  });
};

/**
 * 获取图表数据
 * @param query 查询参数
 */
export const getChartData = (query: ChartDataQuery): AxiosPromise<ChartDataResponse> => {
  return request({
    url: '/quality/overview/chart-data',
    method: 'get',
    params: query
  });
};

/**
 * 获取设备状态分布数据
 */
export const getDeviceStatusDistribution = (): AxiosPromise<{normal: number, disabled: number}> => {
  return request({
    url: '/quality/overview/device-status-distribution',
    method: 'get'
  });
}; 
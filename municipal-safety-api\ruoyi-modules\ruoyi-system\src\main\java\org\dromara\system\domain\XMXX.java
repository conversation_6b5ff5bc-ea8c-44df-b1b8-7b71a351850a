package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.tenant.core.TenantEntity;

import java.math.BigDecimal;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
@TableName("xmxx")
public class XMXX extends BaseEntity {
    @TableId(value = "ID")
    private Integer id;
    private String xmszs; // 项目所在市
    private String xmszq; // 项目所在区
    private String projectCode; // 项目代码
    private String builderLicenceNum; // 施工许可证编号
    private String projectName; // 工程名称
    private String address; // 工程地址
    private BigDecimal cost; // 工程总造价（万元）
    private String structureTypeNum; // 结构体系
    private String prjFunctionNum; // 工程用途
    private String investType; // 项目投资类型
    private String baseType; // 基础类型
    private BigDecimal area; // 总面积（㎡）
    private Date beginDate; // 计划开工日期
    private Date endDate; // 计划竣工日期
    private String superviseProgress; // 报监形象进度
    private Date informDate; // 告知时间
    private String declareDescribe; // 申报需要说明的情况
    private String superviseOrgan; // 质量监督机构
    private String superviseOrganSocialCreditCode; // 质量监督机构统一社会信用代码
    private String areaCode; // 报监工程所在辖区
    private String superviseUser; // 主监人
    private String locationX; // 项目坐标(经度)
    private String locationY; // 项目坐标(纬度)
    @TableLogic
    private String delFlag; //删除标志（0代表存在 1代表删除）
}

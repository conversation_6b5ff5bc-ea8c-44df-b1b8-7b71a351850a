2025-04-30 08:31:16 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-04-30 08:31:16 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.15 with PID 9428 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-snailjob-server\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-30 08:31:16 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-04-30 08:31:18 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-04-30 08:31:18 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-04-30 08:31:18 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-30 08:31:18 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-04-30 08:31:18 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-04-30 08:31:18 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1864 ms
2025-04-30 08:31:20 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-04-30 08:31:20 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-30 08:31:21 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-30 08:31:21 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-30 08:31:21 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-30 08:31:21 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-04-30 08:31:21 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-04-30 08:31:21 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-04-30 08:31:21 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.4.0
2025-04-30 08:31:21 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-04-30 08:31:21 [main] INFO  c.a.s.s.common.rpc.server.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.GrpcServer, port = 17888
2025-04-30 08:31:21 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-04-30 08:31:21 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-04-30 08:31:21 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-04-30 08:31:21 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-04-30 08:31:21 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-04-30 08:31:21 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-04-30 08:31:21 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-04-30 08:31:21 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-04-30 08:31:21 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-04-30 08:31:21 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-04-30 08:31:21 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-04-30 08:31:21 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-04-30 08:31:21 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.4.0
2025-04-30 08:31:21 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 6.073 seconds (process running for 6.869)
2025-04-30 08:31:21 [snail-job-scheduled-thread-4] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-30 08:31:22 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as cc20f5b8d840
2025-04-30 08:31:22 [http-nio-8800-exec-1] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-30 08:31:22 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-30 08:31:22 [http-nio-8800-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-30 08:31:22 [snail-job-scheduled-thread-4] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4650fcc7
2025-04-30 08:31:22 [snail-job-scheduled-thread-4] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-30 08:31:22 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1917376180681273344]
2025-04-30 08:31:31 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[1] remoteNodeSize:[3]
2025-04-30 08:31:31 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-30 08:31:31 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-30 08:31:43 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-13] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1917376226348863488]
2025-04-30 08:32:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-9] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[777] 任务调度成功.
2025-04-30 08:33:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-13] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[778] 任务调度成功.
2025-04-30 08:34:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-17] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[779] 任务调度成功.
2025-04-30 08:35:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-20] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[780] 任务调度成功.
2025-04-30 08:36:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-23] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[781] 任务调度成功.
2025-04-30 08:37:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-27] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[782] 任务调度成功.
2025-04-30 08:38:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-30] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[783] 任务调度成功.
2025-04-30 08:39:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-33] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[784] 任务调度成功.
2025-04-30 08:40:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-37] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[785] 任务调度成功.
2025-04-30 08:41:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-40] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[786] 任务调度成功.
2025-04-30 08:42:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-43] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[787] 任务调度成功.
2025-04-30 08:43:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-47] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[788] 任务调度成功.
2025-04-30 08:43:15 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[3] remoteNodeSize:[4]
2025-04-30 08:43:15 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-30 08:43:15 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-30 08:44:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-50] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[789] 任务调度成功.
2025-04-30 08:45:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-53] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[790] 任务调度成功.
2025-04-30 08:46:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-57] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[791] 任务调度成功.
2025-04-30 08:47:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-60] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[792] 任务调度成功.
2025-04-30 08:48:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-63] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[793] 任务调度成功.
2025-04-30 08:49:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-66] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[794] 任务调度成功.
2025-04-30 08:50:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-70] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[795] 任务调度成功.
2025-04-30 08:51:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-73] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[796] 任务调度成功.
2025-04-30 08:52:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-76] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[797] 任务调度成功.
2025-04-30 08:53:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-79] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[798] 任务调度成功.
2025-04-30 08:54:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-83] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[799] 任务调度成功.
2025-04-30 08:55:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-86] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[800] 任务调度成功.
2025-04-30 08:56:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-89] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[801] 任务调度成功.
2025-04-30 08:57:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-92] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[802] 任务调度成功.
2025-04-30 08:58:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-96] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[803] 任务调度成功.
2025-04-30 08:59:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-99] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[804] 任务调度成功.
2025-04-30 09:00:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-102] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[805] 任务调度成功.
2025-04-30 09:01:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-105] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[806] 任务调度成功.
2025-04-30 09:02:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-109] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[807] 任务调度成功.
2025-04-30 09:03:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-112] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[808] 任务调度成功.
2025-04-30 09:04:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-115] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[809] 任务调度成功.
2025-04-30 09:05:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-118] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[810] 任务调度成功.
2025-04-30 09:06:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-122] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[811] 任务调度成功.
2025-04-30 09:07:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-125] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[812] 任务调度成功.
2025-04-30 09:08:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-128] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[813] 任务调度成功.
2025-04-30 09:09:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-131] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[814] 任务调度成功.
2025-04-30 09:10:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-135] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[815] 任务调度成功.
2025-04-30 09:11:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-138] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[816] 任务调度成功.
2025-04-30 09:12:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-141] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[817] 任务调度成功.
2025-04-30 09:13:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-144] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[818] 任务调度成功.
2025-04-30 09:14:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-148] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[819] 任务调度成功.
2025-04-30 09:15:01 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-151] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[820] 任务调度成功.
2025-04-30 09:16:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-154] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[821] 任务调度成功.
2025-04-30 09:17:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-157] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[822] 任务调度成功.
2025-04-30 09:18:00 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-161] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - taskId:[823] 任务调度成功.
2025-04-30 09:22:37 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[4] remoteNodeSize:[5]
2025-04-30 09:22:37 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-30 09:22:37 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-30 09:22:57 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[5] remoteNodeSize:[4]
2025-04-30 09:22:57 [server-node-balance] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[DEFAULT_SERVER] hostId:[1917379154116771840]
2025-04-30 09:22:57 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-30 09:22:57 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-30 09:23:21 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917379029663371264]
2025-04-30 10:27:34 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-2186] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917374296331227136]
2025-04-30 11:03:21 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917405191637409792]
2025-04-30 11:07:54 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-2935] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917414360939565056]
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.4.0
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-04-30 11:08:34 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1917376180681273344]
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.4.0
2025-04-30 11:08:34 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-04-30 11:08:34 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-30 11:08:34 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-30 11:38:07 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-04-30 11:38:07 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.15 with PID 15532 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-snailjob-server\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-30 11:38:07 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-04-30 11:38:08 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-04-30 11:38:08 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-04-30 11:38:08 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-30 11:38:08 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-04-30 11:38:08 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-04-30 11:38:08 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1704 ms
2025-04-30 11:38:10 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-04-30 11:38:11 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-30 11:38:11 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-30 11:38:11 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-30 11:38:11 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-04-30 11:38:12 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-04-30 11:38:12 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-04-30 11:38:12 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-04-30 11:38:12 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.4.0
2025-04-30 11:38:12 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-04-30 11:38:12 [main] INFO  c.a.s.s.common.rpc.server.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.GrpcServer, port = 17888
2025-04-30 11:38:12 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-04-30 11:38:12 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-04-30 11:38:12 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-04-30 11:38:12 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-04-30 11:38:12 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-04-30 11:38:12 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-04-30 11:38:12 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-04-30 11:38:12 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-04-30 11:38:12 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-04-30 11:38:12 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-04-30 11:38:12 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-04-30 11:38:12 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-04-30 11:38:12 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.4.0
2025-04-30 11:38:12 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 5.913 seconds (process running for 6.861)
2025-04-30 11:38:12 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-30 11:38:12 [RMI TCP Connection(5)-***************] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-30 11:38:12 [RMI TCP Connection(5)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-30 11:38:12 [RMI TCP Connection(5)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-30 11:38:12 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as cc20f5b8d840
2025-04-30 11:38:13 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6a0adcb6
2025-04-30 11:38:13 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-30 11:38:13 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1917423202218123264]
2025-04-30 11:38:22 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[1] remoteNodeSize:[4]
2025-04-30 11:38:22 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-30 11:38:22 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-30 11:38:36 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-12] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1917423267426947072]
2025-04-30 11:54:36 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-302] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917423267426947072]
2025-04-30 11:57:44 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-360] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917418220093472768]
2025-04-30 11:58:38 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[4] remoteNodeSize:[3]
2025-04-30 11:58:38 [server-node-balance] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[DEFAULT_SERVER] hostId:[1917374231181029376]
2025-04-30 11:58:38 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-30 11:58:38 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-30 11:59:44 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-397] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917427918100246528]
2025-04-30 11:59:48 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[3] remoteNodeSize:[4]
2025-04-30 11:59:48 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-30 11:59:48 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-30 16:30:37 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[4] remoteNodeSize:[3]
2025-04-30 16:30:37 [server-node-balance] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[DEFAULT_SERVER] hostId:[1917185753541959680]
2025-04-30 16:30:37 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-30 16:30:37 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-30 16:30:57 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[3] remoteNodeSize:[4]
2025-04-30 16:30:57 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-30 16:30:57 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-30 16:31:26 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-5430] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917185636478967808]
2025-04-30 16:40:20 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[4] remoteNodeSize:[3]
2025-04-30 16:40:20 [server-node-balance] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[DEFAULT_SERVER] hostId:[1917428617085267968]
2025-04-30 16:40:20 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-30 16:40:20 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-30 16:41:10 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[3] remoteNodeSize:[4]
2025-04-30 16:41:10 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-30 16:41:10 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-30 16:41:26 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-5615] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917428640338436096]
2025-04-30 16:42:45 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-5639] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917496920038572032]
2025-04-30 16:44:44 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-5676] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917499603751469056]
2025-04-30 16:46:02 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-5700] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917499463640289280]
2025-04-30 16:47:36 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-5729] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917500219559231488]
2025-04-30 16:58:52 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917500970268295168]
2025-04-30 17:04:06 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-6034] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917503814828507136]
2025-04-30 17:11:29 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[4] remoteNodeSize:[5]
2025-04-30 17:11:29 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-30 17:11:29 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-30 17:27:52 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917427196558659584]
2025-04-30 17:29:12 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-6489] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917511093061525504]
2025-04-30 17:29:52 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917505117029793792]
2025-04-30 17:33:32 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917511370321698816]
2025-04-30 17:39:47 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-6680] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917511652661411840]
2025-04-30 17:41:32 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917514095537348608]
2025-04-30 17:47:36 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-6825] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917514558915731456]
2025-04-30 17:49:46 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-6865] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917516063651573760]
2025-04-30 17:55:12 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917516619761807360]
2025-04-30 18:00:07 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-7057] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1917518820567629824]
2025-04-30 18:01:34 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[5] remoteNodeSize:[4]
2025-04-30 18:01:34 [server-node-balance] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[DEFAULT_SERVER] hostId:[1917499436473778176]
2025-04-30 18:01:34 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-04-30 18:01:34 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.4.0
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-04-30 18:02:21 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1917423202218123264]
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.4.0
2025-04-30 18:02:21 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-04-30 18:02:21 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-30 18:02:21 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.

package org.dromara.attendance.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.attendance.domain.MAttRecord;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class MAttRecordBoToMAttRecordMapperImpl implements MAttRecordBoToMAttRecordMapper {

    @Override
    public MAttRecord convert(MAttRecordBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MAttRecord mAttRecord = new MAttRecord();

        mAttRecord.setSearchValue( arg0.getSearchValue() );
        mAttRecord.setCreateDept( arg0.getCreateDept() );
        mAttRecord.setCreateBy( arg0.getCreateBy() );
        mAttRecord.setCreateTime( arg0.getCreateTime() );
        mAttRecord.setUpdateBy( arg0.getUpdateBy() );
        mAttRecord.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            mAttRecord.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        mAttRecord.setId( arg0.getId() );
        mAttRecord.setRuleId( arg0.getRuleId() );
        mAttRecord.setPersonId( arg0.getPersonId() );
        mAttRecord.setPersonType( arg0.getPersonType() );
        mAttRecord.setRealName( arg0.getRealName() );
        mAttRecord.setIdNumber( arg0.getIdNumber() );
        mAttRecord.setRealTimeFace( arg0.getRealTimeFace() );
        mAttRecord.setSn( arg0.getSn() );
        mAttRecord.setSource( arg0.getSource() );
        mAttRecord.setContent( arg0.getContent() );
        mAttRecord.setAttTime( arg0.getAttTime() );
        mAttRecord.setAttDate( arg0.getAttDate() );
        mAttRecord.setAttResult( arg0.getAttResult() );
        mAttRecord.setWhichTime( arg0.getWhichTime() );

        return mAttRecord;
    }

    @Override
    public MAttRecord convert(MAttRecordBo arg0, MAttRecord arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setRuleId( arg0.getRuleId() );
        arg1.setPersonId( arg0.getPersonId() );
        arg1.setPersonType( arg0.getPersonType() );
        arg1.setRealName( arg0.getRealName() );
        arg1.setIdNumber( arg0.getIdNumber() );
        arg1.setRealTimeFace( arg0.getRealTimeFace() );
        arg1.setSn( arg0.getSn() );
        arg1.setSource( arg0.getSource() );
        arg1.setContent( arg0.getContent() );
        arg1.setAttTime( arg0.getAttTime() );
        arg1.setAttDate( arg0.getAttDate() );
        arg1.setAttResult( arg0.getAttResult() );
        arg1.setWhichTime( arg0.getWhichTime() );

        return arg1;
    }
}

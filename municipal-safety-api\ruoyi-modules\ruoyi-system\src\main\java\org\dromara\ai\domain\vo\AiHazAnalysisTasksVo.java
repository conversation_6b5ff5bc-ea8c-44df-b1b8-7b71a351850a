package org.dromara.ai.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.ai.domain.AiHazAnalysisTasks;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.projects.domain.vo.PrjHazardousItemsVo;
import org.dromara.projects.domain.vo.PrjProjectsVo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 智能隐患分析任务视图对象 ai_haz_analysis_tasks
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiHazAnalysisTasks.class)
public class AiHazAnalysisTasksVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分析任务ID
     */
    @ExcelProperty(value = "分析任务ID")
    private Long taskId;

    /**
     * 关联项目ID
     */
    @ExcelProperty(value = "关联项目ID")
    private Long projectId;

    /**
     * 预警来源类型 CAMERA[摄像头] APP[用户App上报]
     */
    private String sourceType;

    /**
     * 提交分析的专家用户ID
     */
    @ExcelProperty(value = "提交分析的专家用户ID")
    private Long expertUserId;

    /**
     * 照片上传时间
     */
    @ExcelProperty(value = "照片上传时间")
    private Date uploadTime;

    /**
     * 上传的照片文档ID
     */
    private Long photoDocumentId;

    /**
     * 上传的照片文档ID
     */
    @Translation(type = TransConstant.OSS_ID_TO_URL, mapper = "photoDocumentId")
    private Long photoDocumentUrl;

    /**
     * AI分析后返回的带标注的照片文档ID
     */
    private Long aiPhotoDocumentId;

    /**
     * AI分析后返回的带标注的照片文档ID
     */
    @Translation(type = TransConstant.OSS_ID_TO_URL, mapper = "aiPhotoDocumentId")
    private Long aiPhotoDocumentUrl;

    /**
     * 拍照时GPS坐标
     */
    @ExcelProperty(value = "拍照时GPS坐标")
    private String gpsLocation;

    /**
     * 拍照位置文字描述
     */
    @ExcelProperty(value = "拍照位置文字描述")
    private String locationDescription;

    /**
     * AI模型识别输出结果
     */
    @ExcelProperty(value = "AI模型识别输出结果")
    private String aiRecognitionRawResult;

    /**
     * 关联的已知危大工程项ID
     */
    @ExcelProperty(value = "关联的已知危大工程项ID")
    private Long itemId;

    /**
     * 任务状态
     */
    @ExcelProperty(value = "任务状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ai_haz_analysis_tasks_status")
    private String status;

    /**
     * 复检状态（PENDING_RECHECK[待复检]、FINISH_RECHECK[复检完成]）
     */
    private String recheckStatus;

    /**
     * 因此次分析发现问题而生成的工单ID
     */
    @ExcelProperty(value = "因此次分析发现问题而生成的工单ID")
    private Long relatedWorkOrderId;

    private String projectName;

    private String dangerId;

    private Integer dangerListType;

    /**
     * 涉危工程清单父级名称
     */
    private String parentName;

    /**
     * 工程名称
     */
    private String itemName;

    /**
     * 关联的危大项目信息
     */
    private PrjProjectsVo prjProjectsVo;


    /**
     * 关联的危大工程信息
     */
    private PrjHazardousItemsVo prjHazardousItemsVo;


}

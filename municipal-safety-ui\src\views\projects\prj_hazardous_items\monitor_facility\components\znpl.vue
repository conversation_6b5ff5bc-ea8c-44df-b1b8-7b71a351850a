<template>
  <div class="app-container">

    <el-table v-loading="loading" :data="sprayingList">
      <el-table-column label="名称" align="center" prop="relayStatus">
        <template v-slot="scope">
          <el-popover trigger="hover" placement="top" title="继电器状态" style="display: flex;justify-content: center;">
            <p v-for="(item, index) in scope.row.relayStatus.split('')"
              style="color: #22c55e;display: flex;justify-content: space-between;">
              <span>继电器{{ index + 1 }}</span>
              <span v-if="item == '0'">未使用</span>
              <span v-if="item == '1'">吸合</span>
              <span v-if="item == '2'">断开</span>
            </p>
            <div slot="reference" style="width: 70px;">
              <el-tag size="medium">继电器组</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="工作模式" align="center" prop="workMode">
        <template v-slot="scope">
          <span v-if="scope.row.workMode == '0'">自动</span>
          <span v-if="scope.row.workMode == '1'">手动</span>
          <span v-if="scope.row.workMode == '2'">定时</span>
          <span v-if="scope.row.workMode == '3'">周期</span>
          <span v-if="scope.row.workMode == '4'">远程</span>
        </template>
      </el-table-column>
      <el-table-column label="水位传感器" align="center" prop="waterSensor">
        <template v-slot="scope">
          <span v-if="scope.row.waterSensor == '0'">正常</span>
          <span v-if="scope.row.waterSensor == '1'">下限位</span>
          <span v-if="scope.row.waterSensor == '2'">上限位</span>
          <span v-if="scope.row.waterSensor == '3'">异常</span>
        </template>
      </el-table-column>
      <el-table-column label="设备状态" align="center" prop="status">
        <template v-slot="scope">
          <span v-if="scope.row.status == '0'">正常</span>
          <span v-else>异常</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />


  </div>
</template>

<script>
import { listSpraying } from "@/api/projects/facility/index";

export default {
  name: "Spraying",
  props: {
    devNo: {
      type: String,
      default: ""
    }
  },
  data () {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 绿能喷淋设备表格数据
      sprayingList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        devNo: null
      },
    };
  },
  created () {
    this.getList();
  },
  methods: {
    /** 查询绿能喷淋设备列表 */
    async getList () {
      this.sprayingList = []
      this.loading = true
      this.queryParams.devNo = this.devNo
      const res = await listSpraying(this.queryParams)
      this.sprayingList = res.rows;
      this.total = res.total;
      this.loading = false;
    },
  }
};
</script>

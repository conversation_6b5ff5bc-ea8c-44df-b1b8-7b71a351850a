{"doc": " 流程实例视图\n\n <AUTHOR>\n", "fields": [{"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "tenantId", "doc": " 租户ID\n"}, {"name": "delFlag", "doc": " 删除标记\n"}, {"name": "definitionId", "doc": " 对应flow_definition表的id\n"}, {"name": "flowName", "doc": " 流程定义名称\n"}, {"name": "flowCode", "doc": " 流程定义编码\n"}, {"name": "businessId", "doc": " 业务id\n"}, {"name": "nodeType", "doc": " 节点类型（0开始节点 1中间节点 2结束节点 3互斥网关 4并行网关）\n"}, {"name": "nodeCode", "doc": " 流程节点编码   每个流程的nodeCode是唯一的,即definitionId+nodeCode唯一,在数据库层面做了控制\n"}, {"name": "nodeName", "doc": " 流程节点名称\n"}, {"name": "variable", "doc": " 流程变量\n"}, {"name": "flowStatus", "doc": " 流程状态（0待提交 1审批中 2 审批通过 3自动通过 8已完成 9已退回 10失效）\n"}, {"name": "flowStatusName", "doc": " 流程状态\n"}, {"name": "activityStatus", "doc": " 流程激活状态（0挂起 1激活）\n"}, {"name": "formCustom", "doc": " 审批表单是否自定义（Y是 N否）\n"}, {"name": "formPath", "doc": " 审批表单路径\n"}, {"name": "ext", "doc": " 扩展字段，预留给业务系统使用\n"}, {"name": "version", "doc": " 流程定义版本\n"}, {"name": "createBy", "doc": " 创建者\n"}, {"name": "createByName", "doc": " 申请人\n"}, {"name": "category", "doc": " 流程分类id\n"}, {"name": "categoryName", "doc": " 流程分类名称\n"}], "enumConstants": [], "methods": [], "constructors": []}
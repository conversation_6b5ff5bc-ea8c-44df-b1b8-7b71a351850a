package org.dromara.flow.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.flow.domain.PrjHazardousItemsFile;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsFileVoToPrjHazardousItemsFileMapperImpl implements PrjHazardousItemsFileVoToPrjHazardousItemsFileMapper {

    @Override
    public PrjHazardousItemsFile convert(PrjHazardousItemsFileVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsFile prjHazardousItemsFile = new PrjHazardousItemsFile();

        prjHazardousItemsFile.setItemFileId( arg0.getItemFileId() );
        prjHazardousItemsFile.setName( arg0.getName() );
        prjHazardousItemsFile.setFileId( arg0.getFileId() );
        prjHazardousItemsFile.setTaskId( arg0.getTaskId() );
        prjHazardousItemsFile.setServiceType( arg0.getServiceType() );
        prjHazardousItemsFile.setCallFileId( arg0.getCallFileId() );

        return prjHazardousItemsFile;
    }

    @Override
    public PrjHazardousItemsFile convert(PrjHazardousItemsFileVo arg0, PrjHazardousItemsFile arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setItemFileId( arg0.getItemFileId() );
        arg1.setName( arg0.getName() );
        arg1.setFileId( arg0.getFileId() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setServiceType( arg0.getServiceType() );
        arg1.setCallFileId( arg0.getCallFileId() );

        return arg1;
    }
}

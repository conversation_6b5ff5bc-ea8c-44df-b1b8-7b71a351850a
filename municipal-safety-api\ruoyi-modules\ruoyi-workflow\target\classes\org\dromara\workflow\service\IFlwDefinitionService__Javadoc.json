{"doc": " 流程定义 服务层\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryList", "paramTypes": ["org.dromara.warm.flow.orm.entity.FlowDefinition", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询流程定义列表\n\n @param flowDefinition 参数\n @param pageQuery      分页\n @return 返回分页列表\n"}, {"name": "unPublishList", "paramTypes": ["org.dromara.warm.flow.orm.entity.FlowDefinition", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询未发布的流程定义列表\n\n @param flowDefinition 参数\n @param pageQuery      分页\n @return 返回分页列表\n"}, {"name": "publish", "paramTypes": ["java.lang.Long"], "doc": " 发布流程定义\n\n @param id 流程定义id\n @return 结果\n"}, {"name": "exportDef", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出流程定义\n\n @param id       流程定义id\n @param response 响应\n @throws IOException 异常\n"}, {"name": "importJson", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": " 导入流程定义\n\n @param file     文件\n @param category 分类\n @return 结果\n"}, {"name": "removeDef", "paramTypes": ["java.util.List"], "doc": " 删除流程定义\n\n @param ids 流程定义id\n @return 结果\n"}, {"name": "syncDef", "paramTypes": ["java.lang.String"], "doc": " 新增租户流程定义\n\n @param tenantId 租户id\n"}], "constructors": []}
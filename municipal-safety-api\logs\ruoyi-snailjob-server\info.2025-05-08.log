2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.4.0
2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-05-08 08:46:46 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1919940154006253568]
2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.4.0
2025-05-08 08:46:46 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-05-08 08:46:46 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-08 08:46:46 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-08 09:25:38 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-08 09:25:38 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.15 with PID 26216 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-snailjob-server\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-08 09:25:38 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-05-08 09:25:40 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-05-08 09:25:40 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-05-08 09:25:40 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-08 09:25:40 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-05-08 09:25:40 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-05-08 09:25:40 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1516 ms
2025-05-08 09:25:41 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-05-08 09:25:42 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-08 09:25:42 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-08 09:25:42 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-08 09:25:42 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-6] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-08 09:25:42 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-05-08 09:25:42 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-05-08 09:25:42 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-05-08 09:25:42 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.4.0
2025-05-08 09:25:42 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-05-08 09:25:42 [main] INFO  c.a.s.s.common.rpc.server.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.GrpcServer, port = 17888
2025-05-08 09:25:42 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-05-08 09:25:42 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-05-08 09:25:42 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-05-08 09:25:42 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-05-08 09:25:42 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-05-08 09:25:42 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-05-08 09:25:42 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-05-08 09:25:42 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-05-08 09:25:42 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-05-08 09:25:42 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-05-08 09:25:42 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-05-08 09:25:42 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-05-08 09:25:42 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.4.0
2025-05-08 09:25:42 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 4.884 seconds (process running for 5.622)
2025-05-08 09:25:43 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-08 09:25:43 [RMI TCP Connection(1)-***************] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-08 09:25:43 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-08 09:25:43 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-05-08 09:25:43 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as cc20f5b8d840
2025-05-08 09:25:44 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@94e8cba
2025-05-08 09:25:44 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-08 09:25:44 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1920288964062597120]
2025-05-08 09:25:53 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-05-08 09:25:53 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-05-08 09:26:00 [snail-job-scheduled-thread-1] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1920288990042112000]
2025-05-08 09:38:13 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.4.0
2025-05-08 09:38:13 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-05-08 09:38:13 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-05-08 09:38:13 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-05-08 09:38:13 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable stop
2025-05-08 09:38:13 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-05-08 09:38:13 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-05-08 09:38:13 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-05-08 09:38:13 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1920288964062597120]
2025-05-08 09:38:13 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-05-08 09:38:13 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-05-08 09:38:13 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-05-08 09:38:13 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.4.0
2025-05-08 09:38:13 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-05-08 09:38:13 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-05-08 09:38:14 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-05-08 09:38:14 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-05-08 09:38:14 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-05-08 09:39:57 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-08 09:39:58 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.15 with PID 13016 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-extend\ruoyi-snailjob-server\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-08 09:39:58 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-05-08 09:40:00 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-05-08 09:40:00 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-05-08 09:40:00 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-08 09:40:00 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-05-08 09:40:00 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-05-08 09:40:00 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2274 ms
2025-05-08 09:40:02 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-05-08 09:40:04 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-08 09:40:04 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-08 09:40:04 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-08 09:40:04 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-05-08 09:40:05 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-05-08 09:40:05 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-05-08 09:40:05 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-05-08 09:40:05 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.4.0
2025-05-08 09:40:05 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-05-08 09:40:06 [main] INFO  c.a.s.s.common.rpc.server.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.GrpcServer, port = 17888
2025-05-08 09:40:06 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-05-08 09:40:06 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-05-08 09:40:06 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-05-08 09:40:06 [main] INFO  c.a.s.s.c.cache.CacheRegisterTable - CacheRegisterTable start
2025-05-08 09:40:06 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-05-08 09:40:06 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-05-08 09:40:06 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-05-08 09:40:06 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-05-08 09:40:06 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-05-08 09:40:06 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-05-08 09:40:06 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-05-08 09:40:06 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-05-08 09:40:06 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.4.0
2025-05-08 09:40:06 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 8.77 seconds (process running for 10.012)
2025-05-08 09:40:06 [snail-job-scheduled-thread-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-08 09:40:06 [RMI TCP Connection(13)-***************] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-08 09:40:06 [RMI TCP Connection(13)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-08 09:40:06 [RMI TCP Connection(13)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 30 ms
2025-05-08 09:40:07 [snail-job-scheduled-thread-1] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@34ecec08
2025-05-08 09:40:07 [snail-job-scheduled-thread-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-08 09:40:07 [registrationTask1] INFO  d.c.b.a.c.r.ApplicationRegistrator - Application registered itself as cc20f5b8d840
2025-05-08 09:40:07 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[DEFAULT_SERVER] namespaceId:[DEFAULT_SERVER_NAMESPACE_ID] hostId:[1920292574049718272]
2025-05-08 09:40:16 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-05-08 09:40:16 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-05-08 09:40:38 [snail-job-scheduled-thread-1] INFO  c.a.s.s.c.cache.CacheRegisterTable - Add cache. groupName:[ruoyi_group] namespaceId:[dev] hostId:[1920292673526022144]
2025-05-08 11:08:04 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[1] remoteNodeSize:[2]
2025-05-08 11:08:04 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-05-08 11:08:04 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-05-08 14:26:56 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-4309] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920314737934544896]
2025-05-08 14:59:55 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-4914] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920292673526022144]
2025-05-08 15:03:06 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920372977842266112]
2025-05-08 15:07:17 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[2] remoteNodeSize:[3]
2025-05-08 15:07:17 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-05-08 15:07:17 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85]]
2025-05-08 15:20:36 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-5288] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920373787678556160]
2025-05-08 15:25:31 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-5377] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920378029583720448]
2025-05-08 18:12:26 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920379259085242368]
2025-05-08 18:15:46 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920421399001669632]
2025-05-08 18:20:06 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920422232569225216]
2025-05-08 18:25:16 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-8693] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920423206373404672]
2025-05-08 19:37:06 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920424498948829184]
2025-05-08 19:46:40 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-10196] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920442559273758720]
2025-05-08 19:48:46 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920445113692852224]
2025-05-08 20:14:26 [server-register-node] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920445721762140160]
2025-05-08 21:01:23 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - 存在远程和本地缓存的节点的数量不一致则触发rebalance. localNodeSize:[3] remoteNodeSize:[2]
2025-05-08 21:01:23 [server-node-balance] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[DEFAULT_SERVER] hostId:[1920314708004024320]
2025-05-08 21:01:23 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-05-08 21:01:23 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63]]
2025-05-08 21:02:30 [NETTY_ACTOR_SYSTEM-pekko.actor.netty-receive-request-dispatcher-11589] INFO  c.a.s.s.c.cache.CacheRegisterTable - Remove cache. groupName:[ruoyi_group] hostId:[1920364573136310272]

{"doc": " 代码生成业务字段表 gen_table_column\n\n <AUTHOR> Li\n", "fields": [{"name": "columnId", "doc": " 编号\n"}, {"name": "tableId", "doc": " 归属表编号\n"}, {"name": "columnName", "doc": " 列名称\n"}, {"name": "columnComment", "doc": " 列描述\n"}, {"name": "columnType", "doc": " 列类型\n"}, {"name": "javaType", "doc": " JAVA类型\n"}, {"name": "javaField", "doc": " JAVA字段名\n"}, {"name": "isPk", "doc": " 是否主键（1是）\n"}, {"name": "isIncrement", "doc": " 是否自增（1是）\n"}, {"name": "isRequired", "doc": " 是否必填（1是）\n"}, {"name": "isInsert", "doc": " 是否为插入字段（1是）\n"}, {"name": "isEdit", "doc": " 是否编辑字段（1是）\n"}, {"name": "isList", "doc": " 是否列表字段（1是）\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": " 是否查询字段（1是）\n"}, {"name": "queryType", "doc": " 查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围）\n"}, {"name": "htmlType", "doc": " 显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件）\n"}, {"name": "dictType", "doc": " 字典类型\n"}, {"name": "sort", "doc": " 排序\n"}], "enumConstants": [], "methods": [], "constructors": []}
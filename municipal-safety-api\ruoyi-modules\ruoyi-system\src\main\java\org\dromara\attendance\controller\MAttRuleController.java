package org.dromara.attendance.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.attendance.domain.bo.MAttRuleBo;
import org.dromara.attendance.domain.vo.MAttRuleVo;
import org.dromara.attendance.service.IMAttRuleService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.vo.SysDictDataVo;
import org.dromara.system.service.ISysDictTypeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 考勤规则
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/attendance/attRule")
public class MAttRuleController extends BaseController {

    private final IMAttRuleService mAttRuleService;
    private final ISysDictTypeService dictTypeService;

    /**
     * 查询考勤规则列表
     */
    @SaCheckPermission("attendance:attRule:list")
    @GetMapping("/list")
    public TableDataInfo<MAttRuleVo> list(MAttRuleBo bo, PageQuery pageQuery) {
        return mAttRuleService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询考勤规则列表
     */
    @SaCheckPermission("attendance:attRule:list")
    @GetMapping("/selectAll")
    public org.dromara.common.web.core.TableDataInfo selectAll(MAttRuleBo bo) {
        startPage();
        return getDataTable(mAttRuleService.selectMattRuleList(bo));
    }

    /**
     * 导出考勤规则列表
     */
    @SaCheckPermission("attendance:attRule:export")
    @Log(title = "考勤规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MAttRuleBo bo, HttpServletResponse response) {
        List<MAttRuleVo> list = mAttRuleService.queryList(bo);
        ExcelUtil.exportExcel(list, "考勤规则", MAttRuleVo.class, response);
    }

    /**
     * 获取考勤规则详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("attendance:attRule:query")
    @GetMapping("/{id}")
    public R<MAttRuleVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long id) {
        return R.ok(mAttRuleService.queryById(id));
    }

    /**
     * 新增考勤规则
     */
    @SaCheckPermission("attendance:attRule:add")
    @Log(title = "考勤规则", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MAttRuleBo bo) {
        return toAjax(mAttRuleService.insertByBo(bo));
    }

    /**
     * 修改考勤规则
     */
    @SaCheckPermission("attendance:attRule:edit")
    @Log(title = "考勤规则", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MAttRuleBo bo) {
        return toAjax(mAttRuleService.updateByBo(bo));
    }

    /**
     * 删除考勤规则
     *
     * @param ids 主键串
     */
    @SaCheckPermission("attendance:attRule:remove")
    @Log(title = "考勤规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mAttRuleService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取项目的角色规则
     * @return
     */
    @GetMapping("/roleList/{projectId}")
    public R<List<SysDictDataVo>> roleList(@PathVariable Long projectId) {
        // 获取字典数据
        List<SysDictDataVo> sysDictDataVos = dictTypeService.selectDictDataByType("personnel_position");

        // 获取规则数据
        List<MAttRuleVo> mAttRuleVos = mAttRuleService.selectMAttRuleByProjectId(projectId);

        // 收集所有需要禁用的值
        Set<String> disabledValues = new HashSet<>();
        for (MAttRuleVo mAttRuleVo : mAttRuleVos) {
            if (mAttRuleVo.getPersonType() != null && !mAttRuleVo.getPersonType().isEmpty()) {
                disabledValues.addAll(Arrays.asList(mAttRuleVo.getPersonType().split(",")));
            }
        }

        // 标记字典数据
        for (SysDictDataVo vo : sysDictDataVos) {
            // 假设匹配的是 dictValue 字段，如果不是请修改
            vo.setDisabled(disabledValues.contains(vo.getDictValue()));
        }

        return R.ok(sysDictDataVos);
    }
}

package org.dromara.projects.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.PrjHazardousItems;
import org.dromara.projects.domain.PrjHazardousItemsToPrjHazardousItemsVoMapper;
import org.dromara.projects.domain.PrjProjectsToPrjProjectsVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjProjectsVoToPrjProjectsMapper.class,PrjProjectsToPrjProjectsVoMapper.class,PrjHazardousItemsToPrjHazardousItemsVoMapper.class},
    imports = {}
)
public interface PrjHazardousItemsVoToPrjHazardousItemsMapper extends BaseMapper<PrjHazardousItemsVo, PrjHazardousItems> {
}

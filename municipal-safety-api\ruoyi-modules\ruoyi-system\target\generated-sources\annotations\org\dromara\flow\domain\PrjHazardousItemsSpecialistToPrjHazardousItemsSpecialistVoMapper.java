package org.dromara.flow.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistBoToPrjHazardousItemsSpecialistMapper;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistVo;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistVoToPrjHazardousItemsSpecialistMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {PrjHazardousItemsSpecialistBoToPrjHazardousItemsSpecialistMapper.class,PrjHazardousItemsSpecialistVoToPrjHazardousItemsSpecialistMapper.class},
    imports = {}
)
public interface PrjHazardousItemsSpecialistToPrjHazardousItemsSpecialistVoMapper extends BaseMapper<PrjHazardousItemsSpecialist, PrjHazardousItemsSpecialistVo> {
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.CJDWMapper">

    <resultMap id="CjdwResultMap" type="org.dromara.system.domain.CJDW">
        <id property="id" column="ID"/>
        <result property="builderLicenceNum" column="BUILDERLICENCENUM"/>
        <result property="corpType" column="CORPTYPE"/>
        <result property="corpName" column="CORPNAME"/>
        <result property="socialCreditCode" column="SOCIALCREDITCODE"/>
        <result property="legalMan" column="LEGALMAN"/>
        <result property="corpQualification" column="CORPQUALIFICATION"/>
        <result property="corpQualificationCode" column="CORPQUALIFICATIONCODE"/>
        <result property="delFlag" column="DEL_FLAG"/>
    </resultMap>

    <!-- Insert a new CJDW record -->
    <insert id="insertCjdw" parameterType="org.dromara.system.domain.CJDW" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CJDW (BUILDERLICENCENUM,
                          CORPTYPE,
                          CORPNAME,
                          SOCIALCREDITCODE,
                          LEGALMAN,
                          CORPQUALIFICATION,
                          CORPQUALIFICATIONCODE,
                          DEL_FLAG)
        VALUES (#{builderLicenceNum},
                #{corpType},
                #{corpName},
                #{socialCreditCode},
                #{legalMan},
                #{corpQualification},
                #{corpQualificationCode},
                '0')
    </insert>

    <!-- 批量插入CJDW操作 -->
    <insert id="batchInsertCjdw" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CJDW (
        BUILDERLICENCENUM,
        CORPTYPE,
        CORPNAME,
        SOCIALCREDITCODE,
        LEGALMAN,
        CORPQUALIFICATION,
        CORPQUALIFICATIONCODE,
        DEL_FLAG
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.builderLicenceNum},
            #{item.corpType},
            #{item.corpName},
            #{item.socialCreditCode},
            #{item.legalMan},
            #{item.corpQualification},
            #{item.corpQualificationCode},
            '0'
            )
        </foreach>
    </insert>

    <!-- Update an existing CJDW record -->
    <update id="updateCjdw" parameterType="org.dromara.system.domain.CJDW">
        UPDATE CJDW
        SET BUILDERLICENCENUM     = #{builderLicenceNum},
            CORPTYPE              = #{corpType},
            CORPNAME              = #{corpName},
            SOCIALCREDITCODE      = #{socialCreditCode},
            LEGALMAN              = #{legalMan},
            CORPQUALIFICATION     = #{corpQualification},
            CORPQUALIFICATIONCODE = #{corpQualificationCode}
        WHERE ID = #{id}
          AND DEL_FLAG = '0'
    </update>

    <!-- Soft delete a CJDW record (set del_flag to '1') -->
    <update id="deleteCjdwById" parameterType="java.lang.Integer">
        UPDATE CJDW
        SET DEL_FLAG = '1'
        WHERE ID = #{id}
          AND DEL_FLAG = '0'
    </update>

    <!-- Select CJDW by ID -->
    <select id="selectCjdwById" parameterType="java.lang.Integer" resultMap="CjdwResultMap">
        SELECT *
        FROM CJDW
        WHERE ID = #{id}
          AND DEL_FLAG = '0'
    </select>

    <!-- Select all CJDW records (not deleted) -->
    <select id="selectAllCjdw" resultMap="CjdwResultMap">
        SELECT *
        FROM CJDW
        WHERE DEL_FLAG = '0'
    </select>

    <!-- Select CJDW by builder licence number -->
    <select id="selectCjdwByBuilderLicenceNum" parameterType="java.lang.String" resultMap="CjdwResultMap">
        SELECT *
        FROM CJDW
        WHERE BUILDERLICENCENUM = #{builderLicenceNum}
          AND DEL_FLAG = '0'
    </select>

    <!-- Select CJDW by social credit code -->
    <select id="selectCjdwBySocialCreditCode" parameterType="java.lang.String" resultMap="CjdwResultMap">
        SELECT *
        FROM CJDW
        WHERE SOCIALCREDITCODE = #{socialCreditCode}
          AND DEL_FLAG = '0'
    </select>

    <!-- Check if a builder licence number exists -->
    <select id="checkBuilderLicenceNumExists" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM CJDW
        WHERE BUILDERLICENCENUM = #{builderLicenceNum}
          AND DEL_FLAG = '0'
    </select>

    <delete id="deleteAllPhysically">
        DELETE FROM CJDW
    </delete>

    <select id="selectBuilderLicenceNum" parameterType="java.lang.String" resultMap="CjdwResultMap">
        select BUILDERLICENCENUM
        from cjdw c
        where c.CORPTYPE = '施工单位'
          and c.SOCIALCREDITCODE = #{qytyshxydm}
        GROUP BY c.BUILDERLICENCENUM
    </select>
</mapper>

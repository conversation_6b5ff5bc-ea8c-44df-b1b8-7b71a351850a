<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="查询条件" prop="type">
              <el-select v-model="queryParams.type" placeholder="请选择查询类型" clearable style="width: 110px">
                <el-option label="项目" value="project" />
                <el-option label="工程" value="item" />
              </el-select>
              <el-input v-model="searchName" placeholder="请输入名称" clearable :disabled="!queryParams.type"
                @keyup.enter="handleQuery" style="width: 200px; margin-left: 6px" />
            </el-form-item>
            <el-form-item label="任务状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable>
                <el-option v-for="dict in ai_haz_analysis_tasks_status" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <!--      <template #header>-->
      <!--        <el-row :gutter="10" class="mb8">-->
      <!--          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
      <!--        </el-row>-->
      <!--      </template>-->

      <el-table v-loading="loading" :data="ai_haz_analysis_tasksList">
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column label="工程名称" align="center" prop="itemName" />
        <el-table-column label="危大类型" align="center" prop="dangerListType">
          <template #default="scope">
            <dict-tag :options="hidden_danger_type" :value="scope.row.dangerListType" />
          </template>
        </el-table-column>
        <el-table-column label="涉危工程" align="center" prop="parentName" />
        <el-table-column label="照片上传时间" align="center" prop="uploadTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.uploadTime) }}</span>
          </template>
        </el-table-column>
        <!--        <el-table-column label="隐患描述" align="center" prop="locationDescription" />-->
        <el-table-column label="任务状态" align="center" prop="status" width="180" show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="ai_haz_analysis_tasks_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="预警来源" align="center" prop="sourceType">
          <template #default="scope">
            <dict-tag :options="ai_haz_analysis_tasks_source_type" :value="scope.row.sourceType" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="查看详情" placement="top">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">查看详情</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog :title="'查看智能隐患分析任务详情'" v-model="dialog.visible" width="1200px" append-to-body>
      <div class="detail-container">
        <el-tabs type="border-card" class="detail-tabs">
          <el-tab-pane label="基础信息">
            <div class="info-section">
              <div class="section-header">
                <el-icon>
                  <InfoFilled />
                </el-icon>
                <span>项目信息</span>
              </div>
              <el-descriptions :column="2" border class="custom-descriptions">
                <el-descriptions-item label="项目名称" :span="2">{{ form.prjProjectsVo?.projectName
                }}</el-descriptions-item>
                <el-descriptions-item label="项目编号">{{ form.prjProjectsVo?.projectCode }}</el-descriptions-item>
                <el-descriptions-item label="建设单位">{{ form.prjProjectsVo?.clientOrgName }}</el-descriptions-item>
                <el-descriptions-item label="施工单位">{{ form.prjProjectsVo?.constructionOrgName }}</el-descriptions-item>
                <el-descriptions-item label="监理单位">{{ form.prjProjectsVo?.supervisionOrgName }}</el-descriptions-item>
              </el-descriptions>
            </div>

            <div class="info-section">
              <div class="section-header">
                <el-icon>
                  <Tools />
                </el-icon>
                <span>工程信息</span>
              </div>
              <el-descriptions :column="2" border class="custom-descriptions">
                <el-descriptions-item label="工程名称">{{ form.prjHazardousItemsVo?.itemName }}</el-descriptions-item>
                <el-descriptions-item label="危大工程类型">{{ form.prjHazardousItemsVo?.parentName }}</el-descriptions-item>
                <el-descriptions-item label="工程范围" :span="2">{{ form.prjHazardousItemsVo?.scopeDetails
                }}</el-descriptions-item>
              </el-descriptions>
            </div>

            <div class="info-section">
              <div class="section-header">
                <el-icon>
                  <Monitor />
                </el-icon>
                <span>AI分析信息</span>
              </div>
              <el-descriptions :column="2" border class="custom-descriptions">
                <el-descriptions-item label="隐患描述">{{ form.locationDescription }}</el-descriptions-item>
                <el-descriptions-item label="拍摄时间">{{ parseTime(form.uploadTime) }}</el-descriptions-item>
                <el-descriptions-item label="预警来源">
                  <dict-tag :options="ai_haz_analysis_tasks_source_type" :value="form.sourceType" />
                </el-descriptions-item>
                <el-descriptions-item label="任务状态">
                  <dict-tag :options="ai_haz_analysis_tasks_status" :value="form.status" />
                </el-descriptions-item>
                <el-descriptions-item label="复查状态">
                  <dict-tag :options="ai_haz_analysis_tasks_recheck_status" :value="form.recheckStatus" />
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-tab-pane>

          <el-tab-pane label="AI识别结果">
            <div class="info-section">
              <div class="section-header">
                <el-icon>
                  <Picture />
                </el-icon>
                <span>现场照片</span>
              </div>
              <div class="image-comparison">
                <div class="comparison-container">
                  <div class="image-wrapper">
                    <div class="image-title">原始照片</div>
                    <ImagePreview v-if="form.photoDocumentUrl" :src="String(form.photoDocumentUrl)"
                      class="detail-image" />
                    <el-empty v-else description="暂无原始照片" />
                  </div>
                  <div class="image-separator">
                    <el-icon>
                      <Right />
                    </el-icon>
                  </div>
                  <div class="image-wrapper">
                    <div class="image-title">AI标注照片</div>
                    <ImagePreview v-if="form.aiPhotoDocumentUrl" :src="String(form.aiPhotoDocumentUrl)"
                      class="detail-image" />
                    <el-empty v-else description="暂无AI标注照片" />
                  </div>
                </div>
              </div>
            </div>

            <div class="info-section">
              <div class="section-header">
                <el-icon>
                  <Warning />
                </el-icon>
                <span>识别结果详情</span>
              </div>
              <div v-if="aiResults.length > 0">
                <el-collapse accordion>
                  <el-collapse-item v-for="(item, index) in aiResults" :key="index"
                    :title="`隐患 ${index + 1}: ${item.violation.slice(0, 50)}...`">
                    <div class="ai-result-card">
                      <div class="result-info">
                        <el-descriptions :column="2" border class="custom-descriptions">
                          <el-descriptions-item label="隐患描述" :span="2" label-width="90px">
                            <div class="description-content">{{ item.violation }}</div>
                          </el-descriptions-item>
                          <el-descriptions-item label="违规等级">
                            <el-tag :type="item.level === '1' ? 'warning' : 'danger'" effect="dark" size="large">
                              {{ item.level === '1' ? '一般隐患' : '重大隐患' }}
                            </el-tag>
                          </el-descriptions-item>
                          <el-descriptions-item label="整改意见" label-width="90px">
                            <div class="description-content">{{ item.measure }}</div>
                          </el-descriptions-item>
                          <el-descriptions-item label="违反条款" :span="2">
                            <div class="description-content">{{ item.regulation }}</div>
                          </el-descriptions-item>
                        </el-descriptions>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
              <el-empty v-else description="暂无AI识别结果" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="dialog.visible = false" round>关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Ai_haz_analysis_tasks" lang="ts">
import { listAi_haz_analysis_tasks, getAi_haz_analysis_tasks } from '@/api/ai/ai_haz_analysis_tasks';
import { Ai_haz_analysis_tasksVO, Ai_haz_analysis_tasksQuery, Ai_haz_analysis_tasksForm } from '@/api/ai/ai_haz_analysis_tasks/types';
import { InfoFilled, Monitor, Tools, Warning, Picture, Right } from '@element-plus/icons-vue';
import ImagePreview from '@/components/ImagePreview/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { ai_haz_analysis_tasks_status, hidden_danger_type, ai_haz_analysis_tasks_recheck_status, ai_haz_analysis_tasks_source_type } = toRefs<any>(
  proxy?.useDict('ai_haz_analysis_tasks_status', 'hidden_danger_type', 'ai_haz_analysis_tasks_recheck_status', 'ai_haz_analysis_tasks_source_type')
);

const ai_haz_analysis_tasksList = ref<Ai_haz_analysis_tasksVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const searchName = ref('');

const queryFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const aiResults = ref<any[]>([]);

const initFormData: Ai_haz_analysis_tasksForm = {
  taskId: undefined,
  projectId: undefined,
  projectName: undefined,
  itemName: undefined,
  dangerListType: undefined,
  parentName: undefined,
  expertUserId: undefined,
  uploadTime: undefined,
  photoDocumentUrl: undefined,
  aiPhotoDocumentUrl: undefined,
  gpsLocation: undefined,
  locationDescription: undefined,
  aiRecognitionRawResult: undefined,
  aiIdentifiedHazards: undefined,
  expertSelectedHazardTypes: undefined,
  expertManualInputDetails: undefined,
  relatedHazardousItemId: undefined,
  expertConfirmationTime: undefined,
  status: undefined,
  relatedWorkOrderId: undefined
};

const data = reactive<PageData<Ai_haz_analysis_tasksForm, Ai_haz_analysis_tasksQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectName: undefined,
    itemName: undefined,
    type: undefined,
    status: undefined,
    params: {}
  },
  rules: {}
});

const { queryParams, form } = toRefs(data);

/** 查询智能隐患分析任务列表 */
const getList = async () => {
  loading.value = true;
  const res = await listAi_haz_analysis_tasks(queryParams.value);
  ai_haz_analysis_tasksList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  // 根据选择的查询类型设置查询参数
  if (queryParams.value.type === 'project') {
    queryParams.value.projectName = searchName.value;
    queryParams.value.itemName = undefined;
  } else if (queryParams.value.type === 'item') {
    queryParams.value.itemName = searchName.value;
    queryParams.value.projectName = undefined;
  } else {
    // 如果没有选择查询类型，则清空相关字段
    queryParams.value.projectName = undefined;
    queryParams.value.itemName = undefined;
  }
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  searchName.value = '';
  queryFormRef.value?.resetFields();
  // 确保重置后清空所有查询条件
  queryParams.value.projectName = undefined;
  queryParams.value.itemName = undefined;
  queryParams.value.status = undefined;
  queryParams.value.type = undefined;
  handleQuery();
};

/** 查看详情按钮操作 */
const handleDetail = async (row: Ai_haz_analysis_tasksVO) => {
  const res = await getAi_haz_analysis_tasks(row.taskId);
  Object.assign(form.value, res.data);
  try {
    if (form.value.aiRecognitionRawResult) {
      const aiResult = JSON.parse(form.value.aiRecognitionRawResult);

      // 只有当状态为有隐患时才显示违规列表
      if (form.value.status === 'AI_ANALYSIS_COMPLETED' && aiResult.violationList) {
        aiResults.value = aiResult.violationList.map((item: any) => ({
          ...item
        }));

        // 只有当aiResults有数据时才添加固定数据
        if (aiResults.value.length > 0) {
          aiResults.value.push({
            violation: '疑似现场消防设施配备不足',
            level: '1', // 1代表一般隐患
            measure: '按照要求配备齐消防设施',
            regulation: '建设工程施工现场消防安全技术规范(GB 50720-2011)'
          });
        }
      } else {
        aiResults.value = [];
      }
    } else {
      aiResults.value = [];
    }
  } catch (error) {
    console.error('解析AI识别结果失败:', error);
    aiResults.value = [];
  }
  dialog.visible = true;
};

// 监听type变化，当type改变时清空searchName
watch(
  () => queryParams.value.type,
  (newVal) => {
    if (newVal === '') {
      searchName.value = '';
    }
  }
);

onMounted(() => {
  getList();
});
</script>

<style scoped>
.ai-result-item {
  margin-bottom: 16px;
}

.detail-container {
  padding: 0;
}

.detail-tabs {
  --el-border-radius-base: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.info-section {
  margin-bottom: 20px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--el-color-primary-light-9);
  font-weight: bold;
  font-size: 16px;
  color: var(--el-color-primary);
  border-bottom: 1px solid var(--el-border-color-light);
}

.section-header .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.custom-descriptions {
  --el-descriptions-item-bordered-label-background: var(--el-fill-color-light);
}

.ai-result-card {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--el-bg-color);
}

.result-info {
  flex: 1;
  padding: 10px 0;
}

.description-content {
  line-height: 1.6;
  padding: 4px 0;
}

.image-comparison {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
}

.comparison-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.image-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.image-title {
  font-weight: bold;
  margin-bottom: 12px;
  color: var(--el-text-color-primary);
  font-size: 16px;
}

.detail-image {
  width: 100%;
  height: 400px;
  border-radius: 4px;
  transition: transform 0.3s;
}

.detail-image:hover {
  transform: scale(1.02);
}

.image-separator {
  margin: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--el-text-color-secondary);
}

.image-error .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.dialog-footer {
  text-align: center;
  margin-top: 16px;
}
</style>

import { Plugin } from 'xgplayer'
// import axios from 'axios'

const { POSITIONS } = Plugin

export default class DemoPlugin extends Plugin {
  // 插件的名称，将作为插件实例的唯一key值
  static get pluginName() {
    return 'demoPlugin'
  }

  static get defaultConfig () {
    return {
      // 挂载在controls的左侧
      position: POSITIONS.CONTROLS_LEFT
    }
  }

  constructor (args) {
    super(args)
    this.danmutext = ''  // 初始化弹幕文本
  }

  beforePlayerInit () {
    // 播放器调用start初始化播放源之前的逻辑
  }

  afterPlayerInit () {
    // 播放器调用start初始化播放源之后的逻辑
  }

  afterCreate () {
    /**
     * 自定义插件 弹幕发送模块
     */
    this.icon = this.find('.danmu-send')
    const input = this.find('.danmu-input')
    
    this.onIconClick = (e) => {
      const danmutext = input.value
      if (!danmutext.trim()) return
      
      const video_id = this.player.root.__vue__?.video_id || ''
      const tokens3 = this.player.root.__vue__?.tokens || ''
      const playertimeinit = this.player.currentTime
      const playertime = playertimeinit * 1000
      
      this.sendDanmu(danmutext, video_id, tokens3, playertime)
      input.value = ''  // 清空输入
    }
    
    this.onClick = () => {
      console.log('当前插件根节点点击事件')
    }
    
    // 对当前插件根节点内部类名为.danmu-send的元素绑定click事件
    this.bind('.danmu-send', 'click', this.onIconClick)
    
    // 添加回车键发送功能
    this.onKeyUp = (e) => {
      if (e.key === 'Enter') {
        this.onIconClick()
      }
    }
    this.bind('.danmu-input', 'keyup', this.onKeyUp)
    
    // 对当前插件根节点绑定click事件
    this.bind('click', this.onClick)
  }
  
  sendDanmu(danmutext, video_id, tokens3, playertime) {
    if (!tokens3 || tokens3 === 'undefined') {
      console.log('未登录或token无效')
      // 可以添加未登录提示
      return
    }
    
    // 先在本地显示弹幕，提升用户体验
    this.player.danmu.sendComment({
      duration: 15000, // 弹幕持续显示时间,毫秒(最低为5000毫秒)
      id: playertime, // 弹幕id，需唯一
      start: playertime, // 弹幕出现时间，毫秒
      prior: true, // 该条弹幕优先显示，默认false
      color: true, // 该条弹幕为彩色弹幕，默认false
      txt: danmutext, // 弹幕文字内容
      style: {  // 弹幕自定义样式
        color: '#ff9500',
        fontSize: '20px',
        border: 'solid 1px #ff9500',
        borderRadius: '50px',
        padding: '5px 11px',
        backgroundColor: 'rgba(255, 255, 255, 0.1)'
      }
    })
    
    // 发送到服务器
    // 替换为你的弹幕发送API地址
    // axios.post(`/api/danmu/send`, {
    //   token: tokens3,
    //   content: danmutext,
    //   video_id: video_id,
    //   time: playertime
    // }).then(res => {
    //   console.log('弹幕发送成功', res)
    // }).catch(error => {
    //   console.error('弹幕发送失败', error)
    // })
  }

  destroy () {
    this.unbind('.danmu-send', 'click', this.onIconClick)
    this.unbind('.danmu-input', 'keyup', this.onKeyUp)
    this.unbind('click', this.onClick)
    this.icon = null
  }

  render () {
    return `<div class="demo-plugin danmu-container">
      <div class="danmutext">
        <input class="danmu-input" type="text" placeholder="发条弹幕吧~" 
          style="width: 320px;
          height: 40px;
          box-sizing: border-box;
          border-radius: 18px;
          background-color: #1d1d1d;
          border: solid 1px rgb(88, 88, 89);
          caret-color: #e7e7e7;
          font-size: 15px;
          color: #fff;
          letter-spacing: 0;
          padding: 3px 60px 3px 15px;
          position: relative;">
        <button class="danmu-send"
          style="position: absolute;
          right: 6px;
          width: 60px;
          height: 30px;
          line-height: 30px;
          border-radius: 15px;
          background-color: #01c8d4;
          color: #222;
          font-size: 12px;
          top: 5px;
          border: none;
          cursor: pointer;">发送</button>
      </div>
    </div>`
  }
}

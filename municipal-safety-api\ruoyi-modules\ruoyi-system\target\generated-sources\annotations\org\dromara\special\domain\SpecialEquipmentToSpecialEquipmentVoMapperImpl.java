package org.dromara.special.domain;

import javax.annotation.processing.Generated;
import org.dromara.special.domain.vo.SpecialEquipmentVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class SpecialEquipmentToSpecialEquipmentVoMapperImpl implements SpecialEquipmentToSpecialEquipmentVoMapper {

    @Override
    public SpecialEquipmentVo convert(SpecialEquipment arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SpecialEquipmentVo specialEquipmentVo = new SpecialEquipmentVo();

        specialEquipmentVo.setEquipmentId( arg0.getEquipmentId() );
        specialEquipmentVo.setConstructionPermitNum( arg0.getConstructionPermitNum() );
        specialEquipmentVo.setRecordNumber( arg0.getRecordNumber() );
        specialEquipmentVo.setEquipmentCategory( arg0.getEquipmentCategory() );
        specialEquipmentVo.setEquipmentName( arg0.getEquipmentName() );
        specialEquipmentVo.setModelSpec( arg0.getModelSpec() );
        specialEquipmentVo.setManufacturer( arg0.getManufacturer() );
        specialEquipmentVo.setManufacturerCode( arg0.getManufacturerCode() );
        specialEquipmentVo.setFactoryNumber( arg0.getFactoryNumber() );
        specialEquipmentVo.setFactoryDate( arg0.getFactoryDate() );
        specialEquipmentVo.setProductionLicense( arg0.getProductionLicense() );
        specialEquipmentVo.setUseYears( arg0.getUseYears() );
        specialEquipmentVo.setPropertyOwner( arg0.getPropertyOwner() );
        specialEquipmentVo.setPropertyOwnerCode( arg0.getPropertyOwnerCode() );
        specialEquipmentVo.setPropertyOwnerAddress( arg0.getPropertyOwnerAddress() );
        specialEquipmentVo.setLegalPerson( arg0.getLegalPerson() );
        specialEquipmentVo.setLegalPersonLicense( arg0.getLegalPersonLicense() );
        specialEquipmentVo.setContacts( arg0.getContacts() );
        specialEquipmentVo.setContactsPhone( arg0.getContactsPhone() );
        specialEquipmentVo.setPrice( arg0.getPrice() );
        specialEquipmentVo.setPurchaseDate( arg0.getPurchaseDate() );
        specialEquipmentVo.setAuthority( arg0.getAuthority() );
        specialEquipmentVo.setAuthorityCode( arg0.getAuthorityCode() );
        specialEquipmentVo.setLocationCity( arg0.getLocationCity() );
        specialEquipmentVo.setLocationCounty( arg0.getLocationCounty() );
        specialEquipmentVo.setLocationArea( arg0.getLocationArea() );
        specialEquipmentVo.setTowerCraneWeight( arg0.getTowerCraneWeight() );
        specialEquipmentVo.setWeightTorque( arg0.getWeightTorque() );
        specialEquipmentVo.setWeightLength( arg0.getWeightLength() );
        specialEquipmentVo.setWorkRange( arg0.getWorkRange() );
        specialEquipmentVo.setWorkRangeWeight( arg0.getWorkRangeWeight() );
        specialEquipmentVo.setImproveHeight( arg0.getImproveHeight() );
        specialEquipmentVo.setLiftingHeight( arg0.getLiftingHeight() );
        specialEquipmentVo.setStructureNumber( arg0.getStructureNumber() );
        specialEquipmentVo.setMaxHeight( arg0.getMaxHeight() );
        specialEquipmentVo.setStandardSectionSpecifications( arg0.getStandardSectionSpecifications() );
        specialEquipmentVo.setStrengthenSection( arg0.getStrengthenSection() );
        specialEquipmentVo.setStandardSection( arg0.getStandardSection() );
        specialEquipmentVo.setElevatorPower( arg0.getElevatorPower() );
        specialEquipmentVo.setImproveSpeed( arg0.getImproveSpeed() );
        specialEquipmentVo.setSafetyModel( arg0.getSafetyModel() );
        specialEquipmentVo.setConstructionElevatorSize( arg0.getConstructionElevatorSize() );
        specialEquipmentVo.setMaxSpan( arg0.getMaxSpan() );
        specialEquipmentVo.setIssuer( arg0.getIssuer() );
        specialEquipmentVo.setCertificateNumber( arg0.getCertificateNumber() );
        specialEquipmentVo.setIssueDate( arg0.getIssueDate() );
        specialEquipmentVo.setUseRegistrationCertificate( arg0.getUseRegistrationCertificate() );
        specialEquipmentVo.setProjectName( arg0.getProjectName() );
        specialEquipmentVo.setProjectAddress( arg0.getProjectAddress() );
        specialEquipmentVo.setProjectId( arg0.getProjectId() );
        specialEquipmentVo.setItemId( arg0.getItemId() );
        specialEquipmentVo.setUsageUnit( arg0.getUsageUnit() );
        specialEquipmentVo.setMaintenanceUnit( arg0.getMaintenanceUnit() );
        specialEquipmentVo.setInstallationUnit( arg0.getInstallationUnit() );
        specialEquipmentVo.setInspectionUnit( arg0.getInspectionUnit() );
        specialEquipmentVo.setProjectManager( arg0.getProjectManager() );
        specialEquipmentVo.setInstallationDate( arg0.getInstallationDate() );
        specialEquipmentVo.setInspectionDate( arg0.getInspectionDate() );
        specialEquipmentVo.setEnterDate( arg0.getEnterDate() );
        specialEquipmentVo.setExitDate( arg0.getExitDate() );
        specialEquipmentVo.setLocation( arg0.getLocation() );
        specialEquipmentVo.setSopId( arg0.getSopId() );
        specialEquipmentVo.setRemarks( arg0.getRemarks() );
        specialEquipmentVo.setDelFlag( arg0.getDelFlag() );
        specialEquipmentVo.setDevNo( arg0.getDevNo() );
        specialEquipmentVo.setProjectCraneNum( arg0.getProjectCraneNum() );
        specialEquipmentVo.setCraneType( arg0.getCraneType() );

        return specialEquipmentVo;
    }

    @Override
    public SpecialEquipmentVo convert(SpecialEquipment arg0, SpecialEquipmentVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setEquipmentId( arg0.getEquipmentId() );
        arg1.setConstructionPermitNum( arg0.getConstructionPermitNum() );
        arg1.setRecordNumber( arg0.getRecordNumber() );
        arg1.setEquipmentCategory( arg0.getEquipmentCategory() );
        arg1.setEquipmentName( arg0.getEquipmentName() );
        arg1.setModelSpec( arg0.getModelSpec() );
        arg1.setManufacturer( arg0.getManufacturer() );
        arg1.setManufacturerCode( arg0.getManufacturerCode() );
        arg1.setFactoryNumber( arg0.getFactoryNumber() );
        arg1.setFactoryDate( arg0.getFactoryDate() );
        arg1.setProductionLicense( arg0.getProductionLicense() );
        arg1.setUseYears( arg0.getUseYears() );
        arg1.setPropertyOwner( arg0.getPropertyOwner() );
        arg1.setPropertyOwnerCode( arg0.getPropertyOwnerCode() );
        arg1.setPropertyOwnerAddress( arg0.getPropertyOwnerAddress() );
        arg1.setLegalPerson( arg0.getLegalPerson() );
        arg1.setLegalPersonLicense( arg0.getLegalPersonLicense() );
        arg1.setContacts( arg0.getContacts() );
        arg1.setContactsPhone( arg0.getContactsPhone() );
        arg1.setPrice( arg0.getPrice() );
        arg1.setPurchaseDate( arg0.getPurchaseDate() );
        arg1.setAuthority( arg0.getAuthority() );
        arg1.setAuthorityCode( arg0.getAuthorityCode() );
        arg1.setLocationCity( arg0.getLocationCity() );
        arg1.setLocationCounty( arg0.getLocationCounty() );
        arg1.setLocationArea( arg0.getLocationArea() );
        arg1.setTowerCraneWeight( arg0.getTowerCraneWeight() );
        arg1.setWeightTorque( arg0.getWeightTorque() );
        arg1.setWeightLength( arg0.getWeightLength() );
        arg1.setWorkRange( arg0.getWorkRange() );
        arg1.setWorkRangeWeight( arg0.getWorkRangeWeight() );
        arg1.setImproveHeight( arg0.getImproveHeight() );
        arg1.setLiftingHeight( arg0.getLiftingHeight() );
        arg1.setStructureNumber( arg0.getStructureNumber() );
        arg1.setMaxHeight( arg0.getMaxHeight() );
        arg1.setStandardSectionSpecifications( arg0.getStandardSectionSpecifications() );
        arg1.setStrengthenSection( arg0.getStrengthenSection() );
        arg1.setStandardSection( arg0.getStandardSection() );
        arg1.setElevatorPower( arg0.getElevatorPower() );
        arg1.setImproveSpeed( arg0.getImproveSpeed() );
        arg1.setSafetyModel( arg0.getSafetyModel() );
        arg1.setConstructionElevatorSize( arg0.getConstructionElevatorSize() );
        arg1.setMaxSpan( arg0.getMaxSpan() );
        arg1.setIssuer( arg0.getIssuer() );
        arg1.setCertificateNumber( arg0.getCertificateNumber() );
        arg1.setIssueDate( arg0.getIssueDate() );
        arg1.setUseRegistrationCertificate( arg0.getUseRegistrationCertificate() );
        arg1.setProjectName( arg0.getProjectName() );
        arg1.setProjectAddress( arg0.getProjectAddress() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setUsageUnit( arg0.getUsageUnit() );
        arg1.setMaintenanceUnit( arg0.getMaintenanceUnit() );
        arg1.setInstallationUnit( arg0.getInstallationUnit() );
        arg1.setInspectionUnit( arg0.getInspectionUnit() );
        arg1.setProjectManager( arg0.getProjectManager() );
        arg1.setInstallationDate( arg0.getInstallationDate() );
        arg1.setInspectionDate( arg0.getInspectionDate() );
        arg1.setEnterDate( arg0.getEnterDate() );
        arg1.setExitDate( arg0.getExitDate() );
        arg1.setLocation( arg0.getLocation() );
        arg1.setSopId( arg0.getSopId() );
        arg1.setRemarks( arg0.getRemarks() );
        arg1.setDelFlag( arg0.getDelFlag() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setProjectCraneNum( arg0.getProjectCraneNum() );
        arg1.setCraneType( arg0.getCraneType() );

        return arg1;
    }
}

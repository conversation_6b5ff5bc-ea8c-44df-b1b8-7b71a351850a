{"doc": " 人员资格证书\n\n <AUTHOR>\n @date 2025-05-10\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.person.domain.bo.SysQualificationBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询人员资格证书列表\n"}, {"name": "export", "paramTypes": ["org.dromara.person.domain.bo.SysQualificationBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出人员资格证书列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取人员资格证书详细信息\n\n @param qualificationId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.person.domain.bo.SysQualificationBo"], "doc": " 新增人员资格证书\n"}, {"name": "edit", "paramTypes": ["org.dromara.person.domain.bo.SysQualificationBo"], "doc": " 修改人员资格证书\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除人员资格证书\n\n @param qualificationIds 主键串\n"}], "constructors": []}
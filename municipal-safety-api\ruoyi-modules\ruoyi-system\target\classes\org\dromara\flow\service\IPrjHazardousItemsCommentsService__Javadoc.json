{"doc": " 质监站隐患清单整改Service接口\n\n <AUTHOR>\n @date 2025-05-28\n", "fields": [], "enumConstants": [], "methods": [{"name": "updateByBo", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsCommentsBo"], "doc": " 修改质监站隐患清单整改\n\n @param bo 质监站隐患清单整改\n @return 是否修改成功\n"}, {"name": "updateById", "paramTypes": ["org.dromara.flow.domain.PrjHazardousItemsComments"], "doc": " 修改清单\n @param comments\n @return\n"}, {"name": "save", "paramTypes": ["org.dromara.flow.domain.PrjHazardousItemsComments"], "doc": " 保存清单\n @param comments\n @return\n"}, {"name": "getDetail", "paramTypes": ["java.lang.String"], "doc": " 获取详情\n @param taskId\n @return\n"}], "constructors": []}
package org.dromara.facility.domain.bo;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.JlLifterReal;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class JlLifterRealBoToJlLifterRealMapperImpl implements JlLifterRealBoToJlLifterRealMapper {

    @Override
    public JlLifterReal convert(JlLifterRealBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        JlLifterReal jlLifterReal = new JlLifterReal();

        jlLifterReal.setId( arg0.getId() );
        jlLifterReal.setDevNo( arg0.getDevNo() );
        jlLifterReal.setTcNo( arg0.getTcNo() );
        jlLifterReal.setLifterRight( arg0.getLifterRight() );
        jlLifterReal.setTcRightStr( arg0.getTcRightStr() );
        jlLifterReal.setDate( arg0.getDate() );
        jlLifterReal.setWeight( arg0.getWeight() );
        jlLifterReal.setWeightPercent( arg0.getWeightPercent() );
        jlLifterReal.setPerson( arg0.getPerson() );
        jlLifterReal.setHeight( arg0.getHeight() );
        jlLifterReal.setHeightPercent( arg0.getHeightPercent() );
        jlLifterReal.setSpeed( arg0.getSpeed() );
        jlLifterReal.setSpeedDir( arg0.getSpeedDir() );
        jlLifterReal.setSlant1( arg0.getSlant1() );
        jlLifterReal.setSlant1Percent( arg0.getSlant1Percent() );
        jlLifterReal.setSlant2( arg0.getSlant2() );
        jlLifterReal.setSlant2Percent( arg0.getSlant2Percent() );
        jlLifterReal.setDriverAuth( arg0.getDriverAuth() );
        jlLifterReal.setFrontDoor( arg0.getFrontDoor() );
        jlLifterReal.setBackDoor( arg0.getBackDoor() );
        jlLifterReal.setDoorLock( arg0.getDoorLock() );
        jlLifterReal.setSystemStatusWeight( arg0.getSystemStatusWeight() );
        jlLifterReal.setSystemStatusHeight( arg0.getSystemStatusHeight() );
        jlLifterReal.setSystemStatusSpeed( arg0.getSystemStatusSpeed() );
        jlLifterReal.setSystemStatusPerson( arg0.getSystemStatusPerson() );
        jlLifterReal.setSystemStatusSlant( arg0.getSystemStatusSlant() );
        jlLifterReal.setSystemStatusFrontDoor( arg0.getSystemStatusFrontDoor() );
        jlLifterReal.setSystemStatusBackDoor( arg0.getSystemStatusBackDoor() );
        jlLifterReal.setSystemStatusWindSpeed( arg0.getSystemStatusWindSpeed() );
        jlLifterReal.setSystemStatusUpperLimit( arg0.getSystemStatusUpperLimit() );
        jlLifterReal.setSystemStatusFallingProtector( arg0.getSystemStatusFallingProtector() );
        jlLifterReal.setWindSpeed( arg0.getWindSpeed() );
        jlLifterReal.setCurrentFloor( arg0.getCurrentFloor() );
        jlLifterReal.setUncovered( arg0.getUncovered() );
        jlLifterReal.setCreateTime( arg0.getCreateTime() );

        return jlLifterReal;
    }

    @Override
    public JlLifterReal convert(JlLifterRealBo arg0, JlLifterReal arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setTcNo( arg0.getTcNo() );
        arg1.setLifterRight( arg0.getLifterRight() );
        arg1.setTcRightStr( arg0.getTcRightStr() );
        arg1.setDate( arg0.getDate() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setWeightPercent( arg0.getWeightPercent() );
        arg1.setPerson( arg0.getPerson() );
        arg1.setHeight( arg0.getHeight() );
        arg1.setHeightPercent( arg0.getHeightPercent() );
        arg1.setSpeed( arg0.getSpeed() );
        arg1.setSpeedDir( arg0.getSpeedDir() );
        arg1.setSlant1( arg0.getSlant1() );
        arg1.setSlant1Percent( arg0.getSlant1Percent() );
        arg1.setSlant2( arg0.getSlant2() );
        arg1.setSlant2Percent( arg0.getSlant2Percent() );
        arg1.setDriverAuth( arg0.getDriverAuth() );
        arg1.setFrontDoor( arg0.getFrontDoor() );
        arg1.setBackDoor( arg0.getBackDoor() );
        arg1.setDoorLock( arg0.getDoorLock() );
        arg1.setSystemStatusWeight( arg0.getSystemStatusWeight() );
        arg1.setSystemStatusHeight( arg0.getSystemStatusHeight() );
        arg1.setSystemStatusSpeed( arg0.getSystemStatusSpeed() );
        arg1.setSystemStatusPerson( arg0.getSystemStatusPerson() );
        arg1.setSystemStatusSlant( arg0.getSystemStatusSlant() );
        arg1.setSystemStatusFrontDoor( arg0.getSystemStatusFrontDoor() );
        arg1.setSystemStatusBackDoor( arg0.getSystemStatusBackDoor() );
        arg1.setSystemStatusWindSpeed( arg0.getSystemStatusWindSpeed() );
        arg1.setSystemStatusUpperLimit( arg0.getSystemStatusUpperLimit() );
        arg1.setSystemStatusFallingProtector( arg0.getSystemStatusFallingProtector() );
        arg1.setWindSpeed( arg0.getWindSpeed() );
        arg1.setCurrentFloor( arg0.getCurrentFloor() );
        arg1.setUncovered( arg0.getUncovered() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}

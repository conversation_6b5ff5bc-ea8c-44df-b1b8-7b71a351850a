{"doc": " <AUTHOR>\n @date 2025/5/18 10:53\n @Description TODO\n @Version 1.0\n", "fields": [{"name": "accessToken", "doc": " 用户token 萤石云平台获取\n"}, {"name": "refreshLock", "doc": " 刷新锁\n"}, {"name": "projectId", "doc": " 项目id\n"}], "enumConstants": [], "methods": [{"name": "capture", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 截图获取到流\n\n @param deviceSerial\n @return\n"}, {"name": "downloadImg", "paramTypes": ["java.lang.String"], "doc": " 下载图片\n\n @param fileId 文件id\n @return\n"}, {"name": "removeFile", "paramTypes": ["java.lang.String"], "doc": " 删除文件\n\n @param fileId 文件id\n @return\n"}, {"name": "getStream", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 获取rtmp地址\n\n @param deviceSerial 设备序列号\n @param protocol     获取流类型\n @return\n"}, {"name": "getRtmp", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取rtmp流\n\n @param deviceSerial\n @return\n"}, {"name": "getEzopen", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取萤石云流地址\n\n @param deviceSerial\n @return\n"}, {"name": "addClusterJob", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer"], "doc": " 新增集群模式的任务\n\n @param jobName 任务名称\n @return 任务id\n"}, {"name": "updateClusterJob", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 修改定时任务任务的间隔时间\n\n @param jobId\n @param intervalTime\n @return\n"}, {"name": "deleteJob", "paramTypes": ["java.lang.Long"], "doc": " 删除定时任务\n\n @param jobId\n @return\n"}, {"name": "startJob", "paramTypes": ["java.lang.Long"], "doc": " 启动任务\n\n @param jobId\n @return\n"}, {"name": "stopJob", "paramTypes": ["java.lang.Long"], "doc": " 停止任务\n\n @param jobId\n @return\n"}, {"name": "getImageByStreamUrl", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 通过流地址截取图片\n\n @param streamUrl\n @param fileSaveParentPath\n @return\n @throws IOException\n"}, {"name": "onLineDeviceSerials", "paramTypes": [], "doc": " 获取在线的设备序列号列表\n\n @return\n"}, {"name": "onLineChannelNoDeviceSerials", "paramTypes": [], "doc": " 获取在线的设备序列号和通道号\n\n @return\n"}], "constructors": []}
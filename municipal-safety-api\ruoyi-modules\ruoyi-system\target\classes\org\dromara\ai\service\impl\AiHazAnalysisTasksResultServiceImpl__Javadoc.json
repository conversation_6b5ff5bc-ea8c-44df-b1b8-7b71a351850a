{"doc": " 隐患AI分析结果Service业务层处理\n\n <AUTHOR>\n @date 2025-05-15\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询隐患AI分析结果\n\n @param resultId 主键\n @return 隐患AI分析结果\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksResultBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询隐患AI分析结果列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 隐患AI分析结果分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksResultBo"], "doc": " 查询符合条件的隐患AI分析结果列表\n\n @param bo 查询条件\n @return 隐患AI分析结果列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksResultBo"], "doc": " 新增隐患AI分析结果\n\n @param bo 隐患AI分析结果\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksResultBo"], "doc": " 修改隐患AI分析结果\n\n @param bo 隐患AI分析结果\n @return 是否修改成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.ai.domain.AiHazAnalysisTasksResult"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除隐患AI分析结果信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
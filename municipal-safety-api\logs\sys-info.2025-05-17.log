2025-05-17 10:08:24 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-17 10:08:30 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-17 10:08:31 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-17 10:19:25 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-17 10:19:25 [main] INFO  o.dromara.MunicipalSafetyApplication - Starting MunicipalSafetyApplication using Java 17.0.15 with PID 24100 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-17 10:19:25 [main] INFO  o.dromara.MunicipalSafetyApplication - The following 1 profile is active: "dev"
2025-05-17 10:19:29 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-17 10:19:30 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-17 10:19:30 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-17 10:19:30 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7da40bf4
2025-05-17 10:19:30 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-17 10:19:30 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-17 10:19:30 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-17 10:19:33 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-17 10:19:33 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-17 10:19:34 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-17 10:19:34 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-17 10:19:35 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-17 10:19:42 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-17 10:19:43 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-17 10:19:43 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-17 10:19:43 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-17 10:19:43 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-17 10:19:43 [main] INFO  o.dromara.MunicipalSafetyApplication - Started MunicipalSafetyApplication in 18.713 seconds (process running for 19.748)
2025-05-17 10:19:43 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-17 10:19:43 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-17 10:19:43 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-17 11:07:40 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-17 11:07:40 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-17 11:07:40 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-17 11:07:40 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-17 11:07:40 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-17 11:07:40 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-17 11:07:40 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-17 11:07:40 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-05-17 11:07:45 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-05-17 11:07:46 [main] INFO  o.dromara.MunicipalSafetyApplication - Starting MunicipalSafetyApplication using Java 17.0.15 with PID 14056 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-05-17 11:07:46 [main] INFO  o.dromara.MunicipalSafetyApplication - The following 1 profile is active: "dev"
2025-05-17 11:07:50 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-05-17 11:07:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-05-17 11:07:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-05-17 11:07:51 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@32f2de5c
2025-05-17 11:07:51 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-05-17 11:07:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-05-17 11:07:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-17 11:07:54 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-05-17 11:07:54 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-05-17 11:07:55 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-05-17 11:07:55 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-05-17 11:07:56 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-05-17 11:08:03 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-05-17 11:08:04 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-05-17 11:08:04 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-05-17 11:08:04 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-05-17 11:08:04 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-05-17 11:08:04 [main] INFO  o.dromara.MunicipalSafetyApplication - Started MunicipalSafetyApplication in 19.648 seconds (process running for 20.58)
2025-05-17 11:08:04 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-05-17 11:08:04 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-05-17 11:08:05 [RMI TCP Connection(5)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-17 11:09:51 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/prj_projects_status],无参数
2025-05-17 11:09:51 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/prj_projects_status],耗时:[194]毫秒
2025-05-17 11:11:10 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sms/code],参数类型[param],参数:[{"phonenumber":["18394145411"]}]
2025-05-17 11:11:10 [XNIO-1 task-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/resource/sms/code:18394145411'
2025-05-17 11:11:11 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sms/code],耗时:[928]毫秒
2025-05-17 11:11:24 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/app_login],参数类型[json],参数:[{"phonenumber":"18394145411","smsCode":"4798","grantType":"sms","clientId":"428a8310cd442757ae699df5d894f051"}]
2025-05-17 11:11:24 [schedule-pool-1] INFO  o.d.s.s.i.SysLogininforServiceImpl - [124.152.7.131]中国|甘肃省|兰州市|联通[兰州市建设工程安全质量监督站][Success][登录成功]
2025-05-17 11:11:24 [XNIO-1 task-3] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1922459015750365186, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJmSllPN1duTmVpeVVLZXJwOWRDV2FrYUxMc3dYNjVFUiIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.gF1ybHCn9hZ5eSyWULtSwiM7qsN18eoT4GhAStFLMts
2025-05-17 11:11:24 [XNIO-1 task-3] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:sys_user:1922459015750365186, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTIyNDU5MDE1NzUwMzY1MTg2Iiwicm5TdHIiOiJoQWl4Z21PbjNlbGVKajZsVkVUa0Z5aTdhd1czb3E2YSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkyMjQ1OTAxNTc1MDM2NTE4NiwidXNlck5hbWUiOiLlhbDlt57luILlu7rorr7lt6XnqIvlronlhajotKjph4_nm5HnnaPnq5kiLCJkZXB0SWQiOjE5MjAzODM3OTIxNjczNzA3NTQsImRlcHROYW1lIjoi5YWw5bee5biC5bu66K6-5bel56iL5a6J5YWo6LSo6YeP55uR552j56uZIiwiZGVwdENhdGVnb3J5IjoiIn0.0MJgvIBpaT35D0zFcuTjmRO4jWRk0lShbfmqXrIF3h4
2025-05-17 11:11:24 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/app_login],耗时:[382]毫秒
2025-05-17 11:11:25 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/prj_projects_status],无参数
2025-05-17 11:11:25 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/prj_projects_status],耗时:[7]毫秒
2025-05-17 11:11:25 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/listAi],参数类型[param],参数:[{"pageSize":["5"],"projectName":[""],"pageNum":["1"]}]
2025-05-17 11:11:25 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/listAi],耗时:[190]毫秒
2025-05-17 11:11:26 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /app/ai/ai_haz_analysis_tasks/app/list],参数类型[param],参数:[{"itemId":[""],"itemName":[""],"pageSize":["5"],"projectName":[""],"type":["project"],"pageNum":["1"]}]
2025-05-17 11:11:26 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/ai_haz_analysis_tasks_status],无参数
2025-05-17 11:11:26 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/ai_haz_analysis_tasks_status],耗时:[2]毫秒
2025-05-17 11:11:27 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /app/ai/ai_haz_analysis_tasks/app/list],耗时:[356]毫秒
2025-05-17 11:11:29 [schedule-pool-2] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1922459015750365186] message:欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-17 11:11:29 [redisson-3-2] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1922459015750365186] message=欢迎登录房屋市政工程质量安全隐患排查治理监管系统
2025-05-17 11:11:30 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_projects/listAiAll],无参数
2025-05-17 11:11:30 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_projects/listAiAll],耗时:[56]毫秒
2025-05-17 11:11:33 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /projects/prj_hazardous_items/app/listAll],参数类型[param],参数:[{"projectId":["1921481309755801601"]}]
2025-05-17 11:11:33 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /projects/prj_hazardous_items/app/listAll],耗时:[91]毫秒
2025-05-17 11:11:42 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /resource/oss/upload],无参数
2025-05-17 11:11:43 [XNIO-1 task-2] INFO  o.d.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-05-17 11:11:44 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /resource/oss/upload],耗时:[1400]毫秒
2025-05-17 11:12:01 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],参数类型[json],参数:[{"projectId":"1921481309755801601","relatedHazardousItemId":"1922184486981308418","photoDocumentId":"1923577144375345154","photoDocumentUrl":"https://ms-static.gsjtsz.cn/2025/05/17/d81ac5ce561945fab60976459354ca4a.jpg","gpsLocation":"","locationDescription":"123456"}]
2025-05-17 11:12:01 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 提交隐患图片到外部AI分析服务，任务ID: 1923577216785809410, 图片URL: https://ms-static.gsjtsz.cn/2025/05/17/d81ac5ce561945fab60976459354ca4a.jpg
2025-05-17 11:12:01 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 发送请求到外部AI服务: http://*************:39528/api/v1/model/dangerousAndMajorEngineeringTask
2025-05-17 11:12:01 [XNIO-1 task-3] INFO  o.d.a.s.i.AiHazAnalysisTasksServiceImpl - 外部AI服务响应: {"code":200,"msg":"请求成功"}
2025-05-17 11:12:01 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/ai/ai_haz_analysis_tasks/upload],耗时:[190]毫秒
2025-05-17 11:12:01 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /app/ai/ai_haz_analysis_tasks/app/list],参数类型[param],参数:[{"itemId":[""],"itemName":[""],"pageSize":["5"],"projectName":[""],"type":["project"],"pageNum":["1"]}]
2025-05-17 11:12:01 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/ai_haz_analysis_tasks_status],无参数
2025-05-17 11:12:01 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/ai_haz_analysis_tasks_status],耗时:[2]毫秒
2025-05-17 11:12:02 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /app/ai/ai_haz_analysis_tasks/app/list],耗时:[279]毫秒
2025-05-17 11:13:26 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-05-17 11:13:26 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-05-17 11:13:26 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-05-17 11:13:26 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-05-17 11:13:26 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-05-17 11:13:26 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-05-17 11:13:26 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-05-17 11:13:26 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye

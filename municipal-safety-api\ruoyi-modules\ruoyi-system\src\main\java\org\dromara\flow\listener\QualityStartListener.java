package org.dromara.flow.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.annotation.Resource;
import org.dromara.ai.domain.AiHazAnalysisTasks;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksVo;
import org.dromara.ai.mapper.AiHazAnalysisTasksMapper;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.mapper.PrjProjectsMapper;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.mapper.SysUserMapper;
import org.dromara.warm.flow.core.entity.Node;
import org.dromara.warm.flow.core.listener.Listener;
import org.dromara.warm.flow.core.listener.ListenerVariable;
import org.dromara.workflow.common.constant.FlowConstant;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Zu Da
 * @date 2025/5/30 10:31
 * @Description 质监站隐患流程 质监站
 * @Version 1.0
 */
@Component
public class QualityStartListener implements Listener {

    @Resource
    private AiHazAnalysisTasksMapper aiHazAnalysisTasksMapper;

    @Override
    public void notify(ListenerVariable listenerVariable) {
        List<Node> nextNodes = listenerVariable.getNextNodes();

        Node node = nextNodes.get(0);

        //是否是结束节点
        if (node.getNodeType().equals(2)) {

            Map<String, Object> variable = listenerVariable.getVariable();

            String serviceId = String.valueOf(variable.get(FlowConstant.BUSINESS_ID));
            LambdaUpdateWrapper<AiHazAnalysisTasks> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(AiHazAnalysisTasks::getRecheckStatus, "FINISH_RECHECK")
                .eq(AiHazAnalysisTasks::getTaskId, serviceId);

            aiHazAnalysisTasksMapper.update(updateWrapper);
        }
    }
}

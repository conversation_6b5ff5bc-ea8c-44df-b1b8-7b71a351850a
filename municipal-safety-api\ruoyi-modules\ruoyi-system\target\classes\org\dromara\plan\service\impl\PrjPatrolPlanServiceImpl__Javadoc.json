{"doc": " 巡检计划Service业务层处理\n\n <AUTHOR>\n @date 2025-06-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询巡检计划\n\n @param planId 主键\n @return 巡检计划\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.plan.domain.bo.PrjPatrolPlanBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询巡检计划列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 巡检计划分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.plan.domain.bo.PrjPatrolPlanBo"], "doc": " 查询符合条件的巡检计划列表\n\n @param bo 查询条件\n @return 巡检计划列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.plan.domain.bo.PrjPatrolPlanBo"], "doc": " 新增巡检计划\n\n @param bo 巡检计划\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.plan.domain.bo.PrjPatrolPlanBo"], "doc": " 修改巡检计划\n\n @param bo 巡检计划\n @return 是否修改成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.plan.domain.PrjPatrolPlan"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除巡检计划信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
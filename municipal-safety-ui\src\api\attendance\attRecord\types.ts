export interface AttRecordVO {
        /**
         * id
         */
            id: string | number;

        /**
         * 规则id
         */
            ruleId: string | number;

        /**
         * 身份Id
         */
            idNumber: string | number;

        /**
         * 实时人脸
         */
            realTimeFace: string;

        /**
         * 设备号
         */
            sn: string;

        /**
         * 自定义内容
         */
            content: string;

        /**
         * 考勤时间
         */
            attTime: string;

        /**
         * 考勤日期
         */
            attDate: string;

        /**
         * 是否删除。0-否，1-是
         */
            deleted: number;

}

export interface AttRecordForm extends BaseEntity {
        /**
         * id
         */
            id?: string | number;

        /**
         * 规则id
         */
            ruleId?: string | number;

        /**
         * 身份Id
         */
            idNumber?: string | number;

        /**
         * 实时人脸
         */
            realTimeFace?: string;

        /**
         * 设备号
         */
            sn?: string;

        /**
         * 自定义内容
         */
            content?: string;

        /**
         * 考勤时间
         */
            attTime?: string;

        /**
         * 考勤日期
         */
            attDate?: string;

        /**
         * 是否删除。0-否，1-是
         */
            deleted?: number;

}

export interface AttRecordQuery extends PageQuery {

        /**
         * 规则id
         */
            ruleId?: string | number;

        /**
         * 身份Id
         */
            idNumber?: string | number;

        /**
         * 实时人脸
         */
            realTimeFace?: string;

        /**
         * 设备号
         */
            sn?: string;

        /**
         * 自定义内容
         */
            content?: string;

        /**
         * 考勤时间
         */
            attTime?: string;

        /**
         * 考勤日期
         */
            attDate?: string;

        /**
         * 是否删除。0-否，1-是
         */
            deleted?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}




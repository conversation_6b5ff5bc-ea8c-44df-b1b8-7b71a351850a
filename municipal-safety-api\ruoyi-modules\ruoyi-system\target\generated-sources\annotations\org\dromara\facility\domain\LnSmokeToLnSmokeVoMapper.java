package org.dromara.facility.domain;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.bo.LnSmokeBoToLnSmokeMapper;
import org.dromara.facility.domain.vo.LnSmokeVo;
import org.dromara.facility.domain.vo.LnSmokeVoToLnSmokeMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {LnSmokeBoToLnSmokeMapper.class,LnSmokeVoToLnSmokeMapper.class},
    imports = {}
)
public interface LnSmokeToLnSmokeVoMapper extends BaseMapper<LnSmoke, LnSmokeVo> {
}

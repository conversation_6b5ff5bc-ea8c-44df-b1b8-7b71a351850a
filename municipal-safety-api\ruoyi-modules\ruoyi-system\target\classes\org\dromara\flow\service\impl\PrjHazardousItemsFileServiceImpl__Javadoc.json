{"doc": " 通用流程附件Service业务层处理\n\n <AUTHOR>\n @date 2025-06-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询通用流程附件\n\n @param itemFileId 主键\n @return 通用流程附件\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsFileBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询通用流程附件列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 通用流程附件分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsFileBo"], "doc": " 查询符合条件的通用流程附件列表\n\n @param bo 查询条件\n @return 通用流程附件列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsFileBo"], "doc": " 新增通用流程附件\n\n @param bo 通用流程附件\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsFileBo"], "doc": " 修改通用流程附件\n\n @param bo 通用流程附件\n @return 是否修改成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.flow.domain.PrjHazardousItemsFile"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除通用流程附件信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}, {"name": "updateByBos", "paramTypes": ["java.util.List"], "doc": " 通过BOS批量更新项目危险物品文件信息\n\n @param bos 待更新的项目危险物品文件信息列表\n @return\n"}], "constructors": []}
package org.dromara.system.controller.system;

import java.util.List;

import cn.dev33.satoken.annotation.SaIgnore;
import jakarta.annotation.security.PermitAll;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.checkerframework.checker.nullness.qual.AssertNonNullIfNonNull;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.system.domain.vo.DivisionVo;
import org.dromara.system.domain.bo.DivisionBo;
import org.dromara.system.service.IDivisionService;

/**
 * 行政区划
 * @date 2025-04-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/division")
public class DivisionController extends BaseController {

    private final IDivisionService divisionService;

    /**
     * 查询行政区划列表
     */
    @GetMapping("/list")
    @SaIgnore
    public R<List<DivisionVo>> list(DivisionBo bo) {
        List<DivisionVo> list = divisionService.queryList(bo);
        return R.ok(list);
    }


    /**
     * 导出行政区划列表
     */
    @SaCheckPermission("system:division:export")
    @Log(title = "行政区划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(DivisionBo bo, HttpServletResponse response) {
        List<DivisionVo> list = divisionService.queryList(bo);
        ExcelUtil.exportExcel(list, "行政区划", DivisionVo.class, response);
    }

    /**
     * 获取行政区划详细信息
     *
     * @param divisionId 主键
     */
    @SaCheckPermission("system:division:query")
    @GetMapping("/{divisionId}")
    public R<DivisionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long divisionId) {
        return R.ok(divisionService.queryById(divisionId));
    }

    /**
     * 新增行政区划
     */
    @SaCheckPermission("system:division:add")
    @Log(title = "行政区划", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DivisionBo bo) {
        return toAjax(divisionService.insertByBo(bo));
    }

    /**
     * 修改行政区划
     */
    @SaCheckPermission("system:division:edit")
    @Log(title = "行政区划", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DivisionBo bo) {
        return toAjax(divisionService.updateByBo(bo));
    }

    /**
     * 删除行政区划
     *
     * @param divisionIds 主键串
     */
    @SaCheckPermission("system:division:remove")
    @Log(title = "行政区划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{divisionIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] divisionIds) {
        return toAjax(divisionService.deleteWithValidByIds(List.of(divisionIds), true));
    }


    /**
     *  获取省列表
     */

    @GetMapping("/getProvinceList")
    @SaIgnore
    public R<List<DivisionVo>> getProvinceList() {
        DivisionBo bo = new DivisionBo();
        bo.setLevel("1");
        bo.setParentCode("100000");
        List<DivisionVo> divisionVos = divisionService.queryList(bo);
        return R.ok(divisionVos);
    }

    /**
     *  获取市列表
     */
    @SaIgnore
    @GetMapping("/getCityList/{parentCode}")
    public R<List<DivisionVo>> getCityList(@PathVariable String parentCode) {
        DivisionBo bo = new DivisionBo();
        bo.setParentCode(parentCode);
        List<DivisionVo> list = divisionService.queryList(bo);
        return R.ok(list);
    }

    /**
     *  获取区列表
     */
    @SaIgnore
    @GetMapping("/getAreaList/{parentCode}")
    public R<List<DivisionVo>> getAreaList(@PathVariable String parentCode) {
        DivisionBo bo = new DivisionBo();
        bo.setParentCode(parentCode);
        List<DivisionVo> list = divisionService.queryList(bo);
        return R.ok(list);
    }
}

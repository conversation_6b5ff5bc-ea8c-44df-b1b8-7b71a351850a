package org.dromara.facility.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.facility.domain.LnNut;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 绿能螺母视图对象 ln_nut
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LnNut.class)
public class LnNutVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 参数
     */
    @ExcelProperty(value = "参数")
    private String para;

    /**
     * 值类型
     */
    @ExcelProperty(value = "值类型")
    private String vt;

    /**
     * 值状态
     */
    @ExcelProperty(value = "值状态")
    private String qds;

    /**
     * 数据时间
     */
    @ExcelProperty(value = "数据时间")
    private Date time;

    /**
     * 数值
     */
    @ExcelProperty(value = "数值")
    private String value;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String devNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}

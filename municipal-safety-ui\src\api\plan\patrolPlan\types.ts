export interface PatrolPlanVO {
  /**
   * 主键
   */
  planId: string | number;

  /**
   * 计划开始时间
   */
  beginTime: string;

  /**
   * 计划结束时间
   */
  endTime: string;

  /**
   * 监督机构id
   */
  deptIds: string | number;

  /**
   * 检查项目id
   */
  projectIds: string | number;

  /**
   * 专家ids
   */
  expertIds: string | number;

  /**
   * 备注
   */
  remarks: string;

}

export interface PatrolPlanForm extends BaseEntity {
  /**
   * 主键
   */
  planId?: string | number;

  /**
   * 计划结束时间
   */
  planName?: string;

  /**
   * 计划开始时间
   */
  beginTime?: string;

  /**
   * 计划结束时间
   */
  endTime?: string;

  /**
   * 监督机构id
   */
  deptIds?: string;

  /**
   * 检查项目id
   */
  projectIds?: string;

  /**
   * 专家ids
   */
  expertIds?: string;

  /**
   * 备注
   */
  remarks?: string;

}

export interface PatrolPlanQuery extends PageQuery {

  /**
   * 计划开始时间
   */
  beginTime?: string;

  /**
   * 计划结束时间
   */
  endTime?: string;

  /**
   * 监督机构id
   */
  deptIds?: string | number;

  /**
   * 检查项目id
   */
  projectIds?: string | number;

  /**
   * 专家ids
   */
  expertIds?: string | number;

  /**
   * 备注
   */
  remarks?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}




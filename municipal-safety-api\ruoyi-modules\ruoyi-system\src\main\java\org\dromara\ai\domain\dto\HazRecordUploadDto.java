package org.dromara.ai.domain.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 隐患记录上传数据传输对象
 *
 * <AUTHOR>
 */
@Data
public class HazRecordUploadDto {

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;


    @NotNull(message = "隐患ID不能为空")
    private Long itemId;

    /**
     * 图片ID
     */
    @NotNull(message = "图片ID不能为空")
    private Long photoDocumentId;

    /**
     * 图片URL
     */
    @NotBlank(message = "图片URL不能为空")
    private String photoDocumentUrl;


    /**
     * GPS位置
     */
    private String gpsLocation;

    /**
     * 位置描述
     */
    private String locationDescription;
}

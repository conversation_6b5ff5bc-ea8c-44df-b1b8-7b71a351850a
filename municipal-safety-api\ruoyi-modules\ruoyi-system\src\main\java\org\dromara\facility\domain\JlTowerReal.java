package org.dromara.facility.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 塔机实时数据对象 jl_tower_real
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("jl_tower_real")
public class JlTowerReal {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 塔机编号
     */
    private String tcNo;

    /**
     * 时间
     */
    private Date addTime;

    /**
     * 厂家及设备类型
     */
    private String lockMachineStatus;

    /**
     * 高度
     */
    private Long height;

    /**
     * 幅度
     */
    private Long realRange;

    /**
     * 回转
     */
    private Long rotary;

    /**
     * 起始重量
     */
    private Long startWeight;

    /**
     * 风速数据
     */
    private Long windSpeed;

    /**
     * 倾角数据
     */
    private Long slant;

    /**
     * 重量百分比
     */
    private Long weightPct;

    /**
     * 力矩百分比
     */
    private Long mofPct;

    /**
     * 风速百分比
     */
    private Long windSpeedPct;

    /**
     * 倾斜百分比
     */
    private Long slantPct;

    /**
     * 报警原因
     */
    private String alarmCause;

    /**
     * 报警原因中文
     */
    private String alarmCauseZh;

    /**
     * 制动状态上
     */
    private Long brakingStatusUp;

    /**
     * 制动状态下
     */
    private Long brakingStatusDown;

    /**
     * 制动状态前
     */
    private Long brakingStatusFront;

    /**
     * 制动状态后
     */
    private Long brakingStatusBack;

    /**
     * 制动状态左
     */
    private Long brakingStatusLeft;

    /**
     * 制动状态右
     */
    private Long brakingStatusRight;

    /**
     * 工作状态 1工作 2空闲
     */
    private Long workingStatus;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建时间
     */
    private Date createTime;
}

package org.dromara.person.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.person.domain.bo.SysPersonBo;
import org.dromara.person.domain.vo.SysPersonVo;

import java.util.Collection;
import java.util.List;

/**
 * 人员基本信息Service接口
 *
 * <AUTHOR> zu da
 * @date 2025-05-09
 */
public interface ISysPersonService {

    /**
     * 查询人员基本信息
     *
     * @param personId 主键
     * @return 人员基本信息
     */
    SysPersonVo queryById(Long personId);

    /**
     * 分页查询人员基本信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 人员基本信息分页列表
     */
    TableDataInfo<SysPersonVo> queryPageList(SysPersonBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的人员基本信息列表
     *
     * @param bo 查询条件
     * @return 人员基本信息列表
     */
    List<SysPersonVo> queryList(SysPersonBo bo);

    /**
     * 新增人员基本信息
     *
     * @param bo 人员基本信息
     * @return 是否新增成功
     */
    Boolean insertByBo(SysPersonBo bo);

    /**
     * 修改人员基本信息
     *
     * @param bo 人员基本信息
     * @return 是否修改成功
     */
    Boolean updateByBo(SysPersonBo bo);

    Boolean moveOutByBo(SysPersonBo sysPersonBo);

    /**
     * 校验并批量删除人员基本信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据身份证号获取 人员id
     * @param idCard
     * @return
     */
    Long getPersonIdByIdCard(String idCard);

    SysPersonVo queryByIdCard(String idCard);
}

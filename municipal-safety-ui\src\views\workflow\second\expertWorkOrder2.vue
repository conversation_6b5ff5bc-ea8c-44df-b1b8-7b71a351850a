<template>
  <div class="p-2" style="height: calc(100vh - 155px);">
    <el-card shadow="never">
      <div style="display: flex; justify-content: space-between">
        <div>
          <el-button v-if="submitButtonShow" :loading="buttonLoading" type="info"
            @click="submitForm('draft')">暂存</el-button>
          <el-button v-if="submitButtonShow" :loading="buttonLoading" type="primary" @click="submitForm('submit')">提
            交</el-button>
          <el-button v-if="approvalButtonShow" :loading="buttonLoading" type="primary"
            @click="approvalVerifyOpen">审批</el-button>
          <el-button v-if="form && form.id" type="primary" @click="handleApprovalRecord">流程进度</el-button>
        </div>
        <div>
          <el-button style="float: right" @click="goBack()">返回</el-button>
        </div>
      </div>
    </el-card>
    <!-- 隐患清单 -->
    <el-row :gutter="10" style="width: 100%;height: 100%;padding: 10px;">
      <el-col :span="5" style="display: flex;flex-direction: column;align-items: flex-start;">
        <div style="margin-bottom: 10px;">
          <p style="text-align: start;color: #409EFF;margin: 0 0 16px;">分析前</p>
          <HeaderPrewiew :src="aiDetailData?.photoDocumentUrl" width="17vw" height="16vw"
            :preview-src-list="[aiDetailData?.photoDocumentUrl]">
          </HeaderPrewiew>
        </div>
        <div>
          <p style="text-align: start;color: #67C23A;margin: 10px 0 16px;">分析后</p>
          <HeaderPrewiew :src="aiDetailData?.aiPhotoDocumentUrl" width="17vw" height="17vw"
            :preview-src-list="[aiDetailData?.aiPhotoDocumentUrl]">
          </HeaderPrewiew>
        </div>
      </el-col>
      <el-col ref="ref1" :span="11" style="display: flex;flex-direction: column;height: 100%;">
        <div style="width: 100%; height: calc(100vh - 220px);overflow-y: auto;">
          <el-card style="margin-bottom: 10px;" v-for="(item, index) in aiDetailData?.violations" :key="index">
            <template #header>
              <div class="card-header">
                <div style="display: flex;align-items: center;">
                  <span style="display: block;padding-bottom: 3px;margin-left: 8px;">问题 {{ index + 1 }}</span>
                </div>
                <div style="display: flex;align-items: center;">
                  <span style="color: #409EFF;">危险级别：</span>
                  <dict-tag :options="hidden_danger_type" :value="item.level" />
                </div>
              </div>
            </template>
            <div style="display: flex;">
              <span style="color: #666;display: block;width: 80px;">隐患描述：</span>
              <span style="display: block;flex: 1;">{{ item.violation }}</span>
            </div>
            <div style="display: flex;margin: 15px 0;">
              <span style="color: #666;display: block;width: 80px;">违反条款：</span>
              <span style="display: block;flex: 1;">{{ item.regulation }}</span>
            </div>
            <div style="display: flex;">
              <span style="color: #666;display: block;width: 80px;">整改意见：</span>
              <span style="display: block;flex: 1;">{{ item.measure
              }}</span>
            </div>
            <template #footer>
              <div>
                <el-descriptions title="" :column="2" border>
                  <template v-for="(item1, index) in item.expertAdvice" :key="index">
                    <el-descriptions-item label="专家" label-width="100px" width="120px">{{ item1.name
                    }}</el-descriptions-item>
                    <el-descriptions-item label="专家建议" label-width="100px">{{ item1.detail }}</el-descriptions-item>
                  </template>
                </el-descriptions>
              </div>
            </template>
          </el-card>
        </div>
      </el-col>
      <el-col :span="8" style="padding-left: 15px;height: 100%;">
        <div style="width: 100%;height: calc(100vh - 220px);overflow-y: auto;">
          <!-- <label>补充说明</label>
          <div style="margin-top: 20px;margin-bottom: 20px;">
            <el-input v-model="bcsmText" :rows="3" type="textarea" :disabled="true" class="bcsmInput" />
          </div> -->
          <div>
            <label>厅/局意见</label>
            <div style="margin-top: 25px;">
              <div>
                <div style="display: flex;margin-bottom: 15px;">
                  <FileUpload v-if="isPreview != 'view'" ref="fileUploadRef" @update:modelValue="handleHallBureauFile"
                    :isShowFileList="false" :isShowTip="false" :fileSize="20">
                    <el-button size="small" type="primary">上传</el-button>
                  </FileUpload>
                  <el-button v-if="isPreview != 'view'" type="primary" size="small"
                    style="margin-left: 10px;margin-right: 15px;" @click="handleSelectUser">选择用户</el-button>
                  <el-radio-group v-model="taskVariables.needModified" class="expertRadio"
                    :disabled="isPreview == 'view' ? true : false">
                    <el-radio :value="1">需要整改</el-radio>
                    <el-radio :value="0">无需整改</el-radio>
                  </el-radio-group>
                </div>
                <div style="margin-bottom: 17px;">
                  <template v-for="item in isPreview == 'view' ? taskVariables.nickNameArr : userInfo"
                    :key="item.expertId">
                    <el-tag type="primary" style="margin-right: 10px;" :closable="isPreview == 'view' ? false : true"
                      @close="handleTagClose(item)">{{
                        item.nickName
                      }}</el-tag>
                  </template>
                </div>
                <el-table :data="formHallBureauData.files" style="width: 100%">
                  <el-table-column type="index" label="序号" width="55px" align="center" />
                  <el-table-column v-if="isPreview == 'view'" prop="name" label="名称" width="180px" align="center" />
                  <el-table-column v-else prop="name" label="名称" width="180px" align="center">
                    <template #default="scope">
                      <el-input v-model="scope.row.name" style="width: 180px;" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="url" label="文件" align="center">
                    <template #default="scope">
                      <el-button type="primary" size="small" @click="handlePreviewChange(scope.row.url)">预览</el-button>
                    </template>
                  </el-table-column>
                  <el-table-column v-if="isPreview != 'view'" label="操作" align="center">
                    <template #default="scope">
                      <el-link type="primary" @click="handleDeleteChange(scope.$index)">删除</el-link>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div style="margin-top: 20px;">
                <span style="font-size: 15px;color: #333;margin-right: 15px;">意见说明</span>
                <el-input :class="isPreview == 'view' ? 'bcsmInput' : ''" v-model="formHallBureauData.remark" :rows="5"
                  type="textarea" style="margin-top: 15px;" :disabled="isPreview == 'view' ? true : false" />
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 提交组件 -->
    <submitVerify ref="submitVerifyRef" :task-variables="taskVariables" :isReadyFile="false" :isSelectBtnDisabled="true"
      @submit-callback="submitCallback" @beforeSubmit="beforeSubmit" />
    <!-- 审批记录 -->
    <approvalRecord ref="approvalRecordRef" />
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" :before-close="handleClose" width="500">
      <el-select v-model="flowCode" placeholder="Select" style="width: 240px">
        <el-option v-for="item in flowCodeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="submitFlow()"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>
    <UserSelect ref="multiInstanceUserRef" :quality_dept="quality_dept" :multiple="true"
      @confirm-call-back="addMultiInstanceUser">
    </UserSelect>
  </div>
</template>

<script setup name="Leave" lang="ts">
import { addLeave, updateLeave } from '@/api/workflow/leave';
import { LeaveForm, LeaveQuery, LeaveVO } from '@/api/workflow/leave/types';
import { startWorkFlow, getTaskVariables } from '@/api/workflow/task';
import { getPrj_hazardous_items_ai_detail } from '@/api/customFlow/api'
import { delOss, listByIds } from '@/api/system/oss/index'
import SubmitVerify from '@/components/Process/submitVerify.vue';
import ApprovalRecord from '@/components/Process/approvalRecord.vue';
import { AxiosResponse } from 'axios';
import { StartProcessBo } from '@/api/workflow/workflowCommon/types';
import HeaderPrewiew from '@/components/ImagePreview/index.vue';
import FileUpload from '@/components/FileUpload/index.vue'
import UserSelect from './UserSelect/index.vue'
import { addHazardousItemsSpecialWarningThirdEcho, addHazardousThirdSave, addHazardousThirdEcho } from '@/api/second/index'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { hidden_danger_type } = toRefs<any>(proxy?.useDict('hidden_danger_type'));

const buttonLoading = ref(false);
const loading = ref(true);
const leaveTime = ref<Array<string>>([]);
// 存放隐患清单详情的数据
const aiDetailData = ref();
// 提交工单的表单参数
const formHallBureauData = reactive({
  remark: '', // 意见说明
  taskId: '', // 任务id
  files: []
})
const fileUploadRef = ref();
// 判断是预览还是编辑状态
const isPreview = ref('')
// 单选框选中的值
const isSelected = ref([]);;
const personComments = ref([]);
//路由参数
const routeParams = ref<Record<string, any>>({});
const multiInstanceUserRef = ref<InstanceType<typeof UserSelect>>();
// 给选择用户传递部门参数查询用户信息
const quality_dept = ref();
const userInfo = ref([]);
const flowCodeOptions = [
  {
    value: 'leave1',
    label: '请假申请-普通'
  },
  {
    value: 'leave2',
    label: '请假申请-排他网关'
  },
  {
    value: 'leave3',
    label: '请假申请-并行网关'
  },
  {
    value: 'leave4',
    label: '请假申请-会签'
  },
  {
    value: 'leave5',
    label: '请假申请-并行会签网关'
  },
  {
    value: 'leave6',
    label: '请假申请-排他并行会签'
  }
];

const flowCode = ref<string>('');

const dialogVisible = reactive<DialogOption>({
  visible: false,
  title: '流程定义'
});
//提交组件
const submitVerifyRef = ref<InstanceType<typeof SubmitVerify>>();
//审批记录组件
const approvalRecordRef = ref<InstanceType<typeof ApprovalRecord>>();

const leaveFormRef = ref<ElFormInstance>();

const submitFormData = ref<StartProcessBo>({
  businessId: '',
  flowCode: '',
  variables: {}
});
const taskVariables = ref<Record<string, any>>({});

const initFormData: LeaveForm = {
  id: undefined,
  leaveType: undefined,
  startDate: undefined,
  endDate: undefined,
  leaveDays: undefined,
  remark: undefined,
  status: 'waiting'
};
const data = reactive<PageData<LeaveForm, LeaveQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    startLeaveDays: undefined,
    endLeaveDays: undefined
  },
  rules: {
    id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
    leaveType: [{ required: true, message: '请假类型不能为空', trigger: 'blur' }],
    leaveTime: [{ required: true, message: '请假时间不能为空', trigger: 'blur' }],
    leaveDays: [{ required: true, message: '请假天数不能为空', trigger: 'blur' }]
  }
});

const handleClose = () => {
  dialogVisible.visible = false;
  flowCode.value = '';
  buttonLoading.value = false;
};
const { form, rules } = toRefs(data);

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  leaveTime.value = [];
  leaveFormRef.value?.resetFields();
};
// 使用ossId查询图片的url地址
const getImageUrl = async (ossId: string | number) => {
  const { data } = await listByIds(ossId);
  return data[0]?.url;
}
// 获取隐患详情数据
const getAiDetail = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(255, 255, 255, 0.8)',
  })
  isSelected.value = [];
  personComments.value = [];
  const res = await getPrj_hazardous_items_ai_detail(taskVariables.value.ai_task_id);
  if (res.code === 200) {
    aiDetailData.value = res.data;
    aiDetailData.value.photoDocumentUrl = await getImageUrl(aiDetailData.value.photoDocumentId);
    aiDetailData.value.aiPhotoDocumentUrl = await getImageUrl(aiDetailData.value.aiPhotoDocumentId);
    aiDetailData.value.violations.forEach(item => {
      isSelected.value.push(item.commentStatus == null ? 0 : item.commentStatus);
      personComments.value.push(item.personComments);
      if (routeParams.value.type == 'view') {
        item.disable = true;
      } else {
        item.disable = false;
      }
    });
  }
  loading.close();
}
// 获取第二条的第三步的中间专家回显数据
const getThirdEcho = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(255, 255, 255, 0.8)',
  })
  const res = await addHazardousItemsSpecialWarningThirdEcho(routeParams.value.id)
  if (res.code === 200) {
    for (let i = 0; i < aiDetailData.value.violations.length; i++) {
      aiDetailData.value.violations[i].expertAdvice = res.data[aiDetailData.value.violations[i].resultId]
    }
  }
  loading.close();
}
// 获取第二条的第三步右侧的厅局意见回显数据
const getThirdEcho2 = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(255, 255, 255, 0.8)',
  })
  const res = await addHazardousThirdEcho(routeParams.value.id)
  if (res.code === 200) {
    // res.data.checkUsers.forEach((item) => {
    //   userInfo.value.push({
    //     userId: item.userId,
    //     nickName: item.name
    //   })
    // })
    for (let i = 0; i < res.data.files.length; i++) {
      const { data } = await listByIds(res.data.files[i].fileId);
      const dataObj = data[data.length - 1]
      formHallBureauData.files.push({
        name: res.data.files[i].name,   //文件名称
        fileId: res.data.files[i].fileId,  //取ossid
        taskId: res.data.files[i].taskId,   //业务id，取地址栏中的id
        url: dataObj.url    //文件地址
      })
    }
    formHallBureauData.taskId = res.data.files[0].taskId;
    formHallBureauData.remark = res.data.remark;
  }
  loading.close();
}
// 点击预览按钮预览文件点击事件
const handlePreviewChange = (urls: string) => {
  const botaUrl = btoa(urls)
  const url = `${import.meta.env.VITE_APP_VIEW_URL}/onlinePreview?url=${botaUrl}`
  // 文件预览
  window.open(url, '_blank')
}
// 获取任务变量数据
const getTaskVariablesData = async () => {
  const res = await getTaskVariables(form.value.id as string);
  if (res.code === 200) {
    taskVariables.value = res.data;
    quality_dept.value = res.data.quality_dept;
  }
}
// 选择用户按钮点击事件
const handleSelectUser = () => {
  multiInstanceUserRef.value.open();
}
// 选择用户以后的自定义事件
const addMultiInstanceUser = async (data) => {
  if (data && data.length > 0) {
    if (data.length > 1) {
      proxy?.$modal.msgError('只能选择一个用户！');
    } else {
      userInfo.value = data;
      taskVariables.value.quality_person = userInfo.value.map(item => item.userId).join(',');
      taskVariables.value.nickNameArr = userInfo.value.map(item => {
        return { nickName: item.userName }
      });
    }
  } else {
    proxy?.$modal.msgError('请选择用户！');
  }
};
// 用户信息标签关闭事件
const handleTagClose = (tag: any) => {
  const index = userInfo.value.indexOf(tag);
  if (index !== -1) {
    userInfo.value.splice(index, 1);
    taskVariables.value.quality_person = userInfo.value.map(item => item.userId).join(',');
  }
}
/** 暂存提交按钮 */
const submitForm = (status: string) => {
  if (leaveTime.value.length === 0) {
    proxy?.$modal.msgError('请假时间不能为空');
    return;
  }
  try {
    leaveFormRef.value?.validate(async (valid: boolean) => {
      form.value.startDate = leaveTime.value[0];
      form.value.endDate = leaveTime.value[1];
      if (valid) {
        buttonLoading.value = true;
        let res: AxiosResponse<LeaveVO>;
        if (form.value.id) {
          res = await updateLeave(form.value);
        } else {
          res = await addLeave(form.value);
        }
        form.value = res.data;
        if (status === 'draft') {
          buttonLoading.value = false;
          proxy?.$modal.msgSuccess('暂存成功');
          proxy.$tab.closePage(proxy.$route);
          proxy.$router.go(-1);
        } else {
          if ((form.value.status === 'draft' && (flowCode.value === '' || flowCode.value === null)) || routeParams.value.type === 'add') {
            flowCode.value = flowCodeOptions[0].value;
            dialogVisible.visible = true;
            return;
          }
          //说明启动过先随意穿个参数
          if (flowCode.value === '' || flowCode.value === null) {
            flowCode.value = 'xx';
          }
          await handleStartWorkFlow(res.data);
        }
      }
    });
  } finally {
    buttonLoading.value = false;
  }
};

const submitFlow = async () => {
  handleStartWorkFlow(form.value);
  dialogVisible.visible = false;
};
//提交申请
const handleStartWorkFlow = async (data: LeaveForm) => {
  try {
    submitFormData.value.flowCode = flowCode.value;
    submitFormData.value.businessId = data.id;
    console.log("流程变量", data);

    //流程变量
    taskVariables.value = {
      leaveDays: data.leaveDays,
      userList: ['1', '3', '4']
    };
    submitFormData.value.variables = taskVariables.value;
    const resp = await startWorkFlow(submitFormData.value);
    if (submitVerifyRef.value) {
      buttonLoading.value = false;
      submitVerifyRef.value.openDialog(resp.data.taskId);
    }
  } finally {
    buttonLoading.value = false;
  }
};
//头部流程进度
const handleApprovalRecord = () => {
  approvalRecordRef.value.init(form.value.id);
};

//提交组件回调
const submitCallback = async () => {
  await proxy.$tab.closePage(proxy.$route);
  proxy.$router.go(-1);
};
// 提交前的通用回调函数
const beforeSubmit = async (fun) => {
  // 提交前的逻辑处理
  fun(true)
};
//头部返回
const goBack = () => {
  proxy.$tab.closePage(proxy.$route);
  proxy.$router.go(-1);
};
//头部审批
const approvalVerifyOpen = async () => {
  if (!taskVariables.value.quality_person && taskVariables.value.needModified == 1) {
    proxy?.$modal.msgError('请选择用户');
    return
  }
  const res = await addHazardousThirdSave(formHallBureauData)
  if (res.code === 200) {
    submitVerifyRef.value.openDialog(routeParams.value.taskId);
  }
  // proxy?.$modal.msgError('该项目清单已经提交审批了，无法再次提交');
};
//校验提交按钮是否显示
const submitButtonShow = computed(() => {
  return (
    routeParams.value.type === 'add' ||
    (routeParams.value.type === 'update' &&
      form.value.status &&
      (form.value.status === 'draft' || form.value.status === 'cancel' || form.value.status === 'back'))
  );
});

//校验审批按钮是否显示
const approvalButtonShow = computed(() => {
  return routeParams.value.type === 'approval' && form.value.status && form.value.status === 'waiting';
});
// 删除
const handleDeleteChange = async (index: number) => {
  let ossId = fileUploadRef.value.fileList[index].ossId;
  await delOss(ossId);
  fileUploadRef.value.fileList.splice(index, 1);
  formHallBureauData.files = formHallBureauData.files.filter(item => item.fileId !== ossId)
}
// 厅局意见报告上传
const handleHallBureauFile = async (ossId: string) => {
  const { data } = await listByIds(ossId);
  const dataObj = data[data.length - 1]
  formHallBureauData.files.push({
    name: dataObj.originalName,   //文件名称
    fileId: dataObj.ossId,  //取ossid
    taskId: routeParams.value.id,   //业务id，取地址栏中的id
    url: dataObj.url    //文件地址
  })
}
onMounted(() => {
  nextTick(async () => {
    routeParams.value = proxy.$route.query;
    reset();
    loading.value = false;
    isPreview.value = routeParams.value.type;
    form.value.id = routeParams.value.id;
    formHallBureauData.taskId = routeParams.value.id;
    await getTaskVariablesData();
    await getAiDetail();
    getThirdEcho()
    // 回显右侧厅局的数据
    if (routeParams.value.type == 'view') {
      getThirdEcho2()
    }
  });
});
</script>
<style lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bcsmInput {
  .el-textarea__inner {
    background-color: #fff !important;
  }
}

.expertRadio {
  flex-wrap: nowrap !important;

  .el-radio {
    margin-right: 15px !important;
    padding-bottom: 10px;
  }
}
</style>

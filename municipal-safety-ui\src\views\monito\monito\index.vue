<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px">
            <el-form-item label="项目名称查询" prop="projectId">
              <el-select v-model="queryParams.projectId" filterable placeholder="请选择项目" style="width: 240px" clearable
                @change="changePro">
                <el-option v-for="item in projectSelectData" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="工程名称查询" prop="itemId">
              <el-select v-model="queryParams.itemId" filterable placeholder="请选择工程" style="width: 240px" clearable>
                <el-option v-for="item in getItemData" :key="item.itemId" :label="item.itemName" :value="item.itemId" />
              </el-select>
            </el-form-item>
            <el-form-item label="设备名称" prop="deviceName">
              <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备类型" prop="deviceType">
              <el-select v-model="queryParams.deviceType" placeholder="请选择设备类型" clearable>
                <el-option v-for="dict in device_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="设备编码" prop="deviceCode">
              <el-input v-model="queryParams.deviceCode" placeholder="请输入设备编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="在线状态" prop="deviceStatus">
              <el-select v-model="queryParams.deviceStatus" placeholder="请选择在线状态" clearable>
                <el-option v-for="dict in device_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否启用" prop="enableSnapshot">
              <el-select v-model="queryParams.enableSnapshot" placeholder="请选择是否启用" clearable>
                <el-option v-for="dict in enable_snapshot" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="抓拍时间间隔(秒)" prop="snapshotTime">
              <el-input v-model="queryParams.snapshotTime" placeholder="请输入抓拍时间间隔(秒)" clearable
                @keyup.enter="handleQuery" />
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['monito:monito:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              v-hasPermi="['monito:monito:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['monito:monito:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['monito:monito:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table class="expertTable1" v-loading="loading" :data="monitoList" @selection-change="handleSelectionChange"
        show-overflow-tooltip>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="项目名称" align="center" prop="projectName" min-width="140px" />
        <el-table-column label="工程名称" align="center" prop="itemName" min-width="100px" />
        <el-table-column label="设备名称" align="center" prop="deviceName" min-width="100px">
          <template #default="scope">
            <el-link v-if="scope.row.deviceType != 3 && scope.row.deviceType != 2" type="primary"
              @click="handleMonitor(scope.row)">{{
                scope.row.deviceName
              }}</el-link>
            <el-link v-else type="primary" @click="handleuav(scope.row)">{{ scope.row.deviceName }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="设备类型" align="center" prop="deviceType" min-width="70px">
          <template #default="scope">
            <dict-tag :options="device_type" :value="scope.row.deviceType" />
          </template>
        </el-table-column>
        <el-table-column label="设备编码" align="center" prop="deviceCode" min-width="100px" />
        <el-table-column label="通道" align="center" prop="channelNo" min-width="70px" />
        <el-table-column label="在线状态" align="center" prop="deviceStatus" min-width="70px">
          <template #default="scope">
            <dict-tag :options="device_status" :value="scope.row.deviceStatus" />
          </template>
        </el-table-column>
        <el-table-column label="是否启用抓拍" align="center" prop="enableSnapshot" min-width="70px">
          <template #default="scope">
            <dict-tag :options="enable_snapshot" :value="scope.row.enableSnapshot" />
          </template>
        </el-table-column>
        <el-table-column label="抓拍时间间隔(秒)" align="center" prop="snapshotTime" min-width="100px" />
        <el-table-column label="备注" align="center" prop="remarks" min-width="100px" />
        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width" min-width="100px">
          <template #default="scope">
            <el-tooltip content="视频监控" placement="top">
              <el-button link :disabled="scope.row.deviceType == 3 || scope.row.deviceType == 2" type="primary"
                @click="handleMonitor(scope.row)" v-hasPermi="['monito:monito:monitor']">
                <span class="iconfont icon-yuntaiyuanqiu"></span>
              </el-button>
            </el-tooltip>
            <el-tooltip :content="scope.row.deviceType == 3 ? '无人机' : '机器狗'" placement="top">
              <el-button :disabled="scope.row.deviceType != 3 && scope.row.deviceType != 2" link type="primary"
                @click="handleuav(scope.row)">
                <span class="iconfont icon-jiqigou1" v-if="scope.row.deviceType == 2"></span>
                <span class="iconfont icon-wurenji" v-else></span>
              </el-button>
            </el-tooltip>

            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['monito:monito:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['monito:monito:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改监控管理对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="750px" append-to-body>
      <el-form ref="monitoFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目名称" prop="projectId" label-width="129px">
          <el-input v-model="form.projectName" placeholder="请选择项目" readonly>
            <template #append>
              <el-button-group>
                <el-button @click="selectProjectChange">选择</el-button>
              </el-button-group>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="工程名称" prop="itemId" label-width="129px">
          <el-input v-model="form.itemName" placeholder="请选择工程" readonly>
            <template #append>
              <el-button-group>
                <el-button @click="selectEngineeringChange">选择</el-button>
              </el-button-group>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName" label-width="129px">
          <el-input v-model="form.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceType" label-width="129px">
          <el-select v-model="form.deviceType" placeholder="请选择设备类型">
            <el-option v-for="dict in device_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备编码" prop="deviceCode" label-width="129px">
          <el-input v-model="form.deviceCode" placeholder="请输入设备编码" />
        </el-form-item>
        <el-form-item label="通道" prop="channelNo" label-width="129px">
          <el-input type="number" v-model.number="form.channelNo" placeholder="请输入纯数字的通道" :min="0" />
        </el-form-item>
        <el-form-item label="是否启用抓拍" prop="enableSnapshot" label-width="129px">
          <el-radio-group v-model="form.enableSnapshot">
            <el-radio v-for="dict in enable_snapshot" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="抓拍时间间隔(秒)" prop="snapshotTime" label-width="129px">
          <el-input v-model="form.snapshotTime" placeholder="请输入抓拍时间间隔(秒)" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks" label-width="129px">
          <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 查看监控视频弹框 -->
    <el-dialog :title="monitor_dialog.title" v-model="monitor_dialog.visible" width="60%" append-to-body
      @close="handleMonitorClose">
      <div class="cinfo">
        <span>{{ monitorRowData.projectName }} - {{ monitorRowData.itemName }} - {{ monitorRowData.deviceName }}</span>
      </div>
      <div class="little_box">
        <MonitorPlayer v-if="isPlayer" :accessToken="accessToken" :url="playUrl"
          @capturePicture="handleCapturePicture" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="monitor_dialog.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入监控管理对话框 -->
    <Project :isShowModel="isShowModel" @update:isShowModel="isShowModelChange"
      @selectionProjectData="handleSelectionProject" />
    <!-- 导入监控管理对话框 -->
    <Project1 :isShowModel1="isShowModel1" :itemId="itemId" @update:isShowModel1="isShowModelChange1"
      @selectionProjectData1="handleSelectionProject1" />
    <!-- 无人机 -->
    <el-dialog :title="uvaordog" v-model="uavisopen" width="60vw" append-to-body @close="closeplay">
      <div style="font-size: 14px; color: #409eff; margin-bottom: 10px">{{ uavinfo.projectName }}</div>
      <PLAYcomp ref="video" :needDanmuComp="vidowflv.needDanmuComp" :playType="vidowflv.playType"
        :propDefinition="vidowflv.propDefinition" :playUrlList="vidowflv.playUrlList" :propIsLive="vidowflv.propIsLive"
        :propControls="vidowflv.propControls" :propPoster="vidowflv.propPoster" :propHeight="vidowflv.propHeight"
        :propWidth="vidowflv.propWidth" :propAutoplay="true" @screenShot="handleScreenShot" v-if="showPlayer" />
      <div style="text-align: right; margin-top: 30px">
        <el-button @click="closeplay">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="Monito" lang="ts">
import PLAYcomp from '@/components/PLAYERcomp/PLAYcomp.vue';
import {
  listMonito,
  getMonito,
  delMonito,
  addMonito,
  updateMonito,
  getAccessToken,
  getPlayMonitorUrl,
  capturePictureAnalysis,
  capture
} from '@/api/monito/monito';
import { nextTick } from 'vue';
import { MonitoVO, MonitoQuery, MonitoForm } from '@/api/monito/monito/types';
import Project from '@/components/Project/index.vue';
import Project1 from '@/components/Project/prj_hazardous.vue';
import MonitorPlayer from '@/components/MonitorPlayer/index.vue';
import { get_prj_search_data, getItemList, getUavTranscoding } from '@/api/projects/prj_hazardous_items';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { device_status, device_type, enable_snapshot } = toRefs<any>(proxy?.useDict('device_status', 'device_type', 'enable_snapshot'));

const monitoList = ref<MonitoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const monitoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const isShowModel = ref(false);
const isShowModel1 = ref(false);
const itemId = ref<string>();
const initFormData: MonitoForm = {
  monitoId: undefined,
  projectId: undefined,
  itemId: undefined,
  projectName: undefined,
  deviceName: undefined,
  deviceType: undefined,
  deviceCode: undefined,
  channelNo: undefined,
  deviceStatus: undefined,
  enableSnapshot: undefined,
  snapshotTime: undefined,
  remarks: undefined
};
const data = reactive<PageData<MonitoForm, MonitoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    projectName: undefined,
    itemName: undefined,
    itemId: undefined,
    deviceName: undefined,
    deviceType: undefined,
    deviceCode: undefined,
    deviceStatus: undefined,
    channelNo: undefined,
    enableSnapshot: undefined,
    snapshotTime: undefined,
    remarks: undefined,
    params: {}
  },
  rules: {
    projectId: [{ required: true, message: '请选择项目', trigger: 'change' }],
    itemId: [{ required: true, message: '请选择工程', trigger: 'change' }],
    deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
    deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
    deviceCode: [{ required: true, message: '请输入设备编码', trigger: 'blur' }],
    deviceStatus: [{ required: true, message: '请选择设备在线状态', trigger: 'change' }],
    enableSnapshot: [{ required: true, message: '请选择是否启用抓拍', trigger: 'change' }],
    snapshotTime: [{ required: true, message: '请输入抓拍时间间隔(秒)', trigger: 'blur' }],
    channelNo: [{ required: true, message: '请输入纯数字的通道', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);
// 查看监控视频弹框
const monitor_dialog = reactive<DialogOption>({
  visible: false,
  title: '实时监控'
});
const accessToken = ref<string>();
const playUrl = ref<string>();
const isPlayer = ref<boolean>(false);
const monitorRowData = ref<MonitoVO>();
const projectSelectData = ref([]);
const getItemData = ref([]);

let vidowflv = ref<any>({
  danmuText: '',
  playType: 'flv',
  propDefinition: [],
  playUrlList: [],
  propIsLive: true,
  propControls: true,
  propPoster: '',
  needDanmuComp: false,
  propHeight: '60vh',
  propWidth: '100%',
  monitoId: null
});
let showPlayer = ref(false);
// 获取监控视频的accessToken
const getMonitorAccessToken = async () => {
  const res = await getAccessToken();
  accessToken.value = res.msg;
};
/** 查询监控管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listMonito(queryParams.value);
  monitoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
const video = ref(null);
const closeplay = () => {
  uavisopen.value = false;
  showPlayer.value = false;
  vidowflv.value = {
    danmuText: '',
    playType: 'flv',
    propDefinition: [],
    playUrlList: [],
    propIsLive: true,
    propControls: true,
    propPoster: '',
    needDanmuComp: false,
    propHeight: '60vh',
    propWidth: '100%',
    monitoId: null
  };

  // 关闭播放器
  video.value?.childMethod();
};
/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  monitoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  getItemData.value = [];
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: MonitoVO[]) => {
  ids.value = selection.map((item) => item.monitoId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加监控管理';
};

let uavisopen = ref(false);

let uavinfo = ref<any>(null);
let uvaordog = ref<any>(null);
// 无人机按钮
const handleuav = async (row) => {
  // 判断机器狗还是无人机
  if (row.deviceType == '3') {
    uvaordog.value = '无人机';
    if (row.deviceCode.indexOf('https') == -1 || row.deviceCode.indexOf('http') == -1) {
      const wrjParams = {
        app: "wrj",//命名空间
        enableAudio: 1,
        enableFmp4: 1,
        enableHls: 1,
        enableMp4: 1,
        enableRtmp: 1,
        enableRtsp: 1,
        stream: "",//唯一id
        url: ""//拉流地址
      }
      wrjParams.stream = row.monitoId;
      wrjParams.url = row.deviceCode;
      getUavTranscoding(wrjParams).then(async (res) => {
        if (res.code == 0) {
          const deviceCode = `https://stream2.gsjtsz.net/wrj/${row.monitoId}.live.flv?k=GSJTSZONLY`;
          uavinfo.value = row;
          vidowflv.value.monitoId = row.monitoId;
          vidowflv.value.propDefinition = [deviceCode];
          vidowflv.value.playUrlList = [deviceCode];
          uavisopen.value = true;
          showPlayer.value = false;
          await nextTick();
          showPlayer.value = true;
        }
      })
      return
    }
    uavinfo.value = row;
    vidowflv.value.monitoId = row.monitoId;
    vidowflv.value.propDefinition = [row.deviceCode];
    vidowflv.value.playUrlList = [row.deviceCode];
    uavisopen.value = true;
    showPlayer.value = false;
    await nextTick();
    showPlayer.value = true;
  } else {
    uvaordog.value = '机器狗';
    uavinfo.value = row;
    vidowflv.value.monitoId = row.monitoId;
    vidowflv.value.propDefinition = [row.deviceCode];
    vidowflv.value.playUrlList = [row.deviceCode];
    uavisopen.value = true;
    showPlayer.value = false;
    await nextTick();
    showPlayer.value = true;
  }
};
let isScreenShot = ref(false);
// 点击截图
const handleScreenShot = (url) => {
  if (!isScreenShot.value) {
    isScreenShot.value = true;
    let params = {
      monitoId: vidowflv.value.monitoId,
      base64: url
    };
    capture(params).then((res) => {
      isScreenShot.value = false;
      if (res.code == 200) {
        proxy?.$modal.msgSuccess('AI分析图片成功');
      }
    }, error => {
      isScreenShot.value = false;
    });
  } else {
    proxy?.$modal.msgError('请勿连续点击');

  }
};

/** 监控按钮操作 */
const handleMonitor = async (row?: MonitoVO) => {
  monitorRowData.value = row;
  // const _monitoId = row?.monitoId || ids.value[0]
  const res = await getPlayMonitorUrl(row.deviceCode, row.channelNo ? row.channelNo : 1);
  if (res.code == 200) {
    playUrl.value = res.msg;
    monitor_dialog.visible = true;
    isPlayer.value = true;
    return;
  }
  proxy?.$modal.msgWarning('暂无监控数据');
};
const handleMonitorClose = () => {
  isPlayer.value = false;
};
const handleCapturePicture = async (val: any) => {
  const res = await capturePictureAnalysis({ base64: val, monitoId: monitorRowData.value.monitoId });
  if (res.code == 200) {
    proxy?.$modal.msgSuccess('AI分析图片成功');
  }
};
/** 修改按钮操作 */
const handleUpdate = async (row?: MonitoVO) => {
  reset();
  const _monitoId = row?.monitoId || ids.value[0];
  const res = await getMonito(_monitoId);
  Object.assign(form.value, res.data);
  itemId.value = res.data.projectId as string;
  dialog.visible = true;
  dialog.title = '修改监控管理';
};

/** 提交按钮 */
const submitForm = () => {
  monitoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.monitoId) {
        await updateMonito(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addMonito(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: MonitoVO) => {
  const _monitoIds = row?.monitoId || ids.value;
  await proxy?.$modal.confirm('是否确认删除监控管理编号为"' + _monitoIds + '"的数据项？').finally(() => (loading.value = false));
  await delMonito(_monitoIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'monito/monito/export',
    {
      ...queryParams.value
    },
    `monito_${new Date().getTime()}.xlsx`
  );
};
const isShowModelChange = (val: boolean) => {
  isShowModel.value = val;
};
const isShowModelChange1 = (val: boolean) => {
  isShowModel1.value = val;
};
const handleSelectionProject = (data: { projectId: string; projectName: string }) => {
  form.value.projectId = data.projectId;
  form.value.projectName = data.projectName;
  itemId.value = data.projectId;
  isShowModel.value = false;
};
const handleSelectionProject1 = (data: { itemId: string; itemName: string }) => {
  form.value.itemId = data.itemId;
  form.value.itemName = data.itemName;
  isShowModel1.value = false;
};
const selectProjectChange = () => {
  isShowModel.value = true;
};
const selectEngineeringChange = () => {
  if (!form.value.projectId) {
    proxy?.$modal.msgError('请先选择项目');
    return;
  }
  isShowModel1.value = true;
};

onMounted(() => {
  getMonitorAccessToken();
  getProjectSelectData();
  getList();
});
//查询项目选择框数据
const getProjectSelectData = async () => {
  const { data } = await get_prj_search_data();
  projectSelectData.value = data;
};
const changePro = async (val) => {
  getItemList(val).then((res) => {
    getItemData.value = res.data;
  });
};
</script>
<style lang="scss" scoped>
@import url('@/assets/fonticon/iconfont.css');

.cinfo {
  color: #409eff;
  font-size: 14px;
}

.little_box {
  height: 60vh;
  border-radius: 8px;
  margin-top: 15px;
  overflow: hidden;
}

.expertTable1 {

  .el-popper,
  .is-dark {
    max-width: 300px;
  }
}

.icon-jiqigou1 {
  font-size: 20px !important;
}
</style>

package org.dromara.projects.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjSafeInstallation;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjSafeInstallationBoToPrjSafeInstallationMapperImpl implements PrjSafeInstallationBoToPrjSafeInstallationMapper {

    @Override
    public PrjSafeInstallation convert(PrjSafeInstallationBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjSafeInstallation prjSafeInstallation = new PrjSafeInstallation();

        prjSafeInstallation.setSearchValue( arg0.getSearchValue() );
        prjSafeInstallation.setCreateDept( arg0.getCreateDept() );
        prjSafeInstallation.setCreateBy( arg0.getCreateBy() );
        prjSafeInstallation.setCreateTime( arg0.getCreateTime() );
        prjSafeInstallation.setUpdateBy( arg0.getUpdateBy() );
        prjSafeInstallation.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            prjSafeInstallation.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        prjSafeInstallation.setInstallationId( arg0.getInstallationId() );
        prjSafeInstallation.setUserName( arg0.getUserName() );
        prjSafeInstallation.setUserPositionName( arg0.getUserPositionName() );
        prjSafeInstallation.setFace( arg0.getFace() );
        prjSafeInstallation.setCertificate( arg0.getCertificate() );
        prjSafeInstallation.setOpenTaskId( arg0.getOpenTaskId() );

        return prjSafeInstallation;
    }

    @Override
    public PrjSafeInstallation convert(PrjSafeInstallationBo arg0, PrjSafeInstallation arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setInstallationId( arg0.getInstallationId() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setUserPositionName( arg0.getUserPositionName() );
        arg1.setFace( arg0.getFace() );
        arg1.setCertificate( arg0.getCertificate() );
        arg1.setOpenTaskId( arg0.getOpenTaskId() );

        return arg1;
    }
}

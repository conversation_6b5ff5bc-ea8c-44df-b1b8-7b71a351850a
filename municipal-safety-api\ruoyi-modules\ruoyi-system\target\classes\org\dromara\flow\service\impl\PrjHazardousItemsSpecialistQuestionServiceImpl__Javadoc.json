{"doc": " 省厅自动工单专家建议Service业务层处理\n\n <AUTHOR>\n @date 2025-06-21\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询省厅自动工单专家建议\n\n @param id 主键\n @return 省厅自动工单专家建议\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistQuestionBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询省厅自动工单专家建议列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 省厅自动工单专家建议分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistQuestionBo"], "doc": " 查询符合条件的省厅自动工单专家建议列表\n\n @param bo 查询条件\n @return 省厅自动工单专家建议列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistQuestionBo"], "doc": " 新增省厅自动工单专家建议\n\n @param bo 省厅自动工单专家建议\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistQuestionBo"], "doc": " 修改省厅自动工单专家建议\n\n @param bo 省厅自动工单专家建议\n @return 是否修改成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.flow.domain.PrjHazardousItemsSpecialistQuestion"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除省厅自动工单专家建议信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}], "constructors": []}
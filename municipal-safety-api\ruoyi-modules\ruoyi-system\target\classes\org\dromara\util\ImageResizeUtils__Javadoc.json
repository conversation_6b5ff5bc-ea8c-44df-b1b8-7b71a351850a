{"doc": " <AUTHOR>\n @date 2024/10/29 9:05\n @Description 图片比例压缩工具类\n @Version 1.0\n", "fields": [], "enumConstants": [], "methods": [{"name": "resizeImage", "paramTypes": ["java.io.InputStream", "java.lang.String", "java.lang.Integer"], "doc": " 压缩图片大小\n\n @param oldImageStream\n @param futureKb\n @return\n @throws IOException\n"}, {"name": "downloadImage", "paramTypes": ["java.lang.String"], "doc": " 下载网络图片\n\n @param imageHttpUrl\n @return\n"}], "constructors": []}
package org.dromara.attendance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.dromara.attendance.domain.MAttRule;
import org.dromara.attendance.domain.bo.MAttRuleBo;
import org.dromara.attendance.domain.vo.MAttRuleVo;
import org.dromara.attendance.mapper.MAttRuleMapper;
import org.dromara.attendance.service.IMAttRuleService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.service.DictService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.mapper.PrjProjectsMapper;
import org.dromara.system.domain.SysEnterpriseInfo;
import org.dromara.system.mapper.SysEnterpriseInfoMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 考勤规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@RequiredArgsConstructor
@Service
public class MAttRuleServiceImpl implements IMAttRuleService {
    @Resource
    private MAttRuleMapper baseMapper;
    @Resource
    private DictService dictService;
    @Resource
    private SysEnterpriseInfoMapper sysEnterpriseInfoMapper;
    @Resource
    private PrjProjectsMapper prjProjectsMapper;

    /**
     * 查询考勤规则
     *
     * @param id 主键
     * @return 考勤规则
     */
    @Override
    public MAttRuleVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询考勤规则列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 考勤规则分页列表
     */
    @Override
    public TableDataInfo<MAttRuleVo> queryPageList(MAttRuleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MAttRule> lqw = buildQueryWrapper(bo);
        Page<MAttRuleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<MAttRuleVo> selectMattRuleList(MAttRuleBo bo) {
        SysEnterpriseInfo sysEnterpriseInfo = sysEnterpriseInfoMapper.selectById(LoginHelper.getEnterpriseId());
        bo.setCreateDept(LoginHelper.isSuperAdmin() ? null : sysEnterpriseInfo.getDeptId());
        List<MAttRuleVo> mAttRuleVos = baseMapper.selectMattRuleList(bo);
        mAttRuleVos.forEach(attRuleVo -> {
            ArrayList<String> arrayList = new ArrayList<>();
            if (attRuleVo.getPersonType() != null) {
                String[] split = attRuleVo.getPersonType().split(",");
                for (int i = 0; i < split.length; i++) {
                    arrayList.add(dictService.getDictLabel("personnel_position", split[i]));
                }
                attRuleVo.setPersonTypeName(arrayList);
            }
        });
        return mAttRuleVos;
    }

    /**
     * 查询符合条件的考勤规则列表
     *
     * @param bo 查询条件
     * @return 考勤规则列表
     */
    @Override
    public List<MAttRuleVo> queryList(MAttRuleBo bo) {
        LambdaQueryWrapper<MAttRule> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MAttRule> buildQueryWrapper(MAttRuleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MAttRule> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MAttRule::getId);
        lqw.eq(bo.getRuleType() != null, MAttRule::getRuleType, bo.getRuleType());
        lqw.eq(bo.getProjectId() != null, MAttRule::getProjectId, bo.getProjectId());
        lqw.eq(bo.getPersonType() != null, MAttRule::getPersonType, bo.getPersonType());
        lqw.eq(bo.getIsAll() != null, MAttRule::getIsAll, bo.getIsAll());
        lqw.eq(StringUtils.isNotBlank(bo.getCheckTime()), MAttRule::getCheckTime, bo.getCheckTime());
        lqw.eq(bo.getElasticTime() != null, MAttRule::getElasticTime, bo.getElasticTime());
        lqw.eq(StringUtils.isNotBlank(bo.getWarning()), MAttRule::getWarning, bo.getWarning());
        lqw.eq(bo.getFieldCheck() != null, MAttRule::getFieldCheck, bo.getFieldCheck());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), MAttRule::getContent, bo.getContent());
        return lqw;
    }

    /**
     * 新增考勤规则
     *
     * @param bo 考勤规则
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MAttRuleBo bo) {
        MAttRuleVo mAttRuleVo = baseMapper.selectMAttRuleByRoleIdAndByProjectId(bo.getPersonType(), bo.getProjectId());
        if (mAttRuleVo != null) {
            throw new ServiceException("已存在此考勤规则！");
        }
        PrjProjectsVo prjProjectsVo = prjProjectsMapper.selectVoById(bo.getProjectId());
        bo.setCreateDept(prjProjectsVo.getConstructionOrgId());
        MAttRule add = MapstructUtils.convert(bo, MAttRule.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改考勤规则
     *
     * @param bo 考勤规则
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MAttRuleBo bo) {
        MAttRule update = MapstructUtils.convert(bo, MAttRule.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MAttRule entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除考勤规则信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public MAttRuleVo selectMAttRuleByRoleIdAndByProjectId(String roleId, Long projectId) {
        return baseMapper.selectMAttRuleByRoleIdAndByProjectId(roleId, projectId);
    }

    @Override
    public List<MAttRuleVo> selectMAttRuleByProjectId(Long projectId) {
        return baseMapper.selectMAttRuleByProjectId(projectId);
    }
}

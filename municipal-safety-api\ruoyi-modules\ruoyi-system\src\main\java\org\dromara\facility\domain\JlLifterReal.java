package org.dromara.facility.domain;

import cn.hutool.core.date.DatePattern;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 升降机实时数据对象 jl_lifter_real
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("jl_lifter_real")
public class JlLifterReal {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 设备编号
     */
    private String devNo;

    /**
     * 吊笼编号
     */
    private String tcNo;

    /**
     * 0 是左   1是右
     */
    private Long lifterRight;

    /**
     * 吊笼编号和左右笼
     */
    private String tcRightStr;

    /**
     * 时间
     */
    private Date date;

    /**
     * 实时起重量
     */
    private Long weight;

    /**
     * 重量百分比
     */
    private Long weightPercent;

    /**
     * 实时人数
     */
    private Long person;

    /**
     * 实时高度
     */
    private Long height;

    /**
     * 高度百分比
     */
    private Long heightPercent;

    /**
     * 实时速度
     */
    private Long speed;

    /**
     * 速度方向 0停止，1上，2下
     */
    private Long speedDir;

    /**
     * 实时倾斜度
     */
    private Long slant1;

    /**
     * 倾斜百分比
     */
    private Long slant1Percent;

    /**
     * 实时倾斜度
     */
    private Long slant2;

    /**
     * 倾斜百分比
     */
    private Long slant2Percent;

    /**
     * 驾驶员身份认证结果 00为未认证 01为已认证
     */
    private String driverAuth;

    /**
     * 前门状态 数值1代表开启，0带便关闭
     */
    private Long frontDoor;

    /**
     * 后门状态 数值1代表开启，0带便关闭
     */
    private Long backDoor;

    /**
     * 门锁异常指示 0无异常1有异常
     */
    private Long doorLock;

    /**
     * 重量
     */
    private Long systemStatusWeight;

    /**
     * 高度限位
     */
    private Long systemStatusHeight;

    /**
     * 超速
     */
    private Long systemStatusSpeed;

    /**
     * 人数
     */
    private Long systemStatusPerson;

    /**
     * 倾斜
     */
    private Long systemStatusSlant;

    /**
     * 前门锁状态：数字0正常,数值1异常
     */
    private Long systemStatusFrontDoor;

    /**
     * 后门锁状态：数字0正常,数值1异常
     */
    private Long systemStatusBackDoor;

    /**
     * 风速，0表示正常，1表示预警，2表示报警
     */
    private Long systemStatusWindSpeed;

    /**
     * 上限位，0表示正常，1表示报警
     */
    private Long systemStatusUpperLimit;

    /**
     * 防坠器，0表示正常，1表示报警
     */
    private Long systemStatusFallingProtector;

    /**
     * 实时风速
     */
    private Long windSpeed;

    /**
     * 当前楼层
     */
    private Long currentFloor;

    /**
     * 未戴安全帽的人数
     */
    private Long uncovered;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 删除标志 (0代表存在 1代表删除)
     */
    @TableLogic
    private String delFlag;
}

{"doc": " 项目人员关联表对象 prj_personnel\n\n <AUTHOR>\n @date 2025-05-09\n", "fields": [{"name": "projectPersonnelId", "doc": " 项目人员关联ID\n"}, {"name": "projectId", "doc": " 项目ID (逻辑外键至 prj_projects.project_id)\n"}, {"name": "personId", "doc": " 人员表id\n"}, {"name": "userId", "doc": " 用户ID (逻辑外键至 sys_users.user_id)\n"}, {"name": "orgId", "doc": " 该人员在项目中的所属单位ID (逻辑外键至 sys_organizations.org_id)\n"}, {"name": "roleOnProject", "doc": " 在本项目中的具体角色/岗位（取字典值）\n"}, {"name": "isSpecialOps", "doc": " 是否特种作业人员 (0:否, 1:是)\n"}, {"name": "startDateOnProject", "doc": " 进入项目日期\n"}, {"name": "endDateOnProject", "doc": " 离开项目日期\n"}, {"name": "delFlag", "doc": " 删除标志 (0代表存在 1代表删除)\n"}], "enumConstants": [], "methods": [], "constructors": []}
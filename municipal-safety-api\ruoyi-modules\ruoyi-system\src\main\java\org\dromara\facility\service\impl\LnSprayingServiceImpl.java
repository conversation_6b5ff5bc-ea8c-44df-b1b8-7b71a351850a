package org.dromara.facility.service.impl;

import com.alibaba.fastjson2.JSON;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.facility.domain.bo.LnSprayingBo;
import org.dromara.facility.domain.vo.LnSprayingVo;
import org.dromara.facility.domain.LnSpraying;
import org.dromara.facility.mapper.LnSprayingMapper;
import org.dromara.facility.service.ILnSprayingService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 绿能喷淋设备Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@RequiredArgsConstructor
@Service
public class LnSprayingServiceImpl implements ILnSprayingService {

    private final LnSprayingMapper baseMapper;

    /**
     * 查询绿能喷淋设备
     *
     * @param id 主键
     * @return 绿能喷淋设备
     */
    @Override
    public LnSprayingVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询绿能喷淋设备列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绿能喷淋设备分页列表
     */
    @Override
    public TableDataInfo<LnSprayingVo> queryPageList(LnSprayingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LnSpraying> lqw = buildQueryWrapper(bo);
        Page<LnSprayingVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的绿能喷淋设备列表
     *
     * @param bo 查询条件
     * @return 绿能喷淋设备列表
     */
    @Override
    public List<LnSprayingVo> queryList(LnSprayingBo bo) {
        LambdaQueryWrapper<LnSpraying> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LnSpraying> buildQueryWrapper(LnSprayingBo bo) {
        LambdaQueryWrapper<LnSpraying> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(LnSpraying::getCreateTime);
        lqw.eq(LnSpraying::getDevNo, bo.getDevNo());
        return lqw;
    }

    /**
     * 新增绿能喷淋设备
     *
     * @param bo 绿能喷淋设备
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LnSprayingBo bo) {
        LnSpraying add = MapstructUtils.convert(bo, LnSpraying.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public void insertByJson(String jsonString) {
        LnSpraying add = MapstructUtils.convert(JSON.parseObject(jsonString, LnSprayingBo.class), LnSpraying.class);
        add.setCreateTime(new Date());
        baseMapper.insert(add);
    }

    /**
     * 修改绿能喷淋设备
     *
     * @param bo 绿能喷淋设备
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LnSprayingBo bo) {
        LnSpraying update = MapstructUtils.convert(bo, LnSpraying.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LnSpraying entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除绿能喷淋设备信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}

<template>
  <div class="p-2">
    <el-card shadow="never">
      <div style="display: flex; justify-content: space-between">
        <div>
          <el-button v-if="approvalButtonShow" :loading="buttonLoading" type="primary"
                     @click="approvalVerifyOpen">审批
          </el-button>
          <el-button v-if="form && form.id" type="primary" @click="handleApprovalRecord">流程进度</el-button>
        </div>
        <!--  todo start-->


        <!--  todo end-->
        <div>
          <el-button style="float: right" @click="goBack()">返回</el-button>
        </div>
      </div>
    </el-card>

    <!-- 提交组件 -->
    <submitVerify ref="submitVerifyRef" :task-variables="taskVariables" :isReadyFile="false" :isSelectBtnDisabled="true"
                  @submit-callback="submitCallback" @beforeSubmit="beforeSubmit" />
    <!-- 审批记录 -->
    <approvalRecord ref="approvalRecordRef" />
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" :before-close="handleClose" width="500">
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="submitFlow()"> 确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Leave" lang="ts">
import { listByIds } from '@/api/system/oss/index';
import SubmitVerify from '@/components/Process/submitVerify.vue';
import ApprovalRecord from '@/components/Process/approvalRecord.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const buttonLoading = ref(false);
const loading = ref(true);
const leaveTime = ref<Array<string>>([]);
// 存放隐患清单详情的数据
const aiDetailData = ref();
// 单选框选中的值
const isSelected = ref([]);
const selectContentArr = ref([]);
const abarbeitungComments = ref([]);
//路由参数
const routeParams = ref<Record<string, any>>({});

const flowCode = ref<string>('');

const dialogVisible = reactive<DialogOption>({
  visible: false,
  title: '流程定义'
});
//提交组件
const submitVerifyRef = ref<InstanceType<typeof SubmitVerify>>();
//审批记录组件
const approvalRecordRef = ref<InstanceType<typeof ApprovalRecord>>();

const leaveFormRef = ref<ElFormInstance>();

const taskVariables = ref<Record<string, any>>({});

const data = reactive<any>({
  form: {}
});

const handleClose = () => {
  dialogVisible.visible = false;
  flowCode.value = '';
  buttonLoading.value = false;
};
const { form } = toRefs(data);

/** 表单重置 */
const reset = () => {
  leaveTime.value = [];
  leaveFormRef.value?.resetFields();
};

// 使用ossId查询图片的url地址
const getImageUrl = async (ossId: string | number) => {
  const { data } = await listByIds(ossId);
  return data[0]?.url;
};

const submitFlow = async () => {
  dialogVisible.visible = false;
};

//审批记录
const handleApprovalRecord = () => {
  approvalRecordRef.value.init(form.value.id);
};

//提交回调
const submitCallback = async () => {
  await proxy.$tab.closePage(proxy.$route);
  proxy.$router.go(-1);
};
// 提交前的通用回调函数
const beforeSubmit = async (fun) => {
  // 提交前的逻辑处理

};
//返回
const goBack = () => {
  proxy.$tab.closePage(proxy.$route);
  proxy.$router.go(-1);
};
//审批
const approvalVerifyOpen = async () => {
  const isAllSelect = isSelected.value.every(item => item == 1);
  if (!isAllSelect) {
    submitVerifyRef.value.openDialog(routeParams.value.taskId);
  } else {
    proxy?.$modal.msgError('该项目清单已经提交审批了，无法再次提交');
  }
};

//校验审批按钮是否显示
const approvalButtonShow = computed(() => {
  return routeParams.value.type === 'approval' && form.value.status && form.value.status === 'waiting';
});

onMounted(() => {
  nextTick(async () => {
    routeParams.value = proxy.$route.query;
    reset();
    loading.value = false;
    form.value.id = routeParams.value.id;
  });
});
</script>
<style lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>

package org.dromara.facility.domain.vo;

import java.util.Date;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.facility.domain.LnHighFormworkReal;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 绿能高支模实时数据视图对象 ln_high_formwork_real
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LnHighFormworkReal.class)
public class LnHighFormworkRealVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 设备id
     */
    @ExcelProperty(value = "设备id")
    private Long eid;

    /**
     * 模板沉降
     */
    @ExcelProperty(value = "模板沉降")
    private String settlement;

    /**
     * 立杆倾角
     */
    @ExcelProperty(value = "立杆倾角")
    private String inclinationAngleOfVerticalPole;

    /**
     * 水平倾角
     */
    @ExcelProperty(value = "水平倾角")
    private String horizontalInclination;

    /**
     * 称重
     */
    @ExcelProperty(value = "称重")
    private String bearing;

    /**
     * 水平位移
     */
    @ExcelProperty(value = "水平位移")
    private String horizontalDisplacement;

    /**
     * 垂直位移
     */
    @ExcelProperty(value = "垂直位移")
    private String verticalDisplacement;

    /**
     * 上报时间
     */
    @ExcelProperty(value = "上报时间")
    private Date eventTime;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String devNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
}

package org.dromara.projects.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjPersonnel;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:15+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjPersonnelVoToPrjPersonnelMapperImpl implements PrjPersonnelVoToPrjPersonnelMapper {

    @Override
    public PrjPersonnel convert(PrjPersonnelVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjPersonnel prjPersonnel = new PrjPersonnel();

        prjPersonnel.setProjectPersonnelId( arg0.getProjectPersonnelId() );
        prjPersonnel.setProjectId( arg0.getProjectId() );
        prjPersonnel.setPersonId( arg0.getPersonId() );
        prjPersonnel.setUserId( arg0.getUserId() );
        prjPersonnel.setOrgId( arg0.getOrgId() );
        prjPersonnel.setRoleOnProject( arg0.getRoleOnProject() );
        prjPersonnel.setIsSpecialOps( arg0.getIsSpecialOps() );
        prjPersonnel.setStartDateOnProject( arg0.getStartDateOnProject() );
        prjPersonnel.setEndDateOnProject( arg0.getEndDateOnProject() );

        return prjPersonnel;
    }

    @Override
    public PrjPersonnel convert(PrjPersonnelVo arg0, PrjPersonnel arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setProjectPersonnelId( arg0.getProjectPersonnelId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setPersonId( arg0.getPersonId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOrgId( arg0.getOrgId() );
        arg1.setRoleOnProject( arg0.getRoleOnProject() );
        arg1.setIsSpecialOps( arg0.getIsSpecialOps() );
        arg1.setStartDateOnProject( arg0.getStartDateOnProject() );
        arg1.setEndDateOnProject( arg0.getEndDateOnProject() );

        return arg1;
    }
}

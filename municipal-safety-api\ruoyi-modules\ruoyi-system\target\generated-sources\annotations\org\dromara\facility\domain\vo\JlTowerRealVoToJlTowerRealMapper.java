package org.dromara.facility.domain.vo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.JlTowerReal;
import org.dromara.facility.domain.JlTowerRealToJlTowerRealVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {JlTowerRealToJlTowerRealVoMapper.class},
    imports = {}
)
public interface JlTowerRealVoToJlTowerRealMapper extends BaseMapper<JlTowerRealVo, JlTowerReal> {
}

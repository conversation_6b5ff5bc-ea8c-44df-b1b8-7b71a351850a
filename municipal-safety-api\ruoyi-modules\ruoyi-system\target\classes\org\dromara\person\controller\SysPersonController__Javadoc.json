{"doc": " 人员基本信息\n\n <AUTHOR> zu <PERSON>\n @date 2025-05-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.person.domain.bo.SysPersonBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询人员基本信息列表\n"}, {"name": "export", "paramTypes": ["org.dromara.person.domain.bo.SysPersonBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出人员基本信息列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取人员基本信息详细信息\n\n @param personId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.person.domain.bo.SysPersonBo"], "doc": " 新增人员基本信息\n"}, {"name": "edit", "paramTypes": ["org.dromara.person.domain.bo.SysPersonBo"], "doc": " 修改人员基本信息\n"}, {"name": "moveOut", "paramTypes": ["org.dromara.person.domain.bo.SysPersonBo"], "doc": " 人员迁出\n\n @param sysPersonBo\n @return\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除人员基本信息\n\n @param personIds 主键串\n"}], "constructors": []}
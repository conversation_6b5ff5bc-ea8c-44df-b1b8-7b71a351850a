package org.dromara.attendance.service;

import org.dromara.attendance.domain.bo.MAttRuleBo;
import org.dromara.attendance.domain.vo.MAttRuleVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 考勤规则Service接口
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
public interface IMAttRuleService {

    /**
     * 查询考勤规则
     *
     * @param id 主键
     * @return 考勤规则
     */
    MAttRuleVo queryById(Long id);

    /**
     * 分页查询考勤规则列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 考勤规则分页列表
     */
    TableDataInfo<MAttRuleVo> queryPageList(MAttRuleBo bo, PageQuery pageQuery);

    List<MAttRuleVo> selectMattRuleList(MAttRuleBo bo);

    /**
     * 查询符合条件的考勤规则列表
     *
     * @param bo 查询条件
     * @return 考勤规则列表
     */
    List<MAttRuleVo> queryList(MAttRuleBo bo);

    /**
     * 新增考勤规则
     *
     * @param bo 考勤规则
     * @return 是否新增成功
     */
    Boolean insertByBo(MAttRuleBo bo);

    /**
     * 修改考勤规则
     *
     * @param bo 考勤规则
     * @return 是否修改成功
     */
    Boolean updateByBo(MAttRuleBo bo);

    /**
     * 校验并批量删除考勤规则信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    MAttRuleVo selectMAttRuleByRoleIdAndByProjectId(String roleId, Long projectId);

    List<MAttRuleVo> selectMAttRuleByProjectId(Long projectId);
}

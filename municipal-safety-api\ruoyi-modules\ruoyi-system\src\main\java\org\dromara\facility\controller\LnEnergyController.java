package org.dromara.facility.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.facility.domain.bo.LnEnergyBo;
import org.dromara.facility.domain.vo.LnEnergyVo;
import org.dromara.facility.service.ILnEnergyService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 绿能用电监测
 *
 * <AUTHOR> Li
 * @date 2025-07-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/lnEnergy")
public class LnEnergyController extends BaseController {

    private final ILnEnergyService lnEnergyService;

    /**
     * 查询绿能用电监测列表
     */
    @GetMapping("/list")
    public TableDataInfo<LnEnergyVo> list(LnEnergyBo bo, PageQuery pageQuery) {
        return lnEnergyService.queryPageList(bo, pageQuery);
    }
}

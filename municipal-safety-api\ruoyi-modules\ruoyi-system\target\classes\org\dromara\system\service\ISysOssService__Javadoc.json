{"doc": " 文件上传 服务层\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.SysOssBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询OSS对象存储列表\n\n @param sysOss    OSS对象存储分页查询对象\n @param pageQuery 分页查询实体类\n @return 结果\n"}, {"name": "listByIds", "paramTypes": ["java.util.Collection"], "doc": " 根据一组 ossIds 获取对应的 SysOssVo 列表\n\n @param ossIds 一组文件在数据库中的唯一标识集合\n @return 包含 SysOssVo 对象的列表\n"}, {"name": "getById", "paramTypes": ["java.lang.Long"], "doc": " 根据 ossId 从缓存或数据库中获取 SysOssVo 对象\n\n @param ossId 文件在数据库中的唯一标识\n @return SysOssVo 对象，包含文件信息\n"}, {"name": "upload", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 上传 MultipartFile 到对象存储服务，并保存文件信息到数据库\n\n @param file 要上传的 MultipartFile 对象\n @return 上传成功后的 SysOssVo 对象，包含文件信息\n"}, {"name": "upload", "paramTypes": ["java.io.InputStream", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 上传 MultipartFile 到对象存储服务，并保存文件信息到数据库\n @param inputStream\n @param fileName\n @param fileType\n @param content\n @return\n"}, {"name": "uploadNoLogin", "paramTypes": ["java.io.InputStream", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 上传 MultipartFile 到对象存储服务，并保存文件信息到数据库\n @param inputStream\n @param fileName\n @param fileType\n @param content\n @return\n"}, {"name": "upload", "paramTypes": ["java.io.File"], "doc": " 上传文件到对象存储服务，并保存文件信息到数据库\n\n @param file 要上传的文件对象\n @return 上传成功后的 SysOssVo 对象，包含文件信息\n"}, {"name": "download", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": " 文件下载方法，支持一次性下载完整文件\n\n @param ossId    OSS对象ID\n @param response HttpServletResponse对象，用于设置响应头和向客户端发送文件内容\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 删除OSS对象存储\n\n @param ids     OSS对象ID串\n @param isValid 判断是否需要校验\n @return 结果\n"}], "constructors": []}
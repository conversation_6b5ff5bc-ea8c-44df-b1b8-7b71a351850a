package org.dromara.flow.domain.bo;

import io.github.linpeilie.AutoMapperConfig__462;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.PrjHazardousItemsComments;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__462.class,
    uses = {},
    imports = {}
)
public interface PrjHazardousItemsCommentsBoToPrjHazardousItemsCommentsMapper extends BaseMapper<PrjHazardousItemsCommentsBo, PrjHazardousItemsComments> {
}

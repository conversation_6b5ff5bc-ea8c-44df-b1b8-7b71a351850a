package org.dromara.flow.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.flow.domain.PrjHazardousItemsSpecialist;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 省厅自动工单视图对象 prj_hazardous_items_specialist
 *
 * <AUTHOR> Li
 * @date 2025-06-20
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjHazardousItemsSpecialist.class)
public class PrjHazardousItemsSpecialistVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 专家id（多个用,隔开）
     */
    @ExcelProperty(value = "专家id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "多=个用,隔开")
    private String specialist;

    /**
     * 厅局补充说明
     */
    @ExcelProperty(value = "厅局补充说明")
    private String instruction;

    /**
     * 厅局下发文件（多个用,隔开）
     */
    @ExcelProperty(value = "厅局下发文件", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "多=个用,隔开")
    private String downPushFile;

    /**
     * 业务id
     */
    @ExcelProperty(value = "业务id")
    private String taskId;


    private List<String> specialistNames;
}

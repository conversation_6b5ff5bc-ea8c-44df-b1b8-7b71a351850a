{"doc": " 政府用户对象导入VO\n\n <AUTHOR>\n", "fields": [{"name": "userName", "doc": " 用户账号\n"}, {"name": "nick<PERSON><PERSON>", "doc": " 用户昵称\n"}, {"name": "deptName", "doc": " 部门名称\n"}, {"name": "email", "doc": " 用户邮箱\n"}, {"name": "phonenumber", "doc": " 手机号码\n"}, {"name": "sex", "doc": " 用户性别\n"}, {"name": "status", "doc": " 帐号状态（0正常 1停用）\n"}, {"name": "provinceCode", "doc": " 省编码\n"}, {"name": "cityCode", "doc": " 市编码\n"}, {"name": "districtCode", "doc": " 区县编码\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "supervisionLevelCode", "doc": " 监督级别编码\n 1：省级 2：地市级 3：区县级\n"}], "enumConstants": [], "methods": [], "constructors": []}
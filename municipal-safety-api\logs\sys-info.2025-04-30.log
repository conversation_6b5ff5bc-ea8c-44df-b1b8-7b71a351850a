2025-04-30 08:31:21 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-04-30 08:31:21 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 19348 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-30 08:31:21 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-04-30 08:31:25 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-30 08:31:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-04-30 08:31:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-04-30 08:31:26 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@********
2025-04-30 08:31:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-04-30 08:31:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-04-30 08:31:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-04-30 08:31:28 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-04-30 08:31:28 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-04-30 08:31:28 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-04-30 08:31:28 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-04-30 08:31:30 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-30 11:14:38 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-04-30 11:14:42 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-30 11:14:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-04-30 11:14:43 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-04-30 11:14:43 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@506654fa
2025-04-30 11:14:43 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-04-30 11:14:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-04-30 11:14:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-04-30 11:14:45 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-04-30 11:14:45 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-04-30 11:14:45 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-04-30 11:14:45 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-04-30 11:14:46 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-30 11:38:17 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-04-30 11:38:21 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-30 11:38:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-04-30 11:38:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-04-30 11:38:22 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@54c697c
2025-04-30 11:38:22 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-04-30 11:38:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-04-30 11:38:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-04-30 11:38:23 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-04-30 11:38:24 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-04-30 11:38:24 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-04-30 11:38:24 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-04-30 11:38:25 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\"treeCode\":\"danger_id\",\"treeName\":\"name\",\"treeParentCode\":\"pre_id\",\"parentMenuId\":1}","remark":null,"treeCode":"danger_id","treeParentCode":"pre_id","treeName":"name","menuIds":null,"parentMenuId":1,"parentMenuName":null,"tree":true,"crud":false,"params":{"treeCode":"danger_id","treeName":"name","treeParentCode":"pre_id","parentMenuId":1}}]
2025-04-30 11:40:27 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[PUT /tool/gen],耗时:[650]毫秒
2025-04-30 11:40:27 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 11:40:27 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[60]毫秒
2025-04-30 11:40:27 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-04-30 11:40:27 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[0]毫秒
2025-04-30 11:40:32 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/preview/1917396113531965441],无参数
2025-04-30 11:40:32 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/preview/1917396113531965441],耗时:[107]毫秒
2025-04-30 11:41:28 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/preview/1917396113531965441],无参数
2025-04-30 11:41:29 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/preview/1917396113531965441],耗时:[105]毫秒
2025-04-30 11:41:33 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/preview/1917396113531965441],无参数
2025-04-30 11:41:34 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/preview/1917396113531965441],耗时:[99]毫秒
2025-04-30 11:42:20 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/1917396113531965441],无参数
2025-04-30 11:42:20 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-04-30 11:42:20 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[74]毫秒
2025-04-30 11:42:20 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/1917396113531965441],耗时:[96]毫秒
2025-04-30 11:42:21 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-04-30 11:42:21 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[47]毫秒
2025-04-30 11:42:24 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-04-30 11:42:24 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 11:42:24 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[1]毫秒
2025-04-30 11:42:24 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[85]毫秒
2025-04-30 11:42:28 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-04-30 11:42:28 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/1917418071493926913],无参数
2025-04-30 11:42:28 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[69]毫秒
2025-04-30 11:42:28 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/1917418071493926913],耗时:[92]毫秒
2025-04-30 11:42:28 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-04-30 11:42:28 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[44]毫秒
2025-04-30 11:42:30 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-04-30 11:42:30 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 11:42:30 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[0]毫秒
2025-04-30 11:42:30 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[76]毫秒
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/synchDb/1917396113531965441],无参数
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561474-00553739][thread:139][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.TABLES
WHERE (TABLE_SCHEMA = ? AND TABLE_TYPE = ?)
]
[param:
param0=municipal-safety(java.lang.String)
param1=BASE TABLE(java.lang.String)
];
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561474-00553739][thread:139][ds:default-master][action:select][执行耗时:00:00:00.028]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561474-00553739][thread:139][ds:default-master][action:select][封装耗时:00:00:00.009][封装行数:59]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561474-00553739][thread:139][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.TABLES
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ? AND TABLE_TYPE = ?)
]
[param:
param0=municipal-safety(java.lang.String)
param1=t_danger_list(java.lang.String)
param2=BASE TABLE(java.lang.String)
];
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561474-00553739][thread:139][ds:default-master][action:select][执行耗时:00:00:00.022]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561474-00553739][thread:139][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561474-00553739][thread:139][ds:default-master][tables][catalog:null][schema:SCHEMA:municipal-safety][pattern:t_danger_list][type:1][result:1][执行耗时:00:00:00.128]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561606-58692826][thread:139][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.COLUMNS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ?)
ORDER BY TABLE_NAME ASC, ORDINAL_POSITION ASC
]
[param:
param0=municipal-safety(java.lang.String)
param1=t_danger_list(java.lang.String)
];
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561606-58692826][thread:139][ds:default-master][action:select][执行耗时:00:00:00.024]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561606-58692826][thread:139][ds:default-master][action:select][封装耗时:00:00:00.001][封装行数:10]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561606-58692826][thread:139][ds:default-master][columns][catalog:null][schema:SCHEMA:municipal-safety][table:TABLE:municipal-safety.t_danger_list][total:10][根据metadata解析:0][根据系统表查询:10][根据驱动内置接口补充:0][执行耗时:00:00:00.035]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561606-58692826][thread:139][ds:default-master][columns][catalog:null][schema:SCHEMA:municipal-safety][table:TABLE:municipal-safety.t_danger_list][total:10][根据metadata解析:0][根据系统表查询:10][根据根据驱动内置接口补充:0][执行耗时:00:00:00.036]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561643-59150712][thread:139][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.STATISTICS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ?)
ORDER BY SEQ_IN_INDEX ASC
]
[param:
param0=municipal-safety(java.lang.String)
param1=t_danger_list(java.lang.String)
];
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561643-59150712][thread:139][ds:default-master][action:select][执行耗时:00:00:00.022]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561643-59150712][thread:139][ds:default-master][action:select][封装耗时:00:00:00.001][封装行数:1]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561670-25732089][thread:139][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE (CONSTRAINT_SCHEMA = ? AND TABLE_NAME LIKE ?)
]
[param:
param0=municipal-safety(java.lang.String)
param1=t_danger_list(java.lang.String)
];
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561670-25732089][thread:139][ds:default-master][action:select][执行耗时:00:00:00.021]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561670-25732089][thread:139][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561670-25732089][thread:139][ds:default-master][constraints][result:1][执行耗时:00:00:00.024]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561694-79655356][thread:139][ds:default-master][action:select][cmd:
show create table `municipal-safety`.t_danger_list
]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561694-79655356][thread:139][ds:default-master][action:select][执行耗时:00:00:00.021]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561694-79655356][thread:139][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561694-79655356][thread:139][ds:default-master][table ddl][table:t_danger_list][result:1][执行耗时:00:00:00.022]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561717-31328110][thread:139][ds:default-master][action:select][cmd:
SHOW CREATE TABLE `municipal-safety`.t_danger_list
]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561717-31328110][thread:139][ds:default-master][action:select][执行耗时:00:00:00.021]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561717-31328110][thread:139][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561717-31328110][thread:139][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.PARTITIONS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME = ?)
]
[param:
param0=municipal-safety(java.lang.String)
param1=t_danger_list(java.lang.String)
];
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561717-31328110][thread:139][ds:default-master][action:select][执行耗时:00:00:00.022]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561717-31328110][thread:139][ds:default-master][action:select][封装耗时:00:00:00.000][封装行数:1]
2025-04-30 11:42:41 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1745984561717-31328110][thread:139][ds:default-master][partition][table:t_danger_list][result:true][执行耗时:00:00:00.046]
2025-04-30 11:42:42 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/synchDb/1917396113531965441],耗时:[1106]毫秒
2025-04-30 11:42:45 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/1917396113531965441],无参数
2025-04-30 11:42:45 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-04-30 11:42:45 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[69]毫秒
2025-04-30 11:42:45 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/1917396113531965441],耗时:[89]毫秒
2025-04-30 11:42:45 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-04-30 11:42:45 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[44]毫秒
2025-04-30 11:42:49 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[PUT /tool/gen],参数类型[json],参数:[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"tableId":"1917396113531965441","dataName":"master","tableName":"t_danger_list","tableComment":"危大工程清单","subTableName":"","subTableFkName":"","className":"DangerList","tplCategory":"tree","packageName":"org.dromara.system","moduleName":"system","businessName":"dangerList","functionName":"dangerList","functionAuthor":"luzuda","genType":"0","genPath":"/","pkColumn":null,"columns":[{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:42:42","columnId":"1917396114765090818","tableId":"1917396113531965441","columnName":"danger_id","columnComment":"主键","columnType":"bigint","javaType":"Long","javaField":"dangerId","isPk":"1","isIncrement":"1","isRequired":"1","isInsert":null,"isEdit":"1","isList":"1","isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":1,"capJavaField":"DangerId","query":false,"increment":true,"required":true,"list":true,"pk":true,"insert":false,"edit":true,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:42:42","columnId":"1917396114828005377","tableId":"1917396113531965441","columnName":"name","columnComment":"名称","columnType":"varchar(255)","javaType":"String","javaField":"name","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"LIKE","htmlType":"input","dictType":"","sort":2,"capJavaField":"Name","query":true,"increment":true,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:42:42","columnId":"1917396114828005378","tableId":"1917396113531965441","columnName":"pre_id","columnComment":"父级id","columnType":"bigint","javaType":"Long","javaField":"preId","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"input","dictType":"","sort":3,"capJavaField":"PreId","query":true,"increment":true,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:42:42","columnId":"1917396114828005379","tableId":"1917396113531965441","columnName":"type","columnComment":"危大类型标识 1危大 2超危大","columnType":"tinyint","javaType":"Long","javaField":"type","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"danger_list_type","sort":4,"capJavaField":"Type","query":true,"increment":true,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:42:42","columnId":"1917396114828005380","tableId":"1917396113531965441","columnName":"remark","columnComment":"备注","columnType":"varchar(255)","javaType":"String","javaField":"remark","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":5,"capJavaField":"Remark","query":false,"increment":true,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":true},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:42:42","columnId":"1917415163012124673","tableId":"1917396113531965441","columnName":"create_dept","columnComment":"创建部门","columnType":"bigint","javaType":"Long","javaField":"createDept","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":6,"capJavaField":"CreateDept","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:42:42","columnId":"1917415163012124674","tableId":"1917396113531965441","columnName":"create_by","columnComment":"创建者","columnType":"bigint","javaType":"Long","javaField":"createBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":7,"capJavaField":"CreateBy","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:42:42","columnId":"1917415163012124675","tableId":"1917396113531965441","columnName":"create_time","columnComment":"创建时间","columnType":"datetime","javaType":"Date","javaField":"createTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"datetime","dictType":"","sort":8,"capJavaField":"CreateTime","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:42:42","columnId":"1917415163012124676","tableId":"1917396113531965441","columnName":"update_by","columnComment":"更新者","columnType":"bigint","javaType":"Long","javaField":"updateBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":9,"capJavaField":"UpdateBy","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:42:42","columnId":"1917415163012124677","tableId":"1917396113531965441","columnName":"update_time","columnComment":"更新时间","columnType":"datetime","javaType":"Date","javaField":"updateTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"datetime","dictType":"","sort":10,"capJavaField":"UpdateTime","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false}],"options":"{\"treeCode\":\"danger_id\",\"treeName\":\"name\",\"treeParentCode\":\"pre_id\",\"parentMenuId\":1}","remark":null,"treeCode":"danger_id","treeParentCode":"pre_id","treeName":"name","menuIds":null,"parentMenuId":1,"parentMenuName":null,"tree":true,"crud":false,"params":{"treeCode":"danger_id","treeName":"name","treeParentCode":"pre_id","parentMenuId":1}}]
2025-04-30 11:42:50 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[PUT /tool/gen],耗时:[561]毫秒
2025-04-30 11:42:50 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 11:42:50 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-04-30 11:42:50 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[0]毫秒
2025-04-30 11:42:50 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[49]毫秒
2025-04-30 11:42:52 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/preview/1917396113531965441],无参数
2025-04-30 11:42:52 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/preview/1917396113531965441],耗时:[97]毫秒
2025-04-30 11:43:35 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-04-30 11:43:35 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/1917396113531965441],无参数
2025-04-30 11:43:35 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[71]毫秒
2025-04-30 11:43:35 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/1917396113531965441],耗时:[92]毫秒
2025-04-30 11:43:36 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-04-30 11:43:36 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[43]毫秒
2025-04-30 11:44:29 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-04-30 11:44:29 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 11:44:29 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[1]毫秒
2025-04-30 11:44:29 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[77]毫秒
2025-04-30 11:44:30 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dangerList/list],无参数
2025-04-30 11:44:30 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dangerList/list],耗时:[22]毫秒
2025-04-30 11:48:03 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/preview/1917396113531965441],无参数
2025-04-30 11:48:03 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/preview/1917396113531965441],耗时:[96]毫秒
2025-04-30 11:48:33 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/preview/1917396113531965441],无参数
2025-04-30 11:48:51 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/preview/1917396113531965441],耗时:[18292]毫秒
2025-04-30 11:48:56 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-04-30 11:48:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/1917396113531965441],无参数
2025-04-30 11:48:56 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[72]毫秒
2025-04-30 11:48:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/1917396113531965441],耗时:[99]毫秒
2025-04-30 11:48:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-04-30 11:48:57 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[42]毫秒
2025-04-30 11:49:13 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[PUT /tool/gen],参数类型[json],参数:[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"tableId":"1917396113531965441","dataName":"master","tableName":"t_danger_list","tableComment":"危大工程清单","subTableName":"","subTableFkName":"","className":"DangerList","tplCategory":"tree","packageName":"org.dromara.system","moduleName":"system","businessName":"dangerList","functionName":"dangerList","functionAuthor":"luzuda","genType":"0","genPath":"/","pkColumn":null,"columns":[{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:45:26","columnId":"1917396114765090818","tableId":"1917396113531965441","columnName":"danger_id","columnComment":"主键","columnType":"bigint","javaType":"Long","javaField":"dangerId","isPk":"1","isIncrement":"1","isRequired":"1","isInsert":null,"isEdit":"0","isList":"0","isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":1,"capJavaField":"DangerId","query":false,"increment":true,"required":true,"list":false,"pk":true,"insert":false,"edit":false,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:45:26","columnId":"1917396114828005377","tableId":"1917396113531965441","columnName":"name","columnComment":"名称","columnType":"varchar(255)","javaType":"String","javaField":"name","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"LIKE","htmlType":"input","dictType":"","sort":2,"capJavaField":"Name","query":true,"increment":true,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:45:26","columnId":"1917396114828005378","tableId":"1917396113531965441","columnName":"pre_id","columnComment":"父级id","columnType":"bigint","javaType":"Long","javaField":"preId","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"input","dictType":"","sort":3,"capJavaField":"PreId","query":true,"increment":true,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:45:26","columnId":"1917396114828005379","tableId":"1917396113531965441","columnName":"type","columnComment":"危大类型标识 1危大 2超危大","columnType":"tinyint","javaType":"Long","javaField":"type","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"danger_list_type","sort":4,"capJavaField":"Type","query":true,"increment":true,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:45:26","columnId":"1917396114828005380","tableId":"1917396113531965441","columnName":"remark","columnComment":"备注","columnType":"varchar(255)","javaType":"String","javaField":"remark","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":5,"capJavaField":"Remark","query":false,"increment":true,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":true},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:45:26","columnId":"1917415163012124673","tableId":"1917396113531965441","columnName":"create_dept","columnComment":"创建部门","columnType":"bigint","javaType":"Long","javaField":"createDept","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":6,"capJavaField":"CreateDept","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:45:26","columnId":"1917415163012124674","tableId":"1917396113531965441","columnName":"create_by","columnComment":"创建者","columnType":"bigint","javaType":"Long","javaField":"createBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":7,"capJavaField":"CreateBy","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:45:26","columnId":"1917415163012124675","tableId":"1917396113531965441","columnName":"create_time","columnComment":"创建时间","columnType":"datetime","javaType":"Date","javaField":"createTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"datetime","dictType":"","sort":8,"capJavaField":"CreateTime","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:45:26","columnId":"1917415163012124676","tableId":"1917396113531965441","columnName":"update_by","columnComment":"更新者","columnType":"bigint","javaType":"Long","javaField":"updateBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":9,"capJavaField":"UpdateBy","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:45:26","columnId":"1917415163012124677","tableId":"1917396113531965441","columnName":"update_time","columnComment":"更新时间","columnType":"datetime","javaType":"Date","javaField":"updateTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"datetime","dictType":"","sort":10,"capJavaField":"UpdateTime","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false}],"options":"{\"treeCode\":\"danger_id\",\"treeName\":\"name\",\"treeParentCode\":\"pre_id\",\"parentMenuId\":1}","remark":null,"treeCode":"danger_id","treeParentCode":"pre_id","treeName":"name","menuIds":null,"parentMenuId":1,"parentMenuName":null,"tree":true,"crud":false,"params":{"treeCode":"danger_id","treeName":"name","treeParentCode":"pre_id","parentMenuId":1}}]
2025-04-30 11:49:14 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[PUT /tool/gen],耗时:[573]毫秒
2025-04-30 11:49:14 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-04-30 11:49:14 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 11:49:14 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[2]毫秒
2025-04-30 11:49:14 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[55]毫秒
2025-04-30 11:49:25 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/1917396113531965441],无参数
2025-04-30 11:49:25 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/1917396113531965441],耗时:[89]毫秒
2025-04-30 11:49:25 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-04-30 11:49:25 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[45]毫秒
2025-04-30 11:49:25 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-04-30 11:49:25 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[22]毫秒
2025-04-30 11:49:26 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[PUT /tool/gen],参数类型[json],参数:[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"tableId":"1917396113531965441","dataName":"master","tableName":"t_danger_list","tableComment":"危大工程清单","subTableName":"","subTableFkName":"","className":"DangerList","tplCategory":"tree","packageName":"org.dromara.system","moduleName":"system","businessName":"dangerList","functionName":"dangerList","functionAuthor":"luzuda","genType":"0","genPath":"/","pkColumn":null,"columns":[{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:49:14","columnId":"1917396114765090818","tableId":"1917396113531965441","columnName":"danger_id","columnComment":"主键","columnType":"bigint","javaType":"Long","javaField":"dangerId","isPk":"1","isIncrement":"1","isRequired":"1","isInsert":null,"isEdit":"0","isList":"0","isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":1,"capJavaField":"DangerId","query":false,"increment":true,"required":true,"list":false,"pk":true,"insert":false,"edit":false,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:49:14","columnId":"1917396114828005377","tableId":"1917396113531965441","columnName":"name","columnComment":"名称","columnType":"varchar(255)","javaType":"String","javaField":"name","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"LIKE","htmlType":"input","dictType":"","sort":2,"capJavaField":"Name","query":true,"increment":true,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:49:14","columnId":"1917396114828005378","tableId":"1917396113531965441","columnName":"pre_id","columnComment":"父级id","columnType":"bigint","javaType":"Long","javaField":"preId","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"input","dictType":"","sort":3,"capJavaField":"PreId","query":true,"increment":true,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:49:14","columnId":"1917396114828005379","tableId":"1917396113531965441","columnName":"type","columnComment":"危大类型标识 1危大 2超危大","columnType":"tinyint","javaType":"Long","javaField":"type","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"danger_list_type","sort":4,"capJavaField":"Type","query":true,"increment":true,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:49:14","columnId":"1917396114828005380","tableId":"1917396113531965441","columnName":"remark","columnComment":"备注","columnType":"varchar(255)","javaType":"String","javaField":"remark","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":5,"capJavaField":"Remark","query":false,"increment":true,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":true},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:49:14","columnId":"1917415163012124673","tableId":"1917396113531965441","columnName":"create_dept","columnComment":"创建部门","columnType":"bigint","javaType":"Long","javaField":"createDept","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":6,"capJavaField":"CreateDept","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:49:14","columnId":"1917415163012124674","tableId":"1917396113531965441","columnName":"create_by","columnComment":"创建者","columnType":"bigint","javaType":"Long","javaField":"createBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":7,"capJavaField":"CreateBy","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:49:14","columnId":"1917415163012124675","tableId":"1917396113531965441","columnName":"create_time","columnComment":"创建时间","columnType":"datetime","javaType":"Date","javaField":"createTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"datetime","dictType":"","sort":8,"capJavaField":"CreateTime","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:49:14","columnId":"1917415163012124676","tableId":"1917396113531965441","columnName":"update_by","columnComment":"更新者","columnType":"bigint","javaType":"Long","javaField":"updateBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":9,"capJavaField":"UpdateBy","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:49:14","columnId":"1917415163012124677","tableId":"1917396113531965441","columnName":"update_time","columnComment":"更新时间","columnType":"datetime","javaType":"Date","javaField":"updateTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"datetime","dictType":"","sort":10,"capJavaField":"UpdateTime","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false}],"options":"{\"treeCode\":\"danger_id\",\"treeName\":\"name\",\"treeParentCode\":\"pre_id\",\"parentMenuId\":1}","remark":null,"treeCode":"danger_id","treeParentCode":"pre_id","treeName":"name","menuIds":null,"parentMenuId":1,"parentMenuName":null,"tree":true,"crud":false,"params":{"treeCode":"danger_id","treeName":"name","treeParentCode":"pre_id","parentMenuId":1}}]
2025-04-30 11:49:27 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[PUT /tool/gen],耗时:[563]毫秒
2025-04-30 11:49:27 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-04-30 11:49:27 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 11:49:27 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[1]毫秒
2025-04-30 11:49:28 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[57]毫秒
2025-04-30 11:50:37 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-04-30 11:50:37 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/1917396113531965441],无参数
2025-04-30 11:50:37 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[69]毫秒
2025-04-30 11:50:37 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/1917396113531965441],耗时:[89]毫秒
2025-04-30 11:50:38 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-04-30 11:50:38 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[43]毫秒
2025-04-30 11:50:48 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[PUT /tool/gen],参数类型[json],参数:[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"tableId":"1917396113531965441","dataName":"master","tableName":"t_danger_list","tableComment":"危大工程清单","subTableName":"","subTableFkName":"","className":"DangerList","tplCategory":"tree","packageName":"org.dromara.system","moduleName":"system","businessName":"dangerList","functionName":"dangerList","functionAuthor":"luzuda","genType":"0","genPath":"/","pkColumn":null,"columns":[{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:49:27","columnId":"1917396114765090818","tableId":"1917396113531965441","columnName":"danger_id","columnComment":"主键","columnType":"bigint","javaType":"Long","javaField":"dangerId","isPk":"1","isIncrement":"1","isRequired":"1","isInsert":null,"isEdit":"0","isList":"0","isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":1,"capJavaField":"DangerId","query":false,"increment":true,"required":true,"list":false,"pk":true,"insert":false,"edit":false,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:49:27","columnId":"1917396114828005377","tableId":"1917396113531965441","columnName":"name","columnComment":"名称","columnType":"varchar(255)","javaType":"String","javaField":"name","isPk":"0","isIncrement":"1","isRequired":"1","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"LIKE","htmlType":"input","dictType":"","sort":2,"capJavaField":"Name","query":true,"increment":true,"required":true,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:49:27","columnId":"1917396114828005378","tableId":"1917396113531965441","columnName":"pre_id","columnComment":"父级id","columnType":"bigint","javaType":"Long","javaField":"preId","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"input","dictType":"","sort":3,"capJavaField":"PreId","query":true,"increment":true,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:49:27","columnId":"1917396114828005379","tableId":"1917396113531965441","columnName":"type","columnComment":"危大类型标识 1危大 2超危大","columnType":"tinyint","javaType":"Long","javaField":"type","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":"1","queryType":"EQ","htmlType":"select","dictType":"danger_list_type","sort":4,"capJavaField":"Type","query":true,"increment":true,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 09:50:32","updateBy":1,"updateTime":"2025-04-30 11:49:27","columnId":"1917396114828005380","tableId":"1917396113531965441","columnName":"remark","columnComment":"备注","columnType":"varchar(255)","javaType":"String","javaField":"remark","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":"1","isEdit":"1","isList":"1","isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":5,"capJavaField":"Remark","query":false,"increment":true,"required":false,"list":true,"pk":false,"insert":true,"edit":true,"superColumn":false,"usableColumn":true},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:49:27","columnId":"1917415163012124673","tableId":"1917396113531965441","columnName":"create_dept","columnComment":"创建部门","columnType":"bigint","javaType":"Long","javaField":"createDept","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":6,"capJavaField":"CreateDept","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":false,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:49:27","columnId":"1917415163012124674","tableId":"1917396113531965441","columnName":"create_by","columnComment":"创建者","columnType":"bigint","javaType":"Long","javaField":"createBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":7,"capJavaField":"CreateBy","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:49:27","columnId":"1917415163012124675","tableId":"1917396113531965441","columnName":"create_time","columnComment":"创建时间","columnType":"datetime","javaType":"Date","javaField":"createTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"datetime","dictType":"","sort":8,"capJavaField":"CreateTime","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:49:27","columnId":"1917415163012124676","tableId":"1917396113531965441","columnName":"update_by","columnComment":"更新者","columnType":"bigint","javaType":"Long","javaField":"updateBy","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"input","dictType":"","sort":9,"capJavaField":"UpdateBy","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false},{"createDept":103,"createBy":1,"createTime":"2025-04-30 11:06:13","updateBy":1,"updateTime":"2025-04-30 11:49:27","columnId":"1917415163012124677","tableId":"1917396113531965441","columnName":"update_time","columnComment":"更新时间","columnType":"datetime","javaType":"Date","javaField":"updateTime","isPk":"0","isIncrement":"1","isRequired":"0","isInsert":null,"isEdit":null,"isList":null,"isQuery":null,"queryType":"EQ","htmlType":"datetime","dictType":"","sort":10,"capJavaField":"UpdateTime","query":false,"increment":true,"required":false,"list":false,"pk":false,"insert":false,"edit":false,"superColumn":true,"usableColumn":false}],"options":"{\"treeCode\":\"danger_id\",\"treeName\":\"name\",\"treeParentCode\":\"pre_id\",\"parentMenuId\":1}","remark":null,"treeCode":"danger_id","treeParentCode":"pre_id","treeName":"name","menuIds":null,"parentMenuId":1,"parentMenuName":null,"tree":true,"crud":false,"params":{"treeCode":"danger_id","treeName":"name","treeParentCode":"pre_id","parentMenuId":1}}]
2025-04-30 11:50:49 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[PUT /tool/gen],耗时:[555]毫秒
2025-04-30 11:50:49 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-04-30 11:50:49 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 11:50:49 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[1]毫秒
2025-04-30 11:50:49 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[61]毫秒
2025-04-30 11:53:17 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.4.0
2025-04-30 11:53:17 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-04-30 11:53:17 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-04-30 11:53:17 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.4.0
2025-04-30 11:53:47 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-04-30 11:53:47 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-04-30 11:53:47 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-04-30 11:53:47 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-04-30 11:53:47 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-04-30 11:53:47 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-04-30 11:53:47 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-04-30 11:53:47 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-04-30 11:53:51 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-04-30 11:53:51 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 23480 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-30 11:53:51 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-04-30 11:53:58 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-30 11:53:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-04-30 11:53:58 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-04-30 11:53:58 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@4f7da3a2
2025-04-30 11:53:58 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-04-30 11:53:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-04-30 11:53:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-04-30 11:54:00 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-04-30 11:54:00 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-04-30 11:54:01 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-04-30 11:54:01 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-04-30 11:54:02 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\"treeCode\":\"danger_id\",\"treeName\":\"name\",\"treeParentCode\":\"pre_id\",\"parentMenuId\":1}","remark":null,"treeCode":"danger_id","treeParentCode":"pre_id","treeName":"name","menuIds":null,"parentMenuId":1,"parentMenuName":null,"crud":false,"tree":true,"params":{"treeCode":"danger_id","treeName":"name","treeParentCode":"pre_id","parentMenuId":1}}]
2025-04-30 11:54:18 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[PUT /tool/gen],耗时:[713]毫秒
2025-04-30 11:54:19 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-04-30 11:54:19 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 11:54:19 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[7]毫秒
2025-04-30 11:54:19 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[67]毫秒
2025-04-30 11:54:22 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/preview/1917396113531965441],无参数
2025-04-30 11:54:22 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/preview/1917396113531965441],耗时:[251]毫秒
2025-04-30 12:00:38 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-04-30 12:00:38 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[146]毫秒
2025-04-30 12:00:38 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-04-30 12:00:38 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[36]毫秒
2025-04-30 12:00:38 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 12:00:38 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[1]毫秒
2025-04-30 12:00:38 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-04-30 12:00:38 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 12:00:38 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[0]毫秒
2025-04-30 12:00:38 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJGZ0JvVUZKczJiQ1hkTWo5bmFLOGpMOGFVUm5udXdRWCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.s4mR-f5tHmvKFrpNOzlhy1M4U7wAwaXeJsvvzGsc238"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 12:00:38 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[52]毫秒
2025-04-30 12:00:47 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/preview/1917396113531965441],无参数
2025-04-30 12:00:47 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/preview/1917396113531965441],耗时:[105]毫秒
2025-04-30 12:00:54 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/1917396113531965441],无参数
2025-04-30 12:00:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/1917396113531965441],耗时:[92]毫秒
2025-04-30 12:00:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-04-30 12:00:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[48]毫秒
2025-04-30 12:00:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-04-30 12:00:55 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[24]毫秒
2025-04-30 12:00:57 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 12:00:57 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[93]毫秒
2025-04-30 12:00:58 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-04-30 12:00:58 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[2]毫秒
2025-04-30 12:01:02 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-04-30 12:01:02 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[68]毫秒
2025-04-30 12:01:02 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-04-30 12:01:02 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-04-30 12:01:02 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 12:01:02 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 12:01:02 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-04-30 12:01:02 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 12:01:02 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[0]毫秒
2025-04-30 12:01:02 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJGZ0JvVUZKczJiQ1hkTWo5bmFLOGpMOGFVUm5udXdRWCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.s4mR-f5tHmvKFrpNOzlhy1M4U7wAwaXeJsvvzGsc238"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 12:01:02 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[53]毫秒
2025-04-30 12:01:05 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/preview/1917396113531965441],无参数
2025-04-30 12:01:05 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/preview/1917396113531965441],耗时:[97]毫秒
2025-04-30 12:01:09 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /tool/gen/preview/1917396113531965441],无参数
2025-04-30 12:01:09 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /tool/gen/preview/1917396113531965441],耗时:[98]毫秒
2025-04-30 16:20:59 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-04-30 16:20:59 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-04-30 16:20:59 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[2]毫秒
2025-04-30 16:20:59 [XNIO-1 task-4] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJ1UHNBV2tydEtVWVRPVmtaOUxGb1M5ekN0cWVUTHpOMCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.8ZwY2YNOWuLGHYXz0q3MS9I1Yof--NdmD4MI3KqCht4
2025-04-30 16:20:59 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[14]毫秒
2025-04-30 16:20:59 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 16:20:59 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-04-30 16:20:59 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 16:20:59 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:***************:'
2025-04-30 16:20:59 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[185]毫秒
2025-04-30 16:21:11 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-04-30 16:21:11 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 16:21:11 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[1]毫秒
2025-04-30 16:21:11 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:***************:'
2025-04-30 16:21:11 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[23]毫秒
2025-04-30 16:21:36 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-04-30 16:21:36 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 16:21:36 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 16:21:36 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/auth/code:***************:'
2025-04-30 16:21:36 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[6]毫秒
2025-04-30 16:32:51 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 16:32:51 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-04-30 16:32:51 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 16:32:51 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:***************:'
2025-04-30 16:32:51 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[7]毫秒
2025-04-30 16:32:56 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"1f359b85b1554a169874ab4a404034e5","code":"me","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-04-30 16:32:56 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[24]毫秒
2025-04-30 16:32:56 [schedule-pool-1] INFO  o.d.s.s.i.SysLogininforServiceImpl - [***************]内网IP[admin][Error][验证码错误]
2025-04-30 16:32:56 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-04-30 16:32:56 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:***************:'
2025-04-30 16:32:56 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[5]毫秒
2025-04-30 16:33:02 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"4bc8ddcbad3744c39dcba186ecafd90d","code":"cb6b","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-04-30 16:33:03 [schedule-pool-2] INFO  o.d.s.s.i.SysLogininforServiceImpl - [***************]内网IP[admin][Success][登录成功]
2025-04-30 16:33:03 [XNIO-1 task-4] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzeUp4dnVXUXhTd3dFWlpTT3VTQ2N3ZHpPWm43aHV4ZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.n4qjYxGuL_pJBVXfJ5KOk1bxqARhMBtT_4fi4Al7wNQ
2025-04-30 16:33:03 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[278]毫秒
2025-04-30 16:33:03 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-04-30 16:33:03 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[48]毫秒
2025-04-30 16:33:03 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-04-30 16:33:03 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[33]毫秒
2025-04-30 16:33:04 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 16:33:04 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[1]毫秒
2025-04-30 16:33:04 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzeUp4dnVXUXhTd3dFWlpTT3VTQ2N3ZHpPWm43aHV4ZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.n4qjYxGuL_pJBVXfJ5KOk1bxqARhMBtT_4fi4Al7wNQ"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 16:33:08 [schedule-pool-1] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录RuoYi-Vue-Plus后台管理系统
2025-04-30 16:33:08 [redisson-3-2] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录RuoYi-Vue-Plus后台管理系统
2025-04-30 16:33:08 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJGZ0JvVUZKczJiQ1hkTWo5bmFLOGpMOGFVUm5udXdRWCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.s4mR-f5tHmvKFrpNOzlhy1M4U7wAwaXeJsvvzGsc238"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 16:33:08 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[9]毫秒
2025-04-30 16:42:36 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-04-30 16:42:36 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[72]毫秒
2025-04-30 16:42:36 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-04-30 16:42:36 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-04-30 16:42:36 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 16:42:36 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 16:42:36 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzeUp4dnVXUXhTd3dFWlpTT3VTQ2N3ZHpPWm43aHV4ZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.n4qjYxGuL_pJBVXfJ5KOk1bxqARhMBtT_4fi4Al7wNQ"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 16:44:51 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-04-30 16:44:51 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[72]毫秒
2025-04-30 16:44:51 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-04-30 16:44:51 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-04-30 16:44:51 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 16:44:51 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 16:44:51 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzeUp4dnVXUXhTd3dFWlpTT3VTQ2N3ZHpPWm43aHV4ZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.n4qjYxGuL_pJBVXfJ5KOk1bxqARhMBtT_4fi4Al7wNQ"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 16:50:41 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-04-30 16:50:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 16:50:41 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 16:50:41 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:***************:'
2025-04-30 16:50:41 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[7]毫秒
2025-04-30 16:50:52 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"c5a7caf61d544201ac10b31753b6bc71","code":"h7po","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-04-30 16:50:53 [schedule-pool-3] INFO  o.d.s.s.i.SysLogininforServiceImpl - [***************]内网IP[admin][Success][登录成功]
2025-04-30 16:50:53 [XNIO-1 task-4] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI4b2NDNjRMQ1l3NkcxR3VsdW1Sd1ZjQndIaDZjTEFWbiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.XP77ik111NAKjV1aHSmROuk8iOOaHUkm-t3IWcl6Qto
2025-04-30 16:50:53 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[233]毫秒
2025-04-30 16:50:53 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-04-30 16:50:53 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[44]毫秒
2025-04-30 16:50:53 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-04-30 16:50:53 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-04-30 16:50:53 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI4b2NDNjRMQ1l3NkcxR3VsdW1Sd1ZjQndIaDZjTEFWbiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.XP77ik111NAKjV1aHSmROuk8iOOaHUkm-t3IWcl6Qto"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 16:50:53 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 16:50:53 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 16:50:58 [schedule-pool-2] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录RuoYi-Vue-Plus后台管理系统
2025-04-30 16:50:58 [redisson-3-3] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录RuoYi-Vue-Plus后台管理系统
2025-04-30 16:51:37 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-04-30 16:51:37 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[67]毫秒
2025-04-30 16:51:37 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-04-30 16:51:37 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-04-30 16:51:37 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 16:51:37 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 16:51:37 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI4b2NDNjRMQ1l3NkcxR3VsdW1Sd1ZjQndIaDZjTEFWbiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.XP77ik111NAKjV1aHSmROuk8iOOaHUkm-t3IWcl6Qto"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 16:51:44 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-04-30 16:51:44 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-04-30 16:51:44 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[12]毫秒
2025-04-30 16:51:44 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[12]毫秒
2025-04-30 16:51:44 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-04-30 16:51:44 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[48]毫秒
2025-04-30 16:52:09 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/social/list],无参数
2025-04-30 16:52:09 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /monitor/online],无参数
2025-04-30 16:52:09 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/profile],无参数
2025-04-30 16:52:09 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_device_type],无参数
2025-04-30 16:52:09 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_device_type],耗时:[2]毫秒
2025-04-30 16:52:09 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /monitor/online],耗时:[8]毫秒
2025-04-30 16:52:09 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/social/list],耗时:[48]毫秒
2025-04-30 16:52:09 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/profile],耗时:[120]毫秒
2025-04-30 16:53:08 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /monitor/online/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 16:53:08 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /monitor/online/list],耗时:[14]毫秒
2025-04-30 16:55:49 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-04-30 16:55:49 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[1]毫秒
2025-04-30 16:55:49 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 16:55:49 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[109]毫秒
2025-04-30 16:55:51 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-04-30 16:55:51 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[1]毫秒
2025-04-30 16:55:51 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-04-30 16:55:51 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[68]毫秒
2025-04-30 17:02:22 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-04-30 17:02:22 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:***************:'
2025-04-30 17:02:22 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 17:02:22 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 17:02:22 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[6]毫秒
2025-04-30 17:02:28 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"30a3bfec407b4990ac5df0e4e776e94c","code":"xeph","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-04-30 17:02:29 [schedule-pool-4] INFO  o.d.s.s.i.SysLogininforServiceImpl - [***************]内网IP[admin][Success][登录成功]
2025-04-30 17:02:29 [XNIO-1 task-4] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJpMVVEcG1KRUI5WlpuMnhyZ0lZUzdpQ25TRHFPZnpncCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.jfeRErlL34_tpW_wVZ-xic3uQptcUkEyglQW24m-vH4
2025-04-30 17:02:29 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[220]毫秒
2025-04-30 17:02:29 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-04-30 17:02:29 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[47]毫秒
2025-04-30 17:02:29 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-04-30 17:02:29 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-04-30 17:02:29 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJpMVVEcG1KRUI5WlpuMnhyZ0lZUzdpQ25TRHFPZnpncCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.jfeRErlL34_tpW_wVZ-xic3uQptcUkEyglQW24m-vH4"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 17:02:29 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 17:02:29 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 17:02:34 [schedule-pool-1] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录RuoYi-Vue-Plus后台管理系统
2025-04-30 17:02:34 [redisson-3-4] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录RuoYi-Vue-Plus后台管理系统
2025-04-30 17:08:02 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/danger_list_type],无参数
2025-04-30 17:08:02 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dangerList/list],无参数
2025-04-30 17:08:02 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/danger_list_type],耗时:[1]毫秒
2025-04-30 17:08:02 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dangerList/list],耗时:[56]毫秒
2025-04-30 17:14:23 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-04-30 17:14:23 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[69]毫秒
2025-04-30 17:14:23 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-04-30 17:14:23 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-04-30 17:14:23 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 17:14:23 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 17:14:23 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJpMVVEcG1KRUI5WlpuMnhyZ0lZUzdpQ25TRHFPZnpncCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.jfeRErlL34_tpW_wVZ-xic3uQptcUkEyglQW24m-vH4"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 17:14:36 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-04-30 17:14:36 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-04-30 17:14:36 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[2]毫秒
2025-04-30 17:14:36 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[2]毫秒
2025-04-30 17:14:37 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageSize":["10"],"pageNum":["1"]}]
2025-04-30 17:14:37 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-04-30 17:14:37 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-04-30 17:14:37 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[22]毫秒
2025-04-30 17:14:37 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[74]毫秒
2025-04-30 17:14:37 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[95]毫秒
2025-04-30 17:16:48 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-04-30 17:16:48 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-04-30 17:16:48 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[1]毫秒
2025-04-30 17:16:48 [XNIO-1 task-4] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJGZ0JvVUZKczJiQ1hkTWo5bmFLOGpMOGFVUm5udXdRWCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.s4mR-f5tHmvKFrpNOzlhy1M4U7wAwaXeJsvvzGsc238
2025-04-30 17:16:48 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[4]毫秒
2025-04-30 17:16:48 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 17:16:48 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-04-30 17:16:48 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[1]毫秒
2025-04-30 17:16:48 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-04-30 17:16:48 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[7]毫秒
2025-04-30 17:16:53 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-04-30 17:16:53 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-04-30 17:16:53 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[14]毫秒
2025-04-30 17:16:58 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"2990c67a6b5946c3b2a0a15a6a6b6e82","code":"lbai","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-04-30 17:16:58 [schedule-pool-5] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Error][验证码错误]
2025-04-30 17:16:58 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[2]毫秒
2025-04-30 17:16:58 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-04-30 17:16:58 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-04-30 17:16:58 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[21]毫秒
2025-04-30 17:17:11 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"4e52d078b1e24ef0baf016b569aac482","code":"knwg","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-04-30 17:17:11 [schedule-pool-3] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-04-30 17:17:11 [XNIO-1 task-4] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI2ZkhMTVc3bExtQ3g0R2hETE92UERxQ0U1MjVGZEhTZSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.JzVMSe0iq0hqMk-mLHOz-ui_M6qJygeqlpmosszdK5E
2025-04-30 17:17:11 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[212]毫秒
2025-04-30 17:17:12 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-04-30 17:17:12 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[47]毫秒
2025-04-30 17:17:12 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-04-30 17:17:12 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-04-30 17:17:12 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 17:17:12 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 17:17:13 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI2ZkhMTVc3bExtQ3g0R2hETE92UERxQ0U1MjVGZEhTZSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.JzVMSe0iq0hqMk-mLHOz-ui_M6qJygeqlpmosszdK5E"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 17:17:16 [schedule-pool-6] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录RuoYi-Vue-Plus后台管理系统
2025-04-30 17:17:16 [redisson-3-1] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录RuoYi-Vue-Plus后台管理系统
2025-04-30 17:17:16 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI4b2NDNjRMQ1l3NkcxR3VsdW1Sd1ZjQndIaDZjTEFWbiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.XP77ik111NAKjV1aHSmROuk8iOOaHUkm-t3IWcl6Qto"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 17:17:16 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[0]毫秒
2025-04-30 17:17:25 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-04-30 17:17:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-04-30 17:17:25 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[0]毫秒
2025-04-30 17:17:25 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI2ZkhMTVc3bExtQ3g0R2hETE92UERxQ0U1MjVGZEhTZSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.JzVMSe0iq0hqMk-mLHOz-ui_M6qJygeqlpmosszdK5E"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 17:17:25 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[1]毫秒
2025-04-30 17:17:25 [schedule-pool-2] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-04-30 17:17:25 [XNIO-1 task-4] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI2ZkhMTVc3bExtQ3g0R2hETE92UERxQ0U1MjVGZEhTZSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.JzVMSe0iq0hqMk-mLHOz-ui_M6qJygeqlpmosszdK5E
2025-04-30 17:17:25 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[14]毫秒
2025-04-30 17:17:26 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-04-30 17:17:26 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 17:17:26 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[1]毫秒
2025-04-30 17:17:26 [XNIO-1 task-4] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 6, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-04-30 17:17:26 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[17]毫秒
2025-04-30 17:17:31 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"e6a38cf8bf9c41ab81df53d8da5de1bf","code":"yeyn","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-04-30 17:17:41 [schedule-pool-7] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-04-30 17:17:41 [XNIO-1 task-4] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJBSEFsamNFcTVKeWpYa3N5bjNuZ29lR3VXWU53S3Y0eCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.tfBnSdDJ1a3Sth_2CwXAbo3CUY7hUAp9HSLZaZE4feM
2025-04-30 17:17:41 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[10500]毫秒
2025-04-30 17:17:41 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-04-30 17:17:41 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[48]毫秒
2025-04-30 17:17:42 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-04-30 17:17:42 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-04-30 17:17:42 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 17:17:42 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 17:17:42 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJBSEFsamNFcTVKeWpYa3N5bjNuZ29lR3VXWU53S3Y0eCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.tfBnSdDJ1a3Sth_2CwXAbo3CUY7hUAp9HSLZaZE4feM"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 17:17:46 [schedule-pool-8] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录RuoYi-Vue-Plus后台管理系统
2025-04-30 17:17:46 [redisson-3-2] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录RuoYi-Vue-Plus后台管理系统
2025-04-30 17:25:07 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-04-30 17:25:07 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[73]毫秒
2025-04-30 17:25:07 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-04-30 17:25:07 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-04-30 17:25:07 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 17:25:07 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 17:25:07 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJpMVVEcG1KRUI5WlpuMnhyZ0lZUzdpQ25TRHFPZnpncCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.jfeRErlL34_tpW_wVZ-xic3uQptcUkEyglQW24m-vH4"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 17:25:36 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-04-30 17:25:36 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-04-30 17:25:36 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[0]毫秒
2025-04-30 17:25:36 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJBSEFsamNFcTVKeWpYa3N5bjNuZ29lR3VXWU53S3Y0eCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.tfBnSdDJ1a3Sth_2CwXAbo3CUY7hUAp9HSLZaZE4feM"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 17:25:36 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[0]毫秒
2025-04-30 17:25:36 [XNIO-1 task-2] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJBSEFsamNFcTVKeWpYa3N5bjNuZ29lR3VXWU53S3Y0eCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.tfBnSdDJ1a3Sth_2CwXAbo3CUY7hUAp9HSLZaZE4feM
2025-04-30 17:25:36 [schedule-pool-4] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-04-30 17:25:36 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[12]毫秒
2025-04-30 17:25:36 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-04-30 17:25:36 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 17:25:36 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[1]毫秒
2025-04-30 17:25:36 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-04-30 17:25:36 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[25]毫秒
2025-04-30 17:25:40 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"admin","password":"admin123","rememberMe":false,"uuid":"1e7112b198144b5aa7f49e828c83ac29","code":"jhg3","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-04-30 17:25:56 [schedule-pool-1] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-04-30 17:25:56 [XNIO-1 task-2] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJHcWNNWWJtbEo0bmc3cFlGUlZSS3BBN0xUT01nMllieiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.i4_NI9-qg5DQRyYirdex8CZTmy3VND_uW-YtgNrHdiM
2025-04-30 17:25:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[15615]毫秒
2025-04-30 17:25:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-04-30 17:25:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[47]毫秒
2025-04-30 17:25:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-04-30 17:25:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-04-30 17:25:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 17:25:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 17:25:57 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJHcWNNWWJtbEo0bmc3cFlGUlZSS3BBN0xUT01nMllieiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.i4_NI9-qg5DQRyYirdex8CZTmy3VND_uW-YtgNrHdiM"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 17:26:01 [schedule-pool-9] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录RuoYi-Vue-Plus后台管理系统
2025-04-30 17:26:01 [redisson-3-3] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录RuoYi-Vue-Plus后台管理系统
2025-04-30 17:26:02 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-04-30 17:26:02 [schedule-pool-5] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-04-30 17:26:02 [XNIO-1 task-2] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJHcWNNWWJtbEo0bmc3cFlGUlZSS3BBN0xUT01nMllieiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.i4_NI9-qg5DQRyYirdex8CZTmy3VND_uW-YtgNrHdiM
2025-04-30 17:26:02 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[6]毫秒
2025-04-30 17:26:02 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-04-30 17:26:02 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[0]毫秒
2025-04-30 17:26:02 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJHcWNNWWJtbEo0bmc3cFlGUlZSS3BBN0xUT01nMllieiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.i4_NI9-qg5DQRyYirdex8CZTmy3VND_uW-YtgNrHdiM"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-04-30 17:26:02 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[0]毫秒
2025-04-30 17:26:03 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-04-30 17:26:03 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-04-30 17:26:03 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-04-30 17:26:03 [XNIO-1 task-5] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-04-30 17:26:03 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[20]毫秒
2025-04-30 17:26:03 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-04-30 17:26:03 [XNIO-1 task-5] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-04-30 17:26:04 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[4]毫秒
2025-04-30 17:26:40 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.4.0
2025-04-30 17:26:40 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-04-30 17:26:40 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-04-30 17:26:40 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.4.0
2025-04-30 17:27:10 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-04-30 17:27:10 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-04-30 17:27:10 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-04-30 17:27:10 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-04-30 17:27:10 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-04-30 17:27:10 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-04-30 17:27:10 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-04-30 17:27:10 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-04-30 17:27:15 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-04-30 17:27:15 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 14432 (D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-30 17:27:15 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-04-30 17:27:20 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-30 17:27:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-04-30 17:27:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-04-30 17:27:21 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@63e81ac6
2025-04-30 17:27:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-04-30 17:27:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-04-30 17:27:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-04-30 17:27:23 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-04-30 17:27:23 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-04-30 17:27:23 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-04-30 17:27:23 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-04-30 17:27:24 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-30 17:28:22 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-04-30 17:28:26 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-30 17:28:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-04-30 17:28:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-04-30 17:28:27 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2396dc4e
2025-04-30 17:28:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-04-30 17:28:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-04-30 17:28:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-04-30 17:28:29 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-04-30 17:28:29 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-04-30 17:28:29 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-04-30 17:28:29 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-04-30 17:28:30 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\target\classes started by about in D:\Work\job\municipal-safety\municipal-safety-api)
2025-04-30 17:32:47 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-04-30 17:32:51 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-30 17:32:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-04-30 17:32:51 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-04-30 17:32:52 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@54c697c
2025-04-30 17:32:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-04-30 17:32:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-04-30 17:32:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-04-30 17:32:53 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-04-30 17:32:53 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-04-30 17:32:54 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-04-30 17:32:54 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-04-30 17:32:55 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>
{"doc": " 项目同步Service接口\n\n <AUTHOR>\n @date 2025-07-28\n", "fields": [], "enumConstants": [], "methods": [{"name": "syncProjectByPermitNo", "paramTypes": ["java.lang.String"], "doc": " 根据施工许可证编号同步项目信息\n\n @param constructionPermitNo 施工许可证编号\n @return 同步结果\n"}, {"name": "processSyncData", "paramTypes": ["org.dromara.projects.domain.dto.sync.SyncResponseDto", "java.lang.String"], "doc": " 处理同步数据\n\n @param syncData             同步响应数据\n @param constructionPermitNo 施工许可证编号\n @return 处理后的项目信息\n"}, {"name": "fetchExternalData", "paramTypes": ["java.lang.String"], "doc": " 获取外部API数据\n\n @param constructionPermitNo 施工许可证编号\n @return 外部API响应数据\n"}, {"name": "syncProjects", "paramTypes": ["java.lang.String"], "doc": " 根据施工许可证编号同步项目信息 （施工方账号审核通过 同步所有信息）\n @return 同步结果\n"}], "constructors": []}
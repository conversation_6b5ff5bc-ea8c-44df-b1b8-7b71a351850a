export interface AttRuleVO {
        /**
         * 主键id
         */
            id: string | number;

        /**
         * 规则类型（0：模板规则，1：普通规则）
         */
            ruleType: number;

        /**
         * 人员类型
         */
            personType: string;

        /**
         * 设置打卡时间（支持多时段，如午休分段）。
         */
            checkTime: string;

        /**
         * 弹性时间：允许迟到/早退的宽限时间（如上班后30分钟内打卡不算迟到）
         */
            elasticTime: number;

        /**
         * 预警机制：根据对应的漏卡次数设置（黄色、橙色、红色）
         */
            warning: string;

        /**
         * 外勤打卡：0：关，1：开
         */
            fieldCheck: number;

        /**
         * 自定义内容
         */
            content: string;

        /**
         * 是否删除。0-否，1-是
         */
            deleted: number;

}

export interface AttRuleForm extends BaseEntity {
        /**
         * 主键id
         */
            id?: string | number;

        /**
         * 规则类型（0：模板规则，1：普通规则）
         */
            ruleType?: number;

        /**
         * 人员类型
         */
            personType?: string;

        /**
         * 设置打卡时间（支持多时段，如午休分段）。
         */
            checkTime?: string;

        /**
         * 弹性时间：允许迟到/早退的宽限时间（如上班后30分钟内打卡不算迟到）
         */
            elasticTime?: number;

        /**
         * 预警机制：根据对应的漏卡次数设置（黄色、橙色、红色）
         */
            warning?: any;

        /**
         * 外勤打卡：0：关，1：开
         */
            fieldCheck?: string;

        /**
         * 自定义内容
         */
            content?: string;

        /**
         * 是否删除。0-否，1-是
         */
            deleted?: number;
        /**
         * 项目类型。0-否，1-是
         */
            projectId?: string

}

export interface AttRuleQuery extends PageQuery {

        /**
         * 规则类型（0：模板规则，1：普通规则）
         */
            ruleType?: number;

        /**
         * 人员类型
         */
            personType?: string;

        /**
         * 设置打卡时间（支持多时段，如午休分段）。
         */
            checkTime?: string;

        /**
         * 弹性时间：允许迟到/早退的宽限时间（如上班后30分钟内打卡不算迟到）
         */
            elasticTime?: number;

        /**
         * 预警机制：根据对应的漏卡次数设置（黄色、橙色、红色）
         */
            warning?: string;

        /**
         * 外勤打卡：0：关，1：开
         */
            fieldCheck?: number;

        /**
         * 自定义内容
         */
            content?: string;

        /**
         * 是否删除。0-否，1-是
         */
            deleted?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}




{"doc": " 智能隐患分析任务Service业务层处理\n\n <AUTHOR>\n @date 2025-05-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询智能隐患分析任务\n\n @param taskId 主键\n @return 智能隐患分析任务\n"}, {"name": "queryByIdNew", "paramTypes": ["java.lang.Long"], "doc": " 查询智能隐患分析任务\n\n @param taskId 主键\n @return 智能隐患分析任务\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询智能隐患分析任务列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 智能隐患分析任务分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksBo"], "doc": " 查询符合条件的智能隐患分析任务列表\n\n @param bo 查询条件\n @return 智能隐患分析任务列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksBo"], "doc": " 新增智能隐患分析任务\n\n @param bo 智能隐患分析任务\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.ai.domain.bo.AiHazAnalysisTasksBo"], "doc": " 修改智能隐患分析任务\n\n @param bo 智能隐患分析任务\n @return 是否修改成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.ai.domain.AiHazAnalysisTasks"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除智能隐患分析任务信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}, {"name": "processAiAnalysisResult", "paramTypes": ["org.dromara.ai.domain.dto.AiAnalysisResultDto"], "doc": " 处理AI分析回调结果\n\n @param aiResult AI分析结果\n @return 是否处理成功\n"}, {"name": "submitToAiAnalysis", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Long"], "doc": " 提交到外部AI分析服务\n\n @param taskId   任务ID\n @param imageUrl 图片URL\n @return 分析任务详情\n"}, {"name": "getParentName", "paramTypes": ["java.lang.String"], "doc": " 根据dangerId获取父级名称\n\n @param dangerId 涉危工程清单ID\n @return 父级名称\n"}, {"name": "queryParentNameById", "paramTypes": ["java.lang.Long"], "doc": " 根据单个dangerId查询父级名称\n\n @param dangerId 单个涉危工程清单ID\n @return 父级名称\n"}, {"name": "queryNewPageList", "paramTypes": ["org.dromara.ai.domain.dto.AiHazAnalysisTasksDto", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " APP端分页查询隐患列表\n\n @param dto       查询条件\n @param pageQuery 分页参数\n @return 分页结果\n"}], "constructors": []}
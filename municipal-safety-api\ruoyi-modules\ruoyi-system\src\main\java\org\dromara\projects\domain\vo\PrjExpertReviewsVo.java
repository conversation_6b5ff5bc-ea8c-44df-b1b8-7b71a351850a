package org.dromara.projects.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.projects.domain.PrjExpertReviews;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * [项目管理] 记录专项施工方案专家论证会议，包含利害关系预警视图对象 prj_expert_reviews
 *
 * <AUTHOR> Li
 * @date 2025-06-05
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrjExpertReviews.class)
public class PrjExpertReviewsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 专家论证ID
     */
    @ExcelProperty(value = "专家论证ID")
    private Long reviewId;

    /**
     * 施工方案ID (逻辑外键至 prj_construction_plans.plan_id)
     */
    @ExcelProperty(value = "施工方案ID (逻辑外键至 prj_construction_plans.plan_id)")
    private Long planId;

    /**
     * 论证会议日期
     */
    @ExcelProperty(value = "论证会议日期")
    private Date reviewDate;

    /**
     * 会议地点
     */
    @ExcelProperty(value = "会议地点")
    private String meetingLocation;

    /**
     * 论证结论 (通过, 修改后通过, 不通过)
     */
    @ExcelProperty(value = "论证结论 (通过, 修改后通过, 不通过)")
    private String conclusion;

    /**
     * 专家意见摘要/修改要求
     */
    @ExcelProperty(value = "专家意见摘要/修改要求")
    private String expertOpinionSummary;

    /**
     * 专家利害关系预警信息 (对应附件四.(二).1)
     */
    @ExcelProperty(value = "专家利害关系预警信息 (对应附件四.(二).1)")
    private String conflictOfInterestWarning;

    /**
     * 论证报告文档ID (逻辑外键至 sys_documents.document_id)
     */
    @ExcelProperty(value = "论证报告文档ID (逻辑外键至 sys_documents.document_id)")
    private Long reportDocumentId;

    /**
     * 会议组织者ID (逻辑外键至 sys_users.user_id)
     */
    @ExcelProperty(value = "会议组织者ID (逻辑外键至 sys_users.user_id)")
    private Long convenorUserId;
}

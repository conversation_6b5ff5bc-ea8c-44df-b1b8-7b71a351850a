package org.dromara.util;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;

/**
 * <AUTHOR>
 * @date 2024/10/29 9:05
 * @Description 图片比例压缩工具类
 * @Version 1.0
 */
public class ImageResizeUtils {

    /**
     * 压缩图片大小
     *
     * @param oldImageStream
     * @param futureKb
     * @return
     * @throws IOException
     */
    private static byte[] resizeImage(InputStream oldImageStream, String jpgType, Integer futureKb) throws IOException {
        try {
            BufferedImage image = ImageIO.read(oldImageStream);
            ByteArrayOutputStream outputStream = null;
            for (double scale = 0.95d; scale > 0.0d; scale -= 0.01d) {
                int newWidth = (int) (image.getWidth() * scale);
                int newHeight = (int) (image.getHeight() * scale);

                BufferedImage bufferedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
                Graphics2D graphics = bufferedImage.createGraphics();
                graphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                graphics.drawImage(image, 0, 0, newWidth, newHeight, null);
                graphics.dispose();

                outputStream = new ByteArrayOutputStream();

                ImageIO.write(bufferedImage, jpgType, outputStream);

                if (outputStream.size() / 1024 <= futureKb) {
                    break;
                }
            }
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static byte[] resizeImageJpg(InputStream oldImageStream, Integer futureKb) throws IOException {
        return resizeImage(oldImageStream, "jpg", futureKb);
    }

    public static byte[] resizeImagePng(InputStream oldImageStream, Integer futureKb) throws IOException {
        return resizeImage(oldImageStream, "png", futureKb);
    }

    public static byte[] resizeImageJpeg(InputStream oldImageStream, Integer futureKb) throws IOException {
        return resizeImage(oldImageStream, "jpeg", futureKb);
    }

    public static byte[] resizeImageJpg(byte[] oldImageStream, Integer futureKb) throws IOException {
        return resizeImageJpg(new ByteArrayInputStream(oldImageStream), futureKb);
    }

    public static byte[] resizeImagePng(byte[] oldImageStream, Integer futureKb) throws IOException {
        return resizeImagePng(new ByteArrayInputStream(oldImageStream), futureKb);
    }

    public static byte[] resizeImageJpeg(byte[] oldImageStream, Integer futureKb) throws IOException {
        return resizeImageJpeg(new ByteArrayInputStream(oldImageStream), futureKb);
    }

    /**
     * 下载网络图片
     *
     * @param imageHttpUrl
     * @return
     */
    public static InputStream downloadImage(String imageHttpUrl) {

        InputStream inputStream = null;
        try {
            URL url = new URL(imageHttpUrl);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(10000);
            inputStream = connection.getInputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return inputStream;
    }
}

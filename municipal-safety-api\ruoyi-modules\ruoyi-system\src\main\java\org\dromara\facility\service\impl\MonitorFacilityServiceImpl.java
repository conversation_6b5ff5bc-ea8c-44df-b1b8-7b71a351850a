package org.dromara.facility.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.expert.mapper.ProjectMapper;
import org.dromara.projects.domain.PrjHazardousItems;
import org.dromara.projects.domain.vo.PrjHazardousItemsVo;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.mapper.PrjHazardousItemsMapper;
import org.dromara.projects.mapper.PrjProjectsMapper;
import org.springframework.stereotype.Service;
import org.dromara.facility.domain.bo.MonitorFacilityBo;
import org.dromara.facility.domain.vo.MonitorFacilityVo;
import org.dromara.facility.domain.MonitorFacility;
import org.dromara.facility.mapper.MonitorFacilityMapper;
import org.dromara.facility.service.IMonitorFacilityService;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 监测设备Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-24
 */
@RequiredArgsConstructor
@Service
public class MonitorFacilityServiceImpl implements IMonitorFacilityService {

    private final MonitorFacilityMapper baseMapper;
    private final PrjProjectsMapper prjProjectsMapper;
    private final PrjHazardousItemsMapper prjHazardousItemsMapper;


    /**
     * 查询监测设备
     *
     * @param id 主键
     * @return 监测设备
     */
    @Override
    public MonitorFacilityVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询监测设备列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 监测设备分页列表
     */
    @Override
    public TableDataInfo<MonitorFacilityVo> queryPageList(MonitorFacilityBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MonitorFacility> lqw = buildQueryWrapper(bo);
        Page<MonitorFacilityVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        List<MonitorFacilityVo> records = result.getRecords();

        if (CollectionUtil.isNotEmpty(records)) {
            List<Long> projectIds = records.stream().map(MonitorFacilityVo::getProjectId).toList();

            List<PrjProjectsVo> prjProjectsVos = prjProjectsMapper.selectVoByIds(projectIds);

            Map<Long, String> projectMap = prjProjectsVos.stream().collect(Collectors.toMap(PrjProjectsVo::getProjectId, PrjProjectsVo::getProjectName));

            List<Long> itemId = records.stream().map(MonitorFacilityVo::getItemId).toList();

            List<PrjHazardousItemsVo> itemsList = prjHazardousItemsMapper.selectVoByIds(itemId);

            Map<Long, String> itemMap = itemsList.stream().collect(Collectors.toMap(PrjHazardousItemsVo::getItemId, PrjHazardousItemsVo::getItemName));

            for (MonitorFacilityVo record : records) {
                record.setProjectName(projectMap.getOrDefault(record.getProjectId(), ""));
                record.setItemName(itemMap.getOrDefault(record.getItemId(), ""));
            }
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的监测设备列表
     *
     * @param bo 查询条件
     * @return 监测设备列表
     */
    @Override
    public List<MonitorFacilityVo> queryList(MonitorFacilityBo bo) {
        LambdaQueryWrapper<MonitorFacility> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MonitorFacility> buildQueryWrapper(MonitorFacilityBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MonitorFacility> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MonitorFacility::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getDevNo()), MonitorFacility::getDevNo, bo.getDevNo());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceType()), MonitorFacility::getDeviceType, bo.getDeviceType());
        lqw.eq(StringUtils.isNotBlank(bo.getManufacturers()), MonitorFacility::getManufacturers, bo.getManufacturers());
        lqw.eq(StringUtils.isNotBlank(bo.getDataSources()), MonitorFacility::getDataSources, bo.getDataSources());
        lqw.eq(bo.getProjectId() != null, MonitorFacility::getProjectId, bo.getProjectId());
        lqw.eq(bo.getItemId() != null, MonitorFacility::getItemId, bo.getItemId());
        return lqw;
    }

    /**
     * 新增监测设备
     *
     * @param bo 监测设备
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MonitorFacilityBo bo) {
        MonitorFacility add = MapstructUtils.convert(bo, MonitorFacility.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改监测设备
     *
     * @param bo 监测设备
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MonitorFacilityBo bo) {
        MonitorFacility update = MapstructUtils.convert(bo, MonitorFacility.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MonitorFacility entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除监测设备信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}

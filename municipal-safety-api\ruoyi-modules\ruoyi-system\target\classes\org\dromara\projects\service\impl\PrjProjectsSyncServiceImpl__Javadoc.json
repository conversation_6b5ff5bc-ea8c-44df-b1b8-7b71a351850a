{"doc": " 项目同步Service业务层处理\n\n <AUTHOR>\n @date 2025-07-28\n", "fields": [], "enumConstants": [], "methods": [{"name": "getSelf", "paramTypes": [], "doc": " 获得自身的代理对象，解决 AOP 生效问题\n\n @return 自己\n"}, {"name": "validateConstructionPermitNo", "paramTypes": ["java.lang.String"], "doc": " 验证施工许可证编号\n"}, {"name": "validateUserPermissionForSync", "paramTypes": ["java.lang.String"], "doc": " 验证用户权限 - 确保施工方用户只能同步属于自己公司的项目\n"}, {"name": "isConstructionCompanyUser", "paramTypes": ["org.dromara.common.core.domain.model.LoginUser"], "doc": " 检查用户是否属于施工方\n"}, {"name": "validateProjectOwnership", "paramTypes": ["org.dromara.common.core.domain.model.LoginUser", "java.lang.String"], "doc": " 验证项目归属 - 检查项目是否属于当前用户的施工公司\n"}, {"name": "preValidateProjectOwnershipFromApi", "paramTypes": ["org.dromara.common.core.domain.model.LoginUser", "java.lang.String"], "doc": " 从外部API预验证项目归属\n"}, {"name": "validateSyncData", "paramTypes": ["org.dromara.projects.domain.dto.sync.SyncResponseDto"], "doc": " 验证同步数据的完整性\n"}, {"name": "processProjectInfo", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 处理项目基本信息\n"}, {"name": "processAreaInfo", "paramTypes": ["org.dromara.projects.domain.vo.PrjProjectsVo", "java.lang.String", "java.lang.String"], "doc": " 处理地区信息\n\n @param projectVo    项目对象\n @param cityName     市名称（接口返回的XMSZS字段）\n @param districtName 区名称（接口返回的XMSZQ字段）\n"}, {"name": "getCityCodeByName", "paramTypes": ["java.lang.String"], "doc": " 根据市名称获取市编码\n\n @param cityName 市名称\n @return 市编码\n"}, {"name": "getDistrictCodeByName", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据区名称和市编码获取区编码\n\n @param districtName 区名称\n @param cityCode     市编码\n @return 区编码\n"}, {"name": "processAndSaveCompanyInfo", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 处理并保存参建单位信息\n"}, {"name": "aggregateCompanyTypes", "paramTypes": ["java.util.List"], "doc": " 聚合同一企业的多个类型记录\n"}, {"name": "saveOrUpdateEnterpriseWithSync", "paramTypes": ["org.dromara.projects.domain.dto.sync.CjdwDto"], "doc": " 保存或更新企业信息（同步版本，整合webAdd和audit逻辑）\n"}, {"name": "createEnterpriseFromDtoWithSync", "paramTypes": ["org.dromara.projects.domain.dto.sync.CjdwDto"], "doc": " 从DTO创建企业实体（同步版本，整合webAdd逻辑）\n"}, {"name": "updateEnterpriseFromDtoWithMultiTypes", "paramTypes": ["org.dromara.system.domain.SysEnterpriseInfo", "org.dromara.projects.domain.dto.sync.CjdwDto"], "doc": " 从DTO更新企业信息（支持多类型）\n"}, {"name": "executeEnterpriseAuditLogic", "paramTypes": ["org.dromara.system.domain.SysEnterpriseInfo"], "doc": " 执行企业审核通过逻辑（整合audit接口逻辑）\n"}, {"name": "createOrUpdateDepartment", "paramTypes": ["org.dromara.system.domain.SysEnterpriseInfo"], "doc": " 创建或更新部门信息（整合audit接口中的部门创建逻辑）\n"}, {"name": "createEnterpriseUser", "paramTypes": ["org.dromara.system.domain.SysEnterpriseInfo"], "doc": " 创建企业用户（整合audit接口中的用户创建逻辑）\n"}, {"name": "getRoleIdsByEnterpriseTypes", "paramTypes": ["java.lang.String"], "doc": " 根据企业类型获取角色ID数组（支持多类型）\n"}, {"name": "mapCorpTypeToEnterpriseType", "paramTypes": ["java.lang.String"], "doc": " 映射企业类型\n"}, {"name": "findSupervisingQsDept", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 根据XMXX的监督机构信息匹配系统中的质监站部门\n 优先通过统一社会信用代码精确匹配；\n 其次按名称模糊匹配，并结合地区(cityCode/districtCode)限定；\n 支持的deptType：GOV_CITY_QS、GOV_DISTRICT_QS（当传入GOV_QS时在列表接口中已映射）\n"}, {"name": "updateProjectCompanyReferences", "paramTypes": ["org.dromara.projects.domain.vo.PrjProjectsVo", "java.util.Map"], "doc": " 更新项目的单位关联信息（设置部门ID）\n"}, {"name": "processPersonnelInfo", "paramTypes": ["org.dromara.projects.domain.vo.PrjProjectsVo", "java.util.List", "java.util.Map"], "doc": " 处理人员信息\n\n @param projectVo        项目信息\n @param xgryList         外部API返回的人员信息列表\n @param companyDeptIdMap 企业类型到部门ID的映射\n @return 人员ID到角色的映射，用于后续绑定项目人员关联\n"}, {"name": "processSpecialOperationPersonnelBatch", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": " 批量处理特种作业人员信息\n\n @param xgryList  外部API返回的人员信息列表\n @param projectId 项目ID\n"}, {"name": "processSpecialOperationPersonnel", "paramTypes": ["org.dromara.projects.domain.dto.sync.XgryDto", "java.lang.Long", "java.util.List", "java.util.List"], "doc": " 处理单个特种作业人员信息\n\n @param xgry              外部API返回的人员信息\n @param projectId         项目ID\n @param dictOperationList 人员类型字典\n @param companyList       企业类型字典\n @return 特种作业人员对象，如果不是特种作业人员或处理失败返回null\n"}, {"name": "createSpecialOperationPersonnel", "paramTypes": ["org.dromara.projects.domain.dto.sync.XgryDto", "java.lang.Long", "java.lang.String", "org.dromara.system.domain.SysDictData", "java.util.List"], "doc": " 创建新的特种作业人员记录\n"}, {"name": "updateSpecialOperationPersonnel", "paramTypes": ["org.dromara.special.domain.SpecialOperationPersonnel", "org.dromara.projects.domain.dto.sync.XgryDto", "java.lang.Long", "java.lang.String", "org.dromara.system.domain.SysDictData", "java.util.List"], "doc": " 更新现有特种作业人员记录\n"}, {"name": "isValidIdCard", "paramTypes": ["java.lang.String"], "doc": " 验证身份证号是否合法\n"}, {"name": "processIndividualPersonnel", "paramTypes": ["org.dromara.projects.domain.dto.sync.XgryDto", "java.util.Map"], "doc": " 处理单个人员信息\n\n @param xgry             外部API返回的人员信息\n @param companyDeptIdMap 企业类型到部门ID的映射\n @return 人员ID，如果处理失败返回null\n"}, {"name": "createPersonFromXgry", "paramTypes": ["org.dromara.projects.domain.dto.sync.XgryDto", "java.util.Map"], "doc": " 从外部API数据创建新人员\n"}, {"name": "updatePersonFromXgry", "paramTypes": ["org.dromara.person.domain.vo.SysPersonVo", "org.dromara.projects.domain.dto.sync.XgryDto", "java.util.Map"], "doc": " 更新现有人员信息\n"}, {"name": "findEnterpriseIdByName", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 根据企业名称查找企业ID\n"}, {"name": "mapPersonTypeToProjectRole", "paramTypes": ["java.lang.String"], "doc": " 映射人员类型到项目角色\n"}, {"name": "bindProjectPersonnelFromSync", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 绑定项目人员关联（同步版本）\n\n @param projectId        项目ID\n @param personnelRoleMap 人员ID到角色的映射\n"}, {"name": "processEquipmentInfo", "paramTypes": ["org.dromara.projects.domain.vo.PrjProjectsVo", "java.util.List"], "doc": " 处理设备信息\n"}, {"name": "processIndividualEquipment", "paramTypes": ["org.dromara.projects.domain.dto.sync.QzjxDto", "java.lang.Long", "java.util.List"], "doc": " 处理单个设备信息\n\n @param qzjx              外部API返回的设备信息\n @param projectId         项目ID\n @param dictEquipmentList 设备类型字典列表\n @return 设备对象，如果处理失败返回null\n"}, {"name": "createEquipmentFromQzjx", "paramTypes": ["org.dromara.projects.domain.dto.sync.QzjxDto", "java.lang.Long", "java.util.List"], "doc": " 从QzjxDto创建新的设备对象\n"}, {"name": "updateEquipmentFromQzjx", "paramTypes": ["org.dromara.special.domain.SpecialEquipment", "org.dromara.projects.domain.dto.sync.QzjxDto", "java.lang.Long", "java.util.List"], "doc": " 更新现有设备信息，返回是否有变更\n"}, {"name": "setEquipmentTechnicalParameters", "paramTypes": ["org.dromara.special.domain.SpecialEquipment", "org.dromara.projects.domain.dto.sync.QzjxDto"], "doc": " 设置设备技术参数\n"}, {"name": "parseStringToBigDecimal", "paramTypes": ["java.lang.String"], "doc": " 安全地将String转换为BigDecimal\n"}, {"name": "saveOrUpdateProject", "paramTypes": ["org.dromara.projects.domain.vo.PrjProjectsVo"], "doc": " 保存或更新项目信息\n"}, {"name": "createProjectBoFromVo", "paramTypes": ["org.dromara.projects.domain.vo.PrjProjectsVo"], "doc": " 从 PrjProjectsVo 创建 PrjProjectsBo 对象\n"}, {"name": "parseDate", "paramTypes": ["java.lang.String"], "doc": " 解析日期字符串\n"}, {"name": "safeTrim", "paramTypes": ["java.lang.String"], "doc": " 安全地去除字符串前后空格\n\n @param str 待处理的字符串\n @return 去除前后空格的字符串，如果输入为null则返回null\n"}, {"name": "extractGenderFromIdCard", "paramTypes": ["java.lang.String"], "doc": " 从身份证号码中提取性别信息\n\n @param idCard 身份证号码\n @return 性别代码（根据sys_user_sex字典：0-男性，1-女性，2-未知），如果提取失败返回null\n"}], "constructors": []}
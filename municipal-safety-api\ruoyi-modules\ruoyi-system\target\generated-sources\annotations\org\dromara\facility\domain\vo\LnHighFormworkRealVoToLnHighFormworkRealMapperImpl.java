package org.dromara.facility.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.LnHighFormworkReal;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T08:57:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnHighFormworkRealVoToLnHighFormworkRealMapperImpl implements LnHighFormworkRealVoToLnHighFormworkRealMapper {

    @Override
    public LnHighFormworkReal convert(LnHighFormworkRealVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnHighFormworkReal lnHighFormworkReal = new LnHighFormworkReal();

        lnHighFormworkReal.setId( arg0.getId() );
        lnHighFormworkReal.setEid( arg0.getEid() );
        lnHighFormworkReal.setSettlement( arg0.getSettlement() );
        lnHighFormworkReal.setInclinationAngleOfVerticalPole( arg0.getInclinationAngleOfVerticalPole() );
        lnHighFormworkReal.setHorizontalInclination( arg0.getHorizontalInclination() );
        lnHighFormworkReal.setBearing( arg0.getBearing() );
        lnHighFormworkReal.setHorizontalDisplacement( arg0.getHorizontalDisplacement() );
        lnHighFormworkReal.setVerticalDisplacement( arg0.getVerticalDisplacement() );
        lnHighFormworkReal.setEventTime( arg0.getEventTime() );
        lnHighFormworkReal.setDevNo( arg0.getDevNo() );
        lnHighFormworkReal.setCreateTime( arg0.getCreateTime() );

        return lnHighFormworkReal;
    }

    @Override
    public LnHighFormworkReal convert(LnHighFormworkRealVo arg0, LnHighFormworkReal arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setEid( arg0.getEid() );
        arg1.setSettlement( arg0.getSettlement() );
        arg1.setInclinationAngleOfVerticalPole( arg0.getInclinationAngleOfVerticalPole() );
        arg1.setHorizontalInclination( arg0.getHorizontalInclination() );
        arg1.setBearing( arg0.getBearing() );
        arg1.setHorizontalDisplacement( arg0.getHorizontalDisplacement() );
        arg1.setVerticalDisplacement( arg0.getVerticalDisplacement() );
        arg1.setEventTime( arg0.getEventTime() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}

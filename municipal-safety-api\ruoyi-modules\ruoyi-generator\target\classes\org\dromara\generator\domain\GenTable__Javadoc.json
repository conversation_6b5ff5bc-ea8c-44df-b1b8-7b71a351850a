{"doc": " 业务表 gen_table\n\n <AUTHOR> Li\n", "fields": [{"name": "tableId", "doc": " 编号\n"}, {"name": "dataName", "doc": " 数据源名称\n"}, {"name": "tableName", "doc": " 表名称\n"}, {"name": "tableComment", "doc": " 表描述\n"}, {"name": "subTableName", "doc": " 关联父表的表名\n"}, {"name": "subTableFkName", "doc": " 本表关联父表的外键名\n"}, {"name": "className", "doc": " 实体类名称(首字母大写)\n"}, {"name": "tplCategory", "doc": " 使用的模板（crud单表操作 tree树表操作 sub主子表操作）\n"}, {"name": "packageName", "doc": " 生成包路径\n"}, {"name": "moduleName", "doc": " 生成模块名\n"}, {"name": "businessName", "doc": " 生成业务名\n"}, {"name": "functionName", "doc": " 生成功能名\n"}, {"name": "function<PERSON><PERSON>or", "doc": " 生成作者\n"}, {"name": "genType", "doc": " 生成代码方式（0zip压缩包 1自定义路径）\n"}, {"name": "gen<PERSON><PERSON>", "doc": " 生成路径（不填默认项目路径）\n"}, {"name": "pkColumn", "doc": " 主键信息\n"}, {"name": "columns", "doc": " 表列信息\n"}, {"name": "options", "doc": " 其它生成选项\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "treeCode", "doc": " 树编码字段\n"}, {"name": "treeParentCode", "doc": " 树父编码字段\n"}, {"name": "treeName", "doc": " 树名称字段\n"}, {"name": "parentMenuId", "doc": " 上级菜单ID字段\n"}, {"name": "parentMenuName", "doc": " 上级菜单名称字段\n"}], "enumConstants": [], "methods": [], "constructors": []}